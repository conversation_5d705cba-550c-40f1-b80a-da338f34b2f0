import Mock from "mockjs";
import webpackMockServer from "webpack-mock-server";
import React from "react";

export default webpackMockServer.add((app, helper) => {

    app.get('/itm-user/api/query/menu/userMenus', (req, res) => {
        const products = Mock.mock([
            {
                "id": 1,
                "nodeId": "1",
                "parentId": null,
                "parentNodeId": null,
                "title": "首页",
                "code": "home",
                "type": "HOME",
                "display": null,
                "image": null,
                "url": "/",
                "sysEnabled": 1,
                "sysIndex": 1,
                "children": [
                    {
                        "id": 2,
                        "nodeId": "2",
                        "parentId": 1,
                        "parentNodeId": "1",
                        "title": "通用管理",
                        "code": "COMMON",
                        "type": "MODULE",
                        "display": null,
                        "image": null,
                        "url": null,
                        "sysEnabled": 1,
                        "sysIndex": 1,
                        "children": [
                            {
                                "id": 11,
                                "nodeId": "11",
                                "parentId": 2,
                                "parentNodeId": "2",
                                "title": "基础信息管理",
                                "code": "base_manage",
                                "type": "MENU",
                                "display": null,
                                "image": null,
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 12,
                                        "nodeId": "12",
                                        "parentId": 11,
                                        "parentNodeId": "11",
                                        "title": "字典管理",
                                        "code": "dict_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/dict",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 13,
                                        "nodeId": "13",
                                        "parentId": 11,
                                        "parentNodeId": "11",
                                        "title": "文件管理",
                                        "code": "file_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/file",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 10,
                                "nodeId": "10",
                                "parentId": 2,
                                "parentNodeId": "2",
                                "title": "用户管理",
                                "code": "user_manage",
                                "type": "MENU",
                                "display": null,
                                "image": null,
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 2,
                                "children": [
                                    {
                                        "id": 14,
                                        "nodeId": "14",
                                        "parentId": 10,
                                        "parentNodeId": "10",
                                        "title": "单位管理",
                                        "code": "unit_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/unit",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 15,
                                        "nodeId": "15",
                                        "parentId": 10,
                                        "parentNodeId": "10",
                                        "title": "部门管理",
                                        "code": "dept_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/dept",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 16,
                                        "nodeId": "16",
                                        "parentId": 10,
                                        "parentNodeId": "10",
                                        "title": "岗位管理",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/post",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 17,
                                        "nodeId": "17",
                                        "parentId": 10,
                                        "parentNodeId": "10",
                                        "title": "条线管理",
                                        "code": "line_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/line",
                                        "sysEnabled": 1,
                                        "sysIndex": 4,
                                        "children": []
                                    },
                                    {
                                        "id": 18,
                                        "nodeId": "18",
                                        "parentId": 10,
                                        "parentNodeId": "10",
                                        "title": "人员管理",
                                        "code": "user_page",
                                        "type": "APP",
                                        "display": null,
                                        "image": null,
                                        "url": "/user",
                                        "sysEnabled": 1,
                                        "sysIndex": 5,
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 3,
                        "nodeId": "3",
                        "parentId": 1,
                        "parentNodeId": "1",
                        "title": "左侧区域",
                        "code": "left_area",
                        "type": "MODULE",
                        "display": null,
                        "image": "",
                        "url": null,
                        "sysEnabled": 1,
                        "sysIndex": 2,
                        "children": [
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "我要查",
                                "code": "check",
                                "type": "MENU",
                                "display": null,
                                "image": "1.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "制度库",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Institutional.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "合同模板库",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "template_library.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "典型案例库",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "typical_case.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 6,
                                "nodeId": "6",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "我要看",
                                "code": "look",
                                "type": "MENU",
                                "display": null,
                                "image": "3.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 2,
                                "children": [
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "决策分析",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "decision_analysis.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "专题看板",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "special_topic.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "法治素养",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "legal_literacy.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "报告专区",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "ReportZone.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "专题报表",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Specialreport.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "普法宣传",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "popularization.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 8,
                                "nodeId": "8",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "我要学",
                                "code": "study",
                                "type": "MENU",
                                "display": null,
                                "image": "4.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 3,
                                "children": [
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "决策分析",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "decision_analysis.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "决策分析",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "decision_analysis.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 9,
                                "nodeId": "9",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "我要办",
                                "code": "do",
                                "type": "MENU",
                                "display": null,
                                "image": "4.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 4,
                                "children": [
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "制度起草",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "drafting.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "制度废止",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Abolition.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "制度评估",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Evaluation.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "合同起草",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "contract.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "事项起草",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Drafting1.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "执法证申领",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Application.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "案件填报",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "reporting.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "文书起草",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Documentdrafting.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "风险填报",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Riskreporting.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }

                                ]
                            }
                        ]
                    },
                    {
                        "id": 4,
                        "nodeId": "4",
                        "parentId": 1,
                        "parentNodeId": "1",
                        "title": "右侧区域",
                        "code": "right_area",
                        "type": "MODULE",
                        "display": null,
                        "image": "",
                        "url": null,
                        "sysEnabled": 1,
                        "sysIndex": 3,
                        "children": [
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "制度管理",
                                "code": "syma",
                                "type": "MENU",
                                "display": null,
                                "image": "5.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "标签管理",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "LabelManagement.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "制度导入",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Institutionals.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    }

                                ]
                            },
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "法治监督",
                                "code": "ruof",
                                "type": "MENU",
                                "display": null,
                                "image": "6.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "合同管理",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "contract1.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "授权管理",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Authorization.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "执法证管理",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Enforcement.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "监督管理",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "administration.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "普法宣传",
                                "code": "popu",
                                "type": "MENU",
                                "display": null,
                                "image": "7.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "法治书屋",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Law_Library.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "AI普法",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Ai_legal.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "直播平台",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "streaming.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "专题普法",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Specialized.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "法治文化建设",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Construction.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "案件纠纷",
                                "code": "disp",
                                "type": "MENU",
                                "display": null,
                                "image": "8.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "纠纷案件管理",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "disputecase.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "典型案例",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "classic_case.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "案件研讨",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Casediscussion.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "案件报告",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Case_report.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "风险防控",
                                "code": "prev",
                                "type": "MENU",
                                "display": null,
                                "image": "9.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "风险清单",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "RiskList.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "风险管理",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "risk_management.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "控烟立法",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Smoking_control.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    },
                                    {
                                        "id": 21,
                                        "nodeId": "21",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "烟草广告",
                                        "code": "typical_case_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "tobacco_advertisement.png",
                                        "url": "/typicalCaseDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 3,
                                        "children": []
                                    }
                                ]
                            },
                            {
                                "id": 5,
                                "nodeId": "5",
                                "parentId": 3,
                                "parentNodeId": "3",
                                "title": "队伍建设",
                                "code": "team",
                                "type": "MENU",
                                "display": null,
                                "image": "10.png",
                                "url": null,
                                "sysEnabled": 1,
                                "sysIndex": 1,
                                "children": [
                                    {
                                        "id": 19,
                                        "nodeId": "19",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "人才库管理",
                                        "code": "institution_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Pool_Management.png",
                                        "url": "/institutionDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 1,
                                        "children": []
                                    },
                                    {
                                        "id": 20,
                                        "nodeId": "20",
                                        "parentId": 5,
                                        "parentNodeId": "5",
                                        "title": "荣誉管理",
                                        "code": "contract_template_database",
                                        "type": "APP",
                                        "display": null,
                                        "image": "Honor_Management.png",
                                        "url": "/contractTemplateDatabase",
                                        "sysEnabled": 1,
                                        "sysIndex": 2,
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "id": 7,
                "nodeId": "7",
                "parentId": null,
                "parentNodeId": null,
                "title": "预警中心",
                "code": "ALERT_CENTER",
                "type": "HOME",
                "display": null,
                "image": null,
                "url": null,
                "sysEnabled": 1,
                "sysIndex": 2,
                "children": []
            }
        ]);

        res.json({
            success: true,
            data: products,
        });
    });

});
