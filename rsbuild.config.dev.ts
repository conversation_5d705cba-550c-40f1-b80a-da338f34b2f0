import { defineConfig } from "@rsbuild/core";
import commonConfig from "./rsbuild.config";

export default defineConfig({
  ...commonConfig,
  server: {
    port: 8000,
    proxy: {
      "/itm-user": "http://10.95.1.111:8290/",
      "/itm-oss": "http://10.95.1.111:8290/",
      "/itm-archive-oss": "http://10.95.1.111:8290/",
      "/itm-supplier": "http://10.95.1.111:8290/",
      "/itm-template": "http://10.95.1.111:8290/",
      "/itm-collect": "http://10.95.1.111:8290/",
      "/itm-compliance": "http://10.95.1.111:8290/",
      "/itm-dict": "http://10.95.1.111:8290/",
      "/itm-timer": "http://10.95.1.111:8290/",
      "/api": "http://10.95.1.111:8290/",
      "/open": "http://10.95.1.111:8290/",
      "/itm-archive-tag": "http://10.95.1.111:8290/",
      "/itm-archive-institution": "http://10.95.1.111:8290/",
      "/itm-legal-review-contract": "http://10.95.1.111:8290/",
      "/itm-abolish": "http://10.95.1.111:8290/",
      "/itm-institution-draft": "http://127.0.0.1:8308/",
      "/itm-supervise": "http://10.95.1.111:8330/",
    },
  },
});
