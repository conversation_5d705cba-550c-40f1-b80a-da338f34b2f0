import React from "react";
import {SupplierState} from "@/pages/supplier/types";
import {ValuePresenter} from "@/pages/supplier/presenter";
import {SupplierApiImpl} from "@/pages/supplier/model";

const initialState: SupplierState = {
    bankDepositList: null,
    enterpriseTypeList: null,
    typeList: null,
    categoryList: null,
    tableRefreshVersion: 0,
    searchParams: null,
    addSupplierVisible: false,
    enterpriseQualificationAttachmentKey: '',
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<SupplierState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new SupplierApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}