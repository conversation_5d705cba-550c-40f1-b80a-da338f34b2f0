export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
interface uploadedFileProps {
    filename: string;
    base64: string;
}

export interface SupplierState {
    bankDepositList: any;
    enterpriseTypeList: any;
    typeList: any;
    categoryList: any;
    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新
    searchParams: any;
    addSupplierVisible: boolean;
    enterpriseQualificationAttachmentKey: string;//附件回显
}


export interface SupplierApi {

    getBankDepositList: () => Promise<any>;

    getEnterpriseTypeList: () => Promise<any>;

    getTypeList: () => Promise<any>;

    getCategoryList: () => Promise<any>;

    loadSupplierTable: (params: any, sort: any, filter: any) => Promise<any>;

    exportSupplierList: (params: any) => Promise<any>;

    getSupplierInfo: (params: any) => Promise<any>;

    deleteSupplier: (params: any) => Promise<{ success: boolean }>;

    generateFileAuth: (params: any) => Promise<any>;

    addOrUpdateSupplier: (formData: FormData) => Promise<any>;
}