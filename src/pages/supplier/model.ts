import {SupplierApi} from "./types";
import {
    list,
    exportSupplierList,
    del,
    addOrUpdateSupplier,
    getBankDepositList,
    getEnterpriseTypeList,
    getTypeList,
    getCategoryList,
    getSupplierInfo, generateFileAuth
} from "@/api/supplier/supplier";


export class SupplierApiImpl implements SupplierApi {

    getBankDepositList(): Promise<any> {
        return getBankDepositList();
    }

    getEnterpriseTypeList(): Promise<any> {
        return getEnterpriseTypeList();
    }

    getTypeList(): Promise<any> {
        return getTypeList();
    }

    getCategoryList(): Promise<any> {
        return getCategoryList();
    }

    loadSupplierTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    exportSupplierList(params: any): Promise<any> {
        return exportSupplierList(params);
    }

    getSupplierInfo(params: any): Promise<any> {
        return getSupplierInfo(params);
    }

    async generateFileAuth(params: any): Promise<{ success: boolean }> {
        return generateFileAuth(params);
    }

    addOrUpdateSupplier(formData: FormData): Promise<any> {
        return addOrUpdateSupplier(formData);
    }

    async deleteSupplier(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

}