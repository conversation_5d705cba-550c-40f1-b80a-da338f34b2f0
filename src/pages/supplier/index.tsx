import React from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {Col, Popconfirm, Row} from "antd";
import {useValuePresenter} from "@/pages/supplier/hooks/usePresenter";
import {Button, Modal, ProTable} from "@itm/components-pc";
import {Form, FormDate, FormInput, FormSelect, FormTextArea, FormUploader} from "@/components/form-pc";
import moment from "moment";
import FormItem from "antd/es/form/FormItem";
import dayjs from 'dayjs';
import ArchiveFileUploader from "@/components/archive/archiveFileUpload";

interface SupplierManagePageProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, name: string) => void;
}

const SupplierManagePage: React.FC<SupplierManagePageProps> = (props) => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();
    const {state, presenter} = useValuePresenter();

    React.useEffect(() => {
        presenter?.getBankDepositList();
        presenter?.getEnterpriseTypeList();
        presenter?.getTypeList();
        presenter?.getCategoryList();
    }, [props.isComponent]);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    const columns: ProColumns<any>[] = [
        // 单选列 - 仅微应用模式下显示
        {
            title: "选择",
            dataIndex: "selection",
            valueType: "radio",
            search: false,
            hidden: !props.isComponent,
            width: 60,
            render: (_, record) => (
                <input
                    type="radio"
                    name="supplierSelection"
                    onClick={() => {
                        if (props.onSelectedChange) {
                            props.onSelectedChange(record.id, record.name);
                        }
                    }}
                />
            ),
        },
        {
            title: "供应商id",
            dataIndex: "id",
            search: false,
            hidden: true
        },
        {
            title: "企业名称",
            dataIndex: "name",
            render: (dom, entity, index, action, schema) => (
                <span title={entity.name} style={{cursor: 'pointer'}}>
                {entity.name?.length > 25 ? `${entity.name.slice(0, 25)}...` : entity.name}
            </span>
            ),
        },
        {
            title: "企业类型",
            dataIndex: "enterpriseType",
            hidden: props.isComponent,
            render: (text) => {
                if (text && typeof text === "string") {
                    const parts = text.split('#');
                    return parts.length > 1 ? parts[1] : text;
                }
                return '-';
            },
            search: !props.isComponent ? {  // 仅在非微应用模式下添加搜索配置
                transform: (value) => ({enterpriseType: value})
            } : false,
            renderFormItem: (schema, config) => {
                if (!state.enterpriseTypeList || state.enterpriseTypeList.length === 0) {
                    return <span>加载中...</span>;
                }
                return (
                    <FormSelect
                        {...config}
                        name="enterpriseType"
                        options={[
                            {
                                label: null,
                                value: null,
                            },
                            ...state.enterpriseTypeList
                        ]}
                        placeholder="请选择企业类型"
                    />
                );
            }
        },
        {
            title: "法人代表",
            dataIndex: "legalPerson",
            hidden: props.isComponent,
            search: !props.isComponent,
        },
        {
            title: "法人联系电话",
            dataIndex: "legalPersonPhone",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "成立时间",
            dataIndex: "establishmentTime",
            search: false,
            hidden: props.isComponent,
            render: (dom, record) => {
                const timestamp = record.establishmentTime;
                if (!timestamp) return '-';
                const isValidTimestamp = !isNaN(Number(timestamp)) && moment(Number(timestamp)).isValid();
                return isValidTimestamp ? moment(Number(timestamp)).format("YYYY-MM-DD HH:mm:ss") : "-";
            },
        },
        {
            title: "供应商分类",
            dataIndex: "category",
            hidden: props.isComponent,
            render: (text) => {
                if (text && typeof text === "string") {
                    const parts = text.split('#');
                    return parts.length > 1 ? parts[1] : text;
                }
                return '-';
            },
            search: !props.isComponent ? {
                transform: (value) => ({category: value})
            } : false,
            renderFormItem: (schema, config) => {
                if (!state.categoryList || state.categoryList.length === 0) {
                    return <span>加载中...</span>;
                }
                return (
                    <FormSelect
                        {...config}
                        name="enterpriseType"
                        options={[
                            {
                                label: null,
                                value: null,
                            },
                            ...state.categoryList
                        ]}
                        placeholder="供应商分类"
                    />
                );
            }
        },
        {
            title: "供应商类别",
            dataIndex: "type",
            search: false,
            hidden: props.isComponent,
            render: (text) => {
                if (text && typeof text === "string") {
                    const parts = text.split('#');
                    return parts.length > 1 ? parts[1] : text;
                }
                return '-';
            }
        },
        {
            title: "状态",
            dataIndex: "status",
            search: false,
            hidden: props.isComponent,
            render: (text) => {
                switch (text) {
                    case 0:
                    case '0':
                        return '正常';
                    case 1:
                    case '1':
                        return '异常';
                    default:
                        return '-';
                }
            }
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            hidden: props.isComponent,
            render: (_, record) => [
                <a
                    key="edit"
                    onClick={async () => {
                        try {
                            const detail = await presenter?.getSupplierInfo(record.id);
                            if (detail) {
                                form.reset();
                                form.setFieldsValue({supplier: detail});
                                presenter?.resetEnterpriseQualificationAttachmentKey()
                                presenter?.setEnterpriseQualificationAttachmentKey((detail as any)?.enterpriseQualificationAttachmentKey);
                                presenter?.setAddSupplierVisible(true); // 打开新增/编辑弹窗
                            }
                        } catch (e) {
                            console.error("加载详情失败", e);
                        }
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title="删除提示"
                    description="确认要删除这个模板吗?"
                    onConfirm={() => {
                        presenter?.deleteSupplier(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    return (
        <PageContainer>
            <Row>
                <Col span={24}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="供应商列表"
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                !props.isComponent && ( // 添加条件判断
                                    <Button
                                        key="upload"
                                        type="primary"
                                        onClick={() => {
                                            form.reset();
                                            presenter?.resetUploadFile()
                                            presenter?.resetEnterpriseQualificationAttachmentKey()
                                            presenter?.setAddSupplierVisible(true)
                                        }}
                                    >
                                        新增供应商
                                    </Button>
                                ),
                                !props.isComponent && ( // 添加条件判断
                                    <Button
                                        key="export"
                                        type="default"
                                        onClick={() => {
                                            presenter?.exportSupplierList();
                                        }}
                                    >
                                        导出数据
                                    </Button>
                                )
                            ].filter(Boolean)
                        }}
                        request={async (params, sort, filter) => {
                            return presenter?.searchSupplierTable(params, sort, filter);
                        }}
                    />
                </Col>
            </Row>


            <Modal
                width={1000}
                centered={true}
                open={state.addSupplierVisible}
                title="新增供应商"
                onCancel={() => {
                    presenter?.setAddSupplierVisible(false);
                    presenter?.resetUploadFile();
                }}
                onOk={async () => {
                    if (!await form.validate()) {
                        return "";
                    }
                    await form.submit();
                    presenter?.addSupplier(form.getFieldsValue());
                    presenter?.resetUploadFile()
                    form.reset();
                }}
            >

                <Form
                    form={form}
                    layout={"vertical"}
                >
                    <Row gutter={16}>
                        {/* 左侧列 */}
                        <Col span={12}>
                            <FormInput
                                required={true}
                                name={["supplier", "id"]}
                                label={"id"}
                                hidden={true}
                            />
                            <FormInput
                                required={true}
                                name={["supplier", "name"]}
                                label={"企业名称"}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['姓名不能为空']
                                }}
                            />
                            <FormInput
                                required={false}
                                name={["supplier", "registrationAuthority"]}
                                label={"登记机关"}
                            />
                            <FormInput
                                required={false}
                                name={["supplier", "registeredCapital"]}
                                label={"注册资金（万元）"}

                            />
                            <FormInput
                                required={false}
                                name={["supplier", "businessScope"]}
                                label={"经营范围"}
                            />
                            <FormDate
                                required={true}
                                name={["supplier", "establishmentTime"]}
                                label={"成立时间"}
                                dateFormat={"YYYY-MM-DD HH:mm:ss"}
                                datePrecision={"day"}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['成立时间不能为空']
                                }}
                            />
                            <FormInput
                                required={true}
                                name={["supplier", "legalPerson"]}
                                label={"法人代表"}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['法人代表不能为空']
                                }}
                            />
                            <FormSelect
                                required={false}
                                name={["supplier", "bankDeposit"]}
                                label={"企业账号开户银行"}
                                options={state.bankDepositList}
                            />
                            <FormItem
                                label="经营起止日期"
                            >
                                <Row gutter={8}>
                                    <Col span={11}>
                                        <FormDate
                                            required={false}
                                            name={["supplier", "businessStartTime"]}
                                            dateFormat={"YYYY-MM-DD HH:mm:ss"}
                                        />
                                    </Col>
                                    <Col span={2}>
                                        <span style={{lineHeight: '32px'}}>至</span>
                                    </Col>
                                    <Col span={11}>
                                        <FormDate
                                            required={false}
                                            name={["supplier", "businessEndTime"]}
                                            dateFormat={"YYYY-MM-DD HH:mm:ss"}
                                        />
                                    </Col>
                                </Row>
                            </FormItem>

                        </Col>

                        {/* 右侧列 */}
                        <Col span={12}>
                            <FormInput
                                required={true}
                                name={["supplier", "license"]}
                                label={"营业执照注册号"}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['营业执照注册号不能为空']
                                }}
                            />
                            <FormSelect
                                required={false}
                                name={["supplier", "enterpriseType"]}
                                label={"企业类型"}
                                options={state.enterpriseTypeList}
                            />
                            <FormInput
                                required={false}
                                name={["supplier", "enterpriseAddress"]}
                                label={"企业地址"}

                            />
                            <FormSelect
                                required={true}
                                name={["supplier", "type"]}
                                label={"供应商类型"}
                                options={state.typeList}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['供应商类型不能为空']
                                }}
                            />
                            <FormSelect
                                required={true}
                                name={["supplier", "category"]}
                                label={"所属分类"}
                                options={state.categoryList}
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return []
                                    }
                                    return ['所属分类不能为空']
                                }}
                            />
                            <FormInput
                                required={false}
                                name={["supplier", "legalPersonPhone"]}
                                label={"法人联系电话"}
                            />
                            <FormInput
                                required={false}
                                name={["supplier", "bankAccount"]}
                                label={"银行账户"}
                            />
                            <FormItem
                                label="合作起止日期"
                                required={true}
                            >
                                <Row gutter={8}>
                                    <Col span={11}>
                                        <FormDate
                                            required={false}
                                            name={["supplier", "cooperateStartTime"]}
                                            dateFormat={"YYYY-MM-DD HH:mm:ss"}
                                            validateFunction={async (content) => {
                                                const value = content.value;
                                                if (value) {
                                                    return []
                                                }
                                                return ['合作起止日期不能为空']
                                            }}
                                        />
                                    </Col>
                                    <Col span={2}>
                                        <span style={{lineHeight: '32px'}}>至</span>
                                    </Col>
                                    <Col span={11}>
                                        <FormDate
                                            required={false}
                                            name={["supplier", "cooperateEndTime"]}
                                            dateFormat={"YYYY-MM-DD HH:mm:ss"}
                                            validateFunction={async (content) => {
                                                const value = content.value;
                                                if (value) {
                                                    return []
                                                }
                                                return ['合作起止日期不能为空']
                                            }}
                                        />
                                    </Col>
                                </Row>
                            </FormItem>
                        </Col>
                    </Row>

                    <ArchiveFileUploader
                        label="企业资质"
                        uploaderMaxCount={10}
                        uploaderAccept="*"
                        archiveFileKey={state.enterpriseQualificationAttachmentKey}
                        onKeyChange={(key) => {
                            presenter?.setEnterpriseQualificationAttachmentKey(key);
                        }}
                    />

                    <FormTextArea
                        required={false}
                        name={["supplier", "remark"]}
                        label={"备注"}
                    />

                </Form>

            </Modal>


        </PageContainer>
    );
};

export default SupplierManagePage;