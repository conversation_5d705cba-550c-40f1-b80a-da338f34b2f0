import {SupplierApi, SupplierState, Dispatch} from "@/pages/supplier/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<SupplierState>;

    private readonly api: SupplierApi;

    private state: SupplierState;

    public constructor(dispatch: Dispatch<SupplierState>, api: SupplierApi, state: SupplierState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: SupplierState) => {
        this.state = newStatus;
    }

    // 刷新表格数据
    public refreshSupplierTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1,
                searchParams: null,
            }
        })
    }

    public getBankDepositList = async () => {
        const res = await this.api.getBankDepositList()
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    bankDepositList: res.data
                }
            })
        }
    }
    public getEnterpriseTypeList = async () => {
        const res = await this.api.getEnterpriseTypeList()
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    enterpriseTypeList: res.data
                }
            })
        }
    }
    public getTypeList = async () => {
        const res = await this.api.getTypeList()
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    typeList: res.data
                }
            })
        }
    }
    public getCategoryList = async () => {
        const res = await this.api.getCategoryList()
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    categoryList: res.data
                }
            })
        }
    }

    // 删除数据
    public deleteSupplier = async (record: SupplierState) => {
        try {
            const res = await this.api.deleteSupplier(record);
            if (res.success) {
                this.refreshSupplierTable();
            }
        } catch (e) {
            console.error("删除失败", e);
        }
    }

    // 标签表格数据加载函数
    public searchSupplierTable = (params: any, sort: any, filter: any) => {
        const {current, pageSize, ...restParams} = params;

        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchParams: restParams,
            };
        });
        return this.api.loadSupplierTable(params, sort, filter);
    }

    public exportSupplierList = () => {
        return this.api.exportSupplierList(this.state.searchParams);
    }

    public getSupplierInfo = async (id: number) => {
        const params = {
            id
        }
        const res = await this.api.getSupplierInfo(params);
        if (res.success) {
            return res.data;
        }
    }

    public setEnterpriseQualificationAttachmentKey = (enterpriseQualificationAttachmentKey: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enterpriseQualificationAttachmentKey: enterpriseQualificationAttachmentKey
            };
        });
    }

    public resetEnterpriseQualificationAttachmentKey = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enterpriseQualificationAttachmentKey: ''
            };
        });
    }

    public resetUploadFile = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
            };
        });
    }

    public setAddSupplierVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addSupplierVisible: v
            }
        })
    }

    public addSupplier = async (fieldsValue: any) => {
        const params = fieldsValue.supplier
        params.enterpriseQualificationAttachmentKey = this.state.enterpriseQualificationAttachmentKey
        await this.api.addOrUpdateSupplier(params);
        this.setAddSupplierVisible(false)
        this.refreshSupplierTable();
    };

}