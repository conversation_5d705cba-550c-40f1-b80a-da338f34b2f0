import {SegmentApi} from "@/pages/segment/types";
import {getSegmentPage,getSegmentDetail,del,save,init} from "@/api/enforcementSegment/segment"

/**
 * Model层接口实现
 * SegmentApiImpl 实现了SegmentApi接口，提供了数据的增删改查操作
 */

export class SegmentApiImpl implements SegmentApi {
    getUnitOptions(): Promise<any>;
    // @ts-ignore
    saveSegment(params: any): Promise<any>{
        return save(params);
    };
    initSegment(id:number[]):Promise<any>{
        return init(id);
    };
    deleteSegment(params: any):Promise<any>{
        return del(params);
    };
    loadSegmentTable(params: any, sort: any, filter: any): Promise<any> {
        return getSegmentPage(params, sort, filter, []);
    }

}