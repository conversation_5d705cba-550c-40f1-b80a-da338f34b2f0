import {SegmentApi, SegmentData, UnitOption} from "@/pages/segment/types";
import {getSegmentPage, getSegmentDetail, del, save, init} from "@/api/enforcementSegment/segment";

/**
 * Model层接口实现
 * SegmentApiImpl 实现了SegmentApi接口，提供了数据的增删改查操作
 */
export class SegmentApiImpl implements SegmentApi {

    async loadSegmentTable(params: any, sort: any, filter: any): Promise<{
        data: SegmentData[];
        success: boolean;
        total: number;
    }> {
        try {
            const response = await getSegmentPage(params, sort, filter, [
                {
                    key: "unitName",
                    type: "LIKE",
                },
                {
                    key: "startNumber",
                    type: "GTE",
                },
                {
                    key: "endNumber",
                    type: "LTE",
                },
                {
                    key: "status",
                    type: "EQ",
                }
            ]);

            if (response.success) {
                return {
                    data: response.data || [],
                    success: true,
                    total: response.total || 0
                };
            } else {
                return {
                    data: [],
                    success: false,
                    total: 0
                };
            }
        } catch (error) {
            console.error('加载号段表格数据失败:', error);
            return {
                data: [],
                success: false,
                total: 0
            };
        }
    }

    async saveSegment(params: Partial<SegmentData>): Promise<{
        success: boolean;
        data?: any;
    }> {
        try {
            const response = await save(params);
            return {
                success: response.success,
                data: response.data
            };
        } catch (error) {
            console.error('保存号段失败:', error);
            return {
                success: false
            };
        }
    }

    async getUnitOptions(): Promise<{
        success: boolean;
        data: UnitOption[];
    }> {
        try {
            // 这里可以调用实际的单位选项API，目前使用模拟数据
            const mockUnitOptions: UnitOption[] = [
                { label: "市公安局", value: "市公安局", code: "SGAJ" },
                { label: "交警支队", value: "交警支队", code: "JJZD" },
                { label: "消防支队", value: "消防支队", code: "XFZD" },
                { label: "城管局", value: "城管局", code: "CGJ" },
                { label: "环保局", value: "环保局", code: "HBJ" }
            ];

            return {
                success: true,
                data: mockUnitOptions
            };
        } catch (error) {
            console.error('获取单位选项失败:', error);
            return {
                success: false,
                data: []
            };
        }
    }

    async initSegment(segmentIds: number[]): Promise<{
        success: boolean;
        data?: any;
    }> {
        try {
            const response = await init(segmentIds);
            return {
                success: response.success,
                data: response.data
            };
        } catch (error) {
            console.error('初始化号段失败:', error);
            return {
                success: false
            };
        }
    }

    async deleteSegment(params: { id: number }): Promise<{
        success: boolean;
    }> {
        try {
            const response = await del(params);
            return {
                success: response.success
            };
        } catch (error) {
            console.error('删除号段失败:', error);
            return {
                success: false
            };
        }
    }
}