import {SegmentApi, SegmentState, Dispatch, SegmentData} from "@/pages/segment/types";
import {message} from "antd";

export class SegmentPresenter {

    private readonly dispatch: Dispatch<SegmentState>;

    private readonly api: SegmentApi;

    private state: SegmentState;
    public constructor(dispatch: Dispatch<SegmentState>, api: SegmentApi, state: SegmentState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }
    public syncState = (newStatus: SegmentState) => {
        this.state = newStatus;
    }
    // 号段表格数据加载函数
    public searchSegmentTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params
        }
        return this.api.loadSegmentTable(searchParams, sort, filter);
    }

    //修改新增模态框的显示状态
    public showSegmentAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                segmentAddVisible: true
            }
        })
    }
    //修改新增模态框的隐藏状态
    public hideSegmentAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                segmentAddVisible: false
            }
        })
    }
    //修改编辑模态框的显示状态
    public showSegmentEditModal = (record: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditSegment: record,
                segmentEditVisible: true
            }
        })
    }
    //修改编辑模态框的隐藏状态
    public hideSegmentEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditSegment: null,
                segmentEditVisible: false,
            }
        })
    }

    //修改当前选中的行数据
    public changeSelectedRowAndKeys = (selectedRowKeys: number[], selectedRows: any[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedRowKeys: selectedRowKeys,
                selectedRows: selectedRows
            }
        })
    }

    //提交号段
    public submitSegment = async (params: any) => {
        try {
            const result = await this.api.saveSegment(params);
            if (result.success) {
                message.success('保存成功');
                this.refreshTable();
                return result;
            } else {
                message.error('保存失败');
                return result;
            }
        } catch (error) {
            console.error('保存号段失败:', error);
            message.error('保存失败');
            return { success: false };
        }
    }
    //删除号段
    public deleteSegment = async (record: SegmentData) => {
        try {
            const result = await this.api.deleteSegment({ id: record.id });
            if (result.success) {
                message.success('删除成功');
                this.refreshTable();
                return result;
            } else {
                message.error('删除失败');
                return result;
            }
        } catch (error) {
            console.error('删除号段失败:', error);
            message.error('删除失败');
            return { success: false };
        }
    }

    // 刷新表格
    private refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

}