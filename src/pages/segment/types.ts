export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface SegmentState {
    segmentEditVisible:boolean,
    segmentAddVisible:boolean,
    currentEditSegment:any,
    //单位列表
    unitOptions:any[],
    //当前选中的行数据ID
    selectedRowKeys:number[],
    //选中的行数据
    selectedRows:any[],
    //表格刷新版本号
    tableRefreshVersion:number,
}
export interface SegmentApi {

    loadSegmentTable: (params: any, sort: any, filter: any) => Promise<any>;

    saveSegment: (params: any) => Promise<any>;

    getUnitOptions: () => Promise<any>;

    initSegment: (SegmentIds: number[]) => Promise<any>;

    deleteSegment: (params: any) => Promise<any>;

}