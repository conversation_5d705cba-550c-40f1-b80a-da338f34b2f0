export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

// 号段数据接口
export interface SegmentData {
    id: number;
    unitName: string;
    startNumber: number;
    endNumber: number;
    totalCount: number;
    usedCount: number;
    availableCount: number;
    status: number; // 0: 未启用, 1: 启用, 2: 已满
    createTime: string;
    updateTime?: string;
    remark?: string;
}

// 单位选项接口
export interface UnitOption {
    label: string;
    value: string;
    code?: string;
}

export interface SegmentState {
    segmentEditVisible: boolean;
    segmentAddVisible: boolean;
    currentEditSegment: SegmentData | null;
    // 单位列表
    unitOptions: UnitOption[];
    // 当前选中的行数据ID
    selectedRowKeys: number[];
    // 选中的行数据
    selectedRows: SegmentData[];
    // 表格刷新版本号
    tableRefreshVersion: number;
}

export interface SegmentApi {
    loadSegmentTable: (params: any, sort: any, filter: any) => Promise<{
        data: SegmentData[];
        success: boolean;
        total: number;
    }>;

    saveSegment: (params: Partial<SegmentData>) => Promise<{
        success: boolean;
        data?: any;
    }>;

    getUnitOptions: () => Promise<{
        success: boolean;
        data: UnitOption[];
    }>;

    initSegment: (segmentIds: number[]) => Promise<{
        success: boolean;
        data?: any;
    }>;

    deleteSegment: (params: { id: number }) => Promise<{
        success: boolean;
    }>;
}