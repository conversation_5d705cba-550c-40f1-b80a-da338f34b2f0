import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {ProTable, Button} from "@/components-pc";
import {Form, InputNumber, Modal, Popconfirm, Select, Input, message, Space} from "antd";
import {useSegmentPresenter} from "@/pages/segment/hooks/usePresenter";
import HRBody from "@/components/HRBody";
import Page from "@/components/Page";
import HRHeader from "@/components/HRHeader";
import {HomeOutlined, PlusOutlined} from "@ant-design/icons";
import HRCard from "@/components/HRCard";
import IProTable from "@/components/HRTable";

const SegmentManagePage: React.FC = () => {
    const actionRef = React.useRef<ActionType>(null);
    const [form] = Form.useForm();
    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useSegmentPresenter();

    React.useEffect(() => {
        actionRef.current?.reload();
    },[state.tableRefreshVersion])

    //使用钩子监听编辑
    React.useEffect(() => {
        if (!state.currentEditSegment){
            form.resetFields()
        }else {
            form.setFieldsValue(state.currentEditSegment)
        }
    },[state.currentEditSegment])
    const columns: ProColumns<any>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
        },
        {
            title: "起始编号",
            dataIndex: "startNumber",
            search: false,
        },
        {
            title: "结束编号",
            dataIndex: "endNumber",
            search: false,
        },
        {
            title: "号码总数",
            dataIndex: "totalCount",
            search: false,
        },
        {
            title: "使用数量",
            dataIndex: "inUseCount",
            search: false,
        },
        {
            title: "未使用数量",
            dataIndex: "unUsedCount",
            search: false,
        },
        {
            title: "未启用",
            dataIndex:"disabledCount",
            search: false,
        },
        {
            title: "作废",
            dataIndex: "revokedCount",
            search: false,
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            render: (_, record) => [
                <Button type="primary"
                    key="update"
                    onClick={() => {
                        presenter.showSegmentEditModal(record);
                    }}
                >编辑</Button>,
                record.unUseCount > 0 ? (
                <Button>初始化</Button>):null,
                    <Popconfirm
                    title="确定要删除吗？"
                    onConfirm={() => {
                        presenter.deleteSegment(record.id);
                    }}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button type="link" danger key="delete">删除</Button>
                </Popconfirm>
            ],
        },
    ];
    // @ts-ignore
    return (
        <Page>
            <HRBody>
                <HRHeader
                    title={"执法证号段管理"}
                    icon={<HomeOutlined/>}
                    tools={
                    <Space>
                        <Button
                        disabled={!state.selectedRowKeys.length}
                        key="init"
                        onClick={() => {
                        // TODO: 实现初始化逻辑
                        console.log("初始化按钮被点击");
                    }}
                        >
                        初始化
                    </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                presenter.showSegmentAddModal()
                            }}
                        >
                            <PlusOutlined/> 新增
                        </Button>
                    </Space>
                    }
                />
                <HRCard>
                    <IProTable
                        columns={columns}
                        actionRef={actionRef}
                        rowKey={"id"}
                        rowSelection={{
                            type: 'checkbox',
                            selectedRowKeys: state.selectedRowKeys,
                            onChange: (keys, rows) => {
                                presenter.changeSelectedRowAndKeys(keys, rows);
                            },
                        }}
                        request={async (params, sort, filter) => {
                            return  presenter.searchSegmentTable(params, sort, filter)
                        }}
                    />
                </HRCard>
                <Modal
                    title={state.segmentAddVisible ? "新增号段" : "编辑号段"}
                    open={state.segmentAddVisible || state.segmentEditVisible}
                    onCancel={() => {
                        if (state.segmentAddVisible) {
                            presenter.hideSegmentAddModal();
                        }else {
                            presenter.hideSegmentEditModal();
                        }
                    }}
                    onOk={() => {
                        presenter.submitSegment(form.getFieldsValue());
                        if (state.segmentAddVisible) {
                            presenter.hideSegmentAddModal();
                        }else {
                            presenter.hideSegmentEditModal();
                        }
                    }}
                >
                    <Form
                        form={form}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 14 }}
                        layout="horizontal"
                        style={{ maxWidth: 600 }}
                    >
                        <Form.Item
                            label="编号"
                            name="id"
                            rules={[{ required: true, message: "请输入编号" }]}
                            hidden={state.segmentAddVisible}
                        >
                            <InputNumber
                                disabled={state.segmentEditVisible}
                                style={{ width: "100%" }}
                            />
                        </Form.Item>
                        <Form.Item
                            label="单位名称"
                            name="unitName"
                            rules={[{ required: true, message: "请选择单位名称" }]}
                        >
                            <Select
                                placeholder="请选择单位名称"
                                options={state.unitOptions}
                            />
                        </Form.Item>
                        <Form.Item
                            label="起始编号"
                            name="startNumber"
                            rules={[{ required: true, message: "请输入起始编号" }]}
                        >
                            <InputNumber
                                placeholder="请输入起始编号"
                                style={{ width: "100%" }}
                                min={0}
                                max={state.currentEditSegment?.startNumber}
                            />
                        </Form.Item>
                        <Form.Item
                            label="结束编号"
                            name="endNumber"
                            rules={[{ required: true, message: "请输入结束编号" }]}
                        >
                            <InputNumber
                                placeholder="请输入结束编号"
                                style={{ width: "100%" }}
                                min={state.currentEditSegment?.endNumber}
                            />
                        </Form.Item>
                        <Form.Item
                            label="备注"
                            name="remark"
                        >
                            <Input.TextArea
                                placeholder="请输入备注信息"
                                rows={3}
                            />
                        </Form.Item>
                    </Form>
                </Modal>
            </HRBody>

        </Page>
    );
}
export default SegmentManagePage;