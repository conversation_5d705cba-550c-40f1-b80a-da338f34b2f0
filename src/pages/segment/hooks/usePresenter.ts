import {SegmentState} from "@/pages/segment/types";
import React from "react";
import {SegmentPresenter} from "@/pages/segment/presenter";
import {SegmentApiImpl} from "@/pages/segment/model";

const initialState: SegmentState = {
    segmentAddVisible: false,
    segmentEditVisible: false,
    currentEditSegment: null,
    unitOptions: [{label: '贵州省烟草局', value: '10001'}, {label: '安顺市烟草局', value: '10002'}],
    selectedRowKeys: [],
    selectedRows: [],
    tableRefreshVersion: 0
}

export  function useSegmentPresenter(){
    const [state, dispatch] = React.useState<SegmentState>(initialState);

    const presenterRef = React.useRef<SegmentPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new SegmentPresenter(dispatch, new SegmentApiImpl(), state);
    }
    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}