export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface EnforcementNumberState {
    enforcementNumberEditVisible:boolean,
    currentEditEnforcementNumber:any,
    checkedEnforcementNumberIds:number[],
    tableRefreshVersion:number,
    //选中的行数据ID
    selectedRowKeys:number[],
    //选中的行数据
    selectedRows:any[]
}
export interface EnforcementNumberApi {

    loadEnforcementNumberTable: (params: any, sort: any, filter: any) => Promise<any>;

    saveEnforcementNumber: (params: any) => Promise<{ success: boolean; data?: any }>;
}