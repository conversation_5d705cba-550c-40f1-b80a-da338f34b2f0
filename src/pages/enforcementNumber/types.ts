export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

// 执法编号数据接口
export interface EnforcementNumberData {
    id: number;
    segmentId: number;
    unitId: number;
    unitName: string | null;
    number: number; // 执法编号
    status: string; // "DISABLED": 未启用, "ENABLED": 已启用, "ASSIGNED": 已分配
    enforcerId: number | null;
    enforcerName: string | null;
    assignTime: number | null;
    enableTime: number | null;
    revokeTime: number | null;
    revokeReason: string | null;
    remark: string | null;
    sysCreator: string;
    sysUpdater: string;
    sysCreateTime: number;
    sysUpdateTime: number;
}

// 单位选项接口
export interface UnitOption {
    label: string;
    value: string;
    code?: string;
}

// 状态选项接口
export interface StatusOption {
    label: string;
    value: string;
}

export interface EnforcementNumberState {
    enforcementNumberEditVisible: boolean;
    enforcementNumberAddVisible?: boolean;
    currentEditEnforcementNumber: EnforcementNumberData | null;
    checkedEnforcementNumberIds: number[];
    tableRefreshVersion: number;
    // 选中的行数据ID
    selectedRowKeys: number[];
    // 选中的行数据
    selectedRows: EnforcementNumberData[];
    statusOptions: StatusOption[];
}

export interface EnforcementNumberApi {
    loadEnforcementNumberTable: (params: any, sort: any, filter: any) => Promise<{
        data: EnforcementNumberData[];
        success: boolean;
        total: number;
    }>;
    getUnitOptions?: () => Promise<{
        success: boolean;
        data: UnitOption[];
    }>;

    getStatusOptions?: () => Promise<{
        success: boolean;
        data: StatusOption[];
    }>;
}