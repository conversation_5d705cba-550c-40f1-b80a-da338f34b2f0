export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

// 字典项接口，参考 dict/label 返回值结构
export interface DictItem {
    id: number;
    name: string;
    code: string;
    state?: number;
    commit?: string;
    value1?: string;
    value2?: string;
    value3?: string;
    children?: DictItem[];
}

// 执法编号数据接口
export interface EnforcementNumberData {
    id: number;
    unitName: string;
    enforcementNumber: string;
    usageStatus: string; // "0": 未使用, "1": 使用中, "2": 未启用
    userId?: string | null;
    userName?: string | null;
    remark?: string;
    createTime: string;
    updateTime?: string;
}

// 单位选项接口
export interface UnitOption {
    label: string;
    value: string;
    code?: string;
}

// 使用状态选项接口
export interface UsageStatusOption {
    label: string;
    value: string;
}

export interface EnforcementNumberState {
    enforcementNumberEditVisible: boolean;
    enforcementNumberAddVisible?: boolean;
    currentEditEnforcementNumber: EnforcementNumberData | null;
    checkedEnforcementNumberIds: number[];
    tableRefreshVersion: number;
    // 选中的行数据ID
    selectedRowKeys: number[];
    // 选中的行数据
    selectedRows: EnforcementNumberData[];
}

export interface EnforcementNumberApi {
    loadEnforcementNumberTable: (params: any, sort: any, filter: any) => Promise<{
        data: EnforcementNumberData[];
        success: boolean;
        total: number;
    }>;
    getUnitOptions?: () => Promise<{
        success: boolean;
        data: UnitOption[];
    }>;

    getUsageStatusOptions?: () => Promise<{
        success: boolean;
        data: UsageStatusOption[];
    }>;
}