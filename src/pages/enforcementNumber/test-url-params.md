# 执法编号管理页面 URL 参数测试

## 支持的 URL 参数

页面支持从 URL 中读取以下查询参数，并自动应用到表格搜索中：

### 1. 单位名称 (unitName)
- 参数名：`unitName`
- 类型：字符串
- 示例：`?unitName=市公安局`

### 2. 使用状态 (usageStatus)
- 参数名：`usageStatus`
- 类型：字符串
- 可选值：
  - `0`: 未使用
  - `1`: 使用中
  - `2`: 未启用
- 示例：`?usageStatus=1`

### 3. 使用人 (userName)
- 参数名：`userName`
- 类型：字符串
- 示例：`?userName=张三`

### 4. 执法编号 (enforcementNumber)
- 参数名：`enforcementNumber`
- 类型：字符串
- 示例：`?enforcementNumber=ZF001001`

## 测试 URL 示例

### 单个参数测试
```
/enforcementNumber?unitName=市公安局
/enforcementNumber?usageStatus=1
/enforcementNumber?userName=张三
/enforcementNumber?enforcementNumber=ZF001001
```

### 多个参数组合测试
```
/enforcementNumber?unitName=市公安局&usageStatus=1
/enforcementNumber?unitName=交警支队&userName=李四
/enforcementNumber?usageStatus=0&userName=王五
/enforcementNumber?unitName=消防支队&usageStatus=1&userName=赵六
```

### 完整参数测试
```
/enforcementNumber?unitName=市公安局&usageStatus=1&userName=张三&enforcementNumber=ZF001001
```

## 实现原理

1. 页面加载时，`presenter.searchEnforcementNumberTable` 方法会自动读取 URL 参数
2. 使用 `URLSearchParams` API 解析当前页面的查询字符串
3. 将 URL 参数与表格的搜索参数合并
4. 传递给后端 API 进行数据查询

## 代码位置

- 实现文件：`src/pages/enforcementNumber/presenter.ts`
- 方法：`searchEnforcementNumberTable`
- 行数：约 20-40 行

## 注意事项

1. URL 参数会覆盖表格中的同名搜索条件
2. 参数值需要进行 URL 编码（浏览器会自动处理）
3. 不支持的参数会被忽略
4. 空值参数会被忽略
