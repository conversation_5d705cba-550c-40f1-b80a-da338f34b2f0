/**
 * 执法编号页面工具函数
 */

// 状态映射
export const STATUS_MAP = {
    "DISABLED": { text: "未启用", status: "Default" },
    "UNUSED": { text: "未使用", status: "Processing" },
    "IN_USE": { text: "使用中", status: "Success" },
    "REVOKED": { text: "作废", status: "Error" },
} as const;

export type StatusType = keyof typeof STATUS_MAP;

/**
 * 生成跳转到执法编号页面的URL
 * @param segmentId 号段ID
 * @param status 状态
 * @param unitName 单位名称（可选）
 * @returns 完整的跳转URL
 */
export function generateEnforcementNumberUrl(
    segmentId: number,
    status?: StatusType,
    unitName?: string
): string {
    const baseUrl = '/enforcement/number';
    const params = new URLSearchParams();
    
    // 添加号段ID参数
    params.append('segmentId', segmentId.toString());
    
    // 添加状态参数
    if (status) {
        params.append('status', status);
    }
    
    // 添加单位名称参数
    if (unitName) {
        params.append('unitName', unitName);
    }
    
    return `${baseUrl}?${params.toString()}`;
}

/**
 * 生成带统计数量的链接组件属性
 * @param segmentId 号段ID
 * @param status 状态
 * @param count 数量
 * @param unitName 单位名称（可选）
 * @returns 链接属性对象
 */
export function generateCountLinkProps(
    segmentId: number,
    status: StatusType,
    count: number,
    unitName?: string
) {
    return {
        href: generateEnforcementNumberUrl(segmentId, status, unitName),
        target: '_blank', // 新窗口打开
        style: { 
            color: getStatusColor(status),
            textDecoration: 'none',
            fontWeight: 'bold'
        },
        title: `查看${STATUS_MAP[status].text}的执法编号 (${count}条)`
    };
}

/**
 * 根据状态获取对应的颜色
 * @param status 状态
 * @returns 颜色值
 */
function getStatusColor(status: StatusType): string {
    const colorMap = {
        "DISABLED": "#d9d9d9",    // 灰色 - 未启用
        "UNUSED": "#1890ff",     // 蓝色 - 未使用  
        "IN_USE": "#52c41a",     // 绿色 - 使用中
        "REVOKED": "#ff4d4f",    // 红色 - 作废
    };
    
    return colorMap[status] || "#000000";
}

/**
 * 解析URL参数获取搜索条件
 * @returns 搜索参数对象
 */
export function parseUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    
    return {
        segmentId: urlParams.get('segmentId'),
        status: urlParams.get('status') as StatusType | null,
        unitName: urlParams.get('unitName'),
        number: urlParams.get('number'),
        enforcerName: urlParams.get('enforcerName'),
    };
}
