import {ActionType, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {Form, Modal, Select, Input, Button, message, Popconfirm} from "antd";
import {useEnforcementNumberPresenter} from "@/pages/enforcementNumber/hooks/usePresenter";
import HRBody from "@/components/HRBody";
import HRHeader from "@/components/HRHeader";
import {HomeOutlined, PlusOutlined, EditOutlined, DeleteOutlined} from "@ant-design/icons";
import Page from "@/components/Page";
import HRCard from "@/components/HRCard";
import IProTable from "@/components/HRTable";
import {EnforcementNumberData, UnitOption, UsageStatusOption} from "@/pages/enforcementNumber/types";

const EnforcementNumberManagePage: React.FC = () => {

    const actionRef = React.useRef<ActionType>(null);
    const [form] = Form.useForm();
    const [unitOptions, setUnitOptions] = React.useState<UnitOption[]>([]);
    const [usageStatusOptions, setUsageStatusOptions] = React.useState<UsageStatusOption[]>([]);

    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useEnforcementNumberPresenter();

    // 初始化选项数据
    React.useEffect(() => {
        const loadOptions = async () => {
            const units = await presenter.getUnitOptions();
            const statuses = await presenter.getUsageStatusOptions();
            setUnitOptions(units);
            setUsageStatusOptions(statuses);
        };
        loadOptions();
    }, []);

    React.useEffect(() => {
        if (state.currentEditEnforcementNumber) {
            form.setFieldsValue(state.currentEditEnforcementNumber);
        } else {
            form.resetFields();
        }
    }, [state.currentEditEnforcementNumber, form]);
    // 处理保存操作
    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            const result = await presenter.saveEnforcementNumber(values);

            if (result.success) {
                message.success('保存成功');
                presenter.hideEnforcementNumberEditModal();
                presenter.hideEnforcementNumberAddModal();
                actionRef.current?.reload();
            } else {
                message.error('保存失败');
            }
        } catch (error) {
            console.error('保存失败:', error);
            message.error('保存失败');
        }
    };

    // 处理删除操作
    const handleDelete = async (record: EnforcementNumberData) => {
        try {
            const result = await presenter.deleteEnforcementNumber(record.id);
            if (result.success) {
                message.success('删除成功');
                actionRef.current?.reload();
            } else {
                message.error('删除失败');
            }
        } catch (error) {
            console.error('删除失败:', error);
            message.error('删除失败');
        }
    };

    // 处理批量删除
    const handleBatchDelete = async () => {
        if (state.selectedRowKeys.length === 0) {
            message.warning('请选择要删除的数据');
            return;
        }

        try {
            const result = await presenter.batchDeleteEnforcementNumbers(state.selectedRowKeys);
            if (result.success) {
                message.success('批量删除成功');
                actionRef.current?.reload();
            } else {
                message.error('批量删除失败');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            message.error('批量删除失败');
        }
    };

    const columns: ProColumns<EnforcementNumberData>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
            width: 80,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
            valueType: "select",
            valueEnum: unitOptions.reduce((acc, item) => {
                acc[item.value] = { text: item.label };
                return acc;
            }, {} as Record<string, { text: string }>),
            fieldProps: {
                placeholder: "请选择单位名称",
            },
        },
        {
            title: "执法编号",
            dataIndex: "enforcementNumber",
            fieldProps: {
                placeholder: "请输入执法编号",
            },
        },
        {
            title: "使用情况",
            dataIndex: "usageStatus",
            valueType: "select",
            valueEnum: {
                "0": {text: "未使用", status: "Default"},
                "1": {text: "使用中", status: "Processing"},
                "2": {text: "未启用", status: "Success"},
            },
            fieldProps: {
                placeholder: "请选择使用情况",
            },
        },
        {
            title: "使用人ID",
            dataIndex: "userId",
            search: false,
            hideInTable: true,
        },
        {
            title: "使用人",
            dataIndex: "userName",
            fieldProps: {
                placeholder: "请输入使用人姓名",
            },
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            ellipsis: true,
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            valueType: "dateTime",
            search: false,
            width: 180,
        },
        {
            title: "操作",
            valueType: "option",
            width: 150,
            render: (_, record) => [
                <Button
                    key="edit"
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => {
                        presenter.changeCurrentEditEnforcementNumber(record);
                        presenter.showEnforcementNumberEditModal(record);
                    }}
                >
                    编辑
                </Button>,
                <Popconfirm
                    key="delete"
                    title="确定要删除这条记录吗？"
                    onConfirm={() => handleDelete(record)}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                    >
                        删除
                    </Button>
                </Popconfirm>,
            ],
        },
    ];
    return (
        <Page>
            <HRBody>
                <HRHeader
                    title={"执法编号管理"}
                    icon={<HomeOutlined/>}
                />
            </HRBody>
            <HRCard>
                <IProTable
                    columns={columns}
                    actionRef={actionRef}
                    rowKey={"id"}
                    rowSelection={{
                        selectedRowKeys: state.selectedRowKeys,
                        onChange: (selectedRowKeys, selectedRows) => {
                            presenter.changeSelectedRowKeys(
                                selectedRowKeys as number[],
                                selectedRows as EnforcementNumberData[]
                            );
                        },
                    }}
                    toolBarRender={() => [
                        <Button
                            key="add"
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => {
                                presenter.showEnforcementNumberAddModal();
                            }}
                        >
                            新增执法编号
                        </Button>,
                        <Popconfirm
                            key="batchDelete"
                            title={`确定要删除选中的 ${state.selectedRowKeys.length} 条记录吗？`}
                            onConfirm={handleBatchDelete}
                            disabled={state.selectedRowKeys.length === 0}
                            okText="确定"
                            cancelText="取消"
                        >
                            <Button
                                danger
                                disabled={state.selectedRowKeys.length === 0}
                                icon={<DeleteOutlined />}
                            >
                                批量删除
                            </Button>
                        </Popconfirm>,
                    ]}
                    request={async (params, sort, filter) => {
                        const result = await presenter.searchEnforcementNumberTable(params, sort, filter);
                        return {
                            data: result.data || [],
                            success: result.success,
                            total: result.total || 0,
                        };
                    }}
                />
            </HRCard>

            {/* 编辑模态框 */}
            <Modal
                title="编辑执法编号"
                open={state.enforcementNumberEditVisible}
                onCancel={() => {
                    presenter.hideEnforcementNumberEditModal();
                    form.resetFields();
                }}
                onOk={handleSave}
                destroyOnClose
            >
                <Form
                    form={form}
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                    layout="horizontal"
                >
                    <Form.Item
                        label="编号"
                        name="id"
                        hidden
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label="单位名称"
                        name="unitName"
                        rules={[{required: true, message: "请选择单位名称"}]}
                    >
                        <Select
                            placeholder="请选择单位名称"
                            options={unitOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        label="执法编号"
                        name="enforcementNumber"
                        rules={[{required: true, message: "请输入执法编号"}]}
                    >
                        <Input
                            placeholder="请输入执法编号，如：ZF001001"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用情况"
                        name="usageStatus"
                        rules={[{required: true, message: "请选择使用情况"}]}
                    >
                        <Select
                            placeholder="请选择使用情况"
                            options={usageStatusOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用人ID"
                        name="userId"
                    >
                        <Input
                            placeholder="请输入使用人ID"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用人"
                        name="userName"
                    >
                        <Input
                            placeholder="请输入使用人姓名"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remark"
                    >
                        <Input.TextArea
                            placeholder="请输入备注信息"
                            rows={3}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 新增模态框 */}
            <Modal
                title="新增执法编号"
                open={state.enforcementNumberAddVisible}
                onCancel={() => {
                    presenter.hideEnforcementNumberAddModal();
                    form.resetFields();
                }}
                onOk={handleSave}
                destroyOnClose
            >
                <Form
                    form={form}
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                    layout="horizontal"
                >
                    <Form.Item
                        label="单位名称"
                        name="unitName"
                        rules={[{required: true, message: "请选择单位名称"}]}
                    >
                        <Select
                            placeholder="请选择单位名称"
                            options={unitOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        label="执法编号"
                        name="enforcementNumber"
                        rules={[{required: true, message: "请输入执法编号"}]}
                    >
                        <Input
                            placeholder="请输入执法编号，如：ZF001001"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用情况"
                        name="usageStatus"
                        rules={[{required: true, message: "请选择使用情况"}]}
                        initialValue="0"
                    >
                        <Select
                            placeholder="请选择使用情况"
                            options={usageStatusOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用人ID"
                        name="userId"
                    >
                        <Input
                            placeholder="请输入使用人ID"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用人"
                        name="userName"
                    >
                        <Input
                            placeholder="请输入使用人姓名"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remark"
                    >
                        <Input.TextArea
                            placeholder="请输入备注信息"
                            rows={3}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </Page>
    );
}
export default EnforcementNumberManagePage;