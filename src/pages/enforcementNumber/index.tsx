import {ActionType, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {Form, Modal, Select, Input,} from "antd";
import {useEnforcementNumberPresenter} from "@/pages/enforcementNumber/hooks/usePresenter";
import HRBody from "@/components/HRBody";
import HRHeader from "@/components/HRHeader";
import {HomeOutlined} from "@ant-design/icons";
import Page from "@/components/Page";
import HRCard from "@/components/HRCard";
import IProTable from "@/components/HRTable";

const EnforcementNumberManagePage: React.FC = () => {

    const actionRef = React.useRef<ActionType>(null);
    const [form] = Form.useForm();
    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useEnforcementNumberPresenter();

    React.useEffect(() => {
        if (state.currentEditEnforcementNumber) {
            form.setFieldsValue(state.currentEditEnforcementNumber)
        }
    }, [state.currentEditEnforcementNumber])
    const columns: ProColumns<any>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
        },
        {
            title: "执法编号",
            dataIndex: "enforcementNumber",
            search: false,
        },
        {
            title: "使用情况",
            dataIndex: "usageStatus",
            valueType: "select",
            valueEnum: {
                "0": {text: "未使用", status: "Default"},
                "1": {text: "使用中", status: "Processing"},
                "2": {text: "未启用", status: "Success"},
            },
            fieldProps: {
                placeholder: "请选择使用情况",
            },
        },
        {
            title: "使用人ID",
            dataIndex: "userId",
            search: false,
            hidden: true,
        },
        {
            title: "使用人",
            dataIndex: "userName",
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            valueType: "dateTime",
        },
    ];
    return (
        <Page>
            <HRBody>
                <HRHeader
                    title={"执法编号管理"}
                    icon={<HomeOutlined/>}
                />
            </HRBody>
            <HRCard>
                <IProTable
                    columns={columns}
                    actionRef={actionRef}
                    rowKey={"id"}
                    request={async (params, sort, filter) => {
                        return presenter.searchEnforcementNumberTable(params, sort, filter)
                    }}
                />
            </HRCard>

            <Modal
                title="编辑执法编号"
                open={state.enforcementNumberEditVisible}
                onCancel={() => {
                    presenter.hideEnforcementNumberEditModal()
                    form.resetFields();
                }}
            >
                <Form
                    form={form}
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                    layout="horizontal"
                >
                    <Form.Item
                        label="单位名称"
                        name="unitName"
                        rules={[{required: true, message: "请选择单位名称"}]}
                    >
                        <Select
                            placeholder="请选择单位名称"
                            options={[
                                {value: "市公安局", label: "市公安局"},
                                {value: "交警支队", label: "交警支队"},
                                {value: "消防支队", label: "消防支队"},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item
                        label="执法编号"
                        name="enforcementNumber"
                        rules={[{required: true, message: "请输入执法编号"}]}
                    >
                        <Input
                            placeholder="请输入执法编号，如：ZF001001"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用情况"
                        name="usageStatus"
                        rules={[{required: true, message: "请选择使用情况"}]}
                    >
                        <Select
                            placeholder="请选择使用情况"
                            options={[
                                {value: "0", label: "未使用"},
                                {value: "1", label: "使用中"},
                                {value: "2", label: "未启用"},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item
                        label="使用人"
                        name="userName"
                    >
                        <Input
                            placeholder="请输入使用人姓名"
                            style={{width: "100%"}}
                        />
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remark"
                    >
                        <Input.TextArea
                            placeholder="请输入备注信息"
                            rows={3}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </Page>
    );
}
export default EnforcementNumberManagePage;