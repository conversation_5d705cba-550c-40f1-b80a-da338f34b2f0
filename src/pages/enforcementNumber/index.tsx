import {ActionType, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {Button} from "antd";
import {useEnforcementNumberPresenter} from "@/pages/enforcementNumber/hooks/usePresenter";
import HRBody from "@/components/HRBody";
import HRHeader from "@/components/HRHeader";
import {HomeOutlined} from "@ant-design/icons";
import Page from "@/components/Page";
import HRCard from "@/components/HRCard";
import IProTable from "@/components/HRTable";
import {EnforcementNumberData, UnitOption, StatusOption} from "@/pages/enforcementNumber/types";

const EnforcementNumberManagePage: React.FC = () => {

    const actionRef = React.useRef<ActionType>(null);
    const [unitOptions, setUnitOptions] = React.useState<UnitOption[]>([]);
    const [statusOptions, setStatusOptions] = React.useState<StatusOption[]>([]);

    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useEnforcementNumberPresenter();

    // 初始化选项数据
    React.useEffect(() => {
        const loadOptions = async () => {
            const units = await presenter.getUnitOptions();
            const statuses = await presenter.getStatusOptions();
            setUnitOptions(units);
            setStatusOptions(statuses);
        };
        loadOptions();
    }, []);

    const columns: ProColumns<EnforcementNumberData>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
            width: 80,
        },
        {
            title: "号段ID",
            dataIndex: "segmentId",
            search: false,
            width: 100,
        },
        {
            title: "单位ID",
            dataIndex: "unitId",
            search: false,
            width: 100,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
            valueType: "select",
            valueEnum: unitOptions.reduce((acc, item) => {
                acc[item.value] = { text: item.label };
                return acc;
            }, {} as Record<string, { text: string }>),
            fieldProps: {
                placeholder: "请选择单位名称",
            },
        },
        {
            title: "执法编号",
            dataIndex: "number",
            fieldProps: {
                placeholder: "请输入执法编号",
            },
        },
        {
            title: "状态",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                "DISABLED": {text: "未启用", status: "Default"},
                "ENABLED": {text: "已启用", status: "Processing"},
                "ASSIGNED": {text: "已分配", status: "Success"},
            },
            fieldProps: {
                placeholder: "请选择状态",
            },
        },
        {
            title: "执法人员ID",
            dataIndex: "enforcerId",
            search: false,
            width: 120,
        },
        {
            title: "执法人员",
            dataIndex: "enforcerName",
            fieldProps: {
                placeholder: "请输入执法人员姓名",
            },
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            ellipsis: true,
            width: 150,
        },
        {
            title: "创建人",
            dataIndex: "sysCreator",
            search: false,
            width: 100,
        },
        {
            title: "创建时间",
            dataIndex: "sysCreateTime",
            valueType: "dateTime",
            search: false,
            render: (_, record) => {
                return new Date(record.sysCreateTime).toLocaleString();
            },
        },
    ];
    return (
        <Page>
            <HRBody>
                <HRHeader
                    title={"执法编号管理"}
                    icon={<HomeOutlined/>}
                />
            </HRBody>
            <HRCard>
                <IProTable
                    columns={columns}
                    actionRef={actionRef}
                    rowKey={"id"}
                    rowSelection={{
                        selectedRowKeys: state.selectedRowKeys,
                        onChange: (selectedRowKeys, selectedRows) => {
                            presenter.changeSelectedRowKeys(
                                selectedRowKeys as number[],
                                selectedRows as EnforcementNumberData[]
                            );
                        },
                    }}
                    request={async (params, sort, filter) => {
                        const result = await presenter.searchEnforcementNumberTable(params, sort, filter);
                        return {
                            data: result.data || [],
                            success: result.success,
                            total: result.total || 0,
                        };
                    }}
                />
            </HRCard>
        </Page>
    );
}
export default EnforcementNumberManagePage;