
import {EnforcementNumberApi} from "@/pages/enforcementNumber/types";


/**
 * Model层接口实现
 * EnforcementNumberApiImpl 实现了EnforcementNumberApi接口，提供了数据的增删改查操作
 */
export class EnforcementNumberApiImpl implements EnforcementNumberApi {

    async loadEnforcementNumberTable(params: any, sort: any, filter: any): Promise<any> {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 假数据
        const mockData = [
            {
                id: 1,
                unitName: "贵阳市烟草局",
                enforcementNumber: "ZF001001",
                usageStatus: "1",
                userId: "user001",
                userName: "张三",
                remark: "正常使用中",
                createTime: "2024-01-15 10:30:00"
            },
            {
                id: 2,
                unitName: "安顺市烟草局",
                enforcementNumber: "ZF001002",
                usageStatus: "0",
                userId: null,
                userName: null,
                remark: "待分配",
                createTime: "2024-01-15 10:31:00"
            },
            {
                id: 3,
                unitName: "长顺县烟草局",
                enforcementNumber: "ZF002001",
                usageStatus: "2",
                userId: "user002",
                userName: "李四",
                remark: "已释放，可重新分配",
                createTime: "2024-01-15 10:32:00"
            },
            {
                id: 4,
                unitName: "铜仁市烟草局",
                enforcementNumber: "ZF002002",
                usageStatus: "1",
                userId: "user003",
                userName: "王五",
                remark: "正常使用中",
                createTime: "2024-01-15 10:33:00"
            },
            {
                id: 5,
                unitName: "毕节市烟草局",
                enforcementNumber: "ZF001003",
                usageStatus: "0",
                userId: null,
                userName: null,
                remark: "新分配编号",
                createTime: "2024-01-15 10:34:00"
            },
            {
                id: 6,
                unitName: "六盘水市烟草局",
                enforcementNumber: "ZF003001",
                usageStatus: "1",
                userId: "user004",
                userName: "赵六",
                remark: "正常使用中",
                createTime: "2024-01-15 10:35:00"
            }
        ];

        // 模拟搜索过滤
        let filteredData = mockData;

        if (params.unitName) {
            filteredData = filteredData.filter(item =>
                item.unitName.includes(params.unitName)
            );
        }

        if (params.usageStatus !== undefined && params.usageStatus !== '') {
            filteredData = filteredData.filter(item =>
                item.usageStatus === params.usageStatus
            );
        }

        if (params.userName) {
            filteredData = filteredData.filter(item =>
                item.userName && item.userName.includes(params.userName)
            );
        }

        // 模拟分页
        const pageSize = params.pageSize || 10;
        const current = params.current || 1;
        const total = filteredData.length;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const data = filteredData.slice(startIndex, endIndex);

        return {
            data,
            total,
            success: true,
            pageSize,
            current
        };
    }

    async saveEnforcementNumber(params: any): Promise<{ success: boolean; data?: any }> {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 300));

        // 模拟保存成功
        console.log("保存执法编号数据:", params);

        return {
            success: true,
            data: {
                id: Date.now(), // 模拟生成的ID
                ...params,
                createTime: new Date().toLocaleString()
            }
        };
    }
}