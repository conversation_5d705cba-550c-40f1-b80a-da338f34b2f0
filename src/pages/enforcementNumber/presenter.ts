import {EnforcementNumberApi, EnforcementNumberState,Dispatch} from "@/pages/enforcementNumber/types";
import {EnforcementNumberApiImpl} from "@/pages/enforcementNumber/model";

export class EnforcementNumberPresenter {

    private readonly dispatch: Dispatch<EnforcementNumberState>;

    private readonly api: EnforcementNumberApi;

    private state: EnforcementNumberState;
    public constructor(dispatch: Dispatch<EnforcementNumberState>, api: EnforcementNumberApiImpl, state: EnforcementNumberState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }
    public syncState = (newStatus: EnforcementNumberState) => {
        this.state = newStatus;
    }
    // 号段表格数据加载函数
    public searchEnforcementNumberTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params
        }
        return this.api.loadEnforcementNumberTable(searchParams, sort, filter);
    }

    //修改编辑模态框的显示状态
    public showEnforcementNumberEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: true
            }
        })
    }
    //修改编辑模态框的隐藏状态
    public hideEnforcementNumberEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: false
            }
        })
    }

    // 保存执法编号数据
    public saveEnforcementNumber = async (params: any) => {
            const res = await this.api.saveEnforcementNumber(params);
    }

    //显示编辑模态框
    public showEnforcementNumberEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: true
            }
        })
    }

    //隐藏编辑模态框
    public hideEnforcementNumberEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: false,
                currentEditEnforcementNumber: null
            }
        })
    }

    //修改选中行数据和行数据ID列表
    public changeSelectedRowKeys = (selectedRowKeys: number[], selectedRows: any[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedRowKeys,
                selectedRows
            }
        })
    }

    //暂存编辑的数据
    public changeCurrentEditEnforcementNumber = (record: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditEnforcementNumber: record
            }
        })
    }
}