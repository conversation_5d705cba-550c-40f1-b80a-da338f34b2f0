import {EnforcementNumberApi, EnforcementNumberState, Dispatch, EnforcementNumberData, UnitOption, StatusOption} from "@/pages/enforcementNumber/types";
import {EnforcementNumberApiImpl} from "@/pages/enforcementNumber/model";

export class EnforcementNumberPresenter {

    private readonly dispatch: Dispatch<EnforcementNumberState>;
    private readonly api: EnforcementNumberApi;
    private state: EnforcementNumberState;

    public constructor(dispatch: Dispatch<EnforcementNumberState>, api: EnforcementNumberApiImpl, state: EnforcementNumberState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: EnforcementNumberState) => {
        this.state = newStatus;
    }

    // 执法编号表格数据加载函数，支持URL参数
    public searchEnforcementNumberTable = (params: any, sort: any, filter: any) => {
        // 从URL获取查询参数
        const urlParams = new URLSearchParams(window.location.search);
        const urlQueryParams: any = {};

        // 提取URL中的查询参数
        if (urlParams.get('unitName')) {
            urlQueryParams.unitName = urlParams.get('unitName');
        }
        if (urlParams.get('status')) {
            urlQueryParams.status = urlParams.get('status');
        }
        if (urlParams.get('segmentId')) {
            urlQueryParams.segmentId = urlParams.get('segmentId');
        }
        // 合并URL参数和表格参数
        const searchParams = {
            ...params,
            ...urlQueryParams
        };

        return this.api.loadEnforcementNumberTable(searchParams, sort, filter);
    }



    // 获取单位选项
    public getUnitOptions = async () => {
        try {
            if (this.api.getUnitOptions) {
                const res = await this.api.getUnitOptions();
                return res.data || [];
            }
            return [];
        } catch (error) {
            console.error('获取单位选项失败:', error);
            return [];
        }
    }

    // 获取状态选项
    public getStatusOptions = async () => {
        const res = await this.api.getStatusOptions();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                statusOptions: res.data || []
            }
        })
    }

    // 修改选中行数据和行数据ID列表
    public changeSelectedRowKeys = (selectedRowKeys: number[], selectedRows: EnforcementNumberData[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedRowKeys,
                selectedRows
            }
        })
    }

    // 暂存编辑的数据
    public changeCurrentEditEnforcementNumber = (record: EnforcementNumberData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditEnforcementNumber: record
            }
        })
    }

    // 刷新表格
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }
}