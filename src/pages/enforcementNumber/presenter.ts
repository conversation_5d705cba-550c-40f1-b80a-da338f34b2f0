import {EnforcementNumberApi, EnforcementNumberState, Dispatch, EnforcementNumberData, UnitOption, UsageStatusOption} from "@/pages/enforcementNumber/types";
import {EnforcementNumberApiImpl} from "@/pages/enforcementNumber/model";

export class EnforcementNumberPresenter {

    private readonly dispatch: Dispatch<EnforcementNumberState>;
    private readonly api: EnforcementNumberApi;
    private state: EnforcementNumberState;

    public constructor(dispatch: Dispatch<EnforcementNumberState>, api: EnforcementNumberApiImpl, state: EnforcementNumberState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: EnforcementNumberState) => {
        this.state = newStatus;
    }

    // 执法编号表格数据加载函数，支持URL参数
    public searchEnforcementNumberTable = (params: any, sort: any, filter: any) => {
        // 从URL获取查询参数
        const urlParams = new URLSearchParams(window.location.search);
        const urlQueryParams: any = {};

        // 提取URL中的查询参数
        if (urlParams.get('unitName')) {
            urlQueryParams.unitName = urlParams.get('unitName');
        }
        if (urlParams.get('usageStatus')) {
            urlQueryParams.usageStatus = urlParams.get('usageStatus');
        }
        if (urlParams.get('userName')) {
            urlQueryParams.userName = urlParams.get('userName');
        }
        if (urlParams.get('enforcementNumber')) {
            urlQueryParams.enforcementNumber = urlParams.get('enforcementNumber');
        }

        // 合并URL参数和表格参数
        const searchParams = {
            ...params,
            ...urlQueryParams
        };

        return this.api.loadEnforcementNumberTable(searchParams, sort, filter);
    }

    // 显示编辑模态框
    public showEnforcementNumberEditModal = (record?: EnforcementNumberData) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: true,
                currentEditEnforcementNumber: record || null
            }
        })
    }

    // 隐藏编辑模态框
    public hideEnforcementNumberEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberEditVisible: false,
                currentEditEnforcementNumber: null
            }
        })
    }

    // 显示新增模态框
    public showEnforcementNumberAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberAddVisible: true,
                currentEditEnforcementNumber: null
            }
        })
    }

    // 隐藏新增模态框
    public hideEnforcementNumberAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                enforcementNumberAddVisible: false,
                currentEditEnforcementNumber: null
            }
        })
    }

    // 保存执法编号数据
    public saveEnforcementNumber = async (params: Partial<EnforcementNumberData>) => {
        try {
            const res = await this.api.saveEnforcementNumber(params);
            if (res.success) {
                // 刷新表格
                this.refreshTable();
                return res;
            }
            return res;
        } catch (error) {
            console.error('保存执法编号失败:', error);
            return { success: false };
        }
    }

    // 删除执法编号
    public deleteEnforcementNumber = async (id: number) => {
        try {
            if (this.api.deleteEnforcementNumber) {
                const res = await this.api.deleteEnforcementNumber({ id });
                if (res.success) {
                    this.refreshTable();
                }
                return res;
            }
            return { success: false };
        } catch (error) {
            console.error('删除执法编号失败:', error);
            return { success: false };
        }
    }

    // 获取单位选项
    public getUnitOptions = async () => {
        try {
            if (this.api.getUnitOptions) {
                const res = await this.api.getUnitOptions();
                return res.data || [];
            }
            return [];
        } catch (error) {
            console.error('获取单位选项失败:', error);
            return [];
        }
    }

    // 获取使用状态选项
    public getUsageStatusOptions = async () => {
        try {
            if (this.api.getUsageStatusOptions) {
                const res = await this.api.getUsageStatusOptions();
                return res.data || [];
            }
            return [];
        } catch (error) {
            console.error('获取使用状态选项失败:', error);
            return [];
        }
    }

    // 修改选中行数据和行数据ID列表
    public changeSelectedRowKeys = (selectedRowKeys: number[], selectedRows: EnforcementNumberData[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedRowKeys,
                selectedRows
            }
        })
    }

    // 暂存编辑的数据
    public changeCurrentEditEnforcementNumber = (record: EnforcementNumberData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditEnforcementNumber: record
            }
        })
    }

    // 刷新表格
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    // 批量删除执法编号
    public batchDeleteEnforcementNumbers = async (ids: number[]) => {
        try {
            const promises = ids.map(id => this.deleteEnforcementNumber(id));
            const results = await Promise.all(promises);
            const allSuccess = results.every(result => result.success);

            if (allSuccess) {
                this.refreshTable();
                // 清空选中状态
                this.changeSelectedRowKeys([], []);
            }

            return { success: allSuccess };
        } catch (error) {
            console.error('批量删除执法编号失败:', error);
            return { success: false };
        }
    }
}