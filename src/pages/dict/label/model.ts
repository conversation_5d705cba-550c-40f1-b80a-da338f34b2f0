import {LabelApi} from "./types";
import {create, del, getDictLabel, list, update} from "@/api/dict/label";


/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class LabelApiImpl implements LabelApi {
    loadLabelTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, [
            {
                key: "name",
                type: "LIKE",
            },
            {
                key: "comments",
                type: "LIKE",
            },
        ]);
    }

    async saveLabel(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await create(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async updateLabel(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await update(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async deleteLabel(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

    getLabelByCode(params: any): Promise<any> {
        return getDictLabel(params);
    }

}