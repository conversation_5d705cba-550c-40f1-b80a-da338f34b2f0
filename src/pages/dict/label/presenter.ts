import {Label<PERSON>pi, LabelState, Dispatch} from "@/pages/dict/label/types";
import {ValueState} from "@/pages/dict/value/types";


export class LabelPresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<LabelState>;

    private readonly api: LabelApi;

    private state: LabelState;

    public constructor(dispatch: Dispatch<LabelState>, api: LabelApi, state: LabelState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: LabelState) => {
        this.state = newStatus;
    }

    // 显示label添加对话框
    public showLabelAddModal = (id: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                labelAddVisible: true,
                currentLabelId: id
            }
        });
    }

    // 隐藏label添加对话框
    public hideLabelAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                labelAddVisible: false,
                currentEditLabel: null
            }
        })
    }

    // 显示label编辑对话框
    public showLabelEditModal = (code: string) => {
        this.getLabelByCode(code);
    }

    // 隐藏label编辑对话框
    public hideLabelEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                labelEditVisible: false,
                currentEditLabel: null
            }
        })
    }
    // 刷新表格数据
    public refreshLabelTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }
    // 删除数据
    public deleteLabel = async (record: LabelState) => {
        try {
            const res = await this.api.deleteLabel(record);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("删除", e);
        }
    }

    public createLabel = async (record: ValueState) => {
        const params = {
            ...record,
            parentId: this.state.currentLabelId,
        }
        try {
            const res = await this.api.saveLabel(params);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("更新失败", e);
        }

    }

    public updateLabel = async (record: ValueState) => {
        try {
            const res = await this.api.updateLabel(record);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("更新失败", e);
        }

    }
    // 标签表格数据加载函数
    public searchLabelTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params
        }
        return this.api.loadLabelTable(searchParams, sort, filter);
    }

    public getLabelByCode = (code: string) => {
        this.api.getLabelByCode(code).then((data) => {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    labelEditVisible: true,
                    currentEditLabel: data.data
                }
            })
        })
    }
    private refresh() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditLabel: null,
                currentLabelId: 0
            }
        })
        this.refreshLabelTable();
    }
}