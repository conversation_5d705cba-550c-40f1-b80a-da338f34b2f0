import {PlusOutlined} from "@ant-design/icons";
import {ActionType, ProColumns} from "@ant-design/pro-components";
import {
    PageContainer,
} from "@ant-design/pro-components";
import {Popconfirm} from "antd";
import React from "react";
import {Form, FormInput, FormStepper} from "@/components/form-pc";
import {useLabelPresenter} from "@/pages/dict/label/hooks/usePresenter";
import {Button, Modal, ProTable} from "@itm/components-pc";
import "./index.scss";

const DictLabel: React.FC = () => {
    const actionRef = React.useRef<ActionType>(null);

    const form = Form.useForm();

    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useLabelPresenter();

    const [reset, setReset] = React.useState(0);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    React.useEffect(() => {
        if (state.currentEditLabel) {
            form.setFieldsValue(state.currentEditLabel);
        } else {
            form.reset();
            form.setFieldsValue({
                parentId: state.currentLabelId, // ← 设置 parentId 到表单
            });
        }
    }, [state.currentEditLabel,reset]);

    const columns: ProColumns<any>[] = [
        {
            title: '编号',
            dataIndex: 'id',
            search: false
        },
        {
            title: "类型名称",
            dataIndex: "name",
        },
        {
            title: "类型编码",
            dataIndex: "code",
            search: false,
        },
        {
            title: "类型描述",
            dataIndex: "comments",
        },
        {
            title: "排序",
            dataIndex: "sysIndex",
            search: false,
        },
        {
            title: "操作",
            valueType: "option",
            width: 200,
            render: (_, record) => [
                <a
                    key="update"
                    onClick={() => {
                        presenter.showLabelEditModal(record.code);
                    }}
                >
                    编辑
                </a>,
                <a
                    key="add"
                    onClick={() => {
                        presenter.showLabelAddModal(record.id);
                    }}
                >
                    添加子项
                </a>,
                <Popconfirm
                    key="delete"
                    title="删除提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={async () => {
                        await presenter.deleteLabel(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    return (
        <PageContainer className={"proTable_box"}>
            <ProTable
                actionRef={actionRef}
                rowKey="id"
                scroll={{ y: 600 }}
                headerTitle="字典类型"
                columns={columns}
                toolBarRender={() => [
                    <Button
                        type="primary"
                        onClick={() => {
                            presenter.showLabelAddModal(0);
                            setReset(prev => prev + 1);
                        }}
                    >
                        <PlusOutlined/> 新增
                    </Button>,
                ]}
                request={async (params, sort, filter) => {
                    return presenter.searchLabelTable(params, sort, filter);
                }}
            />
            <Modal
                open={state.labelAddVisible}
                title={"新增字典类型"}
                destroyOnHidden={true}
                //新版本antd的modal中去除了onClose方法
                onCancel={() => {
                    presenter.hideLabelAddModal();
                    setReset(prev => prev + 1);
                }}
                onOk={async () => {
                    await form.submit();
                    presenter.hideLabelAddModal();
                    setReset(prev => prev + 1);
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        return presenter.createLabel(values);
                    }}
                >
                    <FormInput name={"parentId"} hidden={true}/>
                    <FormInput
                        name={"name"}
                        label="字典类型名称"
                        placeholder={"请输入字典类型名称"}
                        required={true}
                    />
                    <FormInput
                        required={false}
                        name={"comments"}
                        label="字典类型描述"
                        placeholder={"请输入字典类型描述"}
                    />
                    <FormStepper
                        required={false}
                        name="sysIndex"
                        label={"排序"}
                        stepperMinNumber={0}
                    />
                </Form>

            </Modal>

            <Modal
                title="编辑字典类型"
                open={state.labelEditVisible}
                destroyOnHidden={true}
                onCancel={() => {
                    presenter.hideLabelEditModal();
                }}
                onOk={async () => {
                    await form.submit();
                    presenter.hideLabelEditModal();
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        return presenter.updateLabel(values);
                    }}
                >
                    <FormInput
                        name={"id"}
                        hidden={true}
                    />
                    <FormInput
                        name={"code"}
                        label="字典类型编码"
                        disabled={true}
                    />
                    <FormInput name={"parentId"} hidden={true}/>
                    <FormInput
                        name={"name"}
                        label="字典类型名称"
                        placeholder={"请输入字典类型名称"}
                        required={true}
                    />
                    <FormInput
                        required={false}
                        name={"comments"}
                        label="字典类型描述"
                        placeholder={"请输入字典类型描述"}
                    />
                    <FormStepper
                        required={false}
                        name="sysIndex"
                        label={"排序"}
                        stepperMinNumber={0}
                    />
                </Form>
            </Modal>
        </PageContainer>
    );
};

export default DictLabel;
