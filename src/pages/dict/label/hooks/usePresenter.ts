import React from "react";
import {LabelState} from "@/pages/dict/label/types";
import {LabelPresenter} from "@/pages/dict/label/presenter";
import {LabelApiImpl} from "@/pages/dict/label/model";

const initialState: LabelState = {
    labelEditVisible: false, //  标签编辑框是否可见
    labelAddVisible: false, // 标签添加框是否可见
    currentEditLabel: null, // 当前编辑的值
    currentLabelId: 0,
    tableRefreshVersion: 0, // 表格刷新版本号，用于表格刷新
}

export function useLabelPresenter() {
    const [state, dispatch] = React.useState<LabelState>(initialState);

    const presenterRef = React.useRef<LabelPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new LabelPresenter(dispatch, new LabelApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}