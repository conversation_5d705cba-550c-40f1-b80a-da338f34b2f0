export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface LabelState {
    labelEditVisible: boolean; //  label编辑框是否可见
    labelAddVisible: boolean; // label添加框是否可见

    currentEditLabel: any; // 当前编辑的label
    currentLabelId: number; //  当前labelID

    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新
}

export interface LabelApi {

    loadLabelTable: (params: any, sort: any, filter: any) => Promise<any>;

    saveLabel: (params: any) => Promise<{ success: boolean; data?: any }>;

    updateLabel: (params: any) => Promise<{ success: boolean; data?: any }>;

    deleteLabel: (params: any) => Promise<{ success: boolean }>;

    getLabelByCode: (params: any) => Promise<any>;

}