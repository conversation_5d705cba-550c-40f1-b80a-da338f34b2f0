import {DownOutlined} from "@ant-design/icons";
import {ActionType, ProColumns} from "@ant-design/pro-components";
import {
    PageContainer,
} from "@ant-design/pro-components";
import {Col, Popconfirm, Row, TreeProps} from "antd";
import React from "react";
import {Form, FormInput, FormSelect, FormStepper} from "@/components/form-pc";
import {useValuePresenter} from "@/pages/dict/value/hooks/usePresenter";
import {Button, Modal, ProTable, Tree} from "@itm/components-pc";
import './index.scss'

const DictValue: React.FC = () => {
    const actionRef = React.useRef<ActionType>(null);

    const form = Form.useForm();

    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useValuePresenter();

    const [reset, setReset] = React.useState(0);

    // 点击标签树节点时，更新当前标签
    const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
        presenter.updateCurrentLabel(selectedKeys as string[]);
    };

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);


    React.useEffect(() => {
        if (state.currentEditValue) {
            form.setFieldsValue(state.currentEditValue)
        } else {
            form.reset();
            form.setFieldsValue({
                labelId: state.currentEditValue?.labelId ?? state.currentValue?.labelId ?? state.currentValueLabelCode ?? 0,
                parentId: state.currentEditValue?.parentId ?? state.currentValue?.id ?? 0,
            });
        }
    }, [state.currentEditValue, reset]);

    React.useEffect(() => {
        if (state.currentValue) {
            form.setFieldsValue({
                labelId: state.currentValue.labelId,
                parentId: state.currentValue.id,
            });
        }
    }, [state.currentValue]);

    // 当左侧树选中变化时，设置表单字段值
    React.useEffect(() => {
        if (state.currentValueLabelCode) {
            form.setFieldsValue({
                labelId: state.currentValueLabelCode
            });
        }
    }, [state.currentValueLabelCode]);

    // 当组件挂载时，加载标签树数据
    React.useEffect(() => {
        presenter.loadLabelTree();
        presenter.updateCurrentLabel([]);
    }, []);

    const columns: ProColumns<any>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
        },
        {
            title: "名称",
            dataIndex: "name",
        },
        {
            title: "字编码",
            dataIndex: "code",
            search: false,
        },
        {
            title: "描述",
            dataIndex: "comments",
        },
        {
            title: "备选字段1",
            dataIndex: "value1",
            search: false,
        },
        {
            title: "备选字段2",
            dataIndex: "value2",
            search: false,
        },
        {
            title: "备选字段3",
            dataIndex: "value3",
            search: false,
        },
        {
            title: "排序",
            dataIndex: "sysIndex",
            search: false,
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            render: (_, record) => [
                <a
                    key="update"
                    onClick={() => {
                        presenter.showValueEditModal(record.code);
                    }}
                >编辑</a>,

                <a
                    key="add"
                    onClick={() => {
                        presenter.showValueChildAddModal(record);
                    }}
                >添加子项</a>,
                <Popconfirm
                    key="delete"
                    title="删除提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={() => {
                        presenter.deleteValue(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    return (
        <PageContainer>
            <Row>
                <Col span={4}>
                    {state.labelTreeData.length > 0 && (
                        <Tree
                            showLine
                            switcherIcon={<DownOutlined/>}
                            defaultExpandAll={true}
                            onSelect={onSelect}
                            treeData={state.labelTreeData}
                        />
                    )}
                </Col>
                <Col span={20} className={"proTable_box"}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="字典项"
                        scroll={{ y: 600 }}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    type={"primary"}
                                    onClick={() => {
                                        presenter.showValueAddModal();
                                        setReset(prev => prev + 1);
                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {
                            // 获取当前 labelId（来自 Presenter 的 state）
                            const labelId = state.currentValueLabelCode;
                            if (labelId != '') {
                                // 合并 labelId 到 params 中
                                const searchParams = {
                                    ...params,
                                    labelId: labelId ? Number(labelId) : undefined,
                                };
                                return presenter.searchValueTable(searchParams, sort, filter);
                            }
                            return presenter.searchValueTable(params, sort, filter);
                        }}
                    />
                </Col>
            </Row>
            <Modal
                open={state.valueAddVisible}
                title={"新增字典项"}
                destroyOnHidden={true}
                //新版本antd的modal中去除了onClose方法
                onCancel={() => {
                    presenter.hideValueAddModal()
                    setReset(prev => prev + 1);
                }}
                footer={() => (
                    <>
                        <Button
                            key="submit"
                            type="primary"
                            onClick={async () => {
                                await form.submit();
                                presenter.hideValueAddModal();
                                setReset(prev => prev + 1);
                            }}
                        >
                            提交
                        </Button>
                        <Button
                            key="continue"
                            type="default"
                            onClick={async () => {
                                await form.submit(); // 提交当前数据
                                presenter.showValueAddModal();
                                form.reset();   // 提交完成后清空表单
                                form.setFieldsValue({
                                    labelId: state.currentValueLabelCode ? state.currentValueLabelCode : undefined
                                });
                            }}
                        >
                            继续添加
                        </Button>
                        <Button
                            key="cancel"
                            onClick={() => {
                                presenter.hideValueAddModal();
                                setReset(prev => prev + 1);
                            }}
                        >
                            取消
                        </Button>
                    </>
                )}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        return presenter.createValue(values);
                    }}
                >
                    <FormInput name={"parentId"} hidden={true}/>
                    <FormSelect
                        label="选择所属类型"
                        placeholder="请选择所属类型"
                        name={"labelId"}
                        selectMultiple={false}
                        options={state.labelSelectData}
                        required={true}
                    />
                    <FormInput
                        name={"name"}
                        label="字典项名称"
                        placeholder={"请输入字典项名称"}
                        required={true}
                    />
                    <FormInput
                        required={false}
                        name={"comments"}
                        label="字典项描述"
                        placeholder={"请输入字典项描述"}
                    />
                    <FormInput
                        required={false}
                        name={"value1"}
                        label="备选字段1"
                        placeholder={"请输入备选字段1"}
                    />
                    <FormInput
                        required={false}
                        name={"value2"}
                        label="备选字段2"
                        placeholder={"请输入备选字段2"}
                    />
                    <FormInput
                        required={false}
                        name={"value3"}
                        label="备选字段3"
                        placeholder={"请输入备选字段3"}
                    />
                    <FormStepper
                        required={false}
                        name="sysIndex"
                        label={"排序"}
                        stepperMinNumber={0}
                    />
                </Form>
            </Modal>
            <Modal
                open={state.valueChildAddVisible}
                title={"新增子项"}
                destroyOnHidden={true}
                onCancel={() => {
                    presenter.hideValueChildAddModal();
                    setReset(prev => prev + 1);
                }}
                onOk={async () => {
                    await form.submit();
                    presenter.hideValueChildAddModal();
                    setReset(prev => prev + 1);
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        return presenter.createValue(values);
                    }}
                >
                    <FormInput name={"parentId"} hidden={true}/>
                    <FormInput name={"labelId"} hidden={true}/>
                    <FormInput
                        name={"name"}
                        label="字典项名称"
                        placeholder={"请输入字典项名称"}
                        required={true}
                    />
                    <FormInput
                        required={false}
                        name={"comments"}
                        label="字典项描述"
                        placeholder={"请输入字典项描述"}
                    />
                    <FormInput
                        required={false}
                        name={"value1"}
                        label="备选字段1"
                        placeholder={"请输入备选字段1"}
                    />
                    <FormInput
                        required={false}
                        name={"value2"}
                        label="备选字段2"
                        placeholder={"请输入备选字段2"}
                    />
                    <FormInput
                        required={false}
                        name={"value3"}
                        label="备选字段3"
                        placeholder={"请输入备选字段3"}
                    />
                    <FormStepper
                        required={false}
                        name="sysIndex"
                        label={"排序"}
                        stepperMinNumber={0}
                    />
                </Form>
            </Modal>
            <Modal
                open={state.valueEditVisible}
                title={"编辑字典项"}
                destroyOnHidden={true}
                onCancel={() => {
                    presenter.hideValueEditModal();
                }}
                onOk={async () => {
                    await form.submit();
                    presenter.hideValueEditModal();
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        return presenter.updateValue(values);
                    }}
                >
                    <FormInput
                        name={"id"}
                        hidden={true}
                    />
                    <FormInput name={"parentId"} hidden={true}/>
                    <FormSelect
                        name={"labelId"}
                        label="字典类型"
                        disabled={true}
                        options={state.labelSelectData}
                    />
                    <FormInput
                        name={"code"}
                        label="字典项编码"
                        disabled={true}
                    />
                    <FormInput
                        name={"name"}
                        label="字典项名称"
                        placeholder={"请输入字典项名称"}
                        required={true}
                    />
                    <FormInput
                        name={"comments"}
                        label="字典项描述"
                        placeholder={"请输入字典项描述"}
                        required={false}
                    />
                    <FormInput
                        required={false}
                        name={"value1"}
                        label="备选字段1"
                        placeholder={"请输入备选字段1"}
                    />
                    <FormInput
                        required={false}
                        name={"value2"}
                        label="备选字段2"
                        placeholder={"请输入备选字段2"}
                    />
                    <FormInput
                        required={false}
                        name={"value3"}
                        label="备选字段3"
                        placeholder={"请输入备选字段3"}
                    />
                    <FormStepper
                        required={false}
                        name="sysIndex"
                        label={"排序"}
                        stepperMinNumber={0}
                    />
                </Form>
            </Modal>
        </PageContainer>
    );
};

export default DictValue;