import {ValueApi} from "./types";
import {list, update, create, del, getDictValue} from "@/api/dict/value";
import {labelTree} from "@/api/dict/label";


export class ValueApiImpl implements ValueApi {
    loadLabelTree(): Promise<any> {
        return labelTree();
    }

    loadValueTable(params: any, sort: any, filter: any): Promise<any> {
        console.log("params", params);
        console.log("sort", sort);
        console.log("filter", filter);
        return list(params, sort, filter, [
            {
                key: "name",
                type: "LIKE",
            },
            {
                key: "comments",
                type: "LIKE",
            },
        ]);
    }

    async saveValue(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await create(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async updateValue(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await update(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async deleteValue(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

    getValueByCode(params: any): Promise<any> {
        return getDictValue(params);
    }
}