import React from "react";
import {ValueState} from "@/pages/dict/value/types";
import {ValuePresenter} from "@/pages/dict/value/presenter";
import {ValueApiImpl} from "@/pages/dict/value/model";

const initialState: ValueState = {
    currentValueLabelCode: '',

    valueEditVisible: false,
    valueAddVisible: false,
    valueChildAddVisible: false,

    currentEditValue: null,

    currentValue: null,

    tableRefreshVersion: 0,

    labelTreeData: [],
    labelSelectData: [],
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<ValueState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new ValueApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}