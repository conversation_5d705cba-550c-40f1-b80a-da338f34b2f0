export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface ValueState {
    currentValueLabelCode: string; // 当前value的labelCode

    valueEditVisible: boolean; //  value编辑框是否可见
    valueAddVisible: boolean; // value添加框是否可见

    valueChildAddVisible: boolean; // value添加框是否可见

    currentEditValue: any; // 当前编辑的值

    currentValue: any; //  当前value

    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新

    labelTreeData: any[]; // label树结构数据
    labelSelectData: any[]; // label树结构选择数据
}


export interface ValueApi {

    loadLabelTree: () => Promise<any>;

    loadValueTable: (params: any, sort: any, filter: any) => Promise<any>;

    saveValue: (params: any) => Promise<{ success: boolean; data?: any }>;

    updateValue: (params: any) => Promise<{ success: boolean; data?: any }>;

    deleteValue: (params: any) => Promise<{ success: boolean }>;

    getValueByCode: (params: any) => Promise<any>;

}