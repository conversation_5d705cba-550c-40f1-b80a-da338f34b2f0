import {ValueApi, ValueState, Dispatch} from "@/pages/dict/value/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<ValueState>;

    private readonly api: ValueApi;

    private state: ValueState;

    public constructor(dispatch: Dispatch<ValueState>, api: ValueApi, state: ValueState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: ValueState) => {
        this.state = newStatus;
    }

    // 显示value添加对话框
    public showValueAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueAddVisible: true,
                currentValue: null,
                currentEditValue: null
            }
        });
    }

    // 隐藏label添加对话框
    public hideValueAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueAddVisible: false,
            }
        })
    }

    // 显示label编辑对话框
    public showValueEditModal = (code: string) => {
        this.getValueByCode(code);
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueEditVisible: true,
            }
        })
    }

    // 隐藏label编辑对话框
    public hideValueEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueEditVisible: false,
            }
        })
    }

    public showValueChildAddModal(record: ValueState) {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueChildAddVisible: true,
                currentValue: record
            }
        });
    }

    public hideValueChildAddModal() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                valueChildAddVisible: false
            }
        });
    }

    // 刷新表格数据
    public refreshValueTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    // 删除数据
    public deleteValue = async (record: ValueState) => {
        try {
            const res = await this.api.deleteValue(record);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("删除失败", e);
        }
    }

    // 新增数据
    public createValue = async (record: ValueState) => {
        const params = {
            ...record,
            parentId: this.state.currentValue?.id ?? 0,
        }
        try {
            const res = await this.api.saveValue(params);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("保存失败", e);
        }

    }

    // 更新数据
    public updateValue = async (record: ValueState) => {
        try {
            const res = await this.api.updateValue(record);
            if (res.success) {
                this.refresh();
            }
        } catch (e) {
            console.error("更新失败", e);
        }

    }

    // 标签表格数据加载函数
    public searchValueTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params
        }
        return this.api.loadValueTable(searchParams, sort, filter);
    }

    public getValueByCode = (code: string) => {
        this.api.getValueByCode(code).then((data) => {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentEditValue: data.data
                }
            })
        })
    }

    // 设置当前点击的标签数据，然后刷新表格数据
    public updateCurrentLabel = (selected: string[]) => {
        if (selected && selected.length > 0) {
            const label = selected[0];
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentValueLabelCode: label
                }
            });
            this.refreshValueTable();
        }
    }

    // label树数据
    public loadLabelTree = () => {
        this.api.loadLabelTree().then(data => {
            this.dispatch((prevState) => {
                // 确保 data 是数组，否则设为空数组
                const dataArray = Array.isArray(data.data?.list) ? data.data?.list : [];
                const labelTree = this.getTreeOptions(dataArray);
                const labelSelect = this.getSelectOptions(dataArray);
                return {
                    ...prevState,
                    labelTreeData: labelTree,
                    labelSelectData: labelSelect
                }
            })
        });
    }

    public getTreeOptions = (data: any[]): TreeOption[] => {
        return data.map(item => ({
            title: item.name,
            key: item.id.toString(),
            children: item.children ? this.getTreeOptions(item.children) : undefined,
        }));
    };

    public getSelectOptions = (data: any[]): selectOption[] => {
        return data.map(item => ({
            label: item.name,
            value: item.id.toString(),
            children: item.children ? this.getSelectOptions(item.children) : undefined,
        }));
    };

    private refresh() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEditValue: null,
                currentValue: null
            }
        })
        this.refreshValueTable();
    }
}