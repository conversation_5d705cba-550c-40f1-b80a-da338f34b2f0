import { InstitutionApi, InstitutionState, Dispatch } from "./types";

/**
 *  InstitutionPresenter MVVM对象中ViewModel对象
 *  Presenter: 将UI控制对象交给View，Presenter 只做状态和业务逻辑的处理。设计到UI的界面数据刷新也通过数据状态来控制
 */

export interface TreeNode {
  key: string;
  title: string;
  children?: TreeNode[];

  [key: string]: any;
}

export class InstitutionPresenter {
  // 更新数据状态的dispatch函数
  private readonly dispatch: Dispatch<InstitutionState>;
  // 传入的Model对象
  private readonly api: InstitutionApi;
  // 当前的数据状态，由于InstitutionPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
  private state: InstitutionState;

  // 通过构造函数传入数据状态和Model对象
  public constructor(
    state: InstitutionState,
    dispatch: Dispatch<InstitutionState>,
    api: InstitutionApi
  ) {
    this.state = state;
    this.dispatch = dispatch;
    this.api = api;
  }

  // 更新状态数据
  public syncState = (newState: InstitutionState) => {
    this.state = newState;
  };

  // 刷新标签类型树数据
  public loadLabelTree = async () => {
    const res = await this.api.loadInstitutionCategoryNum();
    const numMap = res.data;
    this.api.loadLabelTree().then((res) => {
      const transformedData = res.data.list.map((item: any) => ({
        ...item,
        key: item.code, // 将 code 映射为 key
        title: item.name + "(" + (numMap[item.code] || 0) + ")", // 将 name 映射为 title，处理 undefined 情况
        // 如果有 children 子节点，递归处理
        children: item.children
          ? this.transformTreeData(item.children, numMap)
          : undefined,
      }));

      // 计算每个节点的总值
      const dataWithTotals = this.calculateNodeTotals(transformedData, numMap);

      this.dispatch((prevState) => ({
        ...prevState,
        labelTreeData: dataWithTotals,
      }));
    });
  };

  // 递归转换函数
  private transformTreeData = (list: TreeNode[], numMap: any): TreeNode[] => {
    return list.map((item: TreeNode) => ({
      ...item,
      key: item.code,
      title: item.name + "(" + (numMap[item.code] || 0) + ")",
      children: item.children
        ? this.transformTreeData(item.children, numMap)
        : undefined,
    }));
  };

  // 辅助函数：计算每个节点的总值
  private calculateNodeTotals = (
    nodes: TreeNode[],
    numMap: any
  ): TreeNode[] => {
    return nodes.map((node) => {
      let total = numMap[node.key] || 0;

      if (node.children && node.children.length > 0) {
        const childrenTotals = this.calculateNodeTotals(node.children, numMap);
        const childrenTotal = childrenTotals.reduce(
          (sum, child) => sum + (numMap[child.key] || 0),
          0
        );
        total += childrenTotal;
        node.children = childrenTotals;
      }

      node.title = node.name + "(" + total + ")";
      return node;
    });
  };

  // 显示标签添加对话框
  public showValueAddModal = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        valueAddVisible: true,
      };
    });
  };

  // 隐藏标签添加对话框
  public hideValueAddModal = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        valueAddVisible: false,
      };
    });
  };

  // 显示标签编辑对话框
  public showValueEditModal = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        valueEditVisible: true,
      };
    });
  };

  // 隐藏标签编辑对话框
  public hideValueEditModal = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        valueEditVisible: false,
      };
    });
  };

  // 删除标签数据
  public deleteValue = (record: InstitutionState) => {
    this.api.deleteValue(record);
    this.refreshValueTable();
  };

  // 更新标签数据
  public updateValue = (record: InstitutionState) => {
    this.api.updateValue(record);
    this.hideValueAddModal();
    this.hideValueEditModal();
    this.refreshValueTable();
  };

  // 刷新标签表格数据
  public refreshValueTable = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        tableRefreshVersion: prevState.tableRefreshVersion + 1,
      };
    });
  };

  // 设置当前点击的标签数据，然后刷新表格数据
  public updateCurrentLabel = (info: any) => {
    const selectedNodes = info.selectedNodes;

    if (selectedNodes && selectedNodes.length > 0) {
      const allCodes: string[] = [];

      // 遍历所有选中节点，并递归收集其所有子节点的 code
      for (const node of selectedNodes) {
        allCodes.push(...this.getAllCodesFromNode(node));
      }

      this.dispatch((prevState) => ({
        ...prevState,
        currentLabel: allCodes.join(","),
        currentTagId: selectedNodes[0].id,
      }));
    } else {
      this.dispatch((prevState) => ({
        ...prevState,
        currentLabel: "",
        currentTagId: 0,
      }));
    }
    this.refreshValueTable();
  };

  private getAllCodesFromNode(node: TreeNode): string[] {
    const codes: string[] = [node.code];

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        codes.push(...this.getAllCodesFromNode(child));
      }
    }

    return codes;
  }

  // 标签表格数据加载函数
  public searchValueTable = (params: any, sort: any, filter: any) => {
    // 添加当前标签数据到查询参数中
    const searchParams = {
      ...params,
      currentLabel: this.state.currentLabel,
    };

    return this.api.loadValueTable(searchParams, sort, filter);
  };

  // ---------------------------------

  //   filter 事件
  public onChange = <K extends keyof InstitutionState>(
    key: string,
    value: InstitutionState[K]
  ) => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        [key]: value,
      };
    });
  };

  // 重置表单
  public handlerResetForm = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        title: "",
        level: undefined,
        type: undefined,
        libraryType: undefined,
        company: undefined,
        publicDate: undefined,
      };
    });
    this.loadListData("");
  };

  // 获取table list
  public loadListData = async (filter?: string, isComponent?: boolean) => {
    this.dispatch((prev) => ({
      ...prev,
      loading: true,
    }));
    const params = {
      ...this.state.pagination,
      title: filter ? filter : null,
      tag: this.state.currentLabel ? this.state.currentLabel : null,
      fromComponent: isComponent,
    };
    const data = await this.api.loadListData(params);
    this.dispatch((prevState) => {
      return {
        ...prevState,
        list: data.data,
        loading: false,
        pagination: {
          ...prevState.pagination,
          total: data.total,
        },
      };
    });
  };

  // // 收藏
  public handlerChangeFavorite = (isFavorite: boolean, index: number) => {
    this.dispatch((prevState) => {
      const list = prevState.list;
      list[index].isFavorite = !isFavorite;
      return {
        ...prevState,
        list: [...list],
      };
    });
  };

  // 页码改变回调
  public handlerChangePage = (page: number) => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        pagination: {
          ...prevState.pagination,
          current: page,
        },
      };
    });
    setTimeout(() => {
      this.loadListData();
    });
  };

  // 页码或pageSize改变回调
  public handlerChangePageSize = (pageSize: number) => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        pagination: {
          ...prevState.pagination,
          pageSize: pageSize,
        },
      };
    });
    setTimeout(() => {
      this.loadListData();
    });
  };

  public setSelectedId = (id: number) => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        selectedId: id,
      };
    });
  };
}
