import React from "react";
import {InstitutionPresenter} from "../presenter";
import {InstitutionApiImpl} from "../model";
import {InstitutionState} from "../types";

// state状态的初始值
const initialState: InstitutionState = {
    currentLabel: '',
    currentTagId: 0,
    labelAddVisible: false,
    valueAddVisible: false,
    labelEditVisible: false,
    valueEditVisible: false,
    currentEditValue: null,
    tableRefreshVersion: 0,
    labelTreeData: [],

    title: '',
    level: undefined,
    type: undefined,
    libraryType: undefined,
    company: undefined,
    publicDate: undefined,
    list: [],
    pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
    },
    loading: false,
};

/**
 *  useInstitutionPresenter hook
 *  创建 InstitutionPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInstitutionPresenter() {
    const [state, dispatch] = React.useState<InstitutionState>(initialState);

    const presenterRef = React.useRef<InstitutionPresenter>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 InstitutionPresenter 实例
    if (!presenterRef.current) {
        // @ts-ignore
        presenterRef.current = new InstitutionPresenter(state, dispatch, new InstitutionApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
