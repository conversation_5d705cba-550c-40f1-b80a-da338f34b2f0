.collect-list-container {
  background: #f4f5f7;
  padding: 8px 0;
  overflow: hidden;

  .status-btn-bloor {
    cursor: pointer;
    display: inline-block;
    padding: 0 18px;
    color: var(--primary-color);
    border: 3px solid var(--primary-color);
    border-radius: 12px;
    font-weight: 500;
    margin-top: 5px;
  }

  .status-btn-red {
    cursor: pointer;
    display: inline-block;
    padding: 0 18px;
    color: red;
    border: 3px solid red;
    border-radius: 12px;
    font-weight: 500;
    margin-top: 5px;
  }

  .card {
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    line-height: 1.5714285714285714;
    list-style: none;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    height: 100%;
  }

  .test-style {
    font-size: 12px;
  }

  .icon-text {
    color: #999;
    font-size: 12px;
  }

  .btn-style {
    padding: 2px 12px;
    border-radius: 6px;
    border: 1px solid #c7c7c7;
    cursor: pointer;
  }

  .icon {
    color: #1660f9;
    margin-left: 13px;
    font-size: 16px;
  }

  .ant-progress-text {
    display: none;
  }
}

.radio-btn-style {
  padding: 2px 12px;
  border-radius: 6px;
  border: 1px solid #c7c7c7;
  cursor: pointer;
}

.header {
  overflow: hidden;
}

.search {
  width: 100%;
}

.searchBtn {
  margin-left: 16px;
}

.tag {
  margin: 12px 0;
  text-align: center;
}

.content {
  padding: 0 12px;
}

.treeCard {
  height: 100%;
  padding: 0;
}

.tree {
  width: 100%;
  background: #327357;
  padding: 12px 0;
  text-align: center;
}

.tree .header {
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.filterDetailLine {
  margin: 24px 0 0 24px;
}

.content {
  overflow-y: auto;
  overflow-x: hidden;
}

.listItem {
  border-bottom: 1px solid #0194fd;
  padding: 24px 24px 12px 0;
  margin-left: 24px;
}

.divider {
  text-align: center;
}

.contentLine {
  margin-top: 12px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #568ff7;
  cursor: pointer;
}

.listItemContent {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.text {
  font-size: 13px;
  color: #999;
}

.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-left: -12px;
}

.iconItem {
  width: 45%;
  cursor: pointer;
}

.iconItem:nth-child(3),
.iconItem:nth-child(4) {
  margin-top: 12px;
}

.icon {
  color: #1660f9;
  margin-left: 13px;
  font-size: 18px;
}

.iconText {
  color: #1660f9;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  text-align: right;
  padding: 12px 0;
}

:global(.ant-pro-card-body) {
  padding: 0 !important;
}

:global(.ant-pro-card-body) {
}
