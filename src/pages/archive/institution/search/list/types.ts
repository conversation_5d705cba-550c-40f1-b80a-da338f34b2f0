/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: (prevState: T) => T | T) => void;

/**
 * 数据状态类型的定义
 */
export interface InstitutionState {
  currentLabel: string;
  currentTagId: number;
  labelEditVisible: boolean;
  labelAddVisible: boolean;
  valueEditVisible: boolean;
  valueAddVisible: boolean;

  currentEditValue: any;
  tableRefreshVersion: number;
  labelTreeData: any[];

  title?: string;
  level?: string;
  type?: string;
  libraryType?: string;
  company?: string;
  publicDate?: any;

  list: any[];
  pagination: {
    total: number,
    pageSize: number,
    current: number,
  }
  selectedId?: number;
  loading: boolean;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface InstitutionApi {
  loadLabelTree: () => Promise<any>;

  loadInstitutionCategoryNum: () => Promise<any>;

  loadValueTable: (params: any, sort: any, filter: any) => Promise<any>;

  updateLabel: (params: any) => void;

  deleteLabel: (params: any) => void;

  updateValue: (params: any) => void;

  deleteValue: (params: any) => void;

  loadListData: (filter?: any, params?: any, sort?: any) => Promise<any>;
}
