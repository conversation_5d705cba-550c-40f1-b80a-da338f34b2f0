/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-13 11:59:19
 * @Description:
 * @LastEditTime: 2025-07-25 09:44:50
 * @LastEditors: wangjiantao
 */
import { InstitutionApi } from "./types";
import { data } from "@/api/archive/dict";
import {institutionSearch, institutionTagTree, loadInstitutionCategoryNum} from "@/api/archive/institution";
/**
 * Model层接口实现
 * InstitutionApiImpl 实现了InstitutionApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InstitutionApiImpl implements InstitutionApi {
  updateLabel(params: any): void {
    console.log("updateLabel:", params);
  }

  updateValue(params: any): void {
    console.log("updateValue:", params);
  }

  deleteLabel(params: any): void {
    console.log("deleteLabel:", params);
  }

  deleteValue(params: any): void {
    console.log("deleteValue:", params);
  }

  loadLabelTree(): Promise<any> {
    return institutionTagTree();
  }

  loadInstitutionCategoryNum(): Promise<any> {
    return loadInstitutionCategoryNum();
  }

  loadValueTable(params: any, sort: any, filter: any): Promise<any> {
    const list = data.values;
    return Promise.resolve({
      data: list,
      total: list.length,
    });
  }

  loadListData(params: any, sort: any, filter: any): Promise<any> {
    return institutionSearch(params, sort, filter,[]);
  }
}
