/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 17:57:48
 * @Description:
 * @LastEditTime: 2025-07-26 17:08:57
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React, { ReactNode, useRef, useEffect } from "react";
import { ActionType, ProCard, PageContainer } from "@ant-design/pro-components";
import {
  Button,
  Col,
  DatePicker,
  Divider,
  Flex,
  Form,
  Input,
  Pagination,
  Popover,
  Row,
  Select,
  Space,
  Spin,
  Tree,
  TreeProps,
} from "antd";
import {
  DownOutlined,
  RedoOutlined,
  SearchOutlined,
  UpOutlined,
} from "@ant-design/icons";
import { useInstitutionPresenter } from "./hooks/useInstitutionPresenter";
import dayjs from "dayjs";
import "./index.scss";
import { Checkbox } from "@itm/components-pc";

interface InstitutionSearchPageProps {
  isComponent?: boolean;
  onSelectedChange?: (id: number, type: string, name: string) => void;
}

const InstitutionSearchPage: React.FC<InstitutionSearchPageProps> = (props) => {
  // 表格的渲染对象，属于UI组件控制对象，不由Presenter控制，Presenter实现表格的更新通过状态控制，挂在能力对应第一个useEffect
  const actionRef = React.useRef<ActionType>(null);

  // 表单的渲染对象，属于UI组件控制对象，不由Presenter控制，Presenter实现表单的更新通过状态控制，挂在能力对应第二个useEffect
  const [form] = Form.useForm();

  // 通过hooks获取state状态数据与presenter对象
  const { state, presenter } = useInstitutionPresenter();

  // 添加滚动位置保存的ref
  const scrollPositionRef = useRef(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 当组件挂载时，加载标签树数
  React.useEffect(() => {
    presenter.loadLabelTree();
    presenter.updateCurrentLabel([]);
  }, [props.isComponent]);

  const onSelect: TreeProps["onSelect"] = (selectedKeys, info) => {
    presenter.updateCurrentLabel(info);
  };

  React.useEffect(() => {
    actionRef.current?.reload();
    presenter.loadListData("", props.isComponent);
  }, [state.tableRefreshVersion]);

  React.useEffect(() => {
    if (state.currentEditValue) {
      form.setFieldsValue(state.currentEditValue);
    } else {
      form.resetFields();
    }
  }, [form, state.currentEditValue]);

  // 保存滚动位置
  const saveScrollPosition = () => {
    if (scrollContainerRef.current) {
      scrollPositionRef.current = scrollContainerRef.current.scrollTop;
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (scrollContainerRef.current) {
      // 使用requestAnimationFrame确保在渲染完成后执行
      requestAnimationFrame(() => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop = scrollPositionRef.current;
        }
      });
    }
  };

  // 当state变化时恢复滚动位置
  useEffect(() => {
    restoreScrollPosition();
  }, [state]);

  const pagination = {
    display: "flex",
    justifyContent: "flex-end",
    padding: "12px 0",
  };

  // 高亮匹配文本
  const highlightText = (text: string): string | (string | ReactNode)[] => {
    if (!state.title?.trim()) return text;

    const regex = new RegExp(`(${state.title})`, "gi");
    return text.split(regex).map((part, i) =>
      regex.test(part) ? (
        <span style={{ color: "red" }} key={i}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  return (
    <div className="collect-list-container">
      <Row justify="center" style={{ overflow: "hidden" }}>
        <Col span={2} />
        <Col span="7">
          <Form form={form}>
            <Space.Compact style={{ width: "100%" }}>
              <Select
                size="large"
                defaultValue="1"
                options={[
                  {
                    value: "1",
                    label: "制度标题",
                  },
                ]}
              />
              <Input
                size="large"
                style={{ width: "100%" }}
                value={state.title}
                onChange={(e) => presenter.onChange("title", e.target.value)}
              />
              <Button
                size="large"
                iconPosition="start"
                loading={state.loading}
                onClick={() => {
                  // 搜索前保存滚动位置
                  saveScrollPosition();
                  presenter.loadListData(state.title);
                }}
                type="primary"
                icon={<SearchOutlined />}
              >
                搜 索
              </Button>
            </Space.Compact>
          </Form>
        </Col>
        <Col span="4" style={{ marginLeft: 16 }}>
          <Popover
            content={() => (
              <div style={{ width: 700 }}>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <span>制度层级</span>
                    <Space>
                      {[
                        "国家局(总公司)制度",
                        "省局(公司)制度",
                        "市局(公司)制度",
                        "县局(分公司)制度",
                      ].map((levelItem) => {
                        const isSelected = state.level?.includes(levelItem);
                        return (
                          <div
                            key={levelItem}
                            onClick={() => {
                              let newSelectedLevels;
                              if (isSelected) {
                                // 如果已选，取消选中
                                newSelectedLevels = state.level?.filter(
                                  (item) => item !== levelItem
                                );
                              } else {
                                // 否则添加
                                newSelectedLevels = [
                                  ...(state.level || []),
                                  levelItem,
                                ];
                              }
                              presenter.onChange("level", newSelectedLevels);
                            }}
                            className="radio-btn-style"
                            style={{
                              background: isSelected
                                ? "var(--primary-color)"
                                : "",
                              color: isSelected ? "#fff" : "",
                              cursor: "pointer",
                              padding: "4px 12px",
                              borderRadius: "4px",
                              transition: "all 0.2s",
                            }}
                          >
                            {levelItem}
                          </div>
                        );
                      })}
                    </Space>
                  </Space>
                </Col>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <span>制度类型</span>
                    <Space>
                      {["现行制度", "暂行制度", "废止制度"].map((typeItem) => {
                        const isSelected = state.type?.includes(typeItem);
                        return (
                          <div
                            key={typeItem}
                            onClick={() => {
                              let newSelectedTypes;
                              if (isSelected) {
                                newSelectedTypes = state.type?.filter(
                                  (item) => item !== typeItem
                                );
                              } else {
                                newSelectedTypes = [
                                  ...(state.type || []),
                                  typeItem,
                                ];
                              }
                              presenter.onChange("type", newSelectedTypes);
                            }}
                            className="radio-btn-style"
                            style={{
                              background: isSelected
                                ? "var(--primary-color)"
                                : "",
                              color: isSelected ? "#fff" : "",
                              cursor: "pointer",
                              padding: "4px 12px",
                              borderRadius: "4px",
                              transition: "all 0.2s",
                            }}
                          >
                            {typeItem}
                          </div>
                        );
                      })}
                    </Space>
                  </Space>
                </Col>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <span>制度库类别</span>
                    <Space>
                      {["提问库", "纠错库", "建议库", "监督库"].map(
                        (libraryItem) => {
                          const isSelected =
                            state.libraryType?.includes(libraryItem);
                          return (
                            <div
                              key={libraryItem}
                              onClick={() => {
                                let newSelectedLibraries;
                                if (isSelected) {
                                  newSelectedLibraries =
                                    state.libraryType?.filter(
                                      (item) => item !== libraryItem
                                    );
                                } else {
                                  newSelectedLibraries = [
                                    ...(state.libraryType || []),
                                    libraryItem,
                                  ];
                                }
                                presenter.onChange(
                                  "libraryType",
                                  newSelectedLibraries
                                );
                              }}
                              className="radio-btn-style"
                              style={{
                                background: isSelected
                                  ? "var(--primary-color)"
                                  : "",
                                color: isSelected ? "#fff" : "",
                                cursor: "pointer",
                                padding: "4px 12px",
                                borderRadius: "4px",
                                transition: "all 0.2s",
                              }}
                            >
                              {libraryItem}
                            </div>
                          );
                        }
                      )}
                    </Space>
                  </Space>
                </Col>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <span>单位(部门)</span>
                    <Select
                      mode="multiple"
                      size="small"
                      style={{ width: 240 }}
                      onChange={(e) => presenter.onChange("company", e)}
                      options={[
                        {
                          value: "贵州烟草专卖局(公司)(本级)",
                          label: "贵州烟草专卖局(公司)(本级)",
                        },
                      ]}
                    ></Select>
                  </Space>
                </Col>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <span>发文时间</span>
                    <DatePicker.RangePicker
                      size="small"
                      value={state.publicDate}
                      onChange={(date) =>
                        presenter.onChange("publicDate", date)
                      }
                    />
                  </Space>
                </Col>
                <Col span={24} style={{ margin: "24px 0 0 24px" }}>
                  <Space>
                    <Button
                      type="primary"
                      iconPosition="end"
                      icon={<SearchOutlined />}
                      onClick={() => {
                        // 高级搜索前保存滚动位置
                        saveScrollPosition();
                        presenter.loadListData(state.title);
                      }}
                    >
                      查询制度
                    </Button>
                    <Button
                      iconPosition="end"
                      icon={<RedoOutlined />}
                      onClick={() => {
                        // 重置前保存滚动位置
                        saveScrollPosition();
                        presenter.handlerResetForm();
                      }}
                    >
                      重置条件
                    </Button>
                  </Space>
                </Col>
              </div>
            )}
            trigger="click"
          >
            <Button
              size="large"
              iconPosition="end"
              icon={<UpOutlined rotate={state.valueEditVisible ? 180 : 0} />}
              onClick={() =>
                state.valueEditVisible
                  ? presenter.hideValueEditModal()
                  : presenter.showValueEditModal()
              }
            >
              高级检索
            </Button>
          </Popover>
        </Col>
        <Col
          span="24"
          style={{
            margin: "12px 0",
            textAlign: "center",
          }}
        >
          <Space>
            <span style={{ color: "#C7C6C6" }} className="textStyle">
              常用搜索：
            </span>
            <span className="textStyle">烟叶生产</span>
            <Divider size="small" type="vertical" />
            <span className="textStyle">卷烟营销</span>
            <Divider type="vertical" />
            <span className="textStyle">人事管理</span>
            <Divider type="vertical" />
            <span className="textStyle">科技管理</span>
          </Space>
        </Col>
      </Row>
      <Row gutter={12} style={{ padding: "0 12" }}>
        <Col span={6}>
          <div
            className="card"
            style={{
              height: "100%",
              padding: 0,
            }}
          >
            <div
              style={{
                width: "100%",
                background: "var(--primary-color)",
                padding: "12px 0",
                textAlign: "center",
              }}
            >
              <span
                style={{
                  color: "white",
                  fontSize: 20,
                  fontWeight: "bold",
                }}
              >
                制度分类
              </span>
            </div>
            <div
              style={{
                height: `calc(100vh - ${props.isComponent ? 380 : 190}px)`,
                overflow: "auto",
              }}
            >
              {state.labelTreeData.length > 0 && (
                <Tree
                  showLine
                  switcherIcon={<DownOutlined />}
                  defaultExpandAll={true}
                  onSelect={onSelect}
                  treeData={state.labelTreeData}
                />
              )}
            </div>
          </div>
        </Col>

        <Col span={18}>
          <ProCard colSpan={24}>
            <Spin spinning={state.loading}>
              <div
                ref={scrollContainerRef}
                style={{
                  height: `calc(100vh - ${props.isComponent ? 380 : 190}px)`,
                  overflowY: "auto",
                  overflowX: "hidden",
                }}
              >
                {state.list.map((record, index) => (
                  <Row>
                    {props.isComponent && (
                      <Col span={3}>
                        <Checkbox
                          checked={state.selectedId === record.id}
                          onChange={() => {
                            if (state.selectedId === record.id) {
                              // 当前已选中，再次点击取消选中
                              presenter.setSelectedId(0);
                              props.onSelectedChange?.(0, "", "");
                            } else {
                              // 选中当前项
                              presenter.setSelectedId(record.id);
                              props.onSelectedChange?.(
                                record.id,
                                record.type,
                                record.title
                              );
                            }
                          }}
                        />
                      </Col>
                    )}
                    <Col
                      span={24}
                      key={record.id}
                      style={{
                        borderBottom: "1px solid #d1d1d1",
                        padding: "12px 24px 12px 0",
                        marginLeft: 24,
                      }}
                    >
                      <Row justify="space-between" align="middle">
                        <Col span={24}>
                          <Row align="middle">
                            <Col span={21}>
                              <Space size={12}>
                                <div
                                  style={{
                                    fontSize: 16,
                                    fontWeight: 500,
                                    color: "#568ff7",
                                    cursor: "pointer",
                                  }}
                                  onClick={() => {
                                    if (!props.isComponent) {
                                      window.open(
                                        `/#/archive/institution/search/detail?id=${record.id}&type=${record.type}&currentTagId=${state.currentTagId}`,
                                        "_blank"
                                      );
                                    }
                                  }}
                                >
                                  {highlightText(record.title)}
                                </div>
                                <div
                                  className={
                                    record.status === "已废止"
                                      ? "status-btn-red"
                                      : "status-btn-bloor"
                                  }
                                >
                                  {record.status}
                                </div>
                              </Space>
                            </Col>
                            {!props.isComponent &&
                              record.complianceProcessStatus === "finished" && (
                                <Col span={3}>
                                  <Button
                                    size="small"
                                    type="primary"
                                    iconPosition="start"
                                    icon={<SearchOutlined />}
                                    onClick={() => {
                                      window.open(
                                        `/#/compliance/detail?id=${record.id}&type=${record.type}`,
                                        "_blank"
                                      );
                                    }}
                                  >
                                    查看合规风险
                                  </Button>
                                </Col>
                              )}
                            {!props.isComponent && (
                              <Col span={24}>
                                <Flex gap="8px 0" wrap>
                                  <Space>
                                    <span
                                      className="test-style"
                                      style={{ color: "#55acee" }}
                                    >
                                      提问({record.questionCount})
                                    </span>
                                    <Divider size="small" type="vertical" />
                                    <span
                                      className="test-style"
                                      style={{ color: "#cd201f" }}
                                    >
                                      纠错({record.correctCount})
                                    </span>
                                    <Divider size="small" type="vertical" />
                                    <span
                                      className="test-style"
                                      style={{ color: "#3b5999" }}
                                    >
                                      建议({record.suggestCount})
                                    </span>
                                    <Divider size="small" type="vertical" />
                                    <span
                                      className="test-style"
                                      style={{ color: "#55acee" }}
                                    >
                                      监督({record.supervisionCount})
                                    </span>
                                  </Space>
                                </Flex>
                              </Col>
                            )}
                            <Col span={24} style={{ marginTop: 6 }}>
                              <Space>
                                <div className="icon-text">
                                  {record.pushOrgName}
                                </div>
                                <Divider size="small" type="vertical" />
                                <div className="icon-text">
                                  {dayjs(Number(record.pushTime)).format(
                                    "YYYY-MM-DD"
                                  )}{" "}
                                  发布
                                </div>
                                <Divider size="small" type="vertical" />
                                <div className="icon-text">
                                  {record.docNumber}
                                </div>
                                <Divider size="small" type="vertical" />
                                <Popover
                                  content={<div>{record.tag}</div>}
                                  trigger="hover"
                                >
                                  <div className="icon-text">
                                    {record.tag
                                      ? `${record.tag.slice(0, 20)}${
                                          record.tag.length > 20 ? "..." : ""
                                        }`
                                      : ""}
                                  </div>
                                </Popover>
                              </Space>
                            </Col>

                            <Col
                              span={24}
                              style={{
                                marginTop: 6,
                              }}
                            >
                              <span
                                style={{
                                  display: "inline-block",
                                  whiteSpace: "nowrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  width: "100%",
                                }}
                              >
                                {record.content}
                              </span>
                            </Col>
                            {!props.isComponent && (
                              <Col
                                span={24}
                                style={{
                                  marginTop: 6,
                                }}
                              >
                                <Space>
                                  <span className="icon-text">查阅热度:</span>
                                  <div style={{ display: "flex", gap: 4 }}>
                                    {[0, 1, 2, 3, 4].map((index) => {
                                      const percent = 58;
                                      const active =
                                        index < Math.ceil(percent / 20); // 每 20% 一个格子
                                      return (
                                        <div
                                          key={index}
                                          style={{
                                            width: 20,
                                            height: 8,
                                            borderRadius: 2,
                                            backgroundColor: active
                                              ? "#87d068"
                                              : "#e6e6e6",
                                            transition: "background-color 0.3s",
                                          }}
                                        />
                                      );
                                    })}
                                  </div>
                                </Space>
                              </Col>
                            )}
                          </Row>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                ))}
              </div>
              {state.pagination.total ? (
                <Row>
                  <Col span={24} style={pagination}>
                    <Pagination
                      total={state.pagination.total}
                      showQuickJumper
                      showTitle
                      onChange={(current) => {
                        // 分页切换前保存滚动位置
                        saveScrollPosition();
                        presenter.handlerChangePage(current);
                      }}
                      onShowSizeChange={(_, size) => {
                        // 分页大小切换前保存滚动位置
                        saveScrollPosition();
                        presenter.handlerChangePageSize(size);
                      }}
                    />
                  </Col>
                </Row>
              ) : null}
            </Spin>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default InstitutionSearchPage;
