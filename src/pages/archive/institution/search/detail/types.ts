/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-18 13:42:01
 * @Description:
 * @LastEditTime: 2025-07-21 20:30:51
 * @LastEditors: wangjiantao
 */

export type Dispatch<T> = (updater: (prevState: T) => T) => void;

export interface Institution {
  title: string;
  content: string;
}

export interface AnnotationPosition {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

export type InstitutionOrNull = Institution | null | undefined;

export interface InstitutionState {
  data: InstitutionOrNull;
  searchValue: string;
  isPure: boolean /** 清洁版 */;
  isFocus: boolean /** 聚焦命中 */;
  currentPage: number /** 当前页码 */;
  institutionDetail: any;
  institutionNav: any;
  annotations: any[];
  id: number | null;
}

export interface Position {
  x: number;
  y: number;
  isTop: boolean;
}

export interface Annotations {
  clientRect?: {
    left: number;
    top: number;
    right: number;
    bottom: number;
    height: number;
    width: number;
  };
  /** 数据id */
  id?: number;
  linePath?: {
    end: { x: number; y: number };
    mid: { x: number; y: number };
    start: { x: number; y: number };
  };
  /** 批注元素坐标 */
  position?: AnnotationPosition;
  range?: Range;
  /** 选中的文字 */
  text?: string;
  /** 批注文字 */
  commentContent?: string;
  /** 回复信息 */
  children?: Annotations[];
  /** 所选段落id */
  paragraphId?: string;
  /** 所选文本在段落内的起始索引 */
  startIndex?: number | null;
  pId?: string;
  selectedContent?: string;
  selectedStartIndex?: number;
  actionType: number;
  username?: string;
  buckedId: number,
  createTime?: string;
}

export interface SelectedText {
  clientRect: { left: number; top: number; right: number; bottom: number };
  range: any;
  text: string;
}

export interface InitialAnnotations extends Annotations {
  selectedPosition: string;
}

export interface InstitutionApi {
  /**
   * @description 根据id获取文章
   * @param id 文章id
   * */
  loadInstitution: (params: any) => Promise<any>;
  /**
   * @description 收藏文章
   */
  connectInstitution: (InstitutionId: number) => Promise<any>;
  /**
   * @description 导出文章
   * @param id 文章id
   */
  exportInstitution: (InstitutionId: number) => Promise<any>;

  removeQuestion: (id: number) => Promise<any>;

  /**
   * @description 获取批注
   * */
  questionList: (params: { institutionId: number }) => Promise<any>;

  /**
   * @description 页内搜索
   * @param string
   * */
  searchInPage: (value: string) => void;
  /**
   * @description 收藏文章
   */
  connectArticle: (articleId: number) => Promise<any>;

  addOrUpdateQuestion: (body: any) => Promise<any>;

}
