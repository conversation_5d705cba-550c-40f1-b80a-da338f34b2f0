/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 17:57:48
 * @Description: 
 * @LastEditTime: 2025-07-21 15:12:17
 * @LastEditors: wangjiantao
 */
import { useEffect, useRef, useState } from "react";
import { InstitutionPresenter } from "../presenter";
import { InstitutionApiImpl } from "../model";
import { InstitutionState } from "../types";
import { useParams } from "react-router-dom";

// state状态的初始值
const initialState: InstitutionState = {
  data: null,
  searchValue: "",
  isPure: false,
  isFocus: false,
  currentPage: 1,
  institutionDetail: null,
  institutionNav: null,
  annotations: [],
  id: null
};

/**
 *  useInstitutionPresenter hook
 *  创建 InstitutionPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInstitutionPresenter() {
  const [state, dispatch] = useState<InstitutionState>(initialState);

  const htmlRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const presenterRef = useRef<InstitutionPresenter>(null);
  const params = useParams();
  const id = Number(params.id);
  const type = params.type;

  // 如果 presenterRef.current 为空，则创建一个新的 InstitutionPresenter 实例
  if (!presenterRef.current) {
    // @ts-ignore
    presenterRef.current = new InstitutionPresenter(
      state,
      dispatch,
      new InstitutionApiImpl()
    );
  }

  // 当 state 发生变化时，更新 presenter 的状态
  useEffect(() => {
    presenterRef.current?.syncState(state);
  }, [state]);
 

  // 获取文章
  useEffect(() => {
    if (isNaN(id) || !type) return;
    presenterRef.current?.loadInstitutionById(id, type);
  }, []);

  const handlerInputChange = (value: string) => {
    presenterRef.current?.searchInPage(value);
  };

  /**
   * @description 收藏文章
   * */
  const handlerConnectInstitution = () => {
    presenterRef.current?.connectInstitution(1);
  };

  /**
   * @description 纯净/详解
   * */
  const handlerPageStatusChange = () => {
    presenterRef.current?.pageStatusChange();
  };

  /**
   * @param text 选中的文字
   * */
  const handlerClickedAnnotation = (text: string) => {
    console.log(text);
  };

    /**
   * @description 收藏文章
   * */
  const handlerConnectArticle = () => {
    presenterRef.current?.connectArticle(1);
  };

  return {
    state,
    presenter: presenterRef.current,
    handlerConnectInstitution,
    handlerPageStatusChange,
    htmlRef,
    containerRef,
    handlerClickedAnnotation,
    handlerConnectArticle,
  };
}
