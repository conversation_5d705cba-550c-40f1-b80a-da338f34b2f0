/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-18 14:17:13
 * @Description:
 * @LastEditTime: 2025-07-21 15:50:47
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import { InstitutionApi } from "./types";
import {
  addOrUpdateQuestion,
  institutionDetail,
  questionList,
  removeQuestion,
} from "@/api/archive/institution";

/**
 * Model层接口实现
 * InstitutionApiImpl 实现了InstitutionApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InstitutionApiImpl implements InstitutionApi {
  loadInstitution(params: any): Promise<any> {
    return institutionDetail(params);
  }

  addOrUpdateQuestion(body: any): Promise<any> {
    return addOrUpdateQuestion(body);
  }

  removeQuestion(body: any): Promise<any> {
    return removeQuestion(body);
  }


  /**
   * @description 获取批注
   * */
  questionList(params: {
    institutionId: number;
  }): ReturnType<typeof questionList> {
    return questionList(params);
  }


  connectInstitution(id: number): Promise<any> {
    return Promise.resolve({
      code: 200,
      message: "成功",
    });
  }

  exportInstitution(id: number): Promise<any> {
    return Promise.resolve({
      code: 200,
      message: "成功",
    });
  }

  searchInPage(value: string): void {
    console.log(value);
  }

  connectArticle(id: number): Promise<any> {
    return Promise.resolve({
      code: 200,
      message: "成功",
    });
  }
}
