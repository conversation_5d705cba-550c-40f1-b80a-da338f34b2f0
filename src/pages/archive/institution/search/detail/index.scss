.detail-container {
  flex: 1;
  height: 100%;
  width: 100%;
  background-color: #f4f5f7;
  overflow: hidden;

  .ant-float-btn-group-wrap {
    width: 80px !important;

    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }

  .header {
    padding: 14px 12px;
    background-color: white;
    .btn {
      color: var(--primary-color);
      font-size: 20px;
      font-weight: 600;
    }
  }

  .card {
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    line-height: 1.5714285714285714;
    list-style: none;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }

  .tree {
    height: calc(100vh - 52.4px);
    padding: 0;
    border-radius: 0;
    border: 1px solid #f0f0f0;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 12px;
      background-color: var(--primary-color);
      color: white;
    }
  }

  .content {
    padding: 0;
    height: calc(100vh - 52.4px);
    border-radius: 0;
    width: 100%;
    position: relative;
    
    .ant-card-body {
      padding: 0 !important;
    }
  }
}
