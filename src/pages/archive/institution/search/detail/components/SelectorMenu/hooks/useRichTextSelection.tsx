import { useState, useEffect, useRef, RefObject } from "react";

import type { TextAreaRef } from "antd/es/input/TextArea";
import {
  Annotations,
  InitialAnnotations,
  Position,
  SelectedText,
} from "../../../types";
import { debounce, throttle } from "lodash";

export const useRichTextSelection = (
  parentContainerRef: RefObject<HTMLElement | null>,
  htmlRef: RefObject<HTMLElement | null>,
  initialAnnotations: InitialAnnotations[]
) => {
  const [selectedText, setSelectedText] = useState<string>(""); // 选择的html文本
  const [position, setPosition] = useState<Position | null>(null); // 菜单按钮坐标
  const menuRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false); // 菜单按钮显示

  const [annotations, setAnnotations] = useState<Annotations[]>([]);
  const [scrollPosition, setScrollPosition] = useState<{
    x: number;
    y: number;
  }>({ x: 0, y: 0 });
  const [activeAnnotation, setActiveAnnotation] = useState<any>(null);
  const annotationPanelRef = useRef<HTMLDivElement>(null);
  const [_selectedText, _setSelectedText] = useState<SelectedText | null>(null);

  const inputsRef = useRef<TextAreaRef[]>([]); // 输入框的ref

  const rangeRef = useRef<any>(null);
  const selectionRef = useRef<any>(null);

  const lastedRef = useRef<number[]>([]);

  const [inputing, setInputing] = useState<number | string | null>(null);

  const startIndexRef = useRef<number>();

  /**
   * @description 获取选中文本涉及的所有段落ID
   */
  const getSelectedParagraphIds = (range: Range): string => {
    const paragraphIds = new Set<string>();

    // 1. 获取选区起始和结束节点
    const startContainer = range.startContainer;
    const endContainer = range.endContainer;

    // 2. 定义查找段落元素的函数
    const findParagraphElement = (node: Node): HTMLElement | null => {
      let current: Node | null = node;
      while (current && current.nodeType === Node.ELEMENT_NODE) {
        if (
          (current as HTMLElement).tagName === "P" &&
          (current as HTMLElement).id
        ) {
          return current as HTMLElement;
        }
        current = current.parentNode;
      }
      return null;
    };

    // 3. 处理起始节点
    const startParagraph = findParagraphElement(startContainer);
    if (startParagraph) {
      paragraphIds.add(startParagraph.id);
    }

    // 4. 处理结束节点（如果与起始节点不同）
    if (endContainer !== startContainer) {
      const endParagraph = findParagraphElement(endContainer);
      if (endParagraph) {
        paragraphIds.add(endParagraph.id);
      }
    }

    // 5. 检查选区中间是否包含其他段落
    let commonAncestor = range.commonAncestorContainer;
    if (commonAncestor.nodeType === Node.ELEMENT_NODE) {
      const walker = document.createTreeWalker(
        commonAncestor,
        NodeFilter.SHOW_ELEMENT,
        {
          acceptNode: (node) =>
            (node as HTMLElement).tagName === "P" && (node as HTMLElement).id
              ? NodeFilter.FILTER_ACCEPT
              : NodeFilter.FILTER_SKIP,
        }
      );

      while (walker.nextNode()) {
        const p = walker.currentNode as HTMLElement;
        // 检查段落是否与选区相交
        const pRange = document.createRange();
        pRange.selectNodeContents(p);
        if (
          range.compareBoundaryPoints(Range.START_TO_START, pRange) <= 0 &&
          range.compareBoundaryPoints(Range.END_TO_END, pRange) >= 0
        ) {
          paragraphIds.add(p.id);
        }
      }
    }

    // 6. 返回拼接的ID字符串
    return Array.from(paragraphIds).join(",");
  };
  /**
   * @description 计算批注的总高度（包括回复）
   *
   */
  const calculateAnnotationHeight = (annotation: Annotations): number => {
    const baseHeight = 120; // 基础高度（无回复时）
    const replyHeight = 90; // 每条回复的高度
    const repliesCount = annotation.children?.length || 0;
    return baseHeight + repliesCount * replyHeight;
  };

  /**
   * @description 点击批注后将选中文字背景设为红色
   * */
  const highlightSelection = (actionIndex: number) => {
    const id = String(Date.now());
    if (!rangeRef.current) return;
    try {
      // 创建span元素包裹选中内容
      const span = document.createElement("span");
      span.className = `hightlight-text-${id}`;
      // span.className = 'highlighted-text'; // 添加类名
      span.style.backgroundColor = "rgba(236, 145, 145, 1)"; // 或者使用CSS类

      // 提取选中内容并包装在span中
      rangeRef.current.surroundContents(span);
      selectionRef.current?.removeAllRanges();
    } catch (e) {
      // 备选方案：如果直接包装失败，尝试逐个文本节点处理
      fallbackHighlight(id);
    }
    addAnnotation(id, actionIndex);
  };

  // 备选高亮方案，处理复杂选区情况
  const fallbackHighlight = (id: string) => {
    const range = rangeRef.current;
    if (!range) return;
    const startContainer = range.startContainer;
    const endContainer = range.endContainer;

    // 如果选区开始和结束都在同一个文本节点内
    if (
      startContainer === endContainer &&
      startContainer.nodeType === Node.TEXT_NODE
    ) {
      const textNode = startContainer;
      const text = textNode.nodeValue;
      if (!text) return;
      if (!textNode) return;
      const startOffset = range.startOffset;
      const endOffset = range.endOffset;

      // 分割文本节点
      const beforeText = text.substring(0, startOffset);
      const highlightText = text.substring(startOffset, endOffset);
      const afterText = text.substring(endOffset);

      // 创建新的节点结构
      const beforeNode = document.createTextNode(beforeText);
      const highlightSpan = document.createElement("span");
      highlightSpan.className = `hightlight-text-${id}`;
      highlightSpan.style.backgroundColor = "rgba(236, 145, 145, 1)";
      const highlightTextNode = document.createTextNode(highlightText);
      const afterNode = document.createTextNode(afterText);

      // 替换原节点
      textNode.parentNode?.replaceChild(beforeNode, textNode);
      beforeNode.parentNode?.insertBefore(
        highlightSpan,
        beforeNode.nextSibling
      );
      highlightSpan.appendChild(highlightTextNode);
      highlightTextNode.parentNode?.insertBefore(
        afterNode,
        highlightTextNode.nextSibling
      );

      // 清除选区
      selectionRef.current?.removeAllRanges();
    } else {
      // Create a document fragment to hold our highlighted content
      const fragment = range.cloneContents();

      // Wrap all text nodes in the fragment with highlight spans
      const walker = document.createTreeWalker(
        fragment,
        NodeFilter.SHOW_TEXT,
        null
      );

      const textNodes = [];
      while (walker.nextNode()) {
        textNodes.push(walker.currentNode);
      }

      // Process each text node in the selection
      textNodes.forEach((node) => {
        const span = document.createElement("span");
        span.className = `hightlight-text-${id}`;
        span.style.backgroundColor = "rgba(236, 145, 145, 1)";
        node.parentNode?.replaceChild(span, node);
        span.appendChild(node);
      });

      // Delete the original content and insert the highlighted version
      range.deleteContents();
      range.insertNode(fragment);

      // Clear the selection
      selectionRef.current?.removeAllRanges();
    }
  };

  const getNextAnnotationBoxY = () => {
    if (!rangeRef.current || !annotationPanelRef.current) return 20;

    const scrollY = window.scrollY || document.documentElement.scrollTop;
    const panelRect = annotationPanelRef.current.getBoundingClientRect();
    const panelTop = panelRect.top + scrollY;

    const selectionRect = rangeRef.current.getBoundingClientRect();
    const selectionTop = selectionRect.top + scrollY;

    let desiredY = selectionTop - panelTop;

    // 冲突检测，避让已有批注
    const padding = 20;
    const sorted = [...annotations].sort(
      (a, b) => (a.position?.y ?? 0) - (b.position?.y ?? 0)
    );

    for (const ann of sorted) {
      const annTop = ann.position?.y ?? 0;
      const annBottom = annTop + calculateAnnotationHeight(ann);
      const overlap = desiredY < annBottom && desiredY + 120 > annTop;

      if (overlap) {
        desiredY = annBottom + padding;
      }
    }

    return desiredY;
  };

  /**
   * @description 添加新批注
   * @param id 批注唯一ID
   * @param actionIndex 批注类型索引
   */
  const addAnnotation = (id: string, actionIndex: number) => {
    if (!annotationPanelRef.current || !_selectedText || !rangeRef.current) {
      console.warn("Missing required refs for adding annotation");
      return;
    }

    // 1. 获取选区位置信息
    const selectionRect = rangeRef.current.getBoundingClientRect();
    const scrollY = window.scrollY || document.documentElement.scrollTop;
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const startX = selectionRect.right + scrollX;
    const startY = selectionRect.top + scrollY;

    // 2. 获取批注面板位置
    const panelRect = annotationPanelRef.current.getBoundingClientRect();
    const panelTop = panelRect.top + scrollY;
    const panelLeft = panelRect.left + scrollX;

    // ✅ 3. 计算所有现有批注的绝对 bottom 位置（用于避让）
    const existingBottoms = annotations.map((ann) => {
      const annTop = panelTop + (ann.position?.y ?? 0);
      const annHeight = calculateAnnotationHeight(ann);
      return annTop + annHeight;
    });
    const maxBottom = Math.max(panelTop + 20, ...existingBottoms);
    const annotationBoxX = 20;
    const annotationBoxY = getNextAnnotationBoxY(); // ✅ 相对 panel 顶部的 Y 值，带 padding

    // 4. 计算连接线路径
    const midX = startX + 80;
    const endX = panelLeft + annotationBoxX;
    const endY = panelTop + annotationBoxY + 15;

    // 5. 获取段落ID
    const paragraphId = getSelectedParagraphIds(rangeRef.current);

    // 6. 创建批注对象
    const newAnnotation: Annotations = {
      id: Number(id),
      buckedId: Number(id),
      text: selectedText,
      position: { x: annotationBoxX, y: annotationBoxY },
      linePath: {
        start: { x: startX, y: startY },
        mid: { x: midX, y: startY },
        end: { x: endX, y: endY },
      },
      range: rangeRef.current,
      clientRect: selectionRect,
      children: [],
      paragraphId,
      startIndex: startIndexRef.current,
      actionType: actionIndex,
      selectedContent: _selectedText?.text || "",
      selectedStartIndex: startIndexRef.current,
      pId: paragraphId.split(",")[0],
    };

    // 7. 状态处理
    _setSelectedText(null);
    setActiveAnnotation(id);
    setInputing(id);
    setAnnotations((prev) => [...prev, newAnnotation]);

    setTimeout(() => {
      const input = inputsRef.current[inputsRef.current.length - 1];
      input?.focus();
    }, 300);
  };
  // 获取当前连接线位置（考虑滚动）
  const getCurrentLinePath = (annotation: Annotations, index: number) => {
    const panelRect = annotationPanelRef.current?.getBoundingClientRect() || {
      left: 0,
      top: 0,
    };


    // 获取选中文本的当前窗口位置（起点：文本末尾中心）
    let startX, startY;
    const highlight = document.querySelector(
      `.hightlight-text-${annotation.buckedId}`
    );

    if (highlight) {
      const cachedRect = highlight.getBoundingClientRect();
      startX = cachedRect.right; // 文本末尾 X 坐标
      startY = cachedRect.top + cachedRect.height / 2; // 文本垂直中心 Y 坐标
    } else {
      //  fallback：使用保存的位置 + 滚动偏移
      startX = (annotation.clientRect?.right || 0) + window.scrollX;
      startY =
        (annotation.clientRect?.top || 0) +
        (annotation.clientRect?.height || 0) / 2 +
        window.scrollY;
    }

    // 计算批注框的当前窗口位置（终点：批注面板左侧）
    const endX = panelRect.left + window.scrollX + annotation.position!.x;
    const endY = panelRect.top + window.scrollY + annotation.position!.y + 15;

    // 折线路径：直接从起点到终点（L 命令）
    return {
      start: { x: startX, y: startY },
      mid: { x: startX, y: startY }, // 折线无中间点，可设为起点
      end: { x: endX, y: endY },
    };
  };

  // 修复后的获取富文本函数
  const getSelectedHtml = (selection: Selection): string => {
    if (!selection || selection.rangeCount === 0) return "";

    rangeRef.current = selection.getRangeAt(0);

    const container = document.createElement("div");

    // 1. 使用 cloneContents() 复制选区内容
    const fragment = rangeRef.current.cloneContents();

    // 2. 特殊处理：确保图片等媒体元素保留完整属性
    fragment.querySelectorAll("img").forEach((img: HTMLImageElement) => {
      // 复制所有属性到新元素
      const newImg = document.createElement("img");
      // 由于不能直接使用 for...of 循环，使用传统的 for 循环替代
      for (let i = 0; i < img.attributes.length; i++) {
        const attr = img.attributes[i];
        newImg.setAttribute(attr.name, attr.value);
      }
      img.parentNode?.replaceChild(newImg, img);
    });

    // 3. 处理其他富文本元素（如视频、iframe等）
    fragment
      .querySelectorAll("video, iframe")
      .forEach((el: HTMLVideoElement) => {
        const newEl = document.createElement(el.tagName);
        // 由于不能直接使用 for...of 循环，使用传统的 for 循环替代
        for (let i = 0; i < el.attributes.length; i++) {
          const attr = el.attributes[i];
          newEl.setAttribute(attr.name, attr.value);
        }
        el.parentNode?.replaceChild(newEl, el);
      });

    container.appendChild(fragment);
    return container.innerHTML;
  };

  /**
   * 获取选中文本在当前段落中的开始索引
   * @param range 选中的Range对象
   * @param paragraphElement 当前段落元素
   * @returns 选中文本在段落中的开始字符索引
   */
  const getSelectionStartIndex = (
    range: Range,
    paragraphElement: HTMLElement
  ): number => {
    // 1. 创建用于遍历的Range对象
    const paraRange = document.createRange();
    paraRange.selectNodeContents(paragraphElement);
    paraRange.setEnd(range.startContainer, range.startOffset);

    // 2. 获取Range内的文本内容
    const textBeforeSelection = paraRange.toString();

    // 3. 计算文本长度即为开始索引
    return textBeforeSelection.length;
  };

  useEffect(() => {
    const handleMouseUp = (event: MouseEvent) => {
      const selection = window.getSelection();

      selectionRef.current = selection;

      if (!selection || selection.toString().trim() === "") {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }

      // 检查选区是否在批注面板内
      if (
        annotationPanelRef.current &&
        annotationPanelRef.current.contains(selection.anchorNode)
      ) {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }

      // 添加延时确保浏览器完成选区渲染
      const html = getSelectedHtml(selection);
      setSelectedText(html);
      const range = selection.getRangeAt(0);

      const rect = range.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const isTop = rect.top > viewportHeight / 2;

      // 获取包含选中文本的段落元素
      let paragraphElement = range.commonAncestorContainer;
      while (paragraphElement && paragraphElement.nodeName !== "P") {
        if (paragraphElement.parentNode) {
          paragraphElement = paragraphElement.parentNode;
        } else {
          break;
        }
      }

      if (paragraphElement) {
        const _startIndex = getSelectionStartIndex(
          range,
          paragraphElement as HTMLElement
        );
        startIndexRef.current = _startIndex;
      }

      setPosition({
        x: rect.left + rect.width / 2,
        y: isTop ? rect.top - 10 : rect.bottom + 10,
        isTop,
      });

      setIsVisible(true);
    };

    // 将 ref.current 缓存到局部变量中
    const container = parentContainerRef.current;

    // 确保容器存在
    if (!container) return;

    // 监听鼠标按下和松开事件
    container.addEventListener("mouseup", handleMouseUp);

    // 额外监听选择变化，处理通过键盘选择的情况
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      if (!selection || selection.toString().trim() === "") {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }

      // 添加批注面板区域的检查
      if (
        annotationPanelRef.current &&
        annotationPanelRef.current.contains(selection.anchorNode)
      ) {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }
      if (!selection || selection.toString().trim() === "") {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
      }
    };

    container.addEventListener("selectionchange", handleSelectionChange);

    return () => {
      container.addEventListener("mouseup", handleMouseUp);
      container.addEventListener("selectionchange", handleSelectionChange);
    };
  }, [htmlRef, parentContainerRef]);

  // // 点击批注时高亮对应文本
  // const handleAnnotationClick = (id: number) => {
  //   setActiveAnnotation(id);
  //   const highlight = parentContainerRef.current?.querySelector(
  //     `hightlight-text[data-annotation-id="${id}"]`
  //   );
  //   if (highlight) {
  //     highlight.scrollIntoView({ behavior: "smooth", block: "center" });

  //     // 添加临时闪烁效果
  //     // @ts-ignore
  //     highlight.style.animation = "wps-highlight-flicker 0.5s 2";
  //     setTimeout(() => {
  //       // @ts-ignore
  //       highlight.style.animation = "";
  //     }, 1000);
  //   }
  // };

  // 点击菜单外部时关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setPosition(null);
        setIsVisible(false);
      }
    };

    if (position) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [position]);

  // 处理文本选择
  useEffect(() => {
    const handleSelection = () => {
      const selection = window.getSelection();
      if (!selection || selection.toString().trim() === "") {
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }

      // 检查选区是否在 htmlRef.current 范围内
      if (htmlRef.current && !htmlRef.current.contains(selection.anchorNode)) {
        // 如果选区不在 htmlRef.current 内，清空选中状态
        setSelectedText("");
        setPosition(null);
        setIsVisible(false);
        return;
      }

      // 如果选区在 htmlRef.current 内，继续处理
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();

      _setSelectedText({
        text: selection.toString(),
        range: range,
        clientRect: {
          left: rect.left,
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom,
        },
      });
    };

    document.addEventListener("selectionchange", handleSelection);
    return () =>
      document.removeEventListener("selectionchange", handleSelection);
  }, [htmlRef]);

  // 优化后的滚动处理
  useEffect(() => {
    const container = parentContainerRef.current;
    if (!container) return; // 容器未挂载时跳过

    const handleScroll = () => {
      try {
        updateConnectorPositions();
      } catch (error) {
        console.error("滚动处理出错:", error);
      }
    };

    // 使用 lodash 的 throttle，确保正确导入
    const throttledScroll = throttle(handleScroll, 16); // 约60fps

    // 监听容器的 scroll 事件（而非 window）
    container.addEventListener("scroll", throttledScroll);

    // 清理函数：移除监听器并取消节流任务
    return () => {
      container.removeEventListener("scroll", throttledScroll);
      throttledScroll.cancel(); // 关键：取消未执行的节流回调
    };
  }, [parentContainerRef.current, annotations]); // 依赖容器变化

  // 更新连接线位置（同时更新 clientRect）
  const updateConnectorPositions = () => {
    const panelRect = annotationPanelRef.current?.getBoundingClientRect();
    if (!panelRect) return;

    const scrollY = window.scrollY || window.pageYOffset;
    const scrollX = window.scrollX || window.pageXOffset;

    setAnnotations((prevAnnotations) => {
      return prevAnnotations.map((annotation) => {
        // 获取当前高亮元素（根据批注 ID）
        const highlight = document.querySelector(
          `.hightlight-text-${annotation.id}`
        );
        if (!highlight) return annotation;

        // 重新计算 clientRect（视口位置）
        const currentClientRect = highlight.getBoundingClientRect();

        // 计算连接线路径（使用最新的 clientRect）
        const startX = currentClientRect.right + scrollX;
        const startY =
          currentClientRect.top + currentClientRect.height / 2 + scrollY;
        const endX = panelRect.left + scrollX + annotation.position!.x;
        const endY = panelRect.top + scrollY + annotation.position!.y + 15;

        return {
          ...annotation,
          clientRect: {
            // 更新 clientRect 为最新值
            left: currentClientRect.left,
            top: currentClientRect.top,
            right: currentClientRect.right,
            bottom: currentClientRect.bottom,
            width: currentClientRect.width,
            height: currentClientRect.height,
          },
          linePath: {
            start: { x: startX, y: startY },
            mid: { x: startX, y: startY },
            end: { x: endX, y: endY },
          },
        };
      });
    });
  };

  // 计算连接线路径 - 确保正确处理滚动
  const calculateLinePath = (
    annotation: Annotations,
    scrollY: number,
    scrollX: number,
    panelRect: DOMRect
  ) => {
    // 获取高亮元素（起点：文本末尾中心）
    const highlight = document?.querySelector(
      `hightlight-text-${annotation.id}`
    );

    let startX, startY;

    if (highlight) {
      const cachedRect = highlight.getBoundingClientRect();
      startX = cachedRect.right + scrollX; // 加水平滚动偏移
      startY = cachedRect.top + cachedRect.height / 2 + scrollY; // 加垂直滚动偏移
    } else {
      // fallback：使用保存的位置 + 滚动偏移
      startX = (annotation.clientRect!.right || 0) + scrollX;
      startY =
        (annotation.clientRect!.top || 0) +
        (annotation.clientRect!.height || 0) / 2 +
        scrollY;
    }

    // 计算批注框位置（终点：批注面板左侧）
    const endX = panelRect.left + scrollX + annotation.position!.x;
    const endY = panelRect.top + scrollY + annotation.position!.y + 15;

    // 折线路径：直接从起点到终点（L 命令）
    return {
      start: { x: startX, y: startY },
      mid: { x: startX, y: startY }, // 折线无中间点
      end: { x: endX, y: endY },
    };
  };
  // useEffect(() => {
  //   const handleScroll = debounce(() => {
  //     setScrollPosition({
  //       x: window.scrollX || window.pageXOffset,
  //       y: window.scrollY || window.pageYOffset,
  //     });
  //   }, 0);
  //   // 将 ref.current 缓存到局部变量中
  //   const container = parentContainerRef.current;

  //   // 确保容器存在
  //   if (!container) return;

  //   // 使用缓存的容器添加监听器
  //   container.addEventListener("scroll", handleScroll);

  //   // 清理函数使用缓存的容器
  //   return () => {
  //     container.removeEventListener("scroll", handleScroll);
  //   };
  // }, []);

  const handlerBlur = (animation: Annotations) => {
    String(animation.buckedId)
      .split(",")
      .forEach((i) => {
        // 找到关联的高亮元素
        const highlightElement = document?.getElementsByClassName(
          `hightlight-text-${i}`
        ) as any;

        const highlightElementByPcode = document?.getElementsByClassName(
          `hightlight-text-${i}`
        ) as any;

        for (let index = 0; index < highlightElement.length; index++) {
          if (highlightElement.length) {
            // 修改背景色为半透明黄色
            highlightElement[index].style.backgroundColor =
              "rgba(230, 170, 170, .3)";
          }
        }

        for (let index = 0; index < highlightElementByPcode.length; index++) {
          if (highlightElementByPcode.length) {
            // 修改背景色为半透明黄色
            highlightElementByPcode[index].style.backgroundColor =
              "rgba(230, 170, 170, .3)";
          }
        }
      });
  };

  /**
   * @description 删除批注
   * */

  // 在删除批注的函数中调用重新计算
  const handlerRemove = (id: string) => {
    // 1. 先找到并清除对应的高亮元素

    const highlightElements = document.getElementsByClassName(
      `hightlight-text-${id}`
    );

    // Convert HTMLCollection to array for easier manipulation
    const elementsArray = Array.from(highlightElements);

    // Process all highlight elements
    elementsArray.forEach((highlightElement) => {
      // Save parent and text content
      const parent = highlightElement.parentNode;
      const text = highlightElement.textContent;

      if (parent && text) {
        // Replace with text node
        const textNode = document.createTextNode(text);
        parent.replaceChild(textNode, highlightElement);
      }
    });

    // Normalize all modified parents to merge adjacent text nodes
    elementsArray.forEach((highlightElement) => {
      if (highlightElement.parentNode) {
        highlightElement.parentNode.normalize();
      }
    });

    // 2. 从状态中移除批注
    const newAnnotations = annotations.filter(
      (i: Annotations) => String(i.id) !== id
    );
    setAnnotations(newAnnotations);

    recalculateAnnotations();

    // 4. 如果删除的是当前活动的批注，清除活动状态
    if (activeAnnotation == id) {
      setActiveAnnotation(null);
    }
  };

  /**
   * @description 添加重新计算批注位置的函数
   * */
  const recalculateAnnotations = () => {
    setAnnotations((prevAnnotations) => {
      const updatedAnnotations = [...prevAnnotations];
      const panel = annotationPanelRef.current;
      if (!panel) return updatedAnnotations;

      const panelRect = panel.getBoundingClientRect();
      const scrollY = window.scrollY || document.documentElement.scrollTop;
      const panelTop = panelRect.top + scrollY;

      // 按关联文本位置排序批注
      const sortedAnnotations = [...updatedAnnotations].sort((a, b) => {
        const aHighlight = document.querySelector(`.hightlight-text-${a.id}`);
        const bHighlight = document.querySelector(`.hightlight-text-${b.id}`);

        if (!aHighlight || !bHighlight) return 0;

        const aRect = aHighlight.getBoundingClientRect();
        const bRect = bHighlight.getBoundingClientRect();

        return aRect.top - bRect.top;
      });

      let lastBottom = 0;
      const padding = 20;

      return sortedAnnotations.map((annotation, index) => {
        // 获取高亮元素
        const highlight = document.querySelector(
          `.hightlight-text-${annotation.id}`
        );

        if (!highlight) return annotation;

        const highlightRect = highlight.getBoundingClientRect();
        const startY = highlightRect.top + scrollY;

        // 计算新位置 - 确保不会重叠
        const annotationBoxX = 20;
        let annotationBoxY = startY - panelTop;

        // 第一项特殊处理
        if (index === 0) {
          annotationBoxY = Math.max(annotationBoxY, panelTop + padding);
        } else {
          annotationBoxY = Math.max(annotationBoxY, lastBottom + padding);
        }

        // 更新批注高度
        const annotationHeight = calculateAnnotationHeight(annotation);
        lastBottom = annotationBoxY + annotationHeight;

        // 计算折线路径
        const midX = highlightRect.right + scrollX + 80;
        const endX = panelRect.left + annotationBoxX + scrollX;
        const endY = panelTop + annotationBoxY + 15;

        return {
          ...annotation,
          position: { x: annotationBoxX, y: annotationBoxY },
          linePath: {
            start: { x: highlightRect.right + scrollX, y: startY },
            mid: { x: midX, y: startY },
            end: { x: endX, y: endY },
          },
        };
      });
    });
  };
  /**
   * @description 回复批注
   * */
  const handlerReplyAnnotation = (index: number) => {
    const newAnnotations = [...annotations];
    if (!newAnnotations[index].children) newAnnotations[index].children = [];

    // 计算新增高度（基于实际回复项高度）
    const replyHeight =
      calculateAnnotationHeight({ children: [{}] } as Annotations) -
      calculateAnnotationHeight({} as Annotations);

    // 调整后续批注位置
    newAnnotations.forEach((ann, i) => {
      if (i > index) {
        ann.position = {
          x: ann.position!.x ?? 0,
          y: ann.position!.y + replyHeight,
        };

        // 同时调整连接线终点位置
        if (ann.linePath) {
          ann.linePath = {
            ...ann.linePath,
            end: {
              ...ann.linePath.end,
              y: ann.linePath.end.y + replyHeight,
            },
          };
        }
      }
    });

    const id = Date.now();
    newAnnotations[index].children!.push({
      buckedId: id,
      id,
      actionType: newAnnotations[index].actionType,
    });
    setAnnotations(newAnnotations);
    setInputing(id);

    // 自动滚动到新增回复
    setTimeout(() => {
      const replyElement = document.getElementById(`reply-${id}`);
      inputsRef.current[inputsRef.current.length - 1]?.focus();
    }, 100);
  };

  /**
   * @description 输入框事件
   * */
  const handlerInputChange = (
    value: string,
    parentIndex: number,
    childIndex?: number
  ) => {
    if (childIndex === undefined) {
      annotations[parentIndex].commentContent = value;

      setAnnotations([...annotations]);
    } else {
      const record = annotations[parentIndex];
      record.children![childIndex].commentContent = value;
      annotations[parentIndex] = record;
      setAnnotations([...annotations]);
    }
  };

  /**
   * 根据元素ID和文本位置高亮文本
   * @param elementId 元素ID
   * @param text 要匹配的文本
   * @param startIndex 文本在元素中的起始位置
   * @param annotationId 批注ID（可选）
   */
  const highlightTextByPosition = (
    elementId: string,
    text: string,
    startIndex: number,
    buckedId: string // 改为必填
  ) => {
    const element = document.getElementById(elementId);
    if (!element || !text) return;

    // 1. 清理旧高亮
    const oldHighlights = element.getElementsByClassName(
      `hightlight-text-${buckedId}`
    );
    while (oldHighlights[0]) {
      const parent = oldHighlights[0].parentNode;
      if (parent) {
        parent.replaceChild(
          document.createTextNode(oldHighlights[0].textContent || ""),
          oldHighlights[0]
        );
        parent.normalize();
      }
    }

    // 2. 定位目标文本
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    );

    let charCount = 0;
    let targetNode: Text | null = null;
    let targetOffset = 0;

    while (walker.nextNode()) {
      const node = walker.currentNode as Text;
      const nodeText = node.nodeValue || "";
      const nodeLength = nodeText.length;

      if (charCount + nodeLength > startIndex) {
        targetNode = node;
        targetOffset = startIndex - charCount;
        break;
      }
      charCount += nodeLength;
    }

    if (!targetNode) return;

    // 3. 验证文本匹配
    const endOffset = targetOffset + text.length;
    const actualText =
      targetNode.nodeValue?.slice(targetOffset, endOffset) || "";

    if (actualText !== text) {
      console.warn(`Text mismatch at position ${startIndex}: 
      Expected "${text}", found "${actualText}"`);
      return;
    }

    // 4. 创建高亮
    const range = document.createRange();
    range.setStart(targetNode, targetOffset);
    range.setEnd(targetNode, endOffset);

    const highlight = document.createElement("span");
    highlight.className = `hightlight-text-${buckedId}`;
    highlight.style.backgroundColor = "rgba(236, 145, 145, .3)";
    highlight.dataset.annotationId = buckedId;

    try {
      range.surroundContents(highlight);
    } catch (e) {
      console.log("Failed to highlight range:", e);
      // 备选方案：使用文本替换
      const fragment = range.extractContents();
      highlight.appendChild(fragment);
      range.insertNode(highlight);
    }
  };

  /**
   * @description 批注面板点击
   * */
  const handlerClickAnnotationPanel = (annotation: Annotations) => {
    // 处理可能的多段落高亮
    String(annotation.buckedId)
      .split(",")
      .forEach((pid) => {
        const highlightElements = document.getElementsByClassName(
          `hightlight-text-${pid}`
        ) as HTMLCollectionOf<HTMLElement>;

        Array.from(highlightElements).forEach((el) => {
          el.style.backgroundColor = "rgba(236, 145, 145, 1)";
        });
      });
  };
  /**
   * @description 删除回复
   * */
  const handlerRemoveChild = (parentIndex: number, childIndex: number) => {
    annotations[parentIndex].children?.splice(childIndex, 1);
    setAnnotations([...annotations]);
    recalculateAnnotations();
  };

  // 当父组件传入的批注变化时更新本地状态
  useEffect(() => {
    let data = initialAnnotations;
    data = transformData(data);
    setAnnotations([...data]);
  }, [initialAnnotations]);

  /**
   * 修改transformData函数
   */
  const transformData = (data: InitialAnnotations[]) => {
    return data.map((item: any) => {
      let { selectedPosition, ...rest } = item;

      selectedPosition = JSON.parse(selectedPosition);
      // 转换数据格式
      const transformedItem = {
        ...item,
        ...rest,
        positionId: selectedPosition.id,
        position: selectedPosition.position,
        linePath: selectedPosition.linePath,
        clientRect: selectedPosition.clientRect,
        children: selectedPosition.children,
        actionType: item.type,
        paragraphId: item.pid,
        buckedId: selectedPosition.buckedId,
      };

      
      if (
        item.pid &&
        item.selectedContent &&
        item.selectedStartIndex !== undefined
      ) {
        // 处理可能的多段落情况
        const paragraphIds = selectedPosition.paragraphId?.split(",");
        
        (paragraphIds || []).forEach((pid: string) => {
          highlightTextByPosition(
            item.pid,
            item.selectedContent,
            item.selectedStartIndex,
            selectedPosition.buckedId // 使用批注ID作为高亮class
          );
        });
      }

      return transformedItem;
    });
  };

  return {
    selectedText,
    position,
    menuRef,
    isVisible,
    highlightSelection,
    annotations,
    // handleAnnotationClick,
    activeAnnotation,
    getCurrentLinePath,
    inputsRef,
    annotationPanelRef,
    handlerRemove,
    handlerReplyAnnotation,
    inputing,
    setInputing,
    handlerInputChange,
    handlerBlur,
    handlerClickAnnotationPanel,
    handlerRemoveChild,
  };
};
