.wps-annotations-list {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 设置元素间距 */
  position: relative;
  padding: 0 12px;
  padding-bottom: 20px;
  background-color: #f4f5f7;
  height: 100%;
}

.comment-external-group-wrapper {
  width: calc(100% - 22px);
  margin-bottom: 10px;
  position: absolute;
  -webkit-transition: top 0.1s ease;
  transition: top 0.1s ease;
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 8px rgba(0, 0, 0, 0.04);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.04);
  background-color: white;
  padding-bottom: 12px;
  transition: all 0.3px;

  border-left: 5px solid var(--primary-color);

  .text {
    padding: 12px 16px 0 16px;
    color: #999999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.comment-external-group {
  opacity: 0.7;
  width: 100%;
}

.nick-nameplate-section {
  display: flex;
  justify-content: space-between;
}

.avatar-nickname {
  font-weight: 500;
}

.comment-external-group-wrapper:hover {
  opacity: 1;
}

.comment-pc,
svg {
  cursor: pointer;
}
.comment-pc div {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  max-height: 96px; /* 3 行 × 32px 行高 */
  font-size: 14px;
}

.hasInput {
  line-height: 16px;
  padding: 6px 0px;
  font-size: 14px;
}

.empty {
  padding: 16px;
}

.melo-comment-content-wrapper {
  padding: 16px 16px 0;
  font-size: 14px;
}

.content-header {
  display: flex;
  display: -ms-flexbox;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.profile {
  display: -webkit-box;
  display: flex;
  display: -ms-flexbox;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  -ms-flex-positive: 1;
  -ms-flex-negative: 1;
  -ms-flex-preferred-size: auto;
  -webkit-box-flex: 1;
  flex: 1 1 auto;
}
.comment-external-item-toolbar-container {
  position: relative;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-width: 65px;
}

.publish-time {
  vertical-align: middle;
  font-size: 12px;
  color: #777;
  height: 16px;
  white-space: nowrap;
}

/* 在样式文件中添加 */
.comment-external-group-wrapper {
  transition: all 0.3s ease-out;
  will-change: transform;
}

.wps-connector path {
  transition: all 0.3s ease-out;
}

.comment-external-group {
  transition: height 0.3s ease-out;
}

.reply {
  margin-left: 24px;
  border-left: 1px solid #f0f0f0;
  padding-left: 12px;
  margin-top: 12px;
}

.empty-reply {
  padding: 7px;
}

/* 添加折叠控制按钮样式 */
.reply-toggle {
  padding: 4px 8px;
  margin: 4px 0;
  color: #1677ff;
  cursor: pointer;
  font-size: 12px;
  border-radius: 4px;
  display: inline-block;
}

.reply-toggle:hover {
  background-color: #f0f0f0;
}

/* 调整回复区域样式 */
.reply {
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 折叠时的样式 */
.reply.collapsed {
  max-height: 0;
  opacity: 0;
  padding: 0;
  margin: 0;
}
.type-text {
  font-size: 12px;
  color: rgb(107 114 128);
}

.annotation-footer {
  display: flex;
  flex-direction: column-reverse;
  color: rgb(255, 42, 0);
  font-size: 12px;
  text-align: right;
}
.annotation-footer span {
  cursor: pointer;
}
