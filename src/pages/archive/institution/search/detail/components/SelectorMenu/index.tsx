/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-19 16:07:19
 * @Description:
 * @LastEditTime: 2025-07-30 10:42:50
 * @LastEditors: wangjiantao
 */

import { useRichTextSelection } from "./hooks/useRichTextSelection";
import { FC, Fragment, ReactNode, RefObject } from "react";
import "./index.scss";
import { Avatar, Input, Popconfirm, Space } from "antd";
import { Annotations, InitialAnnotations } from "../../types";
import { QuestionCircleOutlined } from "@ant-design/icons";

interface IProps {
  parentContainerRef: RefObject<HTMLDivElement | null>;
  htmlRef: RefObject<HTMLDivElement | null>;
  onChange?: (annotations: Annotations) => void;
  onRemoveCallback?: (
    annotationId: string | number,
    annotationReplyId?: string | number
  ) => void;
  initialAnnotations: InitialAnnotations[];
  actionTypes: {
    text: string;
    icon?: React.ReactNode;
  }[];
}
const SelectorMenu: FC<IProps> = ({
  parentContainerRef,
  htmlRef,
  onChange,
  initialAnnotations,
  onRemoveCallback,
  actionTypes,
}) => {
  const {
    position,
    menuRef,
    isVisible,
    highlightSelection,
    annotationPanelRef,
    annotations,
    inputing,
    setInputing,
    handlerInputChange,
    inputsRef,
    handlerRemove,
    handlerReplyAnnotation,
    handlerBlur,
    handlerClickAnnotationPanel,
    getCurrentLinePath,
    handlerRemoveChild,
  } = useRichTextSelection(parentContainerRef, htmlRef, initialAnnotations);

  const renderCommonType = (type: number) => {
    return actionTypes[type - 1]?.text;
  };

  const renderAnnotation = (
    annotation: Annotations,
    index: number
  ): ReactNode => {
    const linePath = getCurrentLinePath(annotation, index);
    return (
      <Fragment key={annotation.id}>
        {/* <svg
          className="annotation-connector"
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
            zIndex: 0,
          }}
        >
          <path
            d={`M${linePath.start.x},${linePath.start.y} L${linePath.end.x},${linePath.end.y}`}
            stroke="rgba(236, 145, 145, 0.5)"
            strokeWidth="1"
            fill="none"
          />
        </svg> */}

        {/* 箭头标记定义 */}
        {/* <svg style={{ display: "none" }}>
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="rgba(236, 145, 145, 0.5)"
              />
            </marker>
          </defs>
        </svg> */}
        <div
          onMouseLeave={() => {
            handlerBlur(annotation);
          }}
          onMouseEnter={() => handlerClickAnnotationPanel(annotation)}
          className="comment-external-group-wrapper"
          style={{
            top: annotation.position ? `${annotation.position.y}px` : "",
          }}
        >
          <div className="text">{annotation.text || annotation.selectedContent}</div>
          <div id="comment-group-wrapper" className="comment-external-group">
            <div className="">
              <div
                id="comment-id-c_3cwsa0"
                className="melo-comment-content-wrapper"
              >
                {/* <span>{ActionType[annotation.actionType]}</span> */}
                <div className="melo-comment-content-box">
                  <div className="content-header">
                    <div className="profile">
                      <div id="comment-profile-c_3cwsa0">
                        <div className="avatar-nick-nameplate-view direction-row">
                          <div className="nick-nameplate-section">
                            <Space className="nick-nameplate-view">
                              <Avatar
                                size="small"
                                style={{ backgroundColor: "#f56a00" }}
                              >
                                {annotation.username &&
                                  annotation.username?.charAt(
                                    annotation.username.length - 1
                                  )}
                              </Avatar>
                              <div className="avatar-nickname">
                                {annotation.username ||
                                  localStorage.getItem("usernickname")}
                              </div>
                              <div className="dui-trigger dui-tooltip dui-tooltip-wrapper"></div>
                            </Space>
                            {annotation.createTime
                              ? new Date(annotation.createTime).toLocaleString()
                              : new Date().toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {inputing == annotation.id ? (
                    <Input.TextArea
                      style={{ marginTop: 6 }}
                      autoSize={{ minRows: 1, maxRows: 3 }}
                      ref={(el) => (inputsRef.current[index] = el!)}
                      value={annotation.commentContent}
                      onChange={(e) =>
                        handlerInputChange(e.target.value, index)
                      }
                      onBlur={() => onChange && onChange(annotation)}
                    />
                  ) : (
                    <div onClick={() => setInputing(annotation.id!)}>
                      <div
                        className={`comment-text-box comment-pc ${
                          annotation.commentContent ? "hasInput" : "empty"
                        }`}
                      >
                        <div>{annotation.commentContent}</div>
                      </div>
                    </div>
                  )}
                  <Space align="center" size={4} className="type-text">
                    <QuestionCircleOutlined />
                    <span>{renderCommonType(annotation.actionType)}</span>
                  </Space>
                </div>
                <div className="annotation-footer">
                  <Popconfirm
                    title="提示"
                    description="你确定要删除该批注吗?"
                    onConfirm={() => {
                      handlerRemove(String(annotation.buckedId!));
                      onRemoveCallback && onRemoveCallback(annotation.id!);
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <span>删除</span>
                  </Popconfirm>
                </div>
              </div>
            </div>
            {/* <div className="reply">
              {annotation.children?.map((i: Annotations, key: number) => (
                <div
                  key={key}
                  id="comment-external-group-anchor-id-l3btee"
                  style={{
                    transition: "top 0.1s ease",
                    opacity: 1,
                    backgroundColor: "white",
                    width: "calc(100% - 24px)",
                    paddingTop: 4,
                  }}
                >
                  <div
                    id="comment-group-wrapper"
                    className="comment-external-group"
                    style={{ opacity: 1 }}
                  >
                    <div className="">
                      <div>
                        <div className="melo-comment-content-box">
                          <div className="content-header">
                            <div className="profile">
                              <div id="comment-profile-c_3cwsa0">
                                <div className="avatar-nick-nameplate-view direction-row">
                                  <div className="nick-nameplate-section">
                                    <div className="nick-nameplate-view">
                                      <div className="avatar-nickname">
                                        {annotation.username ||
                                          localStorage.getItem("usernickname")}
                                      </div>
                                      <div className="dui-trigger dui-tooltip dui-tooltip-wrapper"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <Space>
                              <div className="comment-external-item-toolbar-container">
                                <span className="comment-external-item-toolbar-padding"></span>
                                <div className="publish-time">06-23 16:00</div>
                              </div>
                              <Dropdown
                                menu={{
                                  items: [
                                    {
                                      key: "2",
                                      label: (
                                        <Space
                                          size={0}
                                          onClick={() => {
                                            handlerRemoveChild(index, key);
                                            onRemoveCallback &&
                                              onRemoveCallback(
                                                annotation.id!,
                                                i.id
                                              );
                                          }}
                                        >
                                          <DeleteOutlined
                                            style={{ color: "#ff4d4f" }}
                                          />
                                          <Button
                                            type="link"
                                            danger
                                            style={{ padding: "0 5px" }}
                                          >
                                            删除
                                          </Button>
                                        </Space>
                                      ),
                                    },
                                  ],
                                }}
                                placement="bottom"
                                arrow={{ pointAtCenter: true }}
                                trigger={["click"]}
                              >
                                <EllipsisOutlined />
                              </Dropdown>
                            </Space>
                          </div>
                          {Boolean(inputing === i.id)}
                          {inputing === i.id ? (
                            <Input.TextArea
                              autoSize={{ minRows: 1, maxRows: 3 }}
                              ref={(el) => {
                                inputsRef.current[index] = el!;
                              }}
                              value={i.common}
                              onChange={(e) => {
                                handlerInputChange(e.target.value, index, key);
                              }}
                              onPressEnter={() => {
                                onRemoveCallback &&
                                  onRemoveCallback(annotation.id!);
                              }}
                              onBlur={() => handlerBlur(annotation)}
                            />
                          ) : (
                            <div onClick={() => setInputing(i.id!)}>
                              <div
                                className={`comment-text-box comment-pc ${
                                  annotation.common ? "hasInput" : "empty-reply"
                                }`}
                              >
                                <div>{i.common}</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div> */}
          </div>
        </div>
      </Fragment>
    );
  };

  return (
    <>
      {position && isVisible ? (
        <div
          ref={menuRef}
          style={{
            position: "fixed",
            left: `${position.x}px`,
            top: `${position.y}px`,
            transform: "translateX(-50%)",
            backgroundColor: "white",
            borderRadius: "4px",
            padding: "2px 4px",
            display: "flex",
            gap: "6px",
            zIndex: 9999,
            boxShadow:
              "0 4px 20px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.04)",
            border: "1px solid rgba(0, 0, 0, 0.05)",
            transition: "all 0.2s cubic-bezier(0.16, 1, 0.3, 1)",
            animation: `${
              isVisible ? "fadeInUp" : "fadeOutDown"
            } 0.2s forwards`,
          }}
        >
          <Space>
            {actionTypes.map((action, index) => (
              <button
                key={action.text}
                onClick={() => highlightSelection(index + 1)}
                style={{
                  background: "none",
                  border: "none",
                  padding: "6px 10px",
                  borderRadius: "4px",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                  fontSize: "12px",
                  transition: "background 0.2s",
                }}
              >
                {action.icon && action.icon}
                {action.text}
              </button>
            ))}
          </Space>
        </div>
      ) : null}

      <div ref={annotationPanelRef}>
        <div
          style={{
            position: "relative",
            width: 370,
            background: "#F4F5F7",
          }}
        >
          <div
            className="wps-annotations-list"
            style={{ height: parentContainerRef.current?.scrollHeight }}
          >
            {annotations.map((i: Annotations, index: number) =>
              renderAnnotation(i, index)
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default SelectorMenu;
