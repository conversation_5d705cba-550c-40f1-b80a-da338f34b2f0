/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-18 09:15:16
 * @Description: 文章详情
 * @LastEditTime: 2025-07-31 14:40:44
 * @LastEditors: wangjiantao
 */
import {
  Card,
  Col,
  Divider,
  FloatButton,
  Row,
  Space,
  Switch,
  Tree,
} from "antd";
import {
  AuditOutlined,
  CloudDownloadOutlined,
  DownOutlined,
  FormOutlined,
  PrinterOutlined,
  QuestionCircleOutlined,
  StarOutlined,
} from "@ant-design/icons";
import { useInstitutionPresenter } from "./hook/useInstitutionPresenter";
import { useLocation } from "react-router-dom";
import React, { useEffect } from "react";
import dayjs from "dayjs";
import { Tooltip } from "antd";
import SelectorMenu from "./components/SelectorMenu";
import "./index.scss";
import TextSearch from "@/components/searchTextInPage";
import { useReactToPrint } from "react-to-print";

const InstitutionDetail = () => {
  // 通过hooks获取state状态数据与presenter对象
  const {
    state,
    handlerConnectArticle,
    handler<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    presenter,
    htmlRef,
    containerRef,
  } = useInstitutionPresenter();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const id = searchParams.get("id");
  const type = searchParams.get("type");
  useEffect(() => {
    if (!id || !type) return;
    presenter?.loadInstitutionById(Number(id), type);
    presenter.setId(id);
  }, [id, type]);

  useEffect(() => {
    presenter?.loadCommentList({ institutionId: Number(id) });
  }, []);

  const reactToPrintFn = useReactToPrint({ contentRef: htmlRef! });

  if (!state.isPure) {
    return (
      <div className="detail-container">
        <Row className="header" align="middle">
          <Col span={12} />
          <Col span={12} style={{ textAlign: "right" }}>
            <Space size={24} align="center">
              <TextSearch containerRef={htmlRef} />
              <Space size={2}>
                <span style={{ fontSize: 12, color: "var(--primary-color)" }}>
                  纯净版
                </span>
                <Switch
                  onChange={handlerPageStatusChange}
                  value={state.isPure}
                  size="small"
                />
              </Space>
            </Space>
          </Col>
        </Row>
        <Row gutter={4}>
          <Col span={4}>
            <div className="card tree">
              <div className="header">
                <span>目录</span>
              </div>
              <div
                style={{
                  flex: 1,
                  overflow: "auto",
                  padding: "6px 12px",
                }}
              >
                <Tree
                  showLine
                  switcherIcon={<DownOutlined />}
                  defaultExpandAll={true}
                  treeData={state.institutionNav}
                  titleRender={(node) => {
                    const title = node.title || "";

                    if (typeof title !== "string") {
                      return String(title);
                    }
                    return (
                      <span
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          presenter.jumpToSection(
                            containerRef,
                            node.key as string
                          )
                        }
                      >
                        <Tooltip title={title}>
                          {title.slice(0, 12) + "..."}
                        </Tooltip>
                      </span>
                    );
                  }}
                />
              </div>
            </div>
          </Col>
          <Col span={20}>
            <Card>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "6px 12px",
                }}
              >
                <Space direction="vertical">
                  <Space align="center">
                    <span
                      style={{
                        fontWeight: "bold",
                        fontSize: 20,
                      }}
                    >
                      {state.institutionDetail?.title}
                    </span>
                    <span
                      style={{
                        cursor: "pointer",
                        display: "inlineBlock",
                        padding: "0 18px",
                        color:
                          state.institutionDetail?.status === "已废止"
                            ? "red"
                            : "var(--primary-color)",
                        border:
                          state.institutionDetail?.status === "已废止"
                            ? "3px solid red"
                            : "3px solid var(--primary-color)",
                        borderRadius: "12px",
                        fontWeight: 500,
                        marginTop: 5,
                      }}
                    >
                      {state.institutionDetail?.status}
                    </span>
                  </Space>
                  <span style={{ fontSize: 12 }}>
                    {" "}
                    {state.institutionDetail?.tagList.map(
                      (tag: string, index: number) => (
                        <React.Fragment key={tag}>
                          <span>{tag}</span>
                          {index <
                            state.institutionDetail?.tagList.length - 1 && (
                            <span>&gt;</span>
                          )}
                        </React.Fragment>
                      )
                    )}
                  </span>
                  <Space>
                    <span style={{ fontSize: 12 }}>
                      {state.institutionDetail?.pushTime ? (
                        <span style={{ fontSize: 12 }}>
                          {dayjs(
                            Number(state.institutionDetail?.pushTime)
                          ).format("YYYY-MM-DD")}
                          发布
                        </span>
                      ) : null}
                    </span>
                    <Divider type="vertical" />
                    <span style={{ fontSize: 12 }}>
                      {state.institutionDetail?.implTime ? (
                        <span style={{ fontSize: 12 }}>
                          {dayjs(
                            Number(state.institutionDetail?.implTime)
                          ).format("YYYY-MM-DD")}
                          实施
                        </span>
                      ) : null}
                    </span>
                  </Space>
                </Space>
              </div>
            </Card>
            <Card id="content" className="card content">
              {/* 修正逗号运算符问题，合并多个类名 */}

              <div
                ref={containerRef}
                style={{
                  display: "flex",
                  width: "100%",
                  overflow: "auto",
                  height: state.isPure
                    ? "calc(100vh - 215px)"
                    : "calc(100vh - 180px)",
                }}
              >
                <div>
                  <div
                    id="html-container"
                    ref={htmlRef}
                    style={{
                      transform: `scale(1.0)`,
                      width: "790px",
                      margin: "0 auto",
                    }}
                    dangerouslySetInnerHTML={{
                      __html: state.institutionDetail?.htmlContent || "",
                    }}
                  ></div>

                  <div className="card" style={{ width: "100%" }}>
                    <div
                      style={{
                        padding: "10px 20px",
                        backgroundColor: "var(--primary-color)",
                      }}
                    >
                      <span
                        style={{
                          fontWeight: "bold",
                          fontSize: 16,
                          color: "white",
                        }}
                      >
                        引用文档
                      </span>
                    </div>
                    <div
                      style={{
                        padding: "0 20px 12px 20px",
                        display: "flex",
                        flexWrap: "wrap",
                      }}
                    >
                      {[
                        "合同审查",
                        "案件审查",
                        "普法宣传",
                        "案件纠纷",
                        "合规义务",
                        "制度提问",
                        "制度纠错",
                        "制度建议",
                        "制度监督",
                        "制度评估",
                      ].map((i) => (
                        <div
                          style={{
                            width: "11.11%",
                            color: "var(--primary-color)",
                            marginTop: 12,
                          }}
                          key={i}
                        >
                          {i}（5）
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <SelectorMenu
                  initialAnnotations={state.annotations}
                  actionTypes={[
                    {
                      text: "提问",
                      icon: <QuestionCircleOutlined />,
                    },
                    {
                      text: "纠错",
                      icon: <FormOutlined />,
                    },
                    {
                      text: "建议",
                      icon: <StarOutlined />,
                    },
                    {
                      text: "监督",
                      icon: <AuditOutlined />,
                    },
                  ]}
                  htmlRef={htmlRef}
                  parentContainerRef={containerRef}
                  onChange={(value) => presenter?.addOrUpdateComment(value)}
                  onRemoveCallback={(annotationId) =>
                    presenter.handlerRemoveQuestion(Number(annotationId))
                  }
                />
              </div>
            </Card>
          </Col>
          <FloatButton.Group shape="circle">
            <FloatButton
              description={<span style={{ color: "#55acee" }}>提问</span>}
              badge={{
                count: state.annotations?.filter((i: any) => i.type === 1)
                  .length,
              }}
              icon={<QuestionCircleOutlined style={{ color: "#55acee" }} />}
            />
            <FloatButton
              description={<span style={{ color: "#cd201f" }}>纠错</span>}
              badge={{
                count: state.annotations.filter((i: any) => i.type === 2)
                  .length,
              }}
              icon={<FormOutlined style={{ color: "#cd201f" }} />}
            />
            <FloatButton
              description={<span style={{ color: "#3b5999" }}>建议</span>}
              badge={{
                count: state.annotations.filter((i: any) => i.type === 3)
                  .length,
              }}
              icon={<StarOutlined style={{ color: "#3b5999" }} />}
            />
            <FloatButton
              description={<span style={{ color: "#87d068" }}>监督</span>}
              badge={{
                count: state.annotations.filter((i: any) => i.type === 4)
                  .length,
              }}
              icon={<AuditOutlined style={{ color: "#87d068" }} />}
            />
          </FloatButton.Group>
        </Row>
      </div>
    );
  }

  if (state.isPure) {
    return (
      <div className="detail-container">
        <Row className="header" align="middle">
          <Col span={8} />
          <Col span={9}>
            <TextSearch containerRef={htmlRef} />
          </Col>
          <Col span={7} style={{ textAlign: "right" }}>
            <Space size={24} align="center">
              <Space size={2}>
                <span style={{ fontSize: 12, color: "var(--primary-color)" }}>
                  纯净版
                </span>
                <Switch
                  onChange={handlerPageStatusChange}
                  value={state.isPure}
                  size="small"
                />
              </Space>
              <CloudDownloadOutlined
                style={{ color: "var(--primary-color)", fontSize: 16 }}
                onClick={presenter.downloadFile}
              />
              <StarOutlined
                style={{ color: "var(--primary-color)", fontSize: 16 }}
                onClick={handlerConnectArticle}
              />
              <PrinterOutlined
                style={{ color: "var(--primary-color)", fontSize: 16 }}
                onClick={reactToPrintFn}
              />
              <span style={{ color: "var(--primary-color)" }}>更多</span>
            </Space>
          </Col>
        </Row>
        <Col span={24}>
          <Card>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: "6px 12px",
              }}
            >
              <Space direction="vertical">
                <Space align="center">
                  <span
                    style={{
                      fontWeight: "bold",
                      fontSize: 20,
                    }}
                  >
                    {state.institutionDetail?.title}
                  </span>
                  <span
                    style={{
                      cursor: "pointer",
                      display: "inlineBlock",
                      padding: "0 18px",
                      color:
                        state.institutionDetail?.status === "已废止"
                          ? "red"
                          : "var(--primary-color)",
                      border:
                        state.institutionDetail?.status === "已废止"
                          ? "3px solid red"
                          : "3px solid var(--primary-color)",
                      borderRadius: "12px",
                      fontWeight: 500,
                      marginTop: 5,
                    }}
                  >
                    {state.institutionDetail?.status}
                  </span>
                </Space>
                <span style={{ fontSize: 12 }}>
                  {" "}
                  {state.institutionDetail?.tagList.map(
                    (tag: string, index: number) => (
                      <React.Fragment key={tag}>
                        <span>{tag}</span>
                        {index <
                          state.institutionDetail?.tagList.length - 1 && (
                          <span>&gt;</span>
                        )}
                      </React.Fragment>
                    )
                  )}
                </span>
                <Space>
                  <span style={{ fontSize: 12 }}>
                    {state.institutionDetail?.pushTime ? (
                      <span style={{ fontSize: 12 }}>
                        {dayjs(
                          Number(state.institutionDetail?.pushTime)
                        ).format("YYYY-MM-DD")}
                        发布
                      </span>
                    ) : null}
                  </span>
                  <Divider type="vertical" />
                  <span style={{ fontSize: 12 }}>
                    {state.institutionDetail?.implTime ? (
                      <span style={{ fontSize: 12 }}>
                        {dayjs(
                          Number(state.institutionDetail?.implTime)
                        ).format("YYYY-MM-DD")}
                        实施
                      </span>
                    ) : null}
                  </span>
                </Space>
              </Space>
            </div>
          </Card>
          <Card
            className="card content"
            style={{
              width: 900,
              margin: "0 auto",
            }}
          >
            {/* 修正逗号运算符问题，合并多个类名 */}

            <div
              ref={containerRef}
              style={{
                display: "flex",
                width: "100%",
                overflow: "auto",
                height: state.isPure
                  ? "calc(100vh - 215px)"
                  : "calc(100vh - 180px)",
              }}
            >
              <div>
                <div
                  ref={htmlRef}
                  style={{
                    transform: `scale(1.0)`,
                    width: "790px",
                    margin: "0 auto",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: state.institutionDetail?.htmlContent || "",
                  }}
                ></div>
              </div>
            </div>
          </Card>
        </Col>
      </div>
    );
  }
};

export default InstitutionDetail;
