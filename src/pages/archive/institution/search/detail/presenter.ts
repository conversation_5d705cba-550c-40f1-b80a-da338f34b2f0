/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-18 13:47:40
 * @Description:
 * @LastEditTime: 2025-07-26 11:12:28
 * @LastEditors: wangjiantao
 */
import { downloadArchiveFile, downloadOssFile } from "@/utils/download";
import {
  InstitutionApi,
  InstitutionState,
  Dispatch,
  Annotations,
} from "./types";
import { message } from "antd";
import { RefObject } from "react";

/**
 *  InstitutionPresenter MVVM对象中ViewModel对象
 *  Presenter: 将UI控制对象交给View，Presenter 只做状态和业务逻辑的处理。设计到UI的界面数据刷新也通过数据状态来控制
 */
export class InstitutionPresenter {
  // 更新数据状态的dispatch函数
  private readonly dispatch: Dispatch<InstitutionState>;
  // 传入的Model对象
  private readonly api: InstitutionApi;
  // 当前的数据状态，由于InstitutionPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
  private state: InstitutionState;

  // 通过构造函数传入数据状态和Model对象
  public constructor(
    state: InstitutionState,
    dispatch: Dispatch<InstitutionState>,
    api: InstitutionApi
  ) {
    this.state = state;
    this.dispatch = dispatch;
    this.api = api;
  }

  // 更新状态数据
  public syncState = (newState: InstitutionState) => {
    this.state = newState;
  };

  /**
   * @description 导出文章
   * @param id 文章id
   */
  public exportInstitution = async (id: number) => {
    const { code } = await this.api.exportInstitution(id);
    if (code !== 200) return;
  };
  /**
   * @description 收藏文章
   * @param id 文章id
   */
  public connectInstitution = async (id: number) => {
    const { code } = await this.api.connectInstitution(id);
    if (code !== 200) return;
  };

  /**
   * @description 获取文章
   * @param id 文章id
   * @param type
   */
  public loadInstitutionById = async (id: number, type: string) => {
    const params = {
      id: id,
      type: type,
    };

    const res = await this.api.loadInstitution(params);

    if (!res.data) return;
    this.dispatch((prevState) => {
      return {
        ...prevState,
        institutionDetail: res.data,
        institutionNav: res.data?.navList || [],
      };
    });
  };

  /**
   * @description 页内搜索
   * @param value
   * */
  public searchInPage = async (value: string) => {
    this.api.searchInPage(value);
  };

  /**
   * @description 页码
   * */
  handlerPagination = (current: number) => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        currentPage: current,
      };
    });
    setTimeout(() => {
      console.log(this.state.currentPage);
    }, 0);
  };

  /**
   * @description 修改聚焦命中状态
   * */
  changeFocusStatus = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        isFocus: !prevState.isFocus,
      };
    });
  };

  pageStatusChange = () => {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        isPure: !prevState.isPure,
      };
    });
  };
  /**
   * @description 收藏文章
   * @param id 文章id
   */
  public connectArticle = async (id: number) => {
    const { code } = await this.api.connectArticle(id);
    if (code !== 200) return;
  };

  public jumpToSection = (containerRef: RefObject<HTMLElement>, targetId: string) => {
    // 获取真正的滚动容器（使用 ref 引用的 div）
    const container = containerRef.current;
    const section = document.getElementById(targetId);

    if (container && section) {
      // 计算相对位置
      const containerRect = container.getBoundingClientRect();
      const sectionRect = section.getBoundingClientRect();

      // 计算需要滚动的距离（考虑容器当前的滚动位置）
      const scrollPosition =
        sectionRect.top - containerRect.top + container.scrollTop;

      // 平滑滚动
      container.scrollTo({
        top: scrollPosition,
        behavior: "smooth",
      });
    }
  };
  public addOrUpdateComment = async (body: Annotations) => {
    const _body = {
      pid: body.paragraphId,
      selectedContent: body.text,
      commentContent: body.commentContent,
      selectedStartIndex: body.startIndex,
      selectedPosition: JSON.stringify(body),
      institutionId: this.state.institutionDetail?.id,
      id: body.id,
      type: body.actionType,
    };
    if (typeof _body.id === "string") delete _body.id;

    await this.api.addOrUpdateQuestion(_body);

    this.loadCommentList({ institutionId: Number(this.state.id) });
    this.dispatch((prevState) => {
      return {
        ...prevState,
        institutionDetail: {
          ...prevState.institutionDetail,
          questionCount: this.state.institutionDetail?.questionCount + 1,
        },
      };
    });
  };

  public async loadCommentList(params: { institutionId: number }) {
    try {
      const questionRes = await this.api.questionList(params);

      // 合并并映射所有注释类型到 Annotations 接口
      const annotations = [
        ...(questionRes.data?.commentList || []).map((item: any) => ({
          paragraphId: item.pId,
          text: item.selectedContent,
          commentContent: item.content,
          startIndex: item.selectedStartIndex,
          ...item, // 可选：保留原始数据
        })),
      ];

      this.dispatch((prevState) => ({
        ...prevState,
        annotations: annotations,
      }));
    } catch (error) {
      console.error("Failed to load and map annotation lists:", error);
    }
  }

  /**
   * @description 下载源文件
   * */
  public downloadFile = () => {
    downloadArchiveFile(this.state.institutionDetail.originFileKey);
  };

  public setId = (id: string) => {
    this.dispatch((prev) => ({
      ...prev,
      id: Number(id),
    }));
  };

  /**
   * @description 删除批注
   * */
  public async handlerRemoveQuestion(id: number) {
    const { success } = await this.api.removeQuestion(id);
    if (!success) return;
    this.loadCommentList({ institutionId: Number(this.state.id) });
  }
}
