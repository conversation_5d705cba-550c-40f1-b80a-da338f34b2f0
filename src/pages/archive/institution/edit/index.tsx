import React, {useCallback, memo} from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import {useInstitutionPresenter} from "@/pages/archive/institution/edit/hooks/useInstitutionalPresenter";
import {Button, Input, Message, Modal, Tabs} from "@itm/components-pc";
import {CloseOutlined} from "@ant-design/icons";
import TagSelector from "@/components/archive/tagSelector";
import {Drawer} from "antd";

const {TabPane} = Tabs;

interface FileTableProps {
    tableType: string;
}

const InstitutionManagePage: React.FC = () => {
    return (
        <PageContainer>
            <Tabs defaultActiveKey="inner" style={{marginTop: 16}}>
                <TabPane tab="行业内" key="inner">
                    <InstitutionTable tableType={"inner"}/>
                </TabPane>
                <TabPane tab="行业外" key="outer">
                    <InstitutionTable tableType={"outer"}/>
                </TabPane>
            </Tabs>
        </PageContainer>
    );
};
const InstitutionTable: React.FC<FileTableProps> = memo(({tableType}) => {

        const actionRef = React.useRef<ActionType>();
        const {state, presenter} = useInstitutionPresenter();


        const handleSearch = useCallback(() => {
            presenter?.refreshTable();
            actionRef.current?.reload();
        }, []);

        const handleReset = useCallback(() => {
            presenter?.resetSearch()
            actionRef.current?.reload();
        }, []);


        const columns = [
            {title: "档案id", dataIndex: "id", hidden: true},
            {
                title: "制度名称",
                dataIndex: "title",
                render: (text: string) => (
                    <span title={text} style={{cursor: "pointer"}}>
                        {text?.length > 50 ? `${text.slice(0, 50)}...` : text}
                    </span>
                ),
            },
            {
                title: "操作",
                valueType: "option",
                render: (text: any, record: any) => {
                    const actions = [];
                    if (tableType === 'inner') {
                        actions.push(
                            <a key="tag" onClick={() => {
                                presenter?.setTagVisible(true);
                                presenter?.setInstitutionId(record.id);
                            }}
                            >
                                设置标签
                            </a>
                        );
                    }
                    actions.push(
                        <a key="编辑条款" onClick={() => {
                            presenter?.loadInstitutionalContent(record.id, tableType);
                            presenter?.setInstitutionId(record.id);
                            presenter?.setContentVisible(true);
                        }}
                        >
                            编辑条款
                        </a>
                    );

                    return actions;
                },
            },
        ] as any[];


        return (
            <>

                <ProTable
                    key={state.tableVersion}
                    actionRef={actionRef}
                    headerTitle={<h3 style={{margin: 0, fontWeight: "bold"}}>文件列表</h3>}
                    toolBarRender={() => [
                        <Input
                            key="title"
                            placeholder="请输入制度名称"
                            value={state.searchTitleName}
                            onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                            style={{width: 200, marginRight: 8}}
                        />,
                        <Button key="search" type="primary" onClick={handleSearch}>
                            搜索
                        </Button>,
                        <Button key="reset" onClick={handleReset}>
                            重置
                        </Button>,
                    ]}
                    columns={columns}
                    search={false}
                    request={async (params, sort, filter) => {
                        return presenter?.searchInstitutionTable(params, sort, filter, tableType);
                    }}
                />

                <Drawer
                    open={state.tagVisible}
                    title="设置标签"
                    placement="left"
                    width={800}
                    onClose={() => presenter?.setTagVisible(false)}
                    styles={{
                        body: {
                            overflow: 'auto',
                            height: '100%',
                        },
                    }}
                    footer={null}
                >
                    <div style={{width: 700}}>
                        <TagSelector
                            dataType={tableType === 'inner' ? 'INSTITUTION_INNER' : 'INSTITUTION_OUTER'}
                            dataId={state.institutionId}
                            onChange={(value) => {
                                presenter?.setTagVisible(false);
                            }}
                        />
                    </div>
                </Drawer>

                <Modal
                    open={state.contentVisible}
                    title="编辑条款"
                    width={1000}
                    style={{maxHeight: '80vh', height: '80vh'}}   // 整个 Modal 的高度
                    bodyStyle={{maxHeight: 'calc(80vh - 100px)', overflowY: 'auto'}} // 内容区自动滚动
                    onCancel={() => presenter?.setContentVisible(false)}
                    onOk={() => {
                        presenter?.updateInstitution()
                        presenter?.setContentVisible(false)
                    }}
                >
                    <div style={{maxHeight: '100%', overflowY: 'auto'}}>
                        {state.institutionContent && state.institutionContent.length > 0 ? (
                            <ul style={{listStyleType: 'none', paddingLeft: 0}}>
                                {state.institutionContent.map((item) => (
                                    <li key={(item as any)?.id} style={{
                                        position: 'relative',
                                        marginBottom: '24px',
                                        padding: '8px',
                                        border: '1px solid #e8e8e8',
                                        borderRadius: '4px'
                                    }}>
                                        {/* 右上角：删除按钮 */}
                                        <Button
                                            type="text"
                                            icon={
                                                <CloseOutlined
                                                    style={{fontSize: 10, color: '#000'}} // 设置为黑色
                                                />
                                            }
                                            onClick={() => presenter?.deleteInstitutionalContent(item.serial)}
                                            style={{
                                                position: 'absolute',
                                                top: -8,
                                                right: -8,
                                                minWidth: 'auto',
                                                width: '24px',
                                                height: '24px',
                                                padding: 0,
                                                zIndex: 1,
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center'
                                            }}
                                        />
                                        {/* 输入框 */}
                                        <Input.TextArea
                                            value={(item as any).content}
                                            onChange={(e) => {
                                                presenter?.updateInstitutionalContent(item.serial, e.target.value);
                                            }}
                                            autoSize={{minRows: 2, maxRows: 6}}
                                            style={{paddingRight: 32}} // 避免文字被按钮遮挡
                                        />
                                        {/* 右下角：新增按钮 */}
                                        <Button
                                            type="dashed"
                                            icon={<span style={{fontSize: 16}}>+</span>}
                                            onClick={() => presenter?.addInstitutionalContent(item.serial + 1)}
                                            style={{
                                                position: 'absolute',
                                                bottom: 0,
                                                right: 0,
                                                minWidth: 'auto',
                                                width: '24px',
                                                height: '24px',
                                                padding: 0,
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center'
                                            }}
                                        />
                                    </li>
                                ))}

                            </ul>
                        ) : (
                            <p>暂无数据</p>
                        )}
                    </div>
                </Modal>

            </>
        );
    }
);


export default InstitutionManagePage;
