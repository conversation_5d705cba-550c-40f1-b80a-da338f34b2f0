/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;


/**
 * 数据状态类型的定义
 */
export interface InstitutionState {
    searchTitleName: string;
    tableVersion: string;
    tagVisible: boolean;
    contentVisible: boolean;
    institutionContent: { serial: number; content: string }[];
    recordId: number;
    institutionId: number;

}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface InstitutionApi {
    loadInstitutionTable: (params: any, sort: any, filter: any) => Promise<any>;
    loadInstitutionalContent: (params: any) => Promise<any>;
    updateInstitution: (body: any) => Promise<any>;
}
