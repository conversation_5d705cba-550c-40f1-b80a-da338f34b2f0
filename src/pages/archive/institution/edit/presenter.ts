import {InstitutionApi, InstitutionState, Dispatch} from "./types";

export class InstitutionPresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<InstitutionState>;
    // 传入的Model对象
    private readonly api: InstitutionApi;
    // 当前的数据状态，由于Presenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: InstitutionState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: InstitutionState, dispatch: Dispatch<InstitutionState>, api: InstitutionApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('InstitutionPresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: InstitutionState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableVersion: `table-${Date.now()}`
            }
        })
    }

    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: "",
            }
        })
    }


    public setSearchFileName = (title: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: title
            }
        })
    }


    public searchInstitutionTable = (params: any, sort: any, filter: any, searchType: string) => {
        const searchParams = {
            ...params,
            title: this.state.searchTitleName,
            searchType: searchType,

        }

        return this.api.loadInstitutionTable(searchParams, sort, filter);
    }

    public setTagVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tagVisible: v
            }
        })
    }

    public setContentVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                contentVisible: v
            }
        })
    }

    public loadInstitutionalContent = async (id: number, tableType: string) => {
        const searchParams = {
            id: id,
        }
        const res = await this.api.loadInstitutionalContent(searchParams);
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionContent: res.data.list,
            }
        })
        return;
    }

    public updateInstitution = () => {
        const body = {
            institutionId: this.state.institutionId,
            institutionContentList: this.state.institutionContent
        };
        return this.api.updateInstitution(body);
    }

    public updateInstitutionalContent = (serial: number, content: string) => {
        this.dispatch((prevState) => ({
            ...prevState,
            institutionContent: prevState.institutionContent.map(item =>
                item.serial === serial ? {...item, content} : item
            )
        }));
    };


    public addInstitutionalContent = (targetSerial?: number) => {
        this.dispatch((prevState) => {
            const list = [...prevState.institutionContent];

            let index = -1;

            if (targetSerial !== undefined) {
                index = list.findIndex(item => item.serial === targetSerial);
            }

            // 如果没找到目标 serial，默认加到最后
            if (index === -1) {
                const newSerial = list.length > 0 ? Math.max(...list.map(i => i.serial)) + 1 : 1;
                list.push({serial: newSerial, content: ''});
            } else {
                // 在目标 serial 所在项的上方插入新项，并重新分配 serial
                const newSerial = list[index].serial;
                list.splice(index, 0, {serial: newSerial, content: ''});

                // 更新后面所有项的 serial（+1）
                for (let i = index + 1; i < list.length; i++) {
                    list[i] = {...list[i], serial: list[i].serial + 1};
                }
            }

            return {
                ...prevState,
                institutionContent: list
            };
        });
    };

    public deleteInstitutionalContent = (serial: number) => {
        this.dispatch((prevState) => {
            let list = [...prevState.institutionContent];

            // 删除指定 serial 的条目
            list = list.filter(item => item.serial !== serial);

            // 重新计算 serial（按顺序从 1 开始）
            list = list
                .sort((a, b) => a.serial - b.serial)
                .map((item, idx) => ({
                    ...item,
                    serial: idx + 1
                }));

            return {
                ...prevState,
                institutionContent: list
            };
        });
    };


    public setRecordId = (v: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recordId: v
            }
        })
    }
    public setInstitutionId = (v: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionId: v
            }
        })
    }

}
