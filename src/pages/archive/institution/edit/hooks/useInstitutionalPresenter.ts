import React from "react";
import {InstitutionState} from "@/pages/archive/institution/edit/types";
import {InstitutionPresenter} from "@/pages/archive/institution/edit/presenter";
import {InstitutionApiImpl} from "@/pages/archive/institution/edit/model";

// state状态的初始值
const initialState: InstitutionState = {
    searchTitleName: "",
    tableVersion: "",
    tagVisible: false,
    contentVisible: false,
    institutionContent: [],
    recordId: 0,
    institutionId: 0,
};

/**
 *  useInstitutionPresenter hook
 *  创建 Presenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInstitutionPresenter() {
    const [state, dispatch] = React.useState<InstitutionState>(initialState);

    const presenterRef = React.useRef<InstitutionPresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 Presenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InstitutionPresenter(state, dispatch, new InstitutionApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
