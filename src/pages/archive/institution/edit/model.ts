import {InstitutionApi} from "./types";
import {
    innerInstitutionList,
    institutionItem,
    updateInstitution
} from "@/api/archive/institution";


/**
 * Model层接口实现
 * ApiImpl 实现了Api接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InstitutionApiImpl implements InstitutionApi {

    loadInstitutionTable(params: any, sort: any, filter: any): Promise<any> {
        return innerInstitutionList(params, sort, filter, []);
    }

    loadInstitutionalContent(params: any): Promise<any> {
        return institutionItem(params);
    }

    updateInstitution(params: any): Promise<any> {
        return updateInstitution(params);
    }
}