import {FileApi, FileState, Dispatch} from "./types";

export class FilePresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<FileState>;
    // 传入的Model对象
    private readonly api: FileApi;
    // 当前的数据状态，由于Presenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: FileState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: FileState, dispatch: Dispatch<FileState>, api: FileApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('ArchiveFilePresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: FileState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableKey: `table-${Date.now()}`
            }
        })
    }

    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchFileName: "",
                startTime: "",
                endTime: ""
            }
        })
    }

    public setPreviewData = (url: string, type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewUrl: url,
                previewType: type,
                previewVisible: true
            }
        })
    }

    public uploadVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadVisible: v
            }
        })
    }

    public setUploadFile = (filename: string, base64: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadedFile: {
                    filename: filename,
                    base64: base64,
                }
            }
        })
    }

    public setSearchTime = (startTime: string, endTime: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                startTime: startTime,
                endTime: endTime
            }
        })
    }

    public setSearchFileName = (fileName: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchFileName: fileName
            }
        })
    }

    public previewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewVisible: v
            }
        })
    }

    public searchArchiveFileTable = (params: any, sort: any, filter: any, bucket: string) => {
        const searchParams = {
            ...params,
            fileName: this.state.searchFileName,
            startTime: this.state.startTime,
            endTime: this.state.endTime,
            bucket: bucket,
        }

        return this.api.loadArchiveFileTable(searchParams, sort, filter);
    }

    public uploadArchiveFile = () => {
        const {filename, base64} = this.state.uploadedFile;
        const blob = this.dataURLtoBlob(base64);
        const file = new File([blob], filename, {type: blob.type});
        const formData = new FormData();
        formData.append("file", file);
        formData.append("source", 'Ve0DUvZl9Lk');
        return this.api.uploadArchiveFile(formData);
    }

    public dataURLtoBlob(dataUrl: string): Blob {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "";
        const bStr = atob(arr[1]);
        let n = bStr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bStr.charCodeAt(n);
        }
        return new Blob([u8arr], {type: mime});
    }

}
