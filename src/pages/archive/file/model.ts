import {FileApi} from "./types";
import {list, upload} from "@/api/archive/archive";


/**
 * Model层接口实现
 * ApiImpl 实现了Api接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class FileApiImpl implements FileApi {

    loadArchiveFileTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    uploadArchiveFile(formData: FormData): Promise<any> {
        return upload(formData);
    }
}