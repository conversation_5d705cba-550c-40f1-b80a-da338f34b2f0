import React, {<PERSON>} from "react";
import {ValidateUtils} from "@codingapi/ui-framework";
import {useArchiveFiletPresenter} from "@/pages/archive/file/hooks/useArchiveFilePresenter";
import {Modal,Message} from "@itm/components-pc";
import {Form, FormUploader} from "@/components/form-pc";
interface FileUploaderProps {
    bucket: string;
    uploadVisible: boolean;
    uploadResult?: (visible: boolean) => void;
}

const FileUploader: FC<FileUploaderProps> = ({bucket, uploadVisible, uploadResult}) => {

    const {state, presenter} = useArchiveFiletPresenter();

    const form = Form.useForm();
    const handleUploadFinish = async () => {
        if (!state.uploadedFile) {
            Message.error("请先选择并上传文件");
            return;
        }

        try {
            const res = await presenter?.uploadArchiveFile();
            if ((res as any)?.success) {
                Message.success("上传成功");
                presenter?.uploadVisible(false);
                uploadResult?.(true);
                form.reset()
                return;
            } else {
                Message.error("上传失败");
                form.reset()
                return;
            }
        } catch (error: any) {
            console.error("上传错误：", error);
            Message.error(error.Message || "上传过程中发生错误");
        }
    };

    return (
        <Modal
            open={uploadVisible}
            title="上传文件"
            onCancel={() => {
                console.log("取消上传文件");
                presenter?.uploadVisible(false);
                uploadResult?.(false);
                form.reset()
            }}
            onOk={async () => {
                console.log("上传文件")
                await form.submit();
                form.reset()
            }}
        >
            <Form form={form} onFinish={handleUploadFinish}>
                <FormUploader
                    name="avatar"
                    label="上传文件"
                    uploaderAccept="*"
                    uploaderMaxCount={1}
                    validateFunction={ValidateUtils.validateNotEmpty}
                    onUploaderUpload={(filename, base64) => {
                        presenter?.setUploadFile(filename, base64);
                        return Promise.resolve({
                            id: "temp-id",
                            name: filename,
                            url: base64,
                        });
                    }}
                />
            </Form>
        </Modal>
    );
};

export default FileUploader;
