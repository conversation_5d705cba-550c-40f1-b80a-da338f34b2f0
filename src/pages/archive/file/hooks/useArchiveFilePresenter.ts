import React from "react";
import {FileState} from "@/pages/archive/file/types";
import {FilePresenter} from "@/pages/archive/file/presenter";
import {FileApiImpl} from "@/pages/archive/file/model";

// state状态的初始值
const initialState: FileState = {
    searchFileName: "",
    startTime: "",
    endTime: "",
    uploadVisible: false,
    previewVisible: false,
    previewUrl: null,
    previewType: null,
    tableKey: 'default',
    uploadedFile: {
        filename: "",
        base64: "",
    },
};

/**
 *  useInstitutionPresenter hook
 *  创建 Presenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useArchiveFiletPresenter() {
    const [state, dispatch] = React.useState<FileState>(initialState);

    const presenterRef = React.useRef<FilePresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 Presenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new FilePresenter(state, dispatch, new FileApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
