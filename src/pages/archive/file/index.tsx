import React, {memo, useCallback} from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import dayjs from "dayjs";
import {useArchiveFiletPresenter} from "@/pages/archive/file/hooks/useArchiveFilePresenter";
import FileUploader from "@/pages/archive/file/components/FileUploader";
import {Button, Input, Message} from "@itm/components-pc";
import {DatePicker} from "antd";

interface FileTableProps {
    tableType: string;
}

const ArchiveManagePage: React.FC = () => {
    return (
        <PageContainer>
            <FileTable tableType={"document"}/>
        </PageContainer>
    );
};

const FileTable: React.FC<FileTableProps> = memo(({tableType}) => {

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useArchiveFiletPresenter();


    const handleSearch = useCallback(() => {
        presenter?.refreshTable();
        actionRef.current?.reload();
    }, []);

    const handleReset = useCallback(() => {
        presenter?.resetSearch()
        actionRef.current?.reload();
    }, []);
    const handlePreviewFile = (record: any) => {
        if (!record.fileUrl) {
            Message.error("文件地址不存在");
            return;
        }
        const fileExtension = record.fileType?.toLowerCase() || "";
        presenter?.setPreviewData(record.fileUrl, fileExtension)
    };

    const columns = [
        {title: "文件标识", dataIndex: "fileKey"},
        {
            title: "文件名", dataIndex: "fileName",
            render: (text: string) => (
                <span title={text} style={{cursor: 'pointer'}}>
                    {text?.length > 20 ? `${text.slice(0, 20)}...` : text}
                </span>),
        },
        {title: "文件大小", dataIndex: "fileSize"},
        {title: "文件类型", dataIndex: "fileType"},
        {title: "创建时间", dataIndex: "createTime"},
        {
            title: "文件来源",
            dataIndex: "source",
            render: (text: string) => {
                if (!text || !text.includes('#')) return text;
                const parts = text.split('#');
                return parts.length > 1 ? parts[1] : text;
            },
        },
        {
            title: "操作",
            valueType: "option",
            render: (text: any, record: any) => {
                return [
                    <a key="download" onClick={() => window.open(record.fileUrl)}>
                        下载
                    </a>,
                ];
            },
        },
    ] as any[];

    return (
        <>
            <ProTable
                key={state.tableKey}
                actionRef={actionRef}
                headerTitle={<h3 style={{margin: 0, fontWeight: "bold"}}>文件列表</h3>}
                toolBarRender={() => [
                    <DatePicker.RangePicker
                        key="date"
                        style={{width: 400, marginRight: 8}}
                        showTime
                        format="YYYY-MM-DD HH:mm:ss"
                        value={
                            state.startTime && state.endTime
                                ? [
                                    dayjs(parseInt(state.startTime)),
                                    dayjs(parseInt(state.endTime))
                                ]
                                : undefined
                        }
                        onChange={(dates) => {
                            if (dates && dates.length === 2) {
                                presenter?.setSearchTime(dates[0]?.valueOf().toString() || "", dates[1]?.valueOf().toString() || "")
                            } else {
                                presenter?.setSearchTime("", "")
                            }
                        }}
                    />,
                    <Input
                        key="fileName"
                        placeholder="请输入文件名"
                        value={state.searchFileName}
                        onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                        style={{width: 200, marginRight: 8}}
                    />,
                    <Button key="search" type="primary" onClick={handleSearch}>
                        搜索
                    </Button>,
                    <Button key="reset" onClick={handleReset}>
                        重置
                    </Button>,
                    <Button
                        key="upload"
                        type="primary"
                        onClick={() => {
                            presenter?.uploadVisible(true);
                        }}
                    >
                        上传文件
                    </Button>,
                ]}
                columns={columns}
                search={false}
                request={async (params, sort, filter) => {
                    return presenter?.searchArchiveFileTable(params, sort, filter, tableType);
                }}
            />

            <FileUploader
                bucket={tableType}
                uploadVisible={state.uploadVisible}
                uploadResult={(result) => {
                    presenter?.uploadVisible(false);
                    if (result) {
                        presenter?.refreshTable();
                    }
                }}
            />

        </>
    );
});


export default ArchiveManagePage;
