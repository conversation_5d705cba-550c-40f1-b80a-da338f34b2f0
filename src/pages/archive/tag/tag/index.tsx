import {DownOutlined} from "@ant-design/icons";
import {ActionType, ProColumns} from "@ant-design/pro-components";
import {
    PageContainer,
} from "@ant-design/pro-components";
import {Col, Popconfirm, Row, TreeProps} from "antd";
import React from "react";
import {Form, FormInput, FormSelect, FormStepper} from "@/components/form-pc";
import {usePresenter} from "./hooks/usePresenter";
import {Button, Modal, ProTable, Tree} from "@itm/components-pc";


const Tag: React.FC = () => {

        const actionRef = React.useRef<ActionType>(null);

        const form = Form.useForm();

        // 通过hooks获取state状态数据与presenter对象
        const {state, presenter} = usePresenter();

        // 点击标签树节点时，更新当前标签
        const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
            presenter.updateSelected(selectedKeys as string[]);
        };

        React.useEffect(() => {
            actionRef.current?.reload();
        }, [state.tableRefreshVersion]);


        React.useEffect(() => {
            if (state.currentEdit) {
                form.setFieldsValue(state.currentEdit)
            }
        }, [state.currentEdit]);

        React.useEffect(() => {
            if (state.parentId > 0) {
                form.setFieldsValue({
                    parentId: String(state.parentId),
                });
            } else {
                form.reset();
            }
        }, [state.parentId]);


        // 当组件挂载时，加载标签树数据
        React.useEffect(() => {
            presenter.loadData();
        }, []);

        const columns: ProColumns<any>[] = [
            {
                title: "序号",
                dataIndex: "serial",
                search: false,
            },
            {
                title: "编号",
                dataIndex: "id",
                disable: true,
                hidden: true,
                search: false,
            },
            {
                title: "编码",
                dataIndex: "code",
                disable: true,
                search: false,
            },
            {
                title: "名称",
                dataIndex: "name",
            },
            {
                title: "排序",
                dataIndex: "sysIndex",
                search: false,
            },
            {
                title: "状态",
                dataIndex: "sysEnabled",
                valueEnum: {
                    0: {
                        text: "禁用",
                        status: "error",
                    },
                    1: {
                        text: "启用",
                        status: "success",
                    },
                },
                render: (_, record) => {
                    return (
                        <Popconfirm
                            title="确认操作"
                            description={`确定要将此标签${(record.children?.length ?? 0) > 0 ? '及其子标签' : ''}状态从【${record.sysEnabled === 1 ? '启用' : '禁用'}】切换为【${record.sysEnabled === 1 ? '禁用' : '启用'}】吗？`}
                            onConfirm={() => {
                                presenter.changeStatus(record);
                            }}
                            okText="确认"
                            cancelText="取消"
                        >
                            <a> {record.sysEnabled === 1 ? '启用' : '禁用'} </a>
                        </Popconfirm>
                    );
                },
            },
            {
                title: "操作",
                key: 'option',
                valueType: "option",
                width: 280,
                render: (_, record) => [
                    <a
                        key="add"
                        onClick={() => {
                            presenter.showAddModal();
                        }}
                    >新增同级标签</a>,
                    <a
                        key="add-children"
                        onClick={() => {
                            presenter.showChildAddModal(record.code);
                        }}
                    >新增下级标签</a>,
                    <a
                        key="update"
                        onClick={() => {
                            presenter.showEditModal(record.code);
                        }}
                    >编辑</a>,
                    <Popconfirm
                        key="delete"
                        title="删除提示"
                        description="确认要删除这条数据吗?"
                        onConfirm={() => {
                            presenter.delete(record);
                        }}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a
                            key="delete"
                            hidden={(record.children?.length ?? 0) !== 0}
                        >
                            删除
                        </a>
                    </Popconfirm>,
                ],
            },
        ];

        return (
            <PageContainer>
                <Row>
                    <Col span={4}>
                        <div>标签分类</div>
                        {state.treeData.length > 0 && (
                            <Tree
                                showLine
                                switcherIcon={<DownOutlined/>}
                                defaultExpandAll={true}
                                onSelect={onSelect}
                                treeData={state.treeData}
                            />
                        )}
                    </Col>
                    <Col span={20}>
                        <ProTable
                            actionRef={actionRef}
                            rowKey="id"
                            columns={columns}
                            toolBarRender={() => {
                                return [
                                    <Button
                                        type={"primary"}
                                        onClick={() => {
                                            console.log("parentId:", state.selected?.id)
                                            presenter.showAddModal();
                                        }}
                                    >新增</Button>
                                ]
                            }}
                            request={async (params, sort, filter) => {
                                // 获取当前 labelId（来自 Presenter 的 state）
                                const parentId = state.selected?.id ?? 0;
                                if (parentId != 0) {
                                    // 合并 labelId 到 params 中
                                    const searchParams = {
                                        ...params,
                                        parentId: parentId,
                                    };
                                    return presenter.searchTable(searchParams, sort, filter);
                                }
                                return presenter.searchTable(params, sort, filter);
                            }}
                        />
                    </Col>
                </Row>
                <Modal
                    open={state.addVisible}
                    title={"新增标签"}
                    destroyOnHidden={true}
                    onCancel={() => {
                        presenter.hideAddModal();
                    }}
                    footer={() => (
                        <>
                            <Button
                                key="submit"
                                type="primary"
                                onClick={async () => {
                                    await form.submit();
                                    presenter.hideAddModal();
                                }}
                            >
                                提交
                            </Button>
                            <Button
                                key="continue"
                                type="default"
                                onClick={async () => {
                                    await form.submit(); // 提交当前数据
                                    form.reset();   // 提交完成后清空表单
                                }}
                            >
                                继续添加
                            </Button>
                            <Button
                                key="cancel"
                                onClick={() => {
                                    presenter.hideAddModal();
                                }}
                            >
                                取消
                            </Button>
                        </>
                    )}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={async (values) => {
                            return presenter.create(values);
                        }}
                    >
                        <FormSelect
                            name={"parentId"}
                            label="父级标签"
                            placeholder={"请选择父级标签"}
                            options={state.selectData}
                        />
                        <FormInput
                            name={"name"}
                            label="标签名称"
                            placeholder={"请输入标签名称"}
                            required={true}
                        />
                        <FormStepper
                            required={false}
                            name="sysIndex"
                            label={"排序"}
                            stepperMinNumber={0}
                        />
                    </Form>
                </Modal>
                <Modal
                    open={state.childAddVisible}
                    title={"新增下级标签"}
                    destroyOnHidden={true}
                    onCancel={() => {
                        presenter.hideChildAddModal();
                    }}
                    footer={() => (
                        <>
                            <Button
                                key="submit"
                                type="primary"
                                onClick={async () => {
                                    await form.submit();
                                    presenter.hideChildAddModal();
                                }}
                            >
                                提交
                            </Button>
                            <Button
                                key="continue"
                                type="default"
                                onClick={async () => {
                                    await form.submit(); // 提交当前数据
                                    form.reset();   // 提交完成后清空表单
                                    form.setFieldsValue({parentId: state.parentId ? state.parentId : 0})
                                }}
                            >
                                继续添加
                            </Button>
                            <Button
                                key="cancel"
                                onClick={() => {
                                    presenter.hideChildAddModal();
                                }}
                            >
                                取消
                            </Button>
                        </>
                    )}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={async (values) => {
                            const finalValues = {
                                ...values,
                                parentId: values.parentId || state.parentId || state.selected?.id || 0,
                            };
                            return presenter.create(finalValues);
                        }}
                    >
                        <FormSelect
                            name={"parentId"}
                            label="父级标签"
                            placeholder={"请选择父级标签"}
                            options={state.selectData}
                        />
                        <FormInput
                            name={"name"}
                            label="标签名称"
                            placeholder={"请输入标签名称"}
                            required={true}
                        />
                        <FormStepper
                            required={false}
                            name="sysIndex"
                            label={"排序"}
                            stepperMinNumber={0}
                        />
                    </Form>
                </Modal>
                <Modal
                    open={state.editVisible}
                    title={"编辑标签"}
                    destroyOnHidden={true}
                    onCancel={() => {
                        presenter.hideEditModal();
                    }}
                    onOk={async () => {
                        await form.submit();
                        presenter.hideEditModal();
                    }}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={async (values) => {
                            return presenter.update(values);
                        }}
                    >
                        <FormInput
                            name={"id"}
                            hidden={true}
                        />
                        <FormInput
                            name={"name"}
                            label="标签名称"
                            placeholder={"请输入标签名称"}
                            required={true}
                        />
                        <FormSelect
                            name={"parentId"}
                            label="父级标签"
                            placeholder={"请选择父级标签"}
                            options={state.selectData}
                            //hidden={state.currentEditArchive?.parentId == 0}
                        />
                        <FormStepper
                            required={false}
                            name="sysIndex"
                            label={"排序"}
                            stepperMinNumber={0}
                        />
                    </Form>
                </Modal>
            </PageContainer>
        );
    }
;

export default Tag;