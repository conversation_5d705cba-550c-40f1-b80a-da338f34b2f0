import {save, del, detail, page, tree, enable, disable} from "@/api/archive/tag";
import {Api} from "./types";


export class ApiImpl implements Api {
    loadData(): Promise<any> {
        return tree();
    }

    loadTable(params: any, sort: any, filter: any): Promise<any> {
        return page(params, sort, filter, [
            {
                key: "name",
                type: "LIKE",
            },
            {
                key: "comments",
                type: "LIKE",
            },
        ]);
    }

    async save(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await save(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async update(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await save(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async delete(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

    detail(params: any): Promise<any> {
        return detail(params);
    }

    async enable(params: any): Promise<{ success: boolean }> {
        try {
            const res = await enable(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

    async disable(params: any): Promise<{ success: boolean }> {
        try {
            const res = await disable(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }
}