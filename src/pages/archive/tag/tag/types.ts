export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface State {
    editVisible: boolean; //  tag编辑框是否可见
    addVisible: boolean; // tag添加框是否可见
    childAddVisible: boolean; // tag添加子项弹框是否可见

    currentEdit: any; // 当前编辑的tag

    parentId: number;
    selected: any; //  当前选中的tag
    selectedCode: string;

    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新

    treeData: any[]; // tag树结构数据
    selectData: any[];
}


export interface Api {

    loadData: () => Promise<any>;

    loadTable: (params: any, sort: any, filter: any) => Promise<any>;

    save: (params: any) => Promise<{ success: boolean; data?: any }>;

    update: (params: any) => Promise<{ success: boolean; data?: any }>;

    delete: (params: any) => Promise<{ success: boolean }>;

    detail: (params: any) => Promise<any>;

    enable:  (params: any) => Promise<{ success: boolean }>;

    disable:  (params: any) => Promise<{ success: boolean }>;

}