import React from "react";
import {State} from "@/pages/archive/tag/tag/types";
import {Presenter} from "@/pages/archive/tag/tag/presenter";
import {ApiImpl} from "@/pages/archive/tag/tag/model";

const initialState: State = {
    editVisible: false,
    addVisible: false,
    childAddVisible: false,

    currentEdit: null,

    parentId: 0,
    selected: null,
    selectedCode: '',

    tableRefreshVersion: 0,

    treeData: [],
    selectData: [],
}

export function usePresenter() {
    const [state, dispatch] = React.useState<State>(initialState);

    const presenterRef = React.useRef<Presenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new Presenter(dispatch, new ApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}