import {Api, State, Dispatch} from "@/pages/archive/tag/tag/types";

interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class Presenter {

    //更新数据状态
    private readonly dispatch: Dispatch<State>;

    private readonly api: Api;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    }

    private refreshCurrentEdit() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEdit: null,
                parentId: 0,
            }
        });
    }

    // 显示Tag添加对话框
    public showAddModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                parentId: prevState.selected?.id ?? 0,
                addVisible: true,
            }
        });
    }

    // 隐藏Tag添加对话框
    public hideAddModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: false,
            }
        })
    }

    // 显示Tag编辑对话框
    public showEditModal = (code: string) => {
        this.refreshCurrentEdit();
        this.api.detail(code).then((res) => {
            const currentEdit = res.data;
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentEdit: currentEdit,
                    editVisible: true,
                }
            });
        })
    }

    // 隐藏Tag编辑对话框
    public hideEditModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editVisible: false,
            }
        })
    }

    public showChildAddModal(code: String) {
        this.refreshCurrentEdit();
        this.api.detail(code).then(res => {
            const currentEdit = res.data;
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    parentId: currentEdit.id,
                    childAddVisible: true,
                }
            })
        });
    }

    public hideChildAddModal() {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                childAddVisible: false
            }
        });
    }

    // 刷新表格数据
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    // 删除数据
    public delete = (record: State) => {
        this.api.delete(record)
            .then((res) => {
                if (res.success) {
                    this.refreshTable();
                    this.loadData();
                }
            })
            .catch((e) => {
                console.error("删除失败", e);
            });
    }

    // 新增数据
    public create = async (params: State) => {
        try {
            const res = await this.api.save(params);
            if (res.success) {
                this.refreshTable();
                this.loadData();
            }
        } catch (e) {
            console.error("保存失败", e);
        }
    }

    // 更新数据
    public update = async (record: State) => {
        try {
            const res = await this.api.update(record);
            if (res.success) {
                this.refreshTable();
                this.loadData();
            }
        } catch (e) {
            console.error("更新失败", e);
        }

    }

    // 表格数据加载函数
    public searchTable = async (params: any, sort: any, filter: any): Promise<any> => {
        const searchParams = {...params};
        const res = await this.api.loadTable(searchParams, sort, filter);
        const data = res.data;
        const serialData = this.generateSerialNumbers(data);
        return Promise.resolve({
            success: true,
            data: serialData,
        })
    }

    // 设置当前点击的标签数据，然后刷新表格数据
    public updateSelected = (selected: string[]) => {
        if (selected && selected.length > 0) {
            const code = selected[0];
            this.api.detail(code).then((res) => {
                const selectedTag = res.data;
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        selected: selectedTag,
                        parentId: selectedTag.id,
                        tableRefreshVersion: prevState.tableRefreshVersion + 1
                    }
                });
            })
        }
    }

    // Tag树数据
    public loadData = () => {
        this.api.loadData().then(data => {
            this.dispatch((prevState) => {
                // 确保 data 是数组，否则设为空数组
                const dataArray = Array.isArray(data.data?.list) ? data.data?.list : [];
                const treeData = this.getTreeOptions(dataArray);
                const selectData = this.getSelectOptions(dataArray);
                return {
                    ...prevState,
                    treeData: treeData,
                    selectData: selectData,
                }
            })
        });
    }

    public getTreeOptions = (data: any[]): TreeOption[] => {
        return data.map(item => ({
            title: item.name,
            key: item.code,
            children: item.children ? this.getTreeOptions(item.children) : undefined,
        }));
    };

    public getSelectOptions = (data: any[]): selectOption[] => {
        return data.map(item => ({
            label: item.name,
            value: item.id.toString(),
            children: item.children ? this.getSelectOptions(item.children) : undefined,
        }));
    };

    // 递归生成 serial 的函数
    public generateSerialNumbers(nodes: any[], parentSerial = '') {
        return nodes.map((node, index) => {
            // 计算当前节点的 serial
            const currentSerial = parentSerial ? `${parentSerial}.${index + 1}` : `${index + 1}`;
            node.serial = currentSerial;

            // 如果有子节点，递归处理
            if (node.children && node.children.length > 0) {
                node.children = this.generateSerialNumbers(node.children, currentSerial);
            }

            return node;
        });
    }

    public changeStatus(record: any) {
        if (record.sysEnabled === 1) {
            this.api.disable(record.id).then((res) => {
                if (res.success) {
                    this.refreshTable();
                }
            });
        } else {
            this.api.enable(record.id).then((res) => {
                if (res.success) {
                    this.refreshTable();
                }
            });
        }
    }
}