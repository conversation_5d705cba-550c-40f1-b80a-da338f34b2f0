import {ActionType, ProColumns} from "@ant-design/pro-components";
import {
    PageContainer,
} from "@ant-design/pro-components";
import {Col, Popconfirm, Row, TreeProps} from "antd";
import React from "react";
import {Form, FormInput, FormSelect, FormStepper} from "@/components/form-pc";
import {usePresenter} from "./hooks/usePresenter";
import {Button,Modal, ProTable, Tree} from "@itm/components-pc";

const TagMapping: React.FC = () => {
        const actionRef = React.useRef<ActionType>(null);

        const form = Form.useForm();

        // 通过hooks获取state状态数据与presenter对象
        const {state, presenter} = usePresenter();

        React.useEffect(() => {
            actionRef.current?.reload();
        }, [state.tableRefreshVersion]);


        React.useEffect(() => {
            if (state.currentEdit) {
                form.setFieldsValue(state.currentEdit)
            } else {
                form.reset();
            }
        }, [state.currentEdit]);

        // 当组件挂载时，标签数据
        React.useEffect(() => {
            presenter.loadTagData();
        }, []);

        const columns: ProColumns<any>[] = [
            {
                title: "编号",
                dataIndex: "id",
                search: false,
            },
            {
                title: "标签编码",
                dataIndex: "tagCode",
                hidden: true,
                search: false,
            },
            {
                title: "数据类型",
                dataIndex: "dataType",
            },
            {
                title: "标签名称",
                dataIndex: "tagName",
                search: false,
            },
            {
                title: "操作",
                key: 'option',
                valueType: "option",
                width: 280,
                render: (_, record) => [
                    <a
                        key="update"
                        onClick={() => {
                            presenter.showEditModal(record.id);
                        }}
                    >编辑</a>,
                    <Popconfirm
                        key="delete"
                        title="删除提示"
                        description="确认要删除这条数据吗?"
                        onConfirm={() => {
                            presenter.delete(record);
                        }}
                        okText="确认"
                        cancelText="取消"
                    >
                        <a
                            key="delete"
                        >
                            删除
                        </a>
                    </Popconfirm>,
                ],
            },
        ];

        return (
            <PageContainer>
                {/*<Row>*/}
                {/*    <Col>*/}
                {/*        <TagCheckbox*/}
                {/*            name={"选择标签"}*/}
                {/*            dataType={"AAA"}*/}
                {/*        />*/}
                {/*    </Col>*/}
                {/*    <Col span={24}>*/}
                        <ProTable
                            actionRef={actionRef}
                            rowKey="id"
                            columns={columns}
                            toolBarRender={() => {
                                return [
                                    <Button
                                        type={"primary"}
                                        onClick={() => {
                                            presenter.showAddModal();
                                        }}
                                    >新增</Button>
                                ]
                            }}
                            request={async (params, sort, filter) => {
                                return presenter.searchTable(params, sort, filter);
                            }}
                        />
                {/*    </Col>*/}
                {/*</Row>*/}
                <Modal
                    open={state.addVisible}
                    title={"新增关联"}
                    destroyOnHidden={true}
                    onCancel={() => {
                        presenter.hideAddModal();
                    }}
                    footer={() => (
                        <>
                            <Button
                                key="submit"
                                type="primary"
                                onClick={async () => {
                                    await form.submit();
                                    presenter.hideAddModal();
                                }}
                            >
                                提交
                            </Button>
                            <Button
                                key="continue"
                                type="default"
                                onClick={async () => {
                                    await form.submit(); // 提交当前数据
                                    form.reset();   // 提交完成后清空表单
                                }}
                            >
                                继续添加
                            </Button>
                            <Button
                                key="cancel"
                                onClick={() => {
                                    presenter.hideAddModal();
                                }}
                            >
                                取消
                            </Button>
                        </>
                    )}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={async (values) => {
                            return presenter.create(values);
                        }}
                    >
                        <FormInput
                            name={"dataType"}
                            label="数据类型"
                            placeholder={"请输入数据类型"}
                            required={true}
                        />
                        <FormSelect
                            name={"tagCode"}
                            label="关联标签"
                            placeholder={"请选择关联标签"}
                            options={state.selectData}
                            required={true}
                        />
                    </Form>
                </Modal>

                <Modal
                    open={state.editVisible}
                    title={"编辑关联"}
                    destroyOnHidden={true}
                    onCancel={() => {
                        presenter.hideEditModal();
                    }}
                    onOk={async () => {
                        await form.submit();
                        presenter.hideEditModal();
                    }}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={async (values) => {
                            return presenter.update(values);
                        }}
                    >
                        <FormInput
                            name={"id"}
                            hidden={true}
                        />
                        <FormInput
                            name={"dataType"}
                            label="数据类型"
                            placeholder={"请输入数据类型"}
                            required={true}
                        />
                        <FormSelect
                            name={"tagCode"}
                            label="关联标签"
                            placeholder={"请选择关联标签"}
                            options={state.selectData}
                            required={true}
                        />
                    </Form>
                </Modal>
            </PageContainer>
        );
    }
;

export default TagMapping;