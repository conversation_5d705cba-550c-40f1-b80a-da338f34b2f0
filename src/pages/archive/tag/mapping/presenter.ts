import {Api, Dispatch, State} from "@/pages/archive/tag/mapping/types";

const topLevelOption = {
    label: '顶级标签',
    value: '0'
};

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class Presenter {

    //更新数据状态
    private readonly dispatch: Dispatch<State>;

    private readonly api: Api;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    }

    private refreshSelected() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selected: null,
                selectedCode: '',
            }
        });
    }

    private refreshCurrentEdit() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentEdit: null,
            }
        });
    }

    // 显示Tag添加对话框
    public showAddModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: true,
            }
        });
    }

    // 隐藏Tag添加对话框
    public hideAddModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: false,
            }
        })
    }

    // 显示Tag编辑对话框
    public showEditModal = (id: number) => {
        console.log("id", id)
        this.refreshCurrentEdit();
        this.api.getById(id).then(res => {
            const currentEdit = res.data;
            console.log("currentEdit", currentEdit);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentEdit: currentEdit,
                    editVisible: true,
                }
            })
        });
    }

    // 隐藏Tag编辑对话框
    public hideEditModal = () => {
        this.refreshCurrentEdit();
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editVisible: false,
            }
        })
    }

    // 刷新表格数据
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    // 删除数据
    public delete = (record: State) => {
        this.api.delete(record)
            .then((res) => {
                if (res.success) {
                    this.refreshTable();
                }
            })
            .catch((e) => {
                console.error("删除失败", e);
            });
    }

    // 新增数据
    public create = async (record: State) => {
        const params = {
            ...record,
        }
        try {
            const res = await this.api.save(params);
            if (res.success) {
                this.refreshTable();
            }
        } catch (e) {
            console.error("保存失败", e);
        }

    }

    // 更新数据
    public update = async (record: State) => {
        try {
            const res = await this.api.update(record);
            if (res.success) {
                this.refreshTable();
            }
        } catch (e) {
            console.error("更新失败", e);
        }
    }

    // 表格数据加载函数
    public searchTable = async (params: any, sort: any, filter: any): Promise<any> => {
        const searchParams = {...params};
        return await this.api.loadTable(searchParams, sort, filter);
    }

    public getByCode = async (code: string): Promise<State> => {
        const data = await this.api.getById(code);
        console.log("data", data)
        return data.data;
    }

    // TagSelect数据加载函数
    public loadTagData = () => {
        this.api.loadTagData().then(data => {
            this.dispatch((prevState) => {
                // 确保 data 是数组，否则设为空数组
                const dataArray = Array.isArray(data.data?.list) ? data.data?.list : [];
                const selectData = this.getSelectOptions(dataArray);
                return {
                    ...prevState,
                    selectData: selectData,
                }
            })
        });
    }

    public getSelectOptions = (data: any[]): selectOption[] => {
        return data.map(item => ({
            label: item.name,
            value: item.code,
            children: item.children ? this.getSelectOptions(item.children) : undefined,
        }));
    };
}