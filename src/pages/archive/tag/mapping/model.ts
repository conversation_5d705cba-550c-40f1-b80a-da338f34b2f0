import {tree} from "@/api/archive/tag";
import {page, create, update, del, detail} from "@/api/archive/mapping";
import {Api} from "./types";


export class ApiImpl implements Api {
    loadTagData(): Promise<any> {
        return tree();
    }

    loadTable(params: any, sort: any, filter: any): Promise<any> {
        return page(params, sort, filter, [
            {
                key: "name",
                type: "LIKE",
            },
            {
                key: "comments",
                type: "LIKE",
            },
        ]);
    }

    async save(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await create(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async update(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await update(params);
            return {success: true, data: res};
        } catch (err) {
            return {success: false};
        }
    }

    async delete(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

    getById(params: any): Promise<any> {
        return detail(params);
    }
}