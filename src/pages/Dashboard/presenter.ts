/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-29 10:23:21
 * @Description:
 * @LastEditTime: 2025-08-01 14:06:14
 * @LastEditors: wangjiantao
 */

import { queryMenuPage } from "@/api/user/menu";
import { Api } from "./model";
import { Dispatch, IState } from "./types";

export class Presenter {
  //更新数据状态
  private readonly dispatch: Dispatch<IState>;

  private readonly api: Api;

  private state: IState;

  public constructor(dispatch: Dispatch<IState>, api: Api, state: IState) {
    this.dispatch = dispatch;
    this.api = api;
    this.state = state;
  }

  public syncState = (newStatus: IState) => {
    this.state = newStatus;
  };

  public init = (menus: any) => {
    this.dispatch((prev) => ({
      ...prev,
      activeMenu: menus?.children[0]?.id,
    }));
    this.changeChildrenMenus(menus?.children[0])
  };

  public handlerClickLeftMenu = (menu: {
    code: string;
    label: string;
    id: number;
  }) => {
    this.dispatch((prev) => ({
      ...prev,
      activeMenu: menu.id,
    }));
    this.changeChildrenMenus(menu);
  };

  public changeChildrenMenus = async (menu: {
    code: string;
    label: string;
    id: number;
  }) => {
    console.log(menu, 'menu');
    
    const { success, data } = await queryMenuPage(
      { parentNodeId: menu.id, pageSize: 50, current: 1 },
      {},
      {},
      []
    );
    if (!success) return;

    this.dispatch((prev) => ({
      ...prev,
      childrenMenus: [...data],
    }));
  };
}
