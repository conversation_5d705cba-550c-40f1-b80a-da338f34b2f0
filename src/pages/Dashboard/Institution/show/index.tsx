import { useEffect } from "react";
import * as echarts from "echarts";
import { ProCard } from "@ant-design/pro-components";
import '../../index.scss'
import { Space } from "antd";

export default function institution() {
  useEffect(() => {
    var myChart = echarts.init(document.getElementById("barChart"));

    var data = {
      xAxisData: [
        "贵阳",
        "遵义",
        "六盘水",
        "安顺",
        "毕节",
        "铜仁",
        "黔东南",
        "黔南",
        "黔西南",
        "贵安",
        "进出口",
        "烟科院",
        "投资",
        "复烤",
      ],
      seriesData: [94, 50, 13, 93, 81, 68, 50, 92, 50, 12, 93, 31, 82, 82],
    };

    var option = {
      title: {
        text: "",
        left: "left",
      },
      xAxis: {
        type: "category",
        data: data.xAxisData,
        axisLabel: {
          color: "#333",
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          color: "#333",
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
        splitLine: {
          lineStyle: {
            color: "#eee",
          },
        },
      },
      series: [
        {
          name: "考核数值",
          type: "bar",
          data: data.seriesData,
          barWidth: 15, // 调整柱子宽度（关键参数）
          itemStyle: {
            color: function () {
              var colorList = [
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#91c7ff" },
                  { offset: 1, color: "#49a2ff" },
                ]),
              ];
              return colorList[0];
            },
            borderRadius: 4,
          },
          label: {
            show: true,
            position: "top",
            color: "#333",
            fontSize: 12,
          },
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
    };

    myChart.setOption(option);
  }, []);

  useEffect(() => {
    // 初始化图表实例
    const myChart = echarts.init(document.getElementById("pieChart"));

    // 图表数据
    const data = [
      { value: 30, name: "党的建设(24.79%)" },
      { value: 17, name: "专卖管理(14.05%)" },
      { value: 21, name: "卷烟营销(17.36%)" },
      { value: 25, name: "综合管理(20.06%)" },
      { value: 28, name: "烟叶管理(23.14%)" },
    ];

    // 配置项
    const option = {
      title: {
        text: "",
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "normal",
        },
      },
      tooltip: {
        trigger: "item",
        formatter: "{b}: {c} ({d}%)", // 显示名称、数值和百分比
      },
      series: [
        {
          name: "制度分类",
          type: "pie",
          radius: "60%", // 饼图半径
          data: data,
          itemStyle: {
            borderRadius: 4, // 扇形圆角
            colors: [
              "#5B8FF9",
              "#5AD8A6",
              "#5D7092",
              "#F6BD16",
              "#6DC8EC",
              "#945FB9",
              "#FF9845",
              "#72CC4A",
              "#27727B",
            ],
          },
          label: {
            show: true,
            position: "outside",
            formatter: "{b|{b}}\n{per|{d}%}",
            rich: {
              b: {
                fontSize: 12,
                lineHeight: 20,
              },
              per: {
                color: "#666",
                fontSize: 11,
              },
            },
          },
        },
      ],
    };

    // 渲染图表
    myChart.setOption(option);

    // 响应窗口大小变化
    window.addEventListener("resize", () => myChart.resize());
  }, []);

  return (
    <>
      {/* <!-- 制度考核柱状图区域 --> */}
      <div style={{ display: "grid" }}>
        <ProCard title="制度考核" headerBordered>
          <div className="bar-chart" id="barChart" />
        </ProCard>

        <div className="stat-grid">
          <ProCard headerBordered title="制度分类" className="stat-module">
            <div className="pie-chart" id="pieChart" />
          </ProCard>
          <ProCard title="制度状态" className="status-container" bordered>
            {/* <!-- 表头 --> */}
            <div className="status-header">
              <span>类型</span>
              <div>
                <span>数量</span>
                <span>占比</span>
              </div>
            </div>
            {/* <!-- 内容列表 --> */}
            <div className="status-list">
              <div className="status-item">
                <div className="type">新制定</div>
                <div className="status-bar">
                  <div
                    className="status-bar-inner"
                    style={{ width: "11%" }}
                  ></div>
                </div>
                <div className="info">
                  <span>23</span>
                  <span>11%</span>
                </div>
              </div>
            </div>
          </ProCard>
        </div>
      </div>

      <ProCard
        title="学习排行"
        extra={
          <div className="ranking-header">
            <div className="ranking-title"></div>
            <Space size={24} className="tab-group">
              <div className="tab active">单位</div>
              <div className="tab">个人</div>
            </Space>
          </div>
        }
      >
        <div className="ranking-header">
          <div className="tab-group">
            <div className="tab active">总榜</div>
            <div className="tab">年榜</div>
            <div className="tab">月榜</div>
            <div className="tab">日榜</div>
          </div>
        </div>
        <table className="ranking-table">
          <thead>
            <tr>
              <th>排名</th>
              <th>姓名</th>
              <th>单位</th>
              <th>时长h</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <span className="rank-badge rank-1">1</span>
              </td>
              <td>
                <img
                  src="https://example.com/avatar1.jpg"
                  alt="avatar"
                  className="avatar"
                />
                昝新会
              </td>
              <td>贵阳市局(公司)</td>
              <td>40.5</td>
            </tr>
            <tr>
              <td>
                <span className="rank-badge rank-2">2</span>
              </td>
              <td>
                <img
                  src="https://example.com/avatar2.jpg"
                  alt="avatar"
                  className="avatar"
                />
                武丽环
              </td>
              <td>遵义市局(公司)</td>
              <td>39.8</td>
            </tr>
            <tr>
              <td>
                <span className="rank-badge rank-3">3</span>
              </td>
              <td>
                <img
                  src="https://example.com/avatar3.jpg"
                  alt="avatar"
                  className="avatar"
                />
                湛筠黛
              </td>
              <td>贵阳市局(公司)</td>
              <td>37.3</td>
            </tr>
            <tr>
              <td>4</td>
              <td>
                <img
                  src="https://example.com/avatar4.jpg"
                  alt="avatar"
                  className="avatar"
                />
                于良萍
              </td>
              <td>遵义市局(公司)</td>
              <td>36.7</td>
            </tr>
          </tbody>
        </table>
      </ProCard>
    </>
  );
}
