/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-31 14:33:10
 * @Description: 
 * @LastEditTime: 2025-08-01 13:45:44
 * @LastEditors: wangjiantao
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface IState {
  activeMenu: number | null;
    childrenMenus: { image: string; title: string, id: number, url: string }[];
}

export interface SupplierApi {}
