/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-29 10:23:31
 * @Description:
 * @LastEditTime: 2025-08-01 13:46:00
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-29 10:23:31
 * @Description:
 * @LastEditTime: 2025-07-29 10:37:58
 * @LastEditors: wangji<PERSON><PERSON>
 */
import React, { useEffect } from "react";
import { Presenter } from "../presenter";
import { Api } from "../model";
import { IState } from "../types";

const initialState: IState = {
  activeMenu: null,
  childrenMenus: [],
};

export function usePresenter() {
  const [state, dispatch] = React.useState<IState>(initialState);

  const presenterRef = React.useRef<Presenter | null>(null);

  // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
  if (!presenterRef.current) {
    presenterRef.current = new Presenter(dispatch, new Api(), state);
  }

  // 当 state 发生变化时，更新 presenter 的状态
  React.useEffect(() => {
    presenterRef.current?.syncState(state);
  }, [state]);

  return {
    state,
    presenter: presenterRef.current,
  };
}
