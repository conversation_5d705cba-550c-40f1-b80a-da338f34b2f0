/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-28 10:40:48
 * @Description:
 * @LastEditTime: 2025-08-01 13:46:27
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import Header from "@/layout/components/Header";
import "./index.scss";
import { ProCard } from "@ant-design/pro-components";
import { Popover } from "antd";
import { usePresenter } from "./hook/usePresenter";
import { Outlet, useNavigate } from "react-router-dom";
import { useMenuStore } from "@/store";
import { useEffect } from "react";

export default function Dashboard() {
  const { state, presenter } = usePresenter();
  const { dashboardMenus } = useMenuStore();
  const navigate = useNavigate();

  useEffect(() => 
    presenter.init(dashboardMenus),
    [])

  return (
    <div className="dashboard-container">
      <Header />
      <main className="content">
        {/* <!-- 左侧侧边栏 --> */}
        <ProCard className="sidebar">
          {(dashboardMenus.children || []).map((i: any) => {
            const imgSrc = (() => {
              try {
                return require(`@/assets/Images/dashboard/${i.image}`);
              } catch (e) {
                console.warn(`Image not found: ${i.image}`);
                return ""; // 或返回一个占位图片
              }
            })();

            return (
              <div
                className="sidebar-item"
                style={{
                  background: state.activeMenu === i.id ? "#E9F2FF" : "",
                }}
                key={i.id}
                onClick={() => presenter.handlerClickLeftMenu(i)}
              >
                <img src={imgSrc} />
                <span>{i.title}</span>
              </div>
            );
          })}
        </ProCard>

        {/* <!-- 主内容区域 --> */}
        <main className="main-content">
          {/* <!-- 顶部功能栏 --> */}
          <ProCard className="top-nav">
            {state.childrenMenus.map((i) => {
              const imgSrc = (() => {
                try {
                  return require(`@/assets/Images/dashboard/${i.image}`);
                } catch (e) {
                  console.warn(`Image not found: ${i.image}`);
                  return ""; // 或返回一个占位图片
                }
              })();
              return (
                <div className="top-nav-item" key={i.id} onClick={() => navigate(i.url)}>
                  <img src={imgSrc} />
                  <span>{i.title}</span>
                </div>
              );
            })}
            {/* <!-- 其他功能按钮... --> */}
          </ProCard>

          {/* <!-- 中间统计模块 --> */}
          <div className="stats-section">
            <Outlet />
            {/* <!-- 制度分类饼图 + 制度状态区域，用 Grid 布局分两列 --> */}
          </div>

          {/* <!-- 右侧排行区域 --> */}
        </main>
      </main>
    </div>
  );
}
