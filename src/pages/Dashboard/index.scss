/* 页面整体容器：侧边栏 + 主内容，用 Flex 布局 */
.dashboard-container {
  height: 100vh; /* 让页面占满视口高度 */
  overflow: hidden;

  .content {
    padding: 6px;
    background-color: #e5eaf7ff;
    display: flex;
    height: calc(100vh - 87px);
    overflow-y: auto;
  }

  .bar-chart {
    width: 100%;
    height: 260px;
  }

  .pie-chart {
    width: 100%;
    height: calc(100vh - 639.58px);
  }

  /* 容器样式：居中、限制宽度、留白 */
  .status-container {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06); /* 阴影增强层次 */
    margin-top: 6px;
    .ant-pro-card-body {
      padding: 20px 36px;
    }
  }

  /* 标题样式 */
  .status-container h2 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px; /* 与表头间距 */
  }

  /* 表头样式：背景色 + 文字对齐 */
  .status-header {
    display: flex;
    align-items: center;
    background-color: #E7F2FF;
    border-radius: 4px;
    padding: 4px 0;
    margin-bottom: 12px; /* 与内容间距 */
    justify-content: space-between;

    span {
      text-align: center; /* 文字居中 */
      color: #909399;
      font-size: 14px;
      display: inline-block;
      width: 80px;
    }
  }

  /* 内容列表：每行布局 */
  .status-list {
    display: flex;
    flex-direction: column;
    gap: 8px; /* 行间距 */
  }

  /* 每行内容：弹性布局，让进度条占满剩余空间 */
  .status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  /* 类型文字：固定宽度，避免跳动 */
  .status-item .type {
    width: 60px; /* 类型宽度，可调整 */
    text-align: right; /* 左对齐 */
    color: #606266;
    font-size: 14px;
    color: #2784fcff;
  }

  /* 数量 + 占比：右侧文字区域 */
  .status-item .info {
    display: flex;
    align-items: center;
    gap: 12px; /* 数量与占比间距 */
    color: #2784fcff;
    font-size: 14px;

    span {
      width: 75px;
      text-align: center;
    }
  }

  /* 进度条容器：占满中间空间 + 圆角 */
  .status-bar {
    flex: 1; /* 占满剩余宽度 */
    height: 8px; /* 进度条高度 */
    background-color: #e9e9e9;
    border-radius: 6px; /* 进度条圆角 */
    margin: 0 12px; /* 与文字间距 */
    overflow: hidden; /* 隐藏超出部分 */
  }

  /* 进度条填充：渐变颜色 + 动态宽度 */
  .status-bar-inner {
    height: 100%;
    border-radius: 6px;
    background: linear-gradient(90deg, #73fdff, #1677ff); /* 渐变蓝绿，可替换 */
    /* width 通过 style 动态设置，如 width: 23% */
  }
}

/* 左侧侧边栏 */
.sidebar {
  width: 140;
  background-color: #fff;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 0;

  .ant-pro-card-body {
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  span {
    color: #999;
    font-size: 18px;
    display: inline-block;
  }
}
.sidebar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 12px 6px 12px;
  cursor: pointer;
  transition: all 0.4s;
  border-radius: 6px;
  height: 16.666%;
}
.sidebar-item:hover {
  background-color: #e9f2ff;
}

/* 主内容区域，占满剩余宽度 */
.main-content {
  display: grid;
  gap: 6px;
  flex: 1;
  padding-left: 6px;
}

/* 顶部功能栏，用 Flex 横向排列按钮 */
.top-nav {
  height: 100px;
  .ant-pro-card-body {
    display: flex;
    .top-nav-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      width: 11.11%;
      cursor: pointer;

      img {
        width: 44px;
        height: 44px;
      }
      span {
        display: inline-block;
        font-weight: 500;
        margin-top: 5px;
      }
    }
  }
}
.nav-btn {
  padding: 8px 16px;
  background-color: #e6f0ff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}
.nav-btn:hover {
  background-color: #c9e2ff;
}

/* 统计模块通用样式 */
.stats-section {
  display: grid;
  grid-template-columns: 3fr 1fr; /* 两列等分 */
  flex-direction: column;
  gap: 6px;
}
.stat-module {
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-top: 6px;
}
.stat-module h3 {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.bar {
  width: 30px;
  background-color: #66b3ff;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  color: #fff;
  font-size: 12px;
  padding-bottom: 4px;
  border-radius: 4px 4px 0 0;
}

/* 饼图 + 状态表格区域，用 Grid 分两列 */
.stat-grid {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列等分 */
  gap: 6px;
}
.pie-chart {
  display: flex;
  justify-content: center;
}
.status-table {
  width: 100%;
  border-collapse: collapse;
}
.status-table th,
.status-table td {
  padding: 8px;
  border-bottom: none;
  text-align: left;
}

/* 右侧排行区域 */
.ranking-section {
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
.ranking-tabs {
  display: flex;
  gap: 10px;
}
.tab {
  background-color: #f5faff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.tab.active {
  background-color: #c9e2ff;
}
/* 容器样式，控制整体宽度和居中 */
.ranking-container {
  width: 360px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标题及切换栏样式 */
.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.ranking-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tab-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.tab {
  font-weight: 600;
  padding: 1px 20px;
  color: #9b9b9bff;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.tab.active {
  background-color: #e6f3ff;
  color: #4096ff;
}

/* 表格样式 */
.ranking-table {
  width: 100%;
  border-collapse: collapse;
}

.ranking-table thead {
  background-color: #f8f9fa;
}

.ranking-table th,
.ranking-table td {
  text-align: center;
  padding: 10px 6px;
  border-bottom: none;
  font-size: 14px;
  color: #9b9b9bff;
}

.ranking-table th {
  font-weight: 500;
  font-size: 12px;
  background-color: white;
}

/* 排名徽章样式，利用字体图标或背景图模拟，这里简单用颜色区分 */
.rank-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  font-size: 12px;
  margin-right: 4px;
}

.rank-1 {
  background-color: #ffd700;
}

.rank-2 {
  background-color: #c0c0c0;
}

.rank-3 {
  background-color: #cd7f32;
}

/* 头像样式，可替换为实际头像路径 */
.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 4px;
  vertical-align: middle;
}
