import {EnforcementCertficateApi, EnforcementCertficateState,Dispatch} from "@/pages/enforcementCertificate/types";

export class EnforcementCertficatePresenter {

    private readonly dispatch: Dispatch<EnforcementCertficateState>;

    private readonly api: EnforcementCertficateApi;

    private state: EnforcementCertficateState;
    public constructor(dispatch: Dispatch<EnforcementCertficateState>, api: EnforcementCertficateApi, state: EnforcementCertficateState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }
    public syncState = (newStatus: EnforcementCertficateState) => {
        this.state = newStatus;
    }
    // 号段表格数据加载函数
    public searchEnforcementCertficateTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params
        }
        return this.api.loadEnforcementCertficateTable(searchParams, sort, filter);
    }

    //修改新增模态框的显示状态
    public showEnforcementCertficateAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                EnforcementCertficateAddVisible: true
            }
        })
    }
    //修改新增模态框的隐藏状态
    public hideEnforcementCertficateAddModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                EnforcementCertficateAddVisible: false
            }
        })
    }
    //隐藏修改模态框
    public hideEnforcementCertficateEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                EnforcementCertficateEditVisible: false,
                currentEditEnforcementCertficate: null
            }
        })
    }
    //显示修改模态框
    public showEnforcementCertficateEditModal = (record: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                EnforcementCertficateEditVisible: true,
                currentEditEnforcementCertficate: record
            }
        })
    }
}