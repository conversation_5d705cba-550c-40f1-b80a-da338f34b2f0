import React from "react";
import {OpinionState} from "@/pages/institution/opinionSummery/types";
import {ValuePresenter} from "@/pages/institution/opinionSummery/presenter";
import {OpinionApiImpl} from "@/pages/institution/opinionSummery/model";

const initialState: OpinionState = {

}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<OpinionState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new OpinionApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}