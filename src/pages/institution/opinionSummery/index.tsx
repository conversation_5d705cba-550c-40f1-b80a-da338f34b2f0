import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import {queryDraftInstitutionDetail, queryOpinionItemList} from "@/api/institution/draft";
import dayjs from "dayjs";

// 定义样式
const Container = styled.div`
  width: 100%;
  overflow-x: auto;
`;


const Table = styled.table`
  width: calc(100% - 20px);
  border-collapse: collapse;
  margin-top: 10px;
  margin-left: 10px; 
  margin-right: 10px; 
`;

const Thead = styled.thead`
  background-color: #e8f0ea;
`;

const Tbody = styled.tbody``;
const Tr = styled.tr``;


const Th = styled.th`
  padding: 8px;
  text-align: center;
  border: 1px solid #ddd;
`;

const Td = styled.td`
  padding: 8px;
  border: 1px solid #ddd;
`;

const Button = styled.button`
  background-color: #327357;
  color: white;
  border: none;
  padding: 4px 8px;
  cursor: pointer;

  &:hover {
    background-color: #327357;
  }
`;

const RedText = styled.span`
  color: red;
`;


const OpinionTable: React.FC = () => {

    const [opinionItems, setOpinionItems] = useState<any[]>([]);

    useEffect(() => {

        const opinionParams = {
            draftId: 1
        }
        queryOpinionItemList(opinionParams).then((res: any) => {
            setOpinionItems(res?.data?.targetList)
        })

    }, []);

    const handleView = (id: number) => {
        console.log(`查看意见 ${id}`);
    };

    return (
        <Container>
            {/* 返回按钮 */}
            <Button style={{marginTop: '15px', marginLeft: '10px',}} onClick={() => console.log('返回')}>返回</Button>
            {/* 生成意见报告按钮 */}
            <Button style={{marginTop: '15px', marginRight: '10px', float: 'right'}}
                    onClick={() => console.log('生成意见报告')}>
                生成意见报告
            </Button>
            {/* 表格 */}
            <Table>
                <Thead>
                    <Tr>
                        <Th>序号</Th>
                        <Th>意见征求对象</Th>
                        <Th>反馈时间</Th>
                        <Th>意见征求对象反馈情况</Th>
                        <Th>操作</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    {opinionItems.map((item, index) => (
                        <Tr key={item.id}>
                            <Td style={{width: '50px', textAlign: 'center'}}>{index + 1}</Td>
                            <Td style={{
                                width: '250px',
                                textAlign: 'center'
                            }}>{item.unitName}/{item.deptName}/{item.username}</Td>
                            <Td style={{
                                width: '150px',
                                textAlign: 'center'
                            }}>{item.processFinishTime == null ? '' : dayjs(item.processFinishTime).format('YYYY.MM.DD HH:mm:ss')}</Td>
                            <Td>
                                {item.processFinishTime ? (
                                    item.itemList.map((feedbackItem: any, idx: number) => (
                                        <div key={idx}>
                                            <div style={{ fontWeight: 'bold' }}>{feedbackItem.selectedContent}</div>
                                            <div>{feedbackItem.content}</div>
                                            {idx < item.itemList.length - 1 && <br />}
                                        </div>
                                    ))
                                ) : (
                                    <RedText>未反馈</RedText>
                                )}
                            </Td>
                            <Td style={{width: '60px', textAlign: 'center'}}>
                                {item.processFinishTime ? (
                                    <Button
                                        style={{
                                            backgroundColor: 'transparent',
                                            color: '#327357',
                                            textAlign: 'center',
                                            display: 'inline-block',
                                            border: 'none',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => handleView(item.id)}
                                    >
                                        查看
                                    </Button>
                                ) : null}
                            </Td>
                        </Tr>
                    ))}
                </Tbody>
            </Table>
        </Container>
    );
};

export default OpinionTable;