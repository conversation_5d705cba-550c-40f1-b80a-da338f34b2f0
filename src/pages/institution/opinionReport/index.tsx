import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import dayjs from "dayjs";
import {useLocation, useNavigate} from "react-router-dom";
import {exportOpinionReport, queryOpinionReport} from "@/api/institution/draft";

// 定义样式
const Container = styled.div`
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
`;

const Title = styled.h2`
  text-align: center;
  margin-bottom: 20px;
`;

const Content = styled.div`
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap; // 保留空白符和换行符
  margin: 0 auto; // 在容器内居中显示
  max-width: 800px; // 设置最大宽度，可根据需要调整
  text-align: left; // 文本本身左对齐
`;

const Paragraph = styled.p`
  text-indent: 2em; // 每个段落首行缩进两格
  margin: 0; // 移除默认的上下外边距
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
`;

const Button = styled.button`
  background-color: #327357;
  color: white;
  border: none;
  padding: 4px 8px;
  cursor: pointer;

  &:hover {
    background-color: #327357;
  }
`;


const OpinionReport: React.FC = () => {
    const [reportData, setReportData] = useState<any>({
        title: "制度起草征求意见报告",
        content: ""
    });
    const navigate = useNavigate();
    const location = useLocation() as { state: { draftId: number } };
    const draftId = location.state.draftId;

    useEffect(() => {
        queryOpinionReport({draftId}).then((res: any) => {
            const data = {
                title: "制度起草征求意见报告",
                content: res.data,
            };
            setReportData(data);
        })
    }, []);

    // 将内容按换行符分割成多个段落
    const paragraphs = reportData.content ? reportData.content.split('\n').map((line, index) => (
        <Paragraph key={index}>{line}</Paragraph>
    )) : null;

    return (
        <Container>
            {/* 返回按钮 */}
            <ButtonContainer>
                <Button onClick={() => navigate(-1)}>返回</Button>
                <Button onClick={() => {
                    console.log('导出',draftId)
                    const params = {
                        draftId: draftId
                    }
                    exportOpinionReport(params)
                }}>导出</Button>
            </ButtonContainer>

            <Title>{reportData.title}</Title>

            <Content>
                {paragraphs}
            </Content>
        </Container>
    );
};

export default OpinionReport;
