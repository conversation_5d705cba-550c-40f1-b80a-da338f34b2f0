/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-09 08:38:19
 * @Description:
 * @LastEditTime: 2025-07-18 15:58:41
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-01 21:33:25
 * @Description:
 * @LastEditTime: 2025-07-09 17:53:44
 * @LastEditors: wangjiantao
 */
import { Button, Space, Tabs, Row } from "@itm/components-pc";
import { useEffect, useState, ChangeEvent, useMemo, Fragment } from "react";
import "./index.scss";
import { ExclamationCircleFilled, UserOutlined } from "@ant-design/icons";
import {
  Col,
  message,
  Modal,
  type TabsProps,
  Form as Aform,
  Divider,
} from "antd";
import GenericFormTable from "@/components/GenericFormTable";
import moment from "moment";
import {
  jointReview,
  queryContractById,
  recall,
  saveContractData,
} from "@/api/contract/contract";
import { File } from "@/components/contract/info";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { Attachment, ATTACHMENT_CONFIG, ISearchParams } from "../draft/types";
import { getWindowParams } from "@/utils/pageofficeParams";
import WordFunction from "@/components/pageOffice/WordFunctional";
import { submitDepGeneralTerm } from "@/api/contract/flow";
import OssFileUploader from "@/components/oss/OssFileUpload";
import { FormOption, ValidateUtils } from "@codingapi/ui-framework";
import { DictSelect } from "@/components/dict";
import FormUserChoiceSelect from "@/components/user/choice/FormUserChoiceSelect";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import { getNoticeRecordById } from "@/api/user/notice";
import CustomSplitter from "@/components/CustomSplitter";
import {FormInput, FormRadio, Form} from "@/components/form-pc";
import SupplierManagePage from "@/pages/supplier";
import { FlowImage } from "@/components/flowImage";

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  status: string;
  creator: string;
  createdAt: number;
  time?: string;
  isShow: boolean;
};

interface IProps extends ISearchParams {
  sourceName: string;
  depositRequired: boolean;
  performanceDeptName: string;
  categoryName: string;
  typeName: string;
  attachments: File[];
  advice?: string;
  process?: string;
  drafterName: string;
  name: string;
  draftTime: string;
  opinions: {
    advice?: string;
    deptName?: string;
    createTime?: number;
    id?: number;
    username: string;
  }[];
}

const options: FormOption[] = [
  { label: "是", value: "0" },
  { label: "否", value: "1" },
];

const _items: TabsProps["items"] = [
  {
    key: "1",
    label: "按部门汇总",
  },
  {
    key: "2",
    label: "按时间汇总",
  },
];

const FlowPageOffice = () => {
  // start 是否可编辑状态
  const [dataSource, setDataSource] = useState<IProps>({
    partyB: null,
    partyA: null,
    name: "",
    source: null,
    performanceDeptId: null,
    performanceAgentId: null,
    acceptanceDeptId: null,
    acceptanceAgentId: null,
    category: null,
    type: null,
    amount: 0,
    partyBName: "",
    _depositRequired: "0",
    draftName: localStorage.getItem("username") ?? "",
    draftTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    sourceName: "",
    depositRequired: false,
    performanceDeptName: "",
    categoryName: "",
    typeName: "",
    attachments: [],
    advice: "",
    process: "",
    drafterName: "",
    opinions: [],
  });
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const [userId, setUserId] = useState<string | null>("");
  const [userIds, setUserIds] = useState<string | null>("");
  const [record, setRecord] = useState<any>(null);
  const [fileKey, setFileKey] = useState<Record<string, string>>({});
  //start 上传附件
  const [uploadData, setUploadData] = useState<Attachment[]>([]);
  const form = Form.useForm();
  const [activeState, setActiveState] = useState<string>("1");
  const [flowUserSelectorVisible, setFlowUserSelectorVisible] = useState(false);

  const customParams = record?.customParams
    ? JSON.parse(record.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { recordId, contractId } = customParams;

  useEffect(() => {
    if (getWindowParams()) {
      const params = getWindowParams();
      localStorage.setItem("token", params.token);
      localStorage.setItem("username", params.username);
      setRecord(params.data);
    }
  }, []);

  useEffect(() => {
    const loadContractById = async () => {
      if (!contractId) return;
      const { data, success } = await queryContractById(contractId);
      if (!success) return;
      data._depositRequired = !data.depositRequired ? "1" : "0";
      data.draftTime = moment(data.draftTime).format("YYYY-MM-DD HH:mm:ss");

      setDataSource({ ...data });
      form.setFieldsValue(data);
      setUploadData([...data.attachments]);
      const _fileKey: Record<string, string> = {};

      data.attachments.forEach((i: any) => {
        if (fileKey[i.fileCategory] === undefined) fileKey[i.fileCategory] = "";
        fileKey[i.fileCategory] = i.fileKey;
      });
      setTimeout(() => {
        form.reloadOptions("performanceDeptId");
        form.reloadOptions("acceptanceDeptId");
      }, 100);
      if (JSON.stringify(_fileKey) === "{}") return;
      setFileKey(_fileKey);
      setTimeout(() => {
        form.reloadOptions("performanceDeptId");
        form.reloadOptions("acceptanceDeptId");
      }, 100);
    };
    loadContractById();

    const loadNoticeDetail = async () => {
      if (!record) return;
      await getNoticeRecordById(record.id);
    };
    loadNoticeDetail();
  }, [record]);

  /**
   * @description 打开送呈部门负责人
   * */

  const handlerOpenSubmitDepGeneralTerm = async () =>
    setFlowUserSelectorVisible(true);

  // 表格头数据
  const tableHeader = [
    {
      label: "起草人",
      value: dataSource.drafterName,
      isformLabel: false,
      isTimeCell: true,
    },
    {
      label: "起草时间",
      value: dataSource.draftTime,
      isformLabel: false,
      isTimeCell: false,
    },
  ];

  const onInputChange = (value: string, fieldName: string) => {
    if (fieldName === "partyA") {
      form.setFieldValue("acceptanceDeptId", null);
      form.setFieldValue("performanceDeptId", null);
      setDataSource((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          acceptanceDeptId: null,
          performanceDeptId: null,
        };
      });
    }

    setDataSource((prev) => ({
      ...prev!,
      [fieldName]: value,
    }));
  };

  const groupedByDept = dataSource?.opinions?.reduce(
    (acc: Record<string, any[]>, item) => {
      const { deptName, advice, createTime, id, username } = item;
      if (!acc[deptName!]) {
        acc[deptName!] = [];
      }
      acc[deptName!].push({
        advice,
        id,
        createTime: new Date(createTime!).toLocaleString(), // 格式化时间
        username,
      });
      return acc;
    },
    {} as Record<string, any[]>
  );

  // 表格数据
  const generateTableRows = () => {
    const commonFields = [
      {
        label: "合同名称",
        value: (
          <FormInput
            name="name"
            required
            validateFunction={ValidateUtils.validateNotEmpty}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              onInputChange(e.target.value, "name")
            }
          />
        ),
        isLabelRed: true,
      },
      {
        label: "合同甲方",
        value: (
          <FormOrgTreeSelect
            style={{ marginBottom: -25 }}
            validateFunction={ValidateUtils.validateNotEmpty}
            placeholder=""
            value={dataSource.partyA}
            name={"partyA"}
            query={{}}
            selectable={["UNIT"]}
            defaultExpandAll={true}
            onChange={(value, form) => {
              form?.setFieldValue("performanceDeptId", null);
              // 重新加载子字段的选项
              form?.reloadOptions("performanceDeptId");
              form?.setFieldValue("acceptanceDeptId", null);
              // 重新加载子字段的选项
              form?.reloadOptions("acceptanceDeptId");
              onInputChange(value, "partyA");
            }}
          />
        ),
        isLabelRed: true,
      },
      {
        label: "合同相对方",
        // partyB
        value: (
          <Aform.Item
            rules={[{ required: true, message: "请选择合同向对方" }]}
            name="partyB"
            style={{ marginBottom: 0 }}
          >
            <Row justify="space-between" style={{ padding: "0 12px" }}>
              <Col
                span={16}
                style={{
                  whiteSpace: "nowrap" /* 禁止换行 */,
                  overflow: "hidden" /* 超出部分隐藏 */,
                  textOverflow: "ellipsis",
                }}
              >
                {dataSource.partyBName}
              </Col>
              <Col span={8} style={{ textAlign: "right" }}>
                <Button type="primary" onClick={() => {
                  showDialog();
                  setVisible(true)
                }}>
                  选择供应商
                </Button>
              </Col>
            </Row>
          </Aform.Item>
        ),
        isLabelRed: true,
      },
      {
        label: "合同来源",
        value: (
          <DictSelect
            style={{ marginBottom: -25 }}
            validateFunction={ValidateUtils.validateNotEmpty}
            name="source"
            value={dataSource.source}
            labelCode="LeNgI92Gy8W"
            onChange={(value) => onInputChange(value, "source")}
          />
        ),
        isLabelRed: true,
      },
      {
        label: "履行单位/部门",
        colSpan: 3,
        value: (
          <FormOrgTreeSelect
            style={{ marginBottom: -25 }}
            placeholder=""
            validateFunction={ValidateUtils.validateNotEmpty}
            name="performanceDeptId"
            parentNodeFieldName={"partyA"}
            query={{ needDept: true }}
            selectable={["DEPT"]}
            onChange={(e: string) => onInputChange(e!, "performanceDeptId")}
          />
        ),
        valueType: (
          <Space size={1}>
            <span style={{ color: "red", marginRight: "20px" }}>经办人</span>
            <FormUserChoiceSelect
              style={{ marginBottom: 0 }}
              name="performanceAgentId"
              onChange={(e: string) => onInputChange(e!, "performanceAgentId")}
              value={dataSource.performanceAgentId || undefined}
            />
          </Space>
        ),
        isLabelRed: true,
      },
      {
        label: "合同验收部门",
        colSpan: 3,
        value: (
          <FormOrgTreeSelect
            validateFunction={ValidateUtils.validateNotEmpty}
            style={{ marginBottom: -25 }}
            placeholder=""
            name="acceptanceDeptId"
            parentNodeFieldName={"partyA"}
            query={{ needDept: true }}
            selectable={["DEPT"]}
            onChange={(e: string) => onInputChange(e!, "acceptanceDeptId")}
          />
        ),
        valueType: (
          <Space size={1}>
            <span style={{ color: "red", marginRight: "20px" }}>经办人</span>
            <FormUserChoiceSelect
              style={{ marginBottom: 0 }}
              validateFunction={ValidateUtils.validateNotEmpty}
              name="acceptanceAgentId"
              onChange={(e: string) => onInputChange(e!, "acceptanceAgentId")}
              value={dataSource.acceptanceAgentId || undefined}
            />
          </Space>
        ),
        isLabelRed: true,
      },
      {
        label: "标的类别",
        value: (
          <DictSelect
            style={{ marginBottom: -25 }}
            validateFunction={ValidateUtils.validateNotEmpty}
            labelCode="LeNgP003Tl2"
            value={dataSource.category}
            name="category"
            onChange={(value) => onInputChange(value, "category")}
          />
        ),
        isLabelRed: true,
      },
      {
        label: "合同类型",
        value: (
          <DictSelect
            validateFunction={ValidateUtils.validateNotEmpty}
            labelCode="LeNT58Sx4gS"
            value={dataSource.type}
            style={{ marginBottom: -25 }}
            name="type"
            onChange={(value) => onInputChange(value, "type")}
          />
        ),
        isLabelRed: true,
      },
      {
        label: "合同金额（万元）",
        value: (
          <Aform.Item required name="amount" style={{ marginBottom: 0 }}>
            <FormInput
              name="amount"
              validateFunction={ValidateUtils.validateNotEmpty}
              inputType="number"
              stepperMinNumber={0}
              sliderMinNumber={0}
              onChange={(e) => onInputChange(String(e), "amount")}
            />
          </Aform.Item>
        ),
        isLabelRed: true,
      },
      {
        label: "是否需缴纳履约保证金",
        value: (
          <div style={{ paddingLeft: 12 }}>
            <FormRadio
              validateFunction={ValidateUtils.validateNotEmpty}
              name="_depositRequired"
              options={options}
            />
          </div>
        ),
        isLabelRed: true,
      },
    ];

    const attachmentFields = {
      label: "附件",
      children: ATTACHMENT_CONFIG[dataSource.source || ""]?.map((item: any) =>
        renderAttachmentItem(item)
      ),
      isLabelRed: true,
    };

    const t = {
      label: "已办意见",
      value: (
        <div className="opinions_already_processed">
          <Tabs defaultActiveKey="1" items={_items} onChange={setActiveState} />
          {activeState == "1" ? (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {Object.entries(groupedByDept || []).map(
                ([deptName, opinions]) => (
                  <div className="opinions_content" key={deptName}>
                    <div>
                      <div>【{deptName}】</div>
                      {opinions.map((opinion: any, index: number) => (
                        <div
                          className="approval-item"
                          key={index}
                          style={{ border: "none" }}
                        >
                          <div className="approval-header">
                            <UserOutlined
                              style={{ color: "var(--primary-color)" }}
                            />
                            [审批人]{opinion.username}: {opinion.advice}
                          </div>
                          <div className="approval-time">
                            {moment(opinion.createTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )}
                          </div>
                        </div>
                      ))}
                      <Divider size="small" />
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {dataSource?.opinions.map((i, index) => (
                <Fragment key={index}>
                  <div className="opinions_content">
                    <div>【{i.deptName}】</div>
                    <div className="approval-item" key={index}>
                      <div className="approval-header">
                        <UserOutlined
                          style={{ color: "var(--primary-color)" }}
                        />
                        [审批人]{i.username}: {i.advice}
                      </div>
                      <div className="approval-time">
                        {moment(i.createTime).format("YYYY-MM-DD HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                </Fragment>
              ))}
            </div>
          )}
        </div>
      ),
      isLabelRed: true,
    };

    return [...commonFields, attachmentFields, t];
  };

  const renderAttachmentItem = ({
    label,
    key,
    fileCategory,
  }: {
    label: string;
    key: string;
    fileCategory: string;
  }) => ({
    label,
    value: (
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <Space direction="vertical">
          {uploadData.map((i) => (
            <a key={i.id} style={{ opacity: 0, fontSize: 12 }}>
              {i.fileName}
            </a>
          ))}
        </Space>
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <OssFileUploader
            label="上传附件"
            uploaderAccept="image/jpeg,image/jpg,application/pdf"
            uploaderMaxCount={5}
            bucket="document"
            ossFileKey={fileKey[fileCategory] || ""}
            onKeyChange={(key: string) => {
              if (fileKey[fileCategory] === undefined)
                fileKey[fileCategory] = "";
              fileKey[fileCategory] = key;
              setFileKey({ ...fileKey });
              setUploadData([
                ...uploadData,
                { fileKey: key, fileCategory, fileName: "", id: Date.now() },
              ]);
            }}
          />
        </div>
      </div>
    ),
  });

  /**
   * @description 保存表格信息
   * */
  const handleSave = async () => {
    form.validate().then(async (res) => {
      if (res) {
        const values = form.getFieldsValue();
        if (!values) return;
        try {
          interface newDataSource extends ISearchParams {
            depositRequired?: boolean;
            attachments?: Attachment[];
          }

          let params: newDataSource = values as ISearchParams;
          params.depositRequired =
            params._depositRequired === "0" ? true : false;
          params.attachments = uploadData;

          params = {
            ...dataSource,
            ...params,
            partyB: dataSource!.partyB,
          };

          // 调用接口保存数据
          const { success } = await saveContractData(params);

          if (!success) return;
        } catch (err) {}
      }
    });
  };

  const tableRows = useMemo(
    () => generateTableRows(),
    [dataSource, uploadData, activeState]
  );

  const [isShow, setIsShow] = useState<boolean>(true);
  const [activeKey, setActiveKey] = useState<string>("1");
  const onChange = (key: string) => {
    setActiveKey(key);
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  /**
   * @description  送呈部门负责人接口
   * */
  const handlerRegulatoryWithJointReview = async () => {
    setLoading(true);
    const params = {
      contractId,
      userId: Number(userId)!,
      recordId,
    };
    await handleSave();
    const { success } = await submitDepGeneralTerm(params);
    setLoading(false);
    if (!success) return;
    message.success("送呈部门负责人成功");
    setFlowUserSelectorVisible(false);
    setTimeout(() => {
      window.pageofficectrl?.CloseWindow();
    }, 1000);
  };

  /**
   * @description 发起会审
   * */
  const handlerApproval = async () => {
    setLoading(true);
    const params = {
      contractId: contractId,
      userIds: (userIds || "").split(","),
      recordId,
    };
    await handleSave();
    const { success } = await jointReview(params);
    setLoading(false);
    if (!success) return;
    message.success("发往会审成功");
    setVisible(false);
    setTimeout(() => {
      window.pageofficectrl?.CloseWindow();
    }, 1000);
  };

  /**
   * @description 撤回
   * */

  const handlerRecall = () => {
    showDialog();
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要撤回吗?",
      async onOk() {
        const params = {
          contractId,
          advice: dataSource.advice,
          recordId,
        };
        const { success } = await recall(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          window.pageofficectrl?.CloseWindow();
        }, 1000);
      },
      onCancel() {
        hideDialog();
      },
    });
  };

  useEffect(() => {
    window.pageofficectrl.Enabled = activeKey === "1";
  }, [activeKey]);

  const showDialog = () => {
    window.pageofficectrl.Enabled = false;
  };

  const hideDialog = () => {
    window.pageofficectrl.Enabled = true;
  };

  return (
    <>
      <div className="sixContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <Space>
          <Button
            color="danger"
            variant="solid"
            onClick={() => {
              handlerRecall();
              showDialog();
            }}
          >
            撤回
          </Button>
          <Button
            type="primary"
            onClick={() => {
              showDialog();
              setShow(true);
            }}
          >
            发往会审
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handlerOpenSubmitDepGeneralTerm();
              showDialog();
            }}
          >
            送呈部门负责人
          </Button>
        </Space>
      </div>
      <div className="sixContractDrafting_body_box">
        {activeKey == "1" && (
          <Form form={form}>
            <CustomSplitter
              left={<WordFunction fileId={contractId} />}
              right={
                <div className={`compliance_right_box`}>
                  <GenericFormTable header={tableHeader} rows={tableRows} />
                </div>
              }
            />
          </Form>
        )}
        {activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>

      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={show}
        cancelText="取消"
        okText="确定"
        confirmLoading={loading}
        onCancel={() => {
          setShow(false);
          hideDialog();
        }}
        onOk={() => {
          hideDialog();
          handlerApproval();
        }}
      >
        <UserChoiceSelect
          multiple
          onChange={setUserIds}
          value={userIds || ""}
        />
      </Modal>

      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={flowUserSelectorVisible}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          setVisible(false);
          hideDialog();
        }}
        confirmLoading={loading}
        onOk={() => {
          handlerRegulatoryWithJointReview();
          hideDialog();
        }}
      >
        <UserChoiceSelect
          onChange={(e: string | null) => setUserId(e)}
          value={userId || undefined}
        />
      </Modal>

      <Modal
        title="选择供应商"
        open={visible}
        okText="选择"
        cancelText="取消"
        className={"flow-modal"}
        onOk={() => setVisible(false)}
        onCancel={() => {
          if (activeKey === "2") hideDialog();
          setVisible(false);
        }}
      >
        <SupplierManagePage
          isComponent
          onSelectedChange={(id: number, name: string) => {
            onInputChange(String(id), "partyB");
            onInputChange(name, "partyBName");
            form.setFieldValue("partyB", id);
          }}
        />
      </Modal>
    </>
  );
};
export default FlowPageOffice;
