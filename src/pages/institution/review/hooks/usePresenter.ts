import React from "react";
import {ReviewState} from "@/pages/institution/review/types";
import {ValuePresenter} from "@/pages/institution/review/presenter";
import {ReviewApiImpl} from "@/pages/institution/review/model";

const initialState: ReviewState = {

}

export function useReviewPresenter() {
    const [state, dispatch] = React.useState<ReviewState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new ReviewApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}