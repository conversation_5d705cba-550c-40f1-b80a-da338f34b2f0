import {<PERSON>ton, Message, Space, Tabs} from "@/components-pc";
import React, {Fragment, useEffect, useRef, useState} from "react";
import "./index.scss";
import {ExclamationCircleFilled, FormOutlined,} from "@ant-design/icons";
import {message, Modal, type TabsProps} from "antd";
import {NoticeFormProps} from "@/components/user/notice/NoticeModalView";
import CustomSplitter from "@/components/CustomSplitter";
import {Form, FormInput} from "@/form-pc";
import {ValidateUtils} from "@/ui-framework";
import {FlowImage} from "@/components/flowImage";
import {
    addOrUpdateItem,
    deleteItem,
    opinionRecallFlow,
    opinionSubmitFlow,
    queryDraftInstitutionDetail,
    showHtml
} from "@/api/institution/draft";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {SelectableUser} from "@/components/user/choice/type";
import SelectorMenu from "@/pages/archive/institution/search/detail/components/SelectorMenu";
import {useOpinionPresenter} from "@/pages/institution/opinion/hooks/usePresenter";
import {DraftReviewInfo} from "@/components/institution/draft/reviewInfo";

interface StepItem {
    title: string;
    description: string;
    component: React.ReactNode;
    ref: React.RefObject<any>;
    showWhen: (current: number) => boolean;
}

// 定义组件状态接口
interface ComponentState {
    isShow: boolean;
    activeKey: string;
    current: number;
    isEdit: boolean;
}

const InstitutionDraftReview: React.FC<NoticeFormProps> = (props) => {
    // 初始化状态
    const [activeState, setActiveState] = useState<ComponentState>({
        isShow: true,
        activeKey: "1",
        current: 4,
        isEdit: true,
    });
    const onChange = (key: string) => {
        setActiveState((pre) => ({...pre, activeKey: key}));
    };
    const items: TabsProps["items"] = [
        {
            key: "1",
            label: "正文",
        },
        {
            key: "2",
            label: "流程",
        }

    ];
    const draftingInfoRef = useRef<any>(null);

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            nodeCode: "",
            draftId: 0,
            targetId: 0,
            institutionName: "",
        };
    const draftId = customParams.draftId;
    const targetId = customParams.targetId;

    const {state, presenter} = useOpinionPresenter();

    const [content, setContent] = useState<string>("");

    const [institution, setInstitution] = useState<any>({});

    const [submitVisible, setSubmitVisible] = useState<boolean>(false);

    const [selectedUsers, hideSelectUserModal] = useState<any[]>([]);

    const [rejectVisible, setRejectVisible] = useState(false);

    const [passVisible, setPassVisible] = useState(false);

    const htmlRef = useRef<HTMLDivElement>(null);

    const containerRef = useRef<HTMLDivElement>(null);

    const form = Form.useForm();

    useEffect(() => {

    }, []);

    useEffect(() => {
        const htmlParams = {
            id: draftId
        };
        showHtml(htmlParams).then((response: any) => {
            setContent(response.data);
        });
        const draftParams = {
            id: draftId
        }
        queryDraftInstitutionDetail(draftParams).then((res: any) => {
            setInstitution(res.data);
        });
        presenter?.loadCommentList(draftId, targetId);
    }, []);

    const stepsData: StepItem[] = [
        {
            title: "制度基础信息",
            description: "制度基础信息",
            component: <DraftReviewInfo ref={draftingInfoRef} draft={institution} nodeCode={customParams.nodeCode}
                                        draftId={draftId} targetId={targetId} itemList={state.itemList}/>,
            ref: draftingInfoRef,
            showWhen: (current) => current >= 0,
        },
    ];


    /**
     * @description 撤回
     * */

    const handlerRecall = () => {
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要撤回吗?",
            async onOk() {
                const params = {
                    recordId: customParams.recordId,
                };
                const {success} = await opinionRecallFlow(params);
                if (!success) return;
                message.success("提交成功");
                setTimeout(() => {
                    props.setVisible(false);
                }, 1000);
            },
        });
    };


    return (
        <>
            <div className="sevenContractDrafting_header_box">
                <div className="tab_item">
                    <Tabs
                        className="item_tabs"
                        defaultActiveKey={activeState.activeKey}
                        items={items}
                        onChange={onChange}
                    />
                </div>
                <div>

                    {customParams.nodeCode == "opinion" && (
                        <Space>
                            <Button color="danger" variant="solid" onClick={handlerRecall}>
                                撤回
                            </Button>
                            <Button type="primary" onClick={() => {
                                setSubmitVisible(true)
                            }}>
                                送呈部门负责人
                            </Button>
                        </Space>
                    )}
                    {customParams.nodeCode == "dept_review" && (
                        <Space>
                            <Button
                                color="danger"
                                variant="solid"
                                onClick={() => setRejectVisible(true)}
                            >
                                退回修改
                            </Button>
                            <Button color="danger" variant="solid" onClick={handlerRecall}>
                                撤回
                            </Button>
                            <Button type="primary" onClick={() => {
                                setSubmitVisible(true)
                            }}>
                                送呈分管领导
                            </Button>
                        </Space>
                    )}
                    {customParams.nodeCode == "cantonal_leader" && (
                        <Space>
                            <Button
                                color="danger"
                                variant="solid"
                                onClick={() => setRejectVisible(true)}
                            >
                                退回修改
                            </Button>
                            <Button type="primary" onClick={() => {
                                setPassVisible(true)
                            }}>
                                同意上报
                            </Button>
                        </Space>
                    )}
                </div>
            </div>
            <div></div>
            <div className="sevenContractDrafting_body_box">
                {activeState.activeKey == "1" && (
                    <>
                        <CustomSplitter
                            left={
                                <div
                                    ref={containerRef}
                                    style={{
                                        display: "flex",
                                        width: "100%",
                                        overflow: "auto",
                                    }}
                                >
                                    <div>
                                        <div
                                            id="html-container"
                                            ref={htmlRef}
                                            style={{
                                                transform: `scale(1.0)`,
                                                width: "790px",
                                                margin: "0 auto",
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: content,
                                            }}
                                        ></div>
                                    </div>
                                    {customParams.nodeCode == "opinion" && (
                                        <SelectorMenu
                                            initialAnnotations={state.annotations}
                                            actionTypes={[
                                                {
                                                    text: "反馈意见",
                                                    icon: <FormOutlined/>,
                                                }
                                            ]}
                                            htmlRef={htmlRef}
                                            parentContainerRef={containerRef}
                                            onChange={(value) => {
                                                console.log(value);
                                                const _body = {
                                                    pid: value.paragraphId,
                                                    selectedContent: value.selectedContent,
                                                    content: value.commentContent,
                                                    selectedStartIndex: value.startIndex,
                                                    selectedPosition: JSON.stringify(value),
                                                    draftId: draftId,
                                                    targetId: targetId,
                                                    id: value.id,
                                                };
                                                addOrUpdateItem(_body)
                                                presenter?.loadCommentList(draftId, targetId)
                                            }
                                            }
                                            onRemoveCallback={(annotationId) => {
                                                const body = {
                                                    id: annotationId,
                                                }
                                                deleteItem(body)
                                                presenter?.loadCommentList(draftId, targetId)
                                            }
                                            }
                                        />
                                    )}
                                </div>
                            }
                            style={{padding: 24}}
                            right={
                                <div className={`compliance_right_box`}>
                                    {stepsData.map((step, index) => (
                                        <Fragment key={index}>
                                            {step.showWhen(activeState.current) && step.component}
                                        </Fragment>
                                    ))}
                                </div>
                            }
                        />
                    </>
                )}
                {activeState.activeKey == "2" && <FlowImage recordId={customParams.recordId}/>}
            </div>
            <UserChoiceModal
                visible={submitVisible}
                hide={() => {
                }}
                onFinish={async (records: SelectableUser[]) => {
                    hideSelectUserModal(records)
                    console.log(records);
                    const body = {
                        draftId: draftId,
                        targetId: targetId,
                        recordId: customParams.recordId,
                        reviewerId: records[0]?.id,
                        institutionName: institution.name,
                        success: true,
                    };
                    opinionSubmitFlow(body).then((res) => {
                        Message.success("提交成功")
                    })
                    setSubmitVisible(false)
                }}
                selectedUsers={selectedUsers}
            />
            <Modal
                title="同意上报"
                width={300}
                centered
                open={passVisible}
                cancelText="取消"
                okText="确定"
                onCancel={() => setPassVisible(false)}
                onOk={async () => {
                    const body = {
                        draftId: draftId,
                        targetId: targetId,
                        recordId: customParams.recordId,
                        institutionName: institution.name,
                        success: true,
                    };
                    opinionSubmitFlow(body).then((res) => {
                        Message.success("提交成功")
                        setPassVisible(false)
                    })
                }}
            />
            <Modal
                title="退回修改"
                width={300}
                centered
                open={rejectVisible}
                cancelText="取消"
                okText="确定"
                onCancel={() => setRejectVisible(false)}
                onOk={async () => {
                    const body = {
                        draftId: draftId,
                        targetId: targetId,
                        recordId: customParams.recordId,
                        institutionName: institution.name,
                        success: false,
                    };
                    opinionSubmitFlow(body).then((res) => {
                        Message.success("提交成功")
                        setRejectVisible(false)
                    })
                }}
            >
                <Form form={form}>
                    <FormInput
                        name="advice"
                        label="退回原因"
                        validateFunction={ValidateUtils.validateNotEmpty}
                    />
                </Form>
            </Modal>
        </>
    );
};
export default InstitutionDraftReview;
