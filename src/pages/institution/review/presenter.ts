import {<PERSON><PERSON><PERSON>, ReviewState, Dispatch} from "@/pages/institution/review/types";

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<ReviewState>;

    private readonly api: ReviewApi;

    private state: ReviewState;

    public constructor(dispatch: Dispatch<ReviewState>, api: ReviewApi, state: ReviewState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: ReviewState) => {
        this.state = newStatus;
    }


}