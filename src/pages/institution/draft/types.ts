import { NamePath } from "rc-field-form/es/interface";
import { FormInstance } from "@codingapi/ui-framework/dist/Form/instance";
import moment from "moment/moment";

export const ATTACHMENT_CONFIG: Record<
  string,
  Array<{ label: string; key: string; fileCategory: string }>
> = {
  VeNgJwCDaIy: [
    { label: "采购文件", key: "purchase", fileCategory: "VeNp1ked1iC" },
    { label: "中标人投标文件", key: "bid", fileCategory: "VeNp4tRQdn6" },
    { label: "中标通知书", key: "notice", fileCategory: "VeVRkxM4RmK" },
    { label: "相关附件", key: "other", fileCategory: "VeVRsuuFf1M" },
  ],
  VeNgKk4VhJo: [
    { label: "相关附件", key: "other", fileCategory: "VeVRsuuFf1M" },
  ],
  VeNgLPIt8kq: [
    { label: "谈判纪要", key: "meeting", fileCategory: "VeVSK5c725w" },
    { label: "请示批准文件", key: "approval", fileCategory: "VeVSLA74eP2" },
    { label: "相关附件", key: "other", fileCategory: "VeVRsuuFf1M" },
  ],
  VeNgM4UiC1I: [
    { label: "相关结果报告", key: "report", fileCategory: "VeVSLjib4ym" },
    { label: "相关附件", key: "other", fileCategory: "VeVRsuuFf1M" },
  ],
};

export interface OrgTreeQuery {
  // 父节点id，如果传入，则从这个节点开始加载
  parentNodeId?: string;
  // 是否需要部门数据
  needDept?: boolean;
  // 是否需要岗位数据
  needPost?: boolean;
}

export interface Attachment {
  fileCategory: string;
  fileKey: string;
  fileName?: string,
  id?: number,
}

// 参数定义
export interface UserChoiceSelectProps {
  onChange?: (value: any, data: any) => void;
  disabled?: boolean;
  value?: string; // 多选时，用英文逗号分隔id
  multiple?: boolean;
}

export enum SelectableOrgType {
  UNIT = "UNIT",
  DEPT = "DEPT",
  POST = "POST",
}

export interface OrgTreeSelectProps {
  value?: string | null;
  onChange?: (value: string | null, data: any[], from?: FormInstance) => void;
  disabled?: boolean;
  multiple?: boolean;

  query: OrgTreeQuery;
  // 当需要联动父节点时必传，当前字段的名称
  fieldName?: NamePath;
  // 当需要联动父节点时必传，当前字段的父节点名称
  parentNodeFieldName?: NamePath;
  // 当需要联动父节点时必传，表单实例
  form?: FormInstance;
  // 可选的组织类型范围
  selectable: SelectableOrgType[];

  defaultExpandAll?: boolean;
}

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  status: string;
  creator: string;
  createdAt: number;
  time?: string;
  isShow: boolean;
};

export interface ISearchParams {
  type: number,
  name: string,
  institutionAbolishEnable: boolean,
  tagList: any[],
  abolishInstitutionIdList: any[],
  opinionEnable: boolean,
  opinionEndTime: string |  null,
  opinionExplain: string,
  opinionTargetList: any[],
  drafterId: number,
  draftTime: string,
  id?: number;
}

export type IDataSource = {
  searchParams: ISearchParams;
  isEdit: boolean;
};