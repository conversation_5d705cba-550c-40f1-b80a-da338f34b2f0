import {<PERSON><PERSON>, <PERSON>, Tabs, Modal, Message} from "@itm/components-pc";
import React, {ChangeEvent, useEffect, useMemo, useRef} from "react";
import "./index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import {FormOption, ValidateUtils} from "@codingapi/ui-framework";
import {Col, DatePicker, Drawer, Form as AForm, Row} from "antd";
import {Form, FormDate, FormInput, FormRadio} from "@/components/form-pc";
import {usePresenter} from "./hook/usePresenter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import LogicFlow from "@logicflow/core";
import {getWindowParams} from "@/utils/pageofficeParams";
import {ATTACHMENT_CONFIG, IDataSource} from "./types";
import {ProCard} from "@ant-design/pro-components";
import FormItem from "antd/es/form/FormItem";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {SelectableUser} from "@/components/user/choice/type";
import TagSelectorWithoutSave from "@/components/archive/tagSelectorWithoutSave";
import InstitutionSearchPage from "@/pages/archive/institution/search/list";
import {useNavigate} from "react-router-dom";
import {POBrowser} from "js-pageoffice";
import {downloadArchiveFile, downloadOssFile} from "@/utils/download";

const Draft = (props: any) => {

    const navigate = useNavigate();

    const {
        form,
        tableHeader,
        tabs,
        dataSource,
        onInputChange,
        tags,
        setTags,
        tagVisible,
        setTagVisible,
        selectedInstitutions,
        setSelectedInstitution,
        institutionVisible,
        setInstitutionVisible,
        optionTargetVisible,
        setOptionTargetVisible,
        optionVisible,
        setOptionVisible,
        selectUsers,
        hideSelectUserModal,
        uploadData,
        setTemplateVisible,
        activeKey,
        setActiveKey,
        handleSave,
        handlerOpenSubmitDepGeneralTerm,
        setFlowParams,
        flowParams,
        flowUserSelectorVisible,
        setFlowUserSelectorVisible,
        handlerSubmitDepGeneralTerm,
        poHtmlCode,
        showDialog,
        hideDialog,
        show,
        setShow,
        userIds,
        setUserIds,
        handlerOpinionStartFlow,
        handlerSaveToDraft,
        loading,
    } = usePresenter();


    const [url, setUrl] = React.useState<string>("");

    const container = React.useRef<HTMLDivElement>(null);

    // 表格数据
    const generateTableRows = (dataSource: IDataSource) => {
        const commonFields = [
            {
                label: "起草类型",
                value: (
                    <div style={{paddingLeft: 12}}>
                        <FormRadio
                            name="type"
                            validateFunction={async (content) => {
                                const value = content.value;
                                if (value) {
                                    return [];
                                }
                                return ["{不能为空"];
                            }}
                            options={[
                                {label: "新制定", value: "1"},
                                {label: "修订", value: "2"},
                            ]}
                        />
                    </div>
                ),
                isLabelRed: true,
            },
            {
                label: "制度名称",
                value: dataSource.isEdit ? (
                    <FormInput
                        name="name"
                        required
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["制度名称不能为空"];
                        }}
                        onChange={(e: ChangeEvent<HTMLInputElement>) =>
                        onInputChange(e.target.value, "name")
                    }
                    />
                ) : (
                    dataSource.searchParams.name || ""
                ),
                isLabelRed: true,
            },
            {
                label: "制度标签",
                value: (
                    <AForm.Item
                        name="tags"
                        rules={[{required: true, message: "请选择标签"}]}
                    >
                        <Row justify="space-between" align="middle" style={{padding: "0 12px"}}>
                            <Col span={21} style={{display: "flex", alignItems: "center"}}>
                                {Array.isArray(tags) ? tags.map((tag: any) => tag.name).join(", ") : ""}
                            </Col>
                            {dataSource.isEdit && (
                                <Col span={3} style={{textAlign: "right"}}>
                                    <Button type="primary" onClick={() => setTagVisible(true)}>
                                        点击维护
                                    </Button>
                                </Col>
                            )}
                        </Row>
                    </AForm.Item>
                ),
                isLabelRed: true,
            },
            {
                label: "废止原制度或相关制度",
                value: (
                    <div style={{ paddingLeft: 12 }}>
                        <Row gutter={8} align="middle">
                            <Col span={6}>
                                <FormRadio
                                    name="institutionAbolishEnable"
                                    onChange={value =>
                                        onInputChange(value, "institutionAbolishEnable")
                                    }
                                    validateFunction={async (content) => {
                                        const value = content.value;
                                        if (value) {
                                            return [];
                                        }
                                        return ["{不能为空"];
                                    }}
                                    options={[
                                        { label: "是", value: 'true' },
                                        { label: "否", value: 'false' },
                                    ]}
                                />
                            </Col>
                            <Col span={18} style={{ textAlign: "right" }}>
                                {form.getFieldValue("institutionAbolishEnable") == 'true' && dataSource.isEdit && (
                                    <Button
                                        type="primary"
                                        size="small"
                                        onClick={() => setInstitutionVisible(true)}
                                    >
                                        选择相关制度
                                    </Button>
                                )}
                            </Col>
                        </Row>
                        {form.getFieldValue("institutionAbolishEnable") == 'true' && (
                            <div style={{ padding: "12px 0" }}>
                                {selectedInstitutions.map((institution: any, index: number) => (
                                    <Row key={institution.id} gutter={8} style={{ marginBottom: 8, alignItems: "center" }}>
                                        <Col span={20}>
                                        <span
                                            style={{
                                                color: 'blue',
                                                cursor: 'pointer',
                                                textDecoration: 'underline'
                                            }}
                                            onClick={() => {
                                                downloadArchiveFile(institution.originFileKey);
                                            }}
                                        >
                                          {institution.name}
                                        </span>
                                        </Col>
                                        {dataSource.isEdit && (
                                            <Col span={4} style={{ textAlign: "right" }}>
                                                <Button
                                                    type="text"
                                                    danger
                                                    onClick={() => {
                                                        // 删除选中的制度
                                                        const newSelectedInstitutions = selectedInstitutions.filter(
                                                            (item: any) => item.id !== institution.id
                                                        );
                                                        setSelectedInstitution(newSelectedInstitutions);
                                                        const idList = newSelectedInstitutions.map((inst: any) => inst.id);
                                                        form.setFieldValue('abolishInstitutionIdList', idList);
                                                    }}
                                                >
                                                    删除
                                                </Button>
                                            </Col>
                                        )}
                                    </Row>
                                ))}
                            </div>
                        )}
                    </div>
                ),
                isLabelRed: true,
            },
            {
                label: "征求意见",
                value: (
                    <div style={{paddingLeft: 12}}>
                        <FormRadio
                            name="opinionEnable"
                            onChange={value =>
                                onInputChange(value, "opinionEnable")
                            }
                            validateFunction={async (content) => {
                                const value = content.value;
                                if (value) {
                                    return [];
                                }
                                return ["{不能为空"];
                            }}
                            options={[
                                {label: "是", value: 'true'},
                                {label: "否", value: 'false'},
                            ]}
                            defaultValue="false"
                        />
                    </div>
                ),
                isLabelRed: true,
            },
            // 征求意见截至时间 - 根据 opinionEnable 控制显示
            ...(form.getFieldValue("opinionEnable") == 'true' ? [{
                label: "征求意见截至时间",
                value: (
                    <div style={{paddingLeft: 12}}>
                        <FormItem name="opinionEndTime">
                            <DatePicker
                                style={{width: "15%"}}
                            />
                        </FormItem>
                    </div>
                ),
                isLabelRed: true,
            }] : []),
            // 征求意见说明 - 根据 opinionEnable 控制显示
            ...(form.getFieldValue("opinionEnable") == 'true' ? [{
                label: "征求意见说明",
                value: (
                    <div style={{paddingLeft: 12}}>
                        <FormInput name="opinionExplain">
                        </FormInput>
                    </div>
                ),
                isLabelRed: true,
            }] : []),
            // 征求意见对象 - 根据 opinionEnable 控制显示
            ...(form.getFieldValue("opinionEnable") == 'true' ? [{
                label: "征求意见对象",
                value: (
                    <AForm.Item
                        name="opinionTargetList"
                        rules={[{required: true, message: "请选择征求对象"}]}
                    >
                        <Row justify="space-between" style={{padding: "0 12px"}}>
                            <Col span={21} style={{display: "flex", alignItems: "center"}}>
                                {selectUsers.map((user: any) => user.name).join(", ")}
                            </Col>
                            {dataSource.isEdit && (
                                <Col span={3} style={{textAlign: "right"}}>
                                    <Button type="primary" onClick={() => setOptionTargetVisible(true)}>
                                        选择征求对象
                                    </Button>
                                </Col>
                            )}
                        </Row>
                    </AForm.Item>
                ),
                isLabelRed: true,
            }] : []),

        ];

        return [...commonFields];
    };

    const tableRows = useMemo(
        () => generateTableRows(dataSource),
        [dataSource, uploadData, tags, selectUsers, selectedInstitutions, form.getFieldsValue]
    );

    const containerRef = useRef<any>(null); // 父容器 ref
    // PageOffice 控件方法
    const Save = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.WebSave();
        }
    };

    const SaveAs = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.ShowDialog(3);
        }
    };

    const PrintSet = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.ShowDialog(5);
        }
    };

    const PrintFile = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.ShowDialog(4);
        }
    };

    const Close = () => {
        window.pageofficectrl.CloseWindow();
    };

    const IsFullScreen = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.FullScreen = !window.pageofficectrl.FullScreen;
        }
    };

    // PageOffice 事件回调函数
    const OnPageOfficeCtrlInit = () => {
        if (window.pageofficectrl) {
            window.pageofficectrl.AddCustomToolButton("保存", "Save", 1);
            window.pageofficectrl.AddCustomToolButton("另存为", "SaveAs", 12);
            window.pageofficectrl.AddCustomToolButton("页面设置", "PrintSet", 0);
            window.pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
            window.pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
            window.pageofficectrl.AddCustomToolButton("-", "", 0);
            window.pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
        }
    };

    const AfterDocumentOpened = () => {
        // 文档打开后事件回调
    };

    const BeforeDocumentSaved = () => {
        // 文档保存前事件回调
    };

    const AfterDocumentSaved = () => {
        // 文档保存后事件回调
    };

    const init = () => {
        // 将方法挂载到window对象
        window.OnPageOfficeCtrlInit = OnPageOfficeCtrlInit;
        window.AfterDocumentOpened = AfterDocumentOpened;
        window.BeforeDocumentSaved = BeforeDocumentSaved;
        window.AfterDocumentSaved = AfterDocumentSaved;

        window.Save = Save;
        window.SaveAs = SaveAs;
        window.PrintSet = PrintSet;
        window.PrintFile = PrintFile;
        window.Close = Close;
        window.IsFullScreen = IsFullScreen;
    };

    useEffect(() => {
        const resizeObserver = new ResizeObserver(() => {
            if (window.pageofficectrl && containerRef.current) {
                // 调用 PageOffice 调整宽度的 API（以官方为准，这里是伪代码）
                window.pageofficectrl.Width = containerRef.current?.offsetWidth;
            }
        });

        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }
        init();
        return () => {
            if (containerRef.current) {
                resizeObserver.unobserve(containerRef.current);
            }
        };
    }, []);
    const handleOpenPOBrowser = () => {
        const token = localStorage.getItem("token");
        const param = {
            token: token,
            id: dataSource.searchParams.id,
            path: "/institution/draft/pageoffice/word",
        };
        const paramStr = JSON.stringify(param);
        POBrowser.setHeader("Authorization", token);
        POBrowser.setProxyBaseAPI("/itm-institution-draft");
        POBrowser.openWindow(
            `/institution/draft/pageoffice/word`,
            "width=1150px;height=900px;",
            paramStr
        );
    };


    return (
        <ProCard boxShadow>
            <div className="draft-container">
                <Form form={form} layout={"vertical"} onFinish={handleSave}>
                    <div className="oneContractDrafting_header_box">
                        <div className="tab_item">
                            <Tabs
                                className="item_tabs"
                                defaultActiveKey={activeKey}
                                items={tabs}
                                onChange={setActiveKey}
                            />
                        </div>
                        <Row style={{width: "100%"}}>
                            <Col span={16}/>
                            <Col span={4}>
                                {activeKey === "2" && (

                                    <Button
                                        type="primary"
                                        onClick={
                                            handleOpenPOBrowser
                                        }
                                    >
                                        去起草
                                    </Button>
                                )}
                                {activeKey === "1" && (
                                    <Button type="primary" onClick={() => handlerSaveToDraft(1)}>
                                        保存表单
                                    </Button>
                                )}
                            </Col>
                            <Col span={4}>
                                <Space>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            setOptionVisible(true)
                                        }}
                                    >
                                        征求意见
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            handlerOpenSubmitDepGeneralTerm();
                                            showDialog();
                                        }}
                                    >
                                        送呈部门负责人
                                    </Button>
                                </Space>
                            </Col>
                        </Row>
                    </div>
                    <div
                        className="oneContractDrafting_body_box"
                        style={{display: activeKey === "1" ? "block" : "none"}}
                    >
                        <div className="oneResearch_conter">
                            <GenericFormTable header={tableHeader} rows={tableRows}/>
                        </div>
                    </div>
                    {activeKey == "2" && (
                        <div
                            style={{
                                transform: `scale(1.4)`,
                                transformOrigin: "top center",
                                width: "790px",
                                margin: "0 auto",
                            }}
                            dangerouslySetInnerHTML={{__html: poHtmlCode}}
                        />
                    )}
                    {activeKey == "3" && (
                        <div className="flow-chart">
                            <div className={"flow-chart-content"} ref={container}/>
                            {url && (
                                <img
                                    src={url}
                                    className={"flow-img"}
                                    style={{width: "70vw", height: "auto"}}
                                />
                            )}
                        </div>
                    )}
                </Form>
                <Drawer
                    open={tagVisible}
                    title="设置标签"
                    placement="left"
                    width={800}
                    onClose={() => setTagVisible(false)}
                    styles={{
                        body: {
                            overflow: 'auto',
                            height: '100%',
                        },
                    }}
                    footer={null}
                >
                    <div style={{width: 700}}>
                        <TagSelectorWithoutSave
                            dataId={dataSource.searchParams.id ?? 0}
                            dataType={'INSTITUTION_DRAFT'}
                            onChange={(value) => {
                                console.log("tag value:", value)
                                setTags(value.tags)
                                setTagVisible(false)
                                const codes = value.tags.map(tag => tag.code);
                                form.setFieldValue('tagList', codes)
                            }}
                        />
                    </div>
                </Drawer>

                <Modal
                    open={institutionVisible}
                    title="选择制度"
                    width="100vw" // 宽度占满屏幕
                    style={{
                        top: 0,
                        left: 0,
                        height: "100vh",
                        maxWidth: "100%",
                        margin: 0,
                        padding: 0,
                        borderRadius: 0,
                    }}
                    bodyStyle={{
                        height: "calc(100vh - 120px)",
                        overflowY: "auto",
                    }}
                    onOk={() => {
                        setInstitutionVisible(false);
                    }}
                    onCancel={() => setInstitutionVisible(false)}
                >
                    <InstitutionSearchPage
                        isComponent={true}
                        onSelectedChange={(id, type, name) => {
                            const selectedInstitution = {id: id, type: type, name: name};
                            const currentSelectedInstitutions = selectedInstitutions || [];
                            const newSelectedInstitutions = [...currentSelectedInstitutions, selectedInstitution];
                            setSelectedInstitution(newSelectedInstitutions);
                            const idList = newSelectedInstitutions.map(institution => institution.id);
                            form.setFieldValue('abolishInstitutionIdList', idList)
                        }}
                    />
                </Modal>
                <UserChoiceModal
                    visible={optionTargetVisible}
                    hide={() => {}}
                    onFinish={async (records: SelectableUser[]) => {
                        setOptionTargetVisible(false)
                        hideSelectUserModal(records)
                        const opinionTargetList = records.map(user => ({
                            targetId: user.id,
                            targetType: 'user'
                        }));
                        form.setFieldValue('opinionTargetList', opinionTargetList)
                    }}
                    selectedUsers={selectUsers}
                />

                <Modal
                    title="选择审批人员"
                    width={800}
                    centered
                    open={show}
                    cancelText="取消"
                    confirmLoading={loading}
                    okText="确定"
                    onCancel={() => {
                        setShow(false);
                        if (activeKey === "2") hideDialog();
                    }}

                    onClose={() => {
                        setShow(false);
                        if (activeKey === "2") hideDialog();
                    }}
                >
                    <UserChoiceSelect
                        multiple
                        onChange={setUserIds}
                        value={userIds || ""}
                    />
                </Modal>

                <Modal
                    title="选择审批人员"
                    width={800}
                    centered
                    open={flowUserSelectorVisible}
                    confirmLoading={loading}
                    cancelText="取消"
                    okText="确定"
                    onCancel={() => {
                        setFlowUserSelectorVisible(false);
                        if (activeKey === "2") hideDialog();
                    }}
                    onClose={() => {
                        if (activeKey === "2") hideDialog();
                        setFlowUserSelectorVisible(false);
                    }}
                    onOk={handlerSubmitDepGeneralTerm}
                >
                    <UserChoiceSelect
                        onChange={(e: string | null) => setFlowParams({userId: e})}
                        value={flowParams.userId}
                    />
                </Modal>

                <Modal
                    open={optionVisible}
                    title="确认发起征求意见"
                    onCancel={() => setOptionVisible(false)}
                    onOk={() => {
                        handlerOpinionStartFlow()
                        setOptionVisible(false)
                        Message.success("提交成功");
                    }}
                ></Modal>
            </div>
        </ProCard>
    );
};
export default Draft;
