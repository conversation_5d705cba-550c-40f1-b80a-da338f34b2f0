import React from "react";
import axios from "axios";

class InstitutionDraftWord extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            poHtmlCode: "",
            isVisible: true, // 默认显示
        };
    }

    //控件中的一些常用方法都在这里调用，比如保存，打印等等
    Save = () => {
        pageofficectrl.WebSave();
    };
    SaveAs = () => {
        pageofficectrl.ShowDialog(3);
    };
    PrintSet = () => {
        pageofficectrl.ShowDialog(5);
    };
    PrintFile = () => {
        pageofficectrl.ShowDialog(4);
    };
    Close = () => {
        window.external.close();
    };
    IsFullScreen = () => {
        pageofficectrl.FullScreen = !pageofficectrl.FullScreen;
    };

    OnPageOfficeCtrlInit = () => {
        // PageOffice的初始化事件回调函数，您可以在这里添加自定义按钮
        pageofficectrl.AddCustomToolButton("保存", "Save", 1);
        pageofficectrl.AddCustomToolButton("另存为", "SaveAs", 12);
        pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
    };
    AfterDocumentOpened = () => {
        // PageOffice的文档打开后事件回调函数
    };
    BeforeDocumentSaved = () => {
        // PageOffice的文档保存前事件回调函数
    };
    AfterDocumentSaved = () => {
        // PageOffice的文档保存后事件回调函数
    };

    init = () => {
        // 将PageOffice控件中的方法通过mounted挂载到window对象上，只有挂载后才能被vue组件识别
        // 以下的为PageOffice事件的回调函数，名称不能改，否则PageOffice控件调用不到
        window.OnPageOfficeCtrlInit = this.OnPageOfficeCtrlInit;
        window.AfterDocumentOpened = this.AfterDocumentOpened;
        window.BeforeDocumentSaved = this.BeforeDocumentSaved;
        window.AfterDocumentSaved = this.AfterDocumentSaved;

        window.Save = this.Save;
        window.SaveAs = this.SaveAs;
        window.PrintSet = this.PrintSet;
        window.PrintFile = this.PrintFile;
        window.Close = this.Close;
        window.IsFullScreen = this.IsFullScreen;
    };

    componentDidMount() {
        this.init();
        const windowParams = pageofficectrl.WindowParams;
        const params = typeof windowParams === 'string' ? JSON.parse(windowParams) : windowParams;
        const token = params.token;
        const id = params.id;
        const url = "/itm-institution-draft/api/pageOffice/showWord?id=" + id;
        axios.get(url, {
            headers: {
                "Authorization": token,
            }
        }).then((response) => {
            this.setState({
                poHtmlCode: response.data,
            });
        });
    }

    render() {
        const {poHtmlCode, isVisible} = this.state;

        pageofficectrl.Enabled = isVisible;

        return (
            <div>
                <div style={{height: "900px"}} dangerouslySetInnerHTML={{__html: poHtmlCode}}/>
            </div>
        );
    }

}

export default InstitutionDraftWord;
