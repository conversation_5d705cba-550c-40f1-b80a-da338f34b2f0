/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description:
 * @LastEditTime: 2025-07-22 08:41:24
 * @LastEditors: wangji<PERSON>ao
 */
import moment from "moment";
import {message, TabsProps} from "antd";
import {useEffect, useState} from "react";
import {saveContractAttachment} from "@/api/contract/contractAttachment";
import {
    getCurrentContract,
    jointReview,
    queryContractById,
    saveContractData,
    saveToDraft,
} from "@/api/contract/contract";
import {submitDepGeneralTerm, detail} from "@/api/contract/flow";
import {wordFunctionalApi} from "@/api/pageoffice";
import {Attachment, IDataSource, ISearchParams} from "../types";
import {Form} from "@/components/form-pc";
import {
    addOrUpdateInstitutionDraft,
    opinionStartFlow,
    queryDraftInstitutionDetail,
    showHtml
} from "@/api/institution/draft";
import dayjs from "dayjs";
import {useLocation} from "react-router-dom";
import {downloadOssFile} from "@/utils/download";

export const usePresenter = () => {
    const form = Form.useForm();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const institutionId = searchParams.get('institutionId');

    // start 是否可编辑状态
    const [dataSource, setDataSource] = useState<IDataSource>({
        searchParams: {
            type: 0,
            name: '',
            institutionAbolishEnable: false,
            tagList: [],
            abolishInstitutionIdList: [],
            opinionEnable: false,
            opinionEndTime: '',
            opinionExplain: '',
            opinionTargetList: [],
            drafterId: localStorage.getItem("userId") ? Number(localStorage.getItem("userId")) : 1,
            draftTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        },
        isEdit: true,
    });

    //start 上传附件
    const [uploadData, setUploadData] = useState<Attachment[]>([]);

    const [tags, setTags] = useState<any[]>([]);

    const [tagVisible, setTagVisible] = useState(false);

    const [selectedInstitutions, setSelectedInstitution] = useState<any[]>([]);

    const [institutionVisible, setInstitutionVisible] = useState(false);

    const [optionTargetVisible, setOptionTargetVisible] = useState(false);

    const [optionVisible, setOptionVisible] = useState(false);

    const [selectUsers, hideSelectUserModal] = useState<any[]>([]);


    const [fileKey, setFileKey] = useState<Record<string, string>>({});

    const [templateVisible, setTemplateVisible] = useState(false);

    const [flowUserSelectorVisible, setFlowUserSelectorVisible] = useState(false);

    const [templateId, setTemplateId] = useState<number | null>(null);

    const [activeKey, setActiveKey] = useState<any>("1");


    const [poHtmlCode, setPoHtmlCode] = useState<any>("");

    const [show, setShow] = useState(false);

    const [userIds, setUserIds] = useState<string | null>("");

    const [loading, setLoading] = useState(false);

    const [isSaveWord, setIsSaveWord] = useState(false);

    const [flowParams, setFlowParams] = useState<{ userId: any }>({
        userId: null,
    });

    const [tabs, setTabs] = useState<TabsProps["items"]>([
        {
            key: "1",
            label: "表单",
            disabled: false,
        },
        {
            key: "2",
            label: "正文",
            disabled: false,
        },
        {
            key: "3",
            label: "流程",
            disabled: false,
        },
    ]);
    // 表格头数据
    const tableHeader = [
        {
            label: "起草人",
            value:
                dataSource.searchParams.draftName ?? localStorage.getItem("username"),
            isFormLabel: false,
            isTimeCell: false,
        },
        {
            label: "起草时间",
            value: form.getFieldValue("draftTime") == null ? dataSource.searchParams.draftTime : form.getFieldValue("draftTime"),
            isFormLabel: false,
            isTimeCell: false,
        },
    ];

    function formatInput(value: string) {
        if (value.startsWith("-")) return;

        let formattedValue = value;

        if (
            formattedValue.length > 1 &&
            formattedValue.startsWith("0") &&
            !formattedValue.startsWith("0.")
        ) {
            formattedValue = formattedValue.replace(/^0+/, "") || "0";
        }

        if (formattedValue === ".") {
            formattedValue = "0.";
        } else if (formattedValue.startsWith(".")) {
            formattedValue = "0" + formattedValue;
        }

        const regex = /^(\d+)?(\.\d{0,6})?$/;

        if (!regex.test(formattedValue)) {
            const match = formattedValue.match(/^(\d+)?(\.\d{0,6})?/);
            if (match) {
                formattedValue = match[0];
            } else {
                return;
            }
        }

        setDataSource((prev) => ({
            ...prev,
            searchParams: {
                ...prev.searchParams,
            },
        }));
    }

    const onInputChange = (value: string, fieldName: string) => {
        setDataSource((prev) => ({
            ...prev,
            searchParams: {
                ...prev.searchParams,
                [fieldName]: value,
            },
        }));
    };

    const fetchFile = async (draftId?: number) => {
        if (!draftId){
            setPoHtmlCode("");
            return;
        }
        try {
            const params = {
                id: draftId,
            };
            const response = await showHtml(params);
            setPoHtmlCode(response.data);
        } catch (error) {
            console.error("获取文件失败:", error);
        }
    };



    /**
     * @description 保存后根据所反id获取保存后的数据
     * */
    const loadDraftInstitutionById = async (institutionId: number) => {
        const params = {
            id: institutionId,
        };
        const {data, success} = await queryDraftInstitutionDetail(params);
        if (!success) return;
        // 处理数据格式以适配表单
        const processedData = {
            ...data,
            draftTime: data.draftTime == null ? moment().format("YYYY-MM-DD HH:mm:ss") : dayjs(data.draftTime).format('YYYY-MM-DD HH:mm:ss'),
            type: data.type.toString(),
            name: data.title,
            // 处理日期字段
            opinionEndTime: data.opinionEndTime ? dayjs(data.opinionEndTime) : null,
            // 处理 opinionTargetList，只提取 targetId 用于表单显示
            opinionTargetList: data.opinionTargetList?.map((item: any)  => ({
                targetId: item.targetId,
                targetType: item.targetType,
            })) || [],
            // 处理标签数据
            tagList: data.tagList || [],
            // 处理废止制度列表
            abolishInstitutionIdList: data.abolishInstitutionList?.map((institution: any) => institution.id) || [],
            opinionEnable: data.opinionEnable.toString(),
            institutionAbolishEnable: data.institutionAbolishEnable.toString(),
        };
        // 使用 setFieldsValue 设置表单值
        form.setFieldsValue(processedData);

        // 更新本地状态
        setDataSource(prev => ({
            ...prev,
            searchParams: {
                ...prev.searchParams,
                ...data
            }
        }));

        // 更新标签状态
        if (data.tagList) {
            setTags(data.tagList.map((tag: string) => ({ code: tag.targetId, name: tag.targetName })));
        }

        // 更新选择的用户状态
        if (data.opinionTargetList) {
            // 这里可能需要根据实际情况调整，如果需要完整的用户信息，
            // 可能需要额外的API调用来获取用户详情
            hideSelectUserModal(data.opinionTargetList.map((item: any) => ({
                id: item.targetId,
                name: item.targetName
            })));
        }

        // 更新选择的制度状态
        if (data.abolishInstitutionList) {
            // 同样，这里可能需要获取制度的详细信息
            setSelectedInstitution(data.abolishInstitutionList);
        }
    };

    /**
     * @description 保存表格信息
     * */
    const handleSave = async () => {
        form.validate().then(async (res) => {
            if (res) {
                const values = form.getFieldsValue();
                try {
                    interface newDataSource extends ISearchParams {
                        depositRequired?: boolean;
                        attachments?: Attachment[];
                    }

                    let params: newDataSource = values as ISearchParams;
                    params.depositRequired =
                        params._depositRequired === "0" ? true : false;
                    params.attachments = uploadData;

                    params = {
                        ...dataSource.searchParams,
                        ...params,
                        partyB: dataSource.searchParams.partyB,
                    };

                    // 调用接口保存数据
                    const {data, success} = await saveContractData(params);

                    if (!success) return;
                    queryDraftInstitutionDetail(data);
                } catch (err) {
                }
            }
        });
    };

    useEffect(() => {
        if (form.getFieldValue("institutionAbolishEnable") == null){
            form.setFieldValue("institutionAbolishEnable", "false")
        }
        if (form.getFieldValue("opinionEnable") == null){
            form.setFieldValue("opinionEnable", "false")
        }
    }, [])

    useEffect(() => {
        const init = async () => {
            if (activeKey === "2") {
                await handlerSaveToDraft();
                if (!isSaveWord) {
                    setIsSaveWord(true);
                    setTimeout(() => {
                        fetchFile(dataSource.searchParams.id);
                    }, 1000);
                }
            }
        };
        init();
    }, [activeKey]);


    /**
     * @description 打开送呈部门负责人
     * */

    const handlerOpenSubmitDepGeneralTerm = async () =>
        setFlowUserSelectorVisible(true);

    const handlerSubmitDepGeneralTerm = async () => {
        try {
            form.validate().then(async (r) => {
                if (!r) {
                    window.pageofficectrl.Enabled = false;
                    setActiveKey("1");
                    return;
                }

                setLoading(true);
                await handlerSaveToDraft();
                const params = {
                    contractId: dataSource.searchParams.id,
                    userId: flowParams.userId,
                };
                const {success} = await submitDepGeneralTerm(params);
                setLoading(false);
                if (!success) return;
                message.success("送呈部门负责人成功");
                setTimeout(() => {
                    window.pageofficectrl?.CloseWindow();
                }, 1000);
            });
        } catch (error) {
        } finally {
            setFlowUserSelectorVisible(false);
            if (activeKey === "2") hideDialog();
        }
    };

    const showDialog = () => {
        window.pageofficectrl.Enabled = false;
    };

    const hideDialog = () => {
        window.pageofficectrl.Enabled = true;
    };

    /**
     * @description 征求意见
     * */
    const handlerOpinionStartFlow = async () => {
        const body = {
            draftId: form.getFieldValue("id")
        }
        await opinionStartFlow(body);

    };

    useEffect(() => {
        const tab = document.querySelectorAll(".ant-tabs-tab");

        const tag = document.createElement("div");
        tag.textContent = "";
        tag.style.background = "#ff4d4f";
        tag.style.borderRadius = "2.5px";
        tag.style.height = "5px";
        tag.style.width = "5px";
        tag.style.position = "absolute";
        tag.style.top = "20px";
        tag.style.right = "15px";
        tag.id = "tag";
        if (!templateId) {
            if (!tab.length) return;

            tab[1].appendChild(tag);
            (tab[1] as HTMLElement).style.position = "relative";
        } else {
            const existingTag = document.getElementById("tag");
            if (existingTag && existingTag.parentNode === tab[1]) {
                tab[1].removeChild(existingTag); // 直接从 tab[1] 移除
            }
            existingTag?.remove();
        }
    }, [templateId]);

    useEffect(() => {
        document.getElementById("root")!.style.height = "100vh";
        const card = document.querySelectorAll(".ant-pro-card ");

        if (!card.length) return;
        (card[0] as HTMLElement).style.height = "100%";
    }, []);

    /**
     * @description 保存草稿
     * */
    const handlerSaveToDraft = async (tag?: number) => {
        const values = form.getFieldsValue();

        interface newDataSource extends ISearchParams {
            depositRequired?: boolean;
            attachments?: Attachment[];
        }

        let params: newDataSource = values as ISearchParams;

        params = {
            ...dataSource.searchParams,
            ...params,
            name: form.getFieldValue("name"),
            tagList: form.getFieldValue("tagList"),
            opinionEndTime:  form.getFieldValue("opinionEndTime") == null ? null : dayjs(form.getFieldValue("opinionEndTime")).format("YYYY-MM-DD HH:mm:ss"),
            abolishInstitutionIdList: form.getFieldValue("abolishInstitutionIdList"),
        };
        const {success, data} = await addOrUpdateInstitutionDraft(params);
        if (!success) return;
        setDataSource(prev => ({
            ...prev,
            searchParams: {
                ...prev.searchParams,
                id: data
            }
        }));
        if (tag) message.success('保存成功')
        loadDraftInstitutionById(data);
    };


    // 替换原来的 useEffect
    useEffect(() => {
        const fetchInstitutionData = async () => {
            if (institutionId != null) {
                loadDraftInstitutionById(Number(institutionId))
            }
        };

        fetchInstitutionData();
    }, [institutionId]); // 依赖 institutionId



    return {
        form,
        tabs,
        tableHeader,
        setDataSource,
        dataSource,
        onInputChange,
        tags,
        setTags,
        tagVisible,
        setTagVisible,
        selectedInstitutions,
        setSelectedInstitution,
        institutionVisible,
        setInstitutionVisible,
        optionTargetVisible,
        setOptionTargetVisible,
        optionVisible,
        setOptionVisible,
        selectUsers,
        hideSelectUserModal,
        fileKey,
        setFileKey,
        uploadData,
        setUploadData,
        formatInput,
        templateVisible,
        setTemplateVisible,
        setTabs,
        setTemplateId,
        templateId,
        activeKey,
        setActiveKey,
        handleSave,
        handlerOpenSubmitDepGeneralTerm,
        setFlowParams,
        flowUserSelectorVisible,
        setFlowUserSelectorVisible,
        flowParams,
        handlerSubmitDepGeneralTerm,
        poHtmlCode,
        setPoHtmlCode,
        showDialog,
        hideDialog,
        show,
        setShow,
        userIds,
        setUserIds,
        handlerOpinionStartFlow,
        handlerSaveToDraft,
        loading,
        setLoading,
    };
};
