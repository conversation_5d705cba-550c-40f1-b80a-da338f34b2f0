import {Button, Message, Space, Tabs} from "@itm/components-pc";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "./index.scss";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { Input, message, Modal, type TabsProps } from "antd";
import { ContractInfo } from "@/components/contract/info";
import { Authorize } from "@/components/contract/authorization";
import { Sign } from "@/components/contract/sign";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import {
  queryContractById,
  recall,
  sendBack,
  submitAuthorize,
  submitSign,
} from "@/api/contract/contract";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import CustomSplitter from "@/components/CustomSplitter";
import { downloadOssFile } from "@/utils/download";
import {Form, FormInput} from "@/components/form-pc";
import { ValidateUtils } from "@codingapi/ui-framework";
import { FlowImage } from "@/components/flowImage";
import moment from "moment";
import {DraftOpinionInfo} from "@/components/institution/draft/opinionInfo";
import {
  opinionRecallFlow,
  opinionSubmitFlow,
  queryDraftInstitutionDetail,
  queryOpinionItemList
} from "@/api/institution/draft";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {SelectableUser} from "@/components/user/choice/type";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref: React.RefObject<any>;
  showWhen: (current: number) => boolean;
}

// 定义组件状态接口
interface ComponentState {
  isShow: boolean;
  activeKey: string;
  current: number;
  isEdit: boolean;
}

const messPending: React.FC<NoticeFormProps> = (props) => {
  // 初始化状态
  const [state, setState] = useState<ComponentState>({
    isShow: true,
    activeKey: "1",
    current: 4,
    isEdit: true,
  });
  const onChange = (key: string) => {
    setState((pre) => ({ ...pre, activeKey: key }));
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];
  const draftingInfoRef = useRef<any>(null);

  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        nodeCode: "draft_opinion3",
        reviewerId: 0,
        draftId: 67,
        targetId:1,
        institutionName: "",
      };


  const [content, setContent] = useState<string>("");

  const [institution, setInstitution] = useState<any>({});

  const [opinionItems, setOpinionItems] = useState<any>({});

  const [submitVisible, setSubmitVisible] = useState<boolean>(false);

  const [selectedUsers, hideSelectUserModal] = useState<any[]>([]);

  const [rejectVisible, setRejectVisible] = useState(false);

  const form = Form.useForm();

  useEffect(() => {

  }, []);

  useEffect(() => {
    const draftParams= {
      id: customParams.draftId
    }
    queryDraftInstitutionDetail(draftParams).then((res: any) => {
      setInstitution(res.data);
    });
    const opinionParams= {
      draftId: customParams.draftId,
      targetId: customParams.targetId
    }
    queryOpinionItemList(opinionParams).then((res: any) => {
      setOpinionItems(res?.data?.targetList[0]?.itemList)
    })

  }, []);

  const stepsData: StepItem[] = [
    {
      title: "制度基础信息",
      description: "制度基础信息",
      component: <DraftOpinionInfo ref={draftingInfoRef} draft={institution} itemList={opinionItems} />,
      ref: draftingInfoRef,
      showWhen: (current) => current >= 0,
    },
  ];


  /**
   * @description 撤回
   * */

  const handlerRecall = () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要撤回吗?",
      async onOk() {
        const params = {
          recordId: customParams.recordId,
        };
        const { success } = await opinionRecallFlow(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };


  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={state.activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>

          {customParams.nodeCode == "draft_opinion1" && (
            <Space>
              <Button color="danger" variant="solid" onClick={handlerRecall}>
                撤回
              </Button>
              <Button type="primary" onClick={()=>{
                setSubmitVisible(true)
              }}>
                送呈部门负责人
              </Button>
            </Space>
          )}
          {customParams.nodeCode == "draft_opinion2" && (
              <Space>
                <Button
                    color="danger"
                    variant="solid"
                    onClick={() => setRejectVisible(true)}
                >
                  退回修改
                </Button>
                <Button color="danger" variant="solid" onClick={handlerRecall}>
                  撤回
                </Button>
                <Button type="primary" onClick={()=>{
                  setSubmitVisible(true)
                }}>
                  送呈分管领导
                </Button>
              </Space>
          )}
          {customParams.nodeCode == "draft_opinion3" && (
              <Space>
                <Button
                    color="danger"
                    variant="solid"
                    onClick={() => setRejectVisible(true)}
                >
                  退回修改
                </Button>
                <Button type="primary" onClick={()=>{
                  setSubmitVisible(true)
                }}>
                  同意上报
                </Button>
              </Space>
          )}
        </div>
      </div>
      <div></div>
      <div className="sevenContractDrafting_body_box">
        {state.activeKey == "1" && (
          <>
            <CustomSplitter
              left={
                <div
                  style={{
                    transform: `scale(1.0)`,
                    transformOrigin: "top left", // 设置缩放原点为左上角
                    width: "789px",
                    height: "calc(100vh - 120px)",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: content,
                  }}
                />
              }
              style={{ padding: 24 }}
              right={
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen(state.current) && step.component}
                    </Fragment>
                  ))}
                </div>
              }
            />
          </>
        )}
        {state.activeKey == "2" && <FlowImage recordId={customParams.recordId} />}
      </div>
      <UserChoiceModal
          visible={submitVisible}
          hide={() => {}}
          onFinish={async (records: SelectableUser[]) => {
            hideSelectUserModal(records)
            console.log(records);
            const body = {
              draftId: customParams.draftId,
              targetId: customParams.targetId,
              recordId: customParams.recordId,
              reviewerId: records[0]?.id,
              institutionName: institution.name,
              success: true,
            };
            opinionSubmitFlow(body).then((res) => {
              Message.success("提交成功")
            })
            setSubmitVisible(false)
          }}
          selectedUsers={selectedUsers}
      />
      <Modal
        title="退回修改"
        width={300}
        centered
        open={rejectVisible}
        cancelText="取消"
        okText="确定"
        onCancel={() => setRejectVisible(false)}
        onOk={async () => {
          const body = {
            draftId: customParams.draftId,
            targetId: customParams.targetId,
            recordId: customParams.recordId,
            institutionName: institution.name,
            success: false,
          };
          opinionSubmitFlow(body).then((res) => {
            Message.success("提交成功")
            setRejectVisible(false)
          })
        }}
      >
        <Form form={form}>
          <FormInput
            name="advice"
            label="退回原因"
            validateFunction={ValidateUtils.validateNotEmpty}
          />
        </Form>
      </Modal>
    </>
  );
};
export default messPending;
