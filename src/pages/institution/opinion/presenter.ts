import {OpinionApi, OpinionState, Dispatch} from "@/pages/institution/opinion/types";

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<OpinionState>;

    private readonly api: OpinionApi;

    private state: OpinionState;

    public constructor(dispatch: Dispatch<OpinionState>, api: OpinionApi, state: OpinionState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: OpinionState) => {
        this.state = newStatus;
    }



}