/* LoginPage.css */
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: url(../../assets/Images/login/login-backgroun.png) 100% 100% no-repeat;
  width: 100%;
  overflow: hidden;

}

.login-content {
}

.login-header {
  margin-bottom: 30px;

  img {
    width: 50%;
    height: auto;
  }
}
.left {
  background-color: #666;
  background: url(../../assets/Images/login/login-img.png) 100% 100% no-repeat;
}

.right {
  display: flex;
}

.login-card {
  width: 440px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 6px 12px;
  height: 495px;
}

.login-tabs .ant-tabs-nav {
  margin-bottom: 24px;
}

.login-tabs .ant-tabs-tab {
  padding: 12px 24px;
}

.qr-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;

  p {
    color: #1279d3ff;
    font-weight: normal;
    margin: 15px 0;
  }
}

.qr-code-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.tip {
  color: #1279d3ff !important;
  font-weight: bold !important;
  margin-bottom: 0 !important;
  margin-top: 30px !important;
  font-size: 15px !important;
}

.qr-code-placeholder p {
  color: #666;
}

.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.login-form-button {
  width: 100%;
  margin-bottom: 15px;
}

.login-footer {
  position: fixed;
  bottom: 0;
  color: #666;
  font-size: 12px;
  width: 100%;
  background: url(../../assets/Images/login/login-bottom-bar.png) 100% 100%
    no-repeat;
  padding: 6px 0;
}

.security-notice {
  margin-bottom: 10px;
  color: #ff4d4f;
}

.footer-links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.footer-links span {
  white-space: nowrap;
  color: white;
}

.qr-code-mock {
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  position: relative;

  img {
    width: 200px;
    height: 200px;
  }
}

@media (max-width: 480px) {
  .login-card {
    width: 90%;
  }

  .footer-links {
    flex-direction: column;
    align-items: center;
  }
}
