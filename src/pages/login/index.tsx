// LoginPage.tsx
import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  Checkbox,
  Row,
  Col,
  Card,
  Image,
  Tabs,
  message,
} from "antd";
import {
  LockOutlined,
  UserOutlined,
  SafetyOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";
import "./index.scss";
import { initUser, login } from "@/api/user/account";
import { useMenuStore } from "@/store";
import { useNavigate } from "react-router";
import { config } from "@/config/theme";

const { TabPane } = Tabs;

const LoginPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("account");
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { setMenus } = useMenuStore();
  const navigate = useNavigate();

  const onFinish = async (values: any) => {
    form.validateFields().then(async (r) => {
      if (!r) return;
      setLoading(true);
      const res = await login(values);
      if (res.success) {
        // 初始化用户信息
        initUser(res.data).then((data) => {
          setMenus(data);
          setTimeout(() => {
            navigate(config.welcomePath);
          }, 100);
        });
        // 暂时使用
        localStorage.setItem("usernickname", res.data.data.name);
        localStorage.setItem("userdeptname", res.data.data.dept.code);
        // 获取首页信息
        // 跳转到欢迎页

        localStorage.setItem("l-isMemory", values.isMemory);
        if (values.isMemory) {
          localStorage.setItem("l-username", values.username);
          localStorage.setItem("l-password", values.password);
        } else {
          localStorage.removeItem("l-username");
          localStorage.removeItem("l-password");
        }
        setLoading(false);
      } else {
        setLoading(false);
        message.error("登录失败，请检查用户名和密码！");
      }
    });
    // 这里添加登录逻辑
  };

  useEffect(() => {
    const isMemory = localStorage.getItem("l-isMemory");
    if (isMemory === "true") {
      const username = localStorage.getItem("l-username");
      const password = localStorage.getItem("l-password");
      form.setFieldsValue({
        username: username,
        password: password,
        isMemory: true,
      });
    } else {
      form.setFieldsValue({
        isMemory: false,
      });
    }
  }, []);

  return (
    <div className="login-container">
      <div className="login-header">
        <img
          src={require("../../assets/Images/login/log-title.png")}
          alt="logo"
        />
      </div>
      <div className="login-content">
        <Row style={{ paddingTop: 80 }}>
          <Col span={14} className="left" />
          <Col span={10} className="right">
            <Card className="login-card">
              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                centered
                className="login-tabs"
              >
                <TabPane
                  tab={
                    <span>
                      <QrcodeOutlined />
                      扫码登录
                    </span>
                  }
                  key="qr"
                >
                  <div className="qr-login-container">
                    <p>请使用移动平台APP扫码登录</p>
                    <div className="qr-code-placeholder">
                      {/* 这里放置二维码图片或二维码生成组件 */}
                      <div className="qr-code-mock">
                        <img
                          src={require("../../assets/Images/login/login-qrcode-border.png")}
                        />
                      </div>
                      <p className="tip">
                        本平台为互联网非涉密平台，严禁处理、传输国家秘密
                      </p>
                    </div>
                  </div>
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <UserOutlined />
                      账号登录
                    </span>
                  }
                  key="account"
                >
                  <Form
                    form={form}
                    name="normal_login"
                    className="login-form"
                    initialValues={{ remember: false }}
                    onFinish={onFinish}
                  >
                    <Form.Item
                      name="username"
                      rules={[{ required: true, message: "请输入手机号/邮箱" }]}
                    >
                      <Input
                        size="large"
                        prefix={
                          <UserOutlined className="site-form-item-icon" />
                        }
                        placeholder="请输入手机号/邮箱"
                      />
                    </Form.Item>
                    <Form.Item
                      name="password"
                      rules={[{ required: true, message: "请输入密码" }]}
                    >
                      <Input
                        size="large"
                        prefix={
                          <LockOutlined className="site-form-item-icon" />
                        }
                        type="password"
                        placeholder="请输入密码"
                      />
                    </Form.Item>
                    <Form.Item
                      name="captcha"
                      // rules={[{ required: true, message: "请输入验证码" }]}
                    >
                      <Row gutter={8}>
                        <Col span={16}>
                          <Input
                            size="large"
                            prefix={
                              <SafetyOutlined className="site-form-item-icon" />
                            }
                            placeholder="请输入验证码"
                          />
                        </Col>
                        <Col span={8}>
                          <Button size="large" style={{ width: "100%" }}>
                            获取验证码
                          </Button>
                        </Col>
                      </Row>
                    </Form.Item>

                    <Button
                      size="large"
                      type="primary"
                      htmlType="submit"
                      className="login-form-button"
                      loading={loading}
                    >
                      登录
                    </Button>

                    <Form.Item name="remember" valuePropName="checked" noStyle>
                      <Checkbox>我已阅读并同意政策法规系统隐私政策</Checkbox>
                    </Form.Item>
                    <p className="tip">
                      本平台为互联网非涉密平台，严禁处理、传输国家秘密
                    </p>
                  </Form>
                </TabPane>
              </Tabs>
            </Card>
          </Col>
        </Row>
      </div>
      <div className="login-footer">
        <div className="footer-links">
          <span>贵州省烟草专卖局(公司)版权所有</span>
          <span>《政策法规系统-隐私政策》</span>
          <span>技术支持:山东莱易信息产业股份有限公司</span>
          <span>【刘林-15228380703 陈坤坤-18770004562】</span>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
