/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-02 11:24:57
 * @Description:
 * @LastEditTime: 2025-07-19 10:36:24
 * @LastEditors: wangjiantao
 */
import { Button, Space, Tabs } from "@itm/components-pc";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "./index.scss";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { Input, message, Modal, type TabsProps } from "antd";
import { ContractInfo } from "@/components/contract/info";
import { Authorize } from "@/components/contract/authorization";
import { Sign } from "@/components/contract/sign";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import {
  queryContractById,
  recall,
  sendBack,
  submitAuthorize,
  submitSign,
} from "@/api/contract/contract";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import CustomSplitter from "@/components/CustomSplitter";
import { downloadOssFile } from "@/utils/download";
import {Form, FormInput} from "@/components/form-pc";
import { ValidateUtils } from "@codingapi/ui-framework";
import { FlowImage } from "@/components/flowImage";
import moment from "moment";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref: React.RefObject<any>;
  showWhen: (current: number) => boolean;
}

// 定义组件状态接口
interface ComponentState {
  isShow: boolean;
  activeKey: string;
  current: number;
  isEdit: boolean;
}

const messPending: React.FC<NoticeFormProps> = (props) => {
  // 初始化状态
  const [state, setState] = useState<ComponentState>({
    isShow: true,
    activeKey: "1",
    current: 4,
    isEdit: true,
  });
  const onChange = (key: string) => {
    setState((pre) => ({ ...pre, activeKey: key }));
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];
  const draftingInfoRef = useRef<any>(null);

  const authorizationRef = useRef<any>(null);

  const signInformationRef = useRef<any>(null);

  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { contractId, templateCode, recordId, processId } = customParams;

  const [content, setContent] = useState<string>("");

  const [contract, setContract] = useState<any>({});

  const [open, setOpen] = useState(false);

  const form = Form.useForm();

  useEffect(() => {
    getContractContentHtml(contractId).then((data) => {
      setContent(data.data);
    });
  }, []);

  useEffect(() => {
    queryContractById(contractId).then((res: any) => {
      setContract(res.data);
    });
  }, []);

  const stepsData: StepItem[] = [
    {
      title: "合同基础信息",
      description: "合同基础信息",
      component: <ContractInfo ref={draftingInfoRef} contract={contract} />,
      ref: draftingInfoRef,
      showWhen: (current) => current >= 0,
    },
    {
      title: "授权委托书信息",
      description: "授权委托书信息",
      component: (
        <Authorize
          ref={authorizationRef}
          contract={contract}
          isEdit={templateCode === "contract_draft_authorize"}
          defaultActive={templateCode === "contract_draft_authorize"}
        />
      ),
      ref: authorizationRef,
      showWhen: (current) =>
        templateCode === "contract_draft_authorize" ||
        templateCode === "contract_draft_sign",
    },
    {
      title: "合同签订信息",
      description: "合同签订信息",
      component: (
        <Sign
          ref={signInformationRef}
          isEdit={templateCode === "contract_draft_sign"}
          contract={contract}
          defaultActive={templateCode === "contract_draft_sign"}
        />
      ),
      ref: signInformationRef,
      showWhen: (current) => templateCode === "contract_draft_sign",
    },
  ];

  /**
   * @description 发放授权书
   * */
  const handlerSubmitAuthorize = async () => {
    const state = authorizationRef.current?.state();

    if (!state?.selectedAuthorize) return message.warning("请选择授权委托书");
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要发放授权书吗?",
      async onOk() {
        const params = {
          contractId,
          serialNumber: state?.serialNumber,
          authorizedId: state?.selectedAuthorize,
          recordId,
        };
        const { success } = await submitAuthorize(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };

  /**
   * @description 合同签订/归档
   * */
  const handlerSubmitSign = async () => {
    const { state } = signInformationRef.current?.state();

    const result = await signInformationRef.current.form.validateFields();

    if (!result) return;
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要将合同归档吗?",
      async onOk() {
        const params = {
          contractId,
          startTime: moment(state.startDate).format("YYYY-MM-DD"),
          endTime: moment(state.endDate).format("YYYY-MM-DD"),
          recordId,
          attachments: [
            ...state.mainFiles,
            ...state.otherFiles,
            ...state.partBFiles,
          ],
        };
        console.log(params);

        const { success } = await submitSign(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };

  /**
   * @description 撤回
   * */

  const handlerRecall = () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要撤回吗?",
      async onOk() {
        const params = {
          contractId,
          recordId,
          processId
        };
        const { success } = await recall(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };

  /**
   * @description 撤回
   * */

  const handlerBack = () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要退回吗?",
      async onOk() {
        const params = {
          contractId,
          advice: form.getFieldValue("advice"),
          recordId,
          processId,
        };
        const { success } = await sendBack(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          setTimeout(() => {
            props.setVisible(false);
          }, 1000);
        }, 1000);
      },
    });
  };

  /**
   * @description 下载合同
   * */
  const handlerDownloadFile = async (tag: 0 | 1) => {
    downloadOssFile(tag ? contract?.powerOfAttorneyFileKey : contract?.fileKey);
  };

  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={state.activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>
          {templateCode === "contract_draft_sign" && (
            <Space>
              <Button
                color="primary"
                variant="solid"
                onClick={() => handlerDownloadFile(0)}
              >
                下载合同
              </Button>
              <Button
                color="primary"
                variant="solid"
                onClick={() => handlerDownloadFile(1)}
              >
                下载委托书
              </Button>
              <Button type="primary" onClick={handlerSubmitSign}>
                归档
              </Button>
            </Space>
          )}

          {templateCode !== "contract_draft_sign" && (
            <Space>
              <Button
                color="danger"
                variant="solid"
                onClick={() => setOpen(true)}
              >
                退回修改
              </Button>
              <Button color="danger" variant="solid" onClick={handlerRecall}>
                撤回
              </Button>
              <Button type="primary" onClick={handlerSubmitAuthorize}>
                发放授权书
              </Button>
            </Space>
          )}
        </div>
      </div>
      <div></div>
      <div className="sevenContractDrafting_body_box">
        {state.activeKey == "1" && (
          <>
            <CustomSplitter
              left={
                <div
                  style={{
                    transform: `scale(1.0)`,
                    transformOrigin: "top left", // 设置缩放原点为左上角
                    width: "789px",
                    height: "calc(100vh - 120px)",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: content,
                  }}
                />
              }
              style={{ padding: 24 }}
              right={
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen(state.current) && step.component}
                    </Fragment>
                  ))}
                </div>
              }
            />
          </>
        )}
        {state.activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>
      <Modal
        title="退回修改"
        width={300}
        centered
        open={open}
        cancelText="取消"
        okText="确定"
        onCancel={() => setOpen(false)}
        onOk={async () => {
          try {
            const result = await form.validate();
            if (!result) return;
            handlerBack();
          } catch (error) {}
        }}
      >
        <Form form={form}>
          <FormInput
            name="advice"
            label="退回原因"
            validateFunction={ValidateUtils.validateNotEmpty}
          />
        </Form>
      </Modal>
    </>
  );
};
export default messPending;
