import React, {useEffect, useMemo, useState} from "react";
import {
    <PERSON>ton,
    Pagination,
    Row,
    Space,
    Checkbox,
    CheckboxGroup,
    Card,
} from "@itm/components-pc";
import "./index.scss";
import {Badge, Form} from "antd";
import {useNavigate} from "react-router";
import {page as contractPage} from "@/api/contract/contract";
import {FormInput} from "@/components/form-pc";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import {Form as IForm} from "@/components/form-pc";
import {POBrowser} from "js-pageoffice";

const Institutional = () => {
    const form = IForm.useForm();
    const [arr, setArr] = useState<any[]>([]);

    const [loading, setLoading] = useState(false);
    const [totalItems, setTotalItems] = useState(200);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);

    const [notifications, setNotifications] = useState<any[]>([]);

    const navigate = useNavigate();

    const handleSearch = () => {
        fetchData(currentPage);
    };

    const fetchData = async (page: number) => {
        setLoading(true);
        const params = {
            ...form.getFieldsValue(),
            current: page,
            pageSize,
        };
        try {
            const result = await contractPage(params, {}, {}, []);
            if (result?.success && result?.data) {
                const list = result.data ?? [];
                const total = result.total ?? 0;
                setNotifications(list);
                setTotalItems(total);
                setCurrentPage(page);
            } else {
                console.error("接口返回异常:", result?.errMessage ?? "未知错误");
                setNotifications([]);
                setTotalItems(0);
            }
        } catch (error) {
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData(currentPage);
    }, []);

    const handlePageChange = (page: number) => {
        fetchData(page);
    };

    const onChange = (checkedValues: any = []) => {
        setArr(checkedValues);
    };

    const formatAmount = (amount: number | null | undefined): string => {
        if (amount == null) return "0.000000";
        return amount.toFixed(6).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    };

    const formatDate = (dateStr: string | null | undefined): string => {
        if (!dateStr) return "-";
        const date = new Date(dateStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            "0"
        )}-${String(date.getDate()).padStart(2, "0")}`;
    };

    return (
        <div className="institutional-contract-container">
            {/* 查询条件卡片 */}
            <Card className="search-card">
                <IForm form={form} layout="horizontal">
                    <Row gutter={[16, 16]}>
                        <Row.Col span={5}>
                            <Form.Item label="单位" name="unitId">
                                <FormOrgTreeSelect
                                    placeholder="请选择单位"
                                    name="unitId"
                                    query={{}}
                                    selectable={["UNIT"]}
                                    defaultExpandAll={false}
                                    onChange={(value, form) => {
                                        form?.setFieldValue("deptId", null);
                                        form?.reloadOptions("deptId");
                                    }}
                                />
                            </Form.Item>
                        </Row.Col>

                        <Row.Col span={5}>
                            <Form.Item name="deptId" label="部门">
                                <FormOrgTreeSelect
                                    placeholder="请选择部门"
                                    name="deptId"
                                    parentNodeFieldName="unitId"
                                    query={{needDept: true}}
                                    selectable={["DEPT"]}
                                    defaultExpandAll={false}
                                />
                            </Form.Item>
                        </Row.Col>

                        <Row.Col span={5}>
                            <FormInput
                                name="name"
                                label="合同名称"
                                placeholder="请输入合同名称"
                            />
                        </Row.Col>

                        <Row.Col span={5}>
                            <FormInput
                                name="serialNumber"
                                label="合同编号"
                                placeholder="请输入合同编号"
                            />
                        </Row.Col>

                        <Row.Col
                            span={4}
                            className="button-col"
                            style={{marginBottom: 50}}
                        >
                            <Space size={12}>
                                <Button type="primary" onClick={handleSearch}>
                                    查询
                                </Button>
                                <Button
                                    onClick={() => {
                                        POBrowser.setProxyBaseAPI("/itm-legal-review-contract");
                                        POBrowser.setHeader("token", localStorage.getItem("token"));
                                        const params = {
                                            token: localStorage.getItem("token"),
                                            username: localStorage.getItem("username"),
                                        };

                                        return POBrowser.openWindowModeless(
                                            `${location.href.replace(
                                                "/contract/list",
                                                "/contact-draft-page-office"
                                            )}`,
                                            `width=${screen.width}px;height=${screen.height}px;`,
                                            JSON.stringify(params)
                                        );
                                    }}
                                >
                                    合同起草
                                </Button>
                            </Space>
                        </Row.Col>
                    </Row>
                </IForm>
            </Card>

            {/* 表格卡片 */}
            <Card className="table-card">
                <div className="table-container">
                    {loading ? (
                        <div className="loading-placeholder">加载中...</div>
                    ) : notifications.length > 0 ? (
                        notifications.map((item, index) => (
                            <div
                                className={`contract-item ${loading ? "loading" : ""}`}
                                key={item.id || index}
                            >
                                <div className="item-left">
                                    <div className="item-checkbox">
                                        <Checkbox value={item.id || index}/>
                                    </div>
                                    <div className="item-content">
                                        <div className="item-title">
                                            {item.name || "未命名合同"}
                                        </div>
                                        <div className="item-details">
                                            <div className="detail-item">
                                                <span className="detail-label">履行部门:</span>
                                                <span className="detail-value">
                          {item.performanceDeptName || "未设置履行部门"}
                        </span>
                                            </div>
                                            <div className="detail-item">
                                                <span className="detail-label">金额:</span>
                                                <span className="detail-value">
                          ¥{formatAmount(item.amount)} 万元
                        </span>
                                            </div>
                                        </div>
                                        <div className="item-details">
                                            <div className="detail-item">
                                                <span className="detail-label">合同类型:</span>
                                                <span className="detail-value">
                          {item.type || "未设置合同类型"}
                        </span>
                                            </div>
                                            <div className="detail-item">
                                                <span className="detail-label">起止时间:</span>
                                                <span className="detail-value">
                          {formatDate(item.startDate)} 至{" "}
                                                    {formatDate(item.endDate)}
                        </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="item-right">
                                    <div className="item-info">
                                        <span className="info-label">合同编号:</span>
                                        <span className="info-value">
                      {item.serialNumber || "暂无编号"}
                    </span>
                                    </div>
                                    <div className="item-status">
                                        <span className="status-label">状态:</span>
                                        <span className={`status-value ${item.status || ""}`}>
                      {item.stageName || "未设置"}
                    </span>
                                    </div>
                                    <div className="item-process">
                                        <span className="process-label">流程:</span>
                                        {item.process ? (
                                            <span className={`process-value ${item.process || ""}`}>
                        <Badge status="processing" text={item.processName}/>
                      </span>
                                        ) : (
                                            <Badge status="default" text='未设置'/>
                                        )}
                                    </div>
                                </div>

                                <div className="item-actions">
                                    <Space size={8}>
                                        <Button
                                            type="primary"
                                            size="small"
                                            hidden={item.stage === "VeU9l25s3t2"}
                                            onClick={() => {
                                                POBrowser.setProxyBaseAPI("/itm-legal-review-contract");
                                                POBrowser.setHeader("token", localStorage.getItem("token"));
                                                const params = {
                                                    token: localStorage.getItem("token"),
                                                    username: localStorage.getItem("username"),
                                                    data: item.id,
                                                };
                                                console.log(params)
                                                return POBrowser.openWindowModeless(
                                                    `${location.href.replace(
                                                        "/contract/list",
                                                        "/contact-renewal-page-office"
                                                    )}`,
                                                    `width=${screen.width}px;height=${screen.height}px;`,
                                                    JSON.stringify(params)
                                                );
                                            }}
                                        >
                                            发起续签
                                        </Button>
                                        <Button
                                            type="primary"
                                            size="small"
                                            onClick={() => navigate(`/contract/change/${item.id}`)}
                                        >
                                            发起变更
                                        </Button>
                                        <Button
                                            danger
                                            size="small"
                                            onClick={() => navigate(`/contract/cancel/${item.id}`)}
                                        >
                                            发起解除
                                        </Button>
                                    </Space>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="no-data">暂无数据</div>
                    )}
                </div>
                <div className="pagination-container">
                    <Pagination
                        current={currentPage}
                        pageSize={pageSize}
                        defaultCurrent={1}
                        total={totalItems}
                        onChange={handlePageChange}
                        showSizeChanger
                        showQuickJumper
                        pageSizeOptions={["10", "20", "50", "100"]}
                    />
                </div>
            </Card>

            {/* 分页组件 */}
        </div>
    );
};

export default Institutional;
