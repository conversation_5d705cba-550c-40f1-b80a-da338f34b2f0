.institutional-contract-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 80px);
  
  .search-card {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
    
    .button-col {
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .table-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
    .table-container {
      max-height: calc(100vh - 300px);
      overflow-y: auto;
      
      .loading-placeholder {
        padding: 40px 0;
        text-align: center;
        color: #999;
      }
    }
  }
  
  .pagination-container {
    width: 100%;
    margin-top: 16px;
    text-align: right;

    .ant-pagination {
      display: block !important;
    }
    
    .ant-pagination-options-size-changer {
      margin-right: 8px;
    }
  }
}

.contract-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #fafafa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
    .item-actions {
      opacity: 1;
    }
  }
  
  &.loading {
    opacity: 0.6;
  }
}

.item-left {
  display: flex;
  flex: 1;
  padding-right: 16px;
}

.item-checkbox {
  margin-right: 16px;
  padding-top: 5px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--primary-color)
}

.item-details {
  display: flex;
  margin-bottom: 6px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  flex: 1;
  width: 100%;
  
  .detail-label {
    color: #999;
    width: 100px;
    font-size: var(--content-font-size);
  }
  
  .detail-value {
    flex: 1;
    font-size: var(--content-font-size);
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.item-right {
  width: 240px;
  padding-left: 16px;
  border-left: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-info, .item-status, .item-process {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label, .status-label, .process-label {
  color: #999;
  width: 70px;
  font-size: var(--content-font-size);
}

.info-value, .status-value, .process-value {
  flex: 1;
  font-size: var(--content-font-size);
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-value.active {
  color: #52c41a;
}

.status-value.pending {
  color: #faad14;
}

.status-value.draft {
  color: #d9d9d9;
}

.item-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-top: 10px;
  
  .ant-btn {
    padding: 0 8px;
    font-size: 12px;
    height: 28px;
    
    & + .ant-btn {
      margin-left: 4px;
    }
  }
}

.no-data {
  padding: 40px 0;
  text-align: center;
  color: #999;
}