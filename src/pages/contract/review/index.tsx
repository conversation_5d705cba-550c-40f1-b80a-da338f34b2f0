/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-01 21:33:25
 * @Description:
 * @LastEditTime: 2025-07-29 16:01:51
 * @LastEditors: wangjiantao
 */
import { Button, Space, Tabs, Row } from "@itm/components-pc";
import React, { Fragment, useEffect, useState } from "react";
import "./index.scss";
import { ExclamationCircleFilled, UserOutlined } from "@ant-design/icons";
import { Divider, Form } from "antd";
import { Input, message, Modal, type TabsProps } from "antd";
import GenericFormTable from "@/components/GenericFormTable";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import moment from "moment";
import {
  approval,
  queryContractById,
  sendBack,
  submitGeneralLegal,
  submitLeader,
} from "@/api/contract/contract";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import { File } from "@/components/contract/info";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { ISearchParams } from "../draft/types";
import { BUTTON_TEXTS, TEMPLATE_CODES } from "@/pages/user/notice/record/types";
import CustomSplitter from "@/components/CustomSplitter";
import { downloadOssFile } from "@/utils/download";
import { FlowImage } from "@/components/flowImage";

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  status: string;
  creator: string;
  createdAt: number;
  time?: string;
  isShow: boolean;
};

interface IProps extends ISearchParams {
  sourceName: string;
  depositRequired: boolean;
  performanceDeptName: string;
  categoryName: string;
  typeName: string;
  attachments: File[];
  advice?: string;
  process?: string;
  drafterName: string;
  name: string;
  draftTime: string;
  opinions: {
    advice?: string;
    deptName?: string;
    createTime?: number;
    id?: number;
    username: string;
  }[];
}

const ContractReview: React.FC<NoticeFormProps> = (props) => {
  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { recordId, contractId, templateCode , processId} = customParams;

  // start 是否可编辑状态
  const [dataSource, setDataSource] = useState<IProps>();
  const [podHtml, setPodHtml] = useState("");
  const [visible, setVisible] = useState(false);
  const [userId, setUserId] = useState<string | null>("");
  const [open, setOpen] = useState(false);

  const [form] = Form.useForm();

  const _items: TabsProps["items"] = [
    {
      key: "1",
      label: "按部门汇总",
    },
    {
      key: "2",
      label: "按时间汇总",
    },
  ];

  useEffect(() => {
    const loadContractById = async () => {
      const { success, data } = await queryContractById(contractId);
      if (!success) return;
      data.draftTime = moment(data.draftTime).format("YYYY-MM-DD HH:mm:ss");
      setDataSource(data);
    };

    const loadContractContentHtml = async () => {
      const { data, success } = await getContractContentHtml(contractId);
      if (!success) return;
      setPodHtml(data);
    };
    loadContractById();
    loadContractContentHtml();
  }, [props.data]);
  // end
  const [activeState, setActiveState] = useState<string>("1");

  const hash: Record<string, string> = {
    VeNp1ked1iC: "采购文件",
    VeNp4tRQdn6: "中标人投标文件",
    VeVRkxM4RmK: "中标通知书",
    VeVRsuuFf1M: "相关附件",
    VeVSK5c725w: "谈判纪要",
    VeVSLA74eP2: "请示批准文件",
    VeVSLjib4ym: "相关结果报告",
  };

  const categorizedFiles = (dataSource?.attachments ?? []).reduce(
    (acc: any, file: File): Record<string, File[]> => {
      const { fileCategory, ...rest } = file;
      // 只处理 hash 中存在的 fileCategory
      if (hash[fileCategory]) {
        const friendlyName = hash[fileCategory];

        if (!acc[friendlyName]) {
          acc[friendlyName] = [];
        }
        acc[friendlyName].push(rest);
      }

      return acc;
    },
    {}
  );

  const onChangeValue = (value: string) => {
    setDataSource((prevState) => {
      return {
        ...prevState!,
        advice: value,
      };
    });
  };

  const groupedByDept = dataSource?.opinions?.reduce((acc, item) => {
    const { deptName, advice, createTime, id, username } = item;
    if (!acc[deptName!]) {
      acc[deptName!] = [];
    }
    acc[deptName!].push({
      advice,
      id,
      createTime: new Date(createTime!).toLocaleString(), // 格式化时间
      username,
    });
    return acc;
  }, {} as Record<string, any>);

  // 表格头数据
  const tableHeader = [
    {
      label: "起草人",
      value: dataSource?.drafterName,
      isformLabel: false,
      isTimeCell: true,
    },
    {
      label: "起草时间",
      value: dataSource?.draftTime,
      isformLabel: false,
      isTimeCell: false,
    },
  ];
  // 表格数据
  const tableRows = [
    {
      label: "合同名称",
      value: dataSource?.name,
      isLabelRed: true,
    },
    {
      label: "合同相对方",
      value: dataSource?.partyBName,
      isLabelRed: true,
    },
    {
      label: "合同来源",
      value: dataSource?.sourceName,
      isLabelRed: true,
    },
    {
      label: "履约部门",
      value: dataSource?.performanceDeptName,
      isLabelRed: true,
    },
    {
      label: "标的类别",
      value: dataSource?.categoryName,
      isLabelRed: true,
    },
    {
      label: "合同类型",
      value: dataSource?.typeName,
      isLabelRed: true,
    },

    {
      label: "合同金额（万元）",
      value: dataSource?.amount,
      isLabelRed: true,
    },
    {
      label: "是否需缴纳履约保证金",
      value: <>{!dataSource?._depositRequired ? "否" : "是"}</>,
      isLabelRed: true,
    },
    {
      label: "附件",
      children: Object.entries(categorizedFiles).flatMap(
        ([category, files]) => [
          {
            label: category,
            value: (files as File[]).map((file: File) => (
              <div key={file.id}>
                <a onClick={() => downloadOssFile(file.fileKey)}>
                  {file.fileName}
                </a>
              </div>
            )),
          },
        ]
      ),
      isLabelRed: true,
    },
    {
      label: "已办意见",
      value: (
        <div className="opinions_already_processed">
          <Tabs defaultActiveKey="1" items={_items} onChange={setActiveState} />
          {activeState == "1" ? (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {Object.entries(groupedByDept || []).map(
                ([deptName, opinions]) => (
                  <div className="opinions_content" key={deptName}>
                    <div>
                      <div>【{deptName}】</div>
                      {opinions.map((opinion: any, index: number) => (
                        <div
                          className="approval-item"
                          key={index}
                          style={{ border: "none" }}
                        >
                          <div className="approval-header">
                            <UserOutlined
                              style={{ color: "var(--primary-color)" }}
                            />
                            [审批人]{opinion.username}: {opinion.advice}
                          </div>
                          <div className="approval-time">
                            {moment(opinion.createTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )}
                          </div>
                        </div>
                      ))}
                      <Divider size="small" />
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {dataSource?.opinions.map((i, index) => (
                <Fragment key={index}>
                  <div className="opinions_content">
                    <div>【{i.deptName}】</div>
                    <div className="approval-item" key={index}>
                      <div className="approval-header">
                        <UserOutlined
                          style={{ color: "var(--primary-color)" }}
                        />{" "}
                        [审批人]{i.username}: {i.advice}
                      </div>
                      <div className="approval-time">
                        {moment(i.createTime).format("YYYY-MM-DD HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                </Fragment>
              ))}
            </div>
          )}
        </div>
      ),
      isLabelRed: true,
    },
    {
      label: "反馈意见",
      value: (
        <>
          <Input.TextArea
            className="textareaStyle"
            placeholder="请输入反馈意见"
            autoSize={{ minRows: 1, maxRows: 10 }} // 固定行数
            value={dataSource?.advice}
            onChange={(e) => onChangeValue(e.target.value)}
          />
        </>
      ),
      isLabelRed: true,
    },
  ];

  const [activeKey, setActiveKey] = useState<string>("1");
  const onChange = (key: string) => {
    setActiveKey(key);
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  /**
   * @description 提交到法规部门
   * */
  /* TODO:
   1. 提交到法规部门尚未有该角色，所以先默认提交到admin账户
*/
  const handlerSubmitToGeneralLegal = async () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要送至法规部门吗?",
      async onOk() {
        const { success } = await submitGeneralLegal({
          contractId,
          userId: 1,
          advice: dataSource?.advice,
          recordId,
        });
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };

  /**
   * @description 送呈主要/分管领导
   * */
  const handlrSubitLeader = async () => {
    const params = {
      contractId,
      userId: Number(userId),
      recordId,
      advice: dataSource?.advice,
    };
    const { success } = await submitLeader(params);
    if (!success) return;
    message.success("提交成功");
    setTimeout(() => {
      props.setVisible(false);
    }, 1000);
  };

  /**
   * @description 退回修改
   * */

  const handlerRecall = () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要退回吗?",
      async onOk() {
        const params = {
          contractId,
          advice: form.getFieldValue("advice"),
          recordId,
          processId
        };
        const { success } = await sendBack(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          setTimeout(() => {
            props.setVisible(false);
          }, 1000);
        }, 1000);
      },
    });
  };

  /**
   * @description 审批通过 在部门领导审核通过实际是将流程送到法规部门
   * */
  const handlerApproval = async () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要审核通过吗?",
      async onOk() {
        /* TODO:
   1. 提交到法规部门尚未有该角色，所以先默认提交到admin账户
*/
        const params = {
          contractId,
          // userId: Number(userId),
          userId: 1,
          advice: dataSource?.advice,
          recordId,
        };
        const http =
          dataSource?.process === "leader_review"
            ? submitGeneralLegal
            : approval;
        const { success } = await http(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
    });
  };

  const shouldShowLeaderButtons = () => {
    return !(
      templateCode === TEMPLATE_CODES.CONTRACT_DRAFT_LEADER ||
      templateCode === TEMPLATE_CODES.REGULATORY_REVIEW ||
      templateCode === TEMPLATE_CODES.CONTRACT_DRAFT_LEGAL_DEPT
    );
  };

  const shouldShowApprovalButton = () => {
    return (
      templateCode === TEMPLATE_CODES.CONTRACT_DRAFT_LEADER ||
      templateCode === TEMPLATE_CODES.REGULATORY_REVIEW ||
      templateCode === TEMPLATE_CODES.CONTRACT_DRAFT_LEGAL_DEPT
    );
  };

  return (
    <>
      <div className="sixContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>
          <Space>
            <Button
              color="danger"
              variant="solid"
              onClick={() => setOpen(true)}
            >
              退回修改
            </Button>

            {shouldShowLeaderButtons() && (
              <Space>
                <Button type="primary" onClick={() => setVisible(true)}>
                  {BUTTON_TEXTS.SUBMIT_TO_LEADER}
                </Button>
                <Button type="primary" onClick={() => setVisible(true)}>
                  {BUTTON_TEXTS.SUBMIT_TO_MANAGER}
                </Button>
                <Button type="primary" onClick={handlerSubmitToGeneralLegal}>
                  {BUTTON_TEXTS.SUBMIT_TO_LEGAL}
                </Button>
              </Space>
            )}

            {shouldShowApprovalButton() && (
              <Space>
                <Button type="primary" onClick={handlerApproval}>
                  {BUTTON_TEXTS.APPROVE}
                </Button>
              </Space>
            )}
          </Space>
        </div>
      </div>
      <div className="sixContractDrafting_body_box">
        {activeKey == "1" && (
          <>
            <CustomSplitter
              style={{ padding: 12 }}
              leftDefaultSize={825}
              left={
                <div
                  style={{
                    transform: `scale(1.0)`,
                    transformOrigin: "top left", // 设置缩放原点为左上角
                    width: "789px",
                    height: "calc(100vh - 120px)",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: podHtml,
                  }}
                />
              }
              right={<GenericFormTable header={tableHeader} rows={tableRows} />}
            />
          </>
        )}
        {activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>

      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={visible}
        cancelText="取消"
        okText="确定"
        onCancel={() => setVisible(false)}
        onOk={handlrSubitLeader}
      >
        <UserChoiceSelect
          multiple={false}
          onChange={setUserId}
          value={userId || undefined}
        />
      </Modal>

      <Modal
        title="退回修改"
        width={300}
        centered
        open={open}
        cancelText="取消"
        okText="确定"
        onCancel={() => setOpen(false)}
        onOk={async () => {
          try {
            const result = await form.validateFields();
            if (!result) return;
            handlerRecall();
          } catch (error) {}
        }}
      >
        <Form form={form} name="dynamic_rule">
          <Form.Item
            name="advice"
            label="退回原因"
            rules={[{ required: true, message: "请输入退回原因" }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default ContractReview;
