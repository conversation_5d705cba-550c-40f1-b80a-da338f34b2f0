import React, {Fragment, useState} from "react";
import "./index.scss";
import OriginalContractInfo from "@/components/contract/originalInfo";
import RenewalContractInfoDraft from "@/components/contract/renewal/renewalDraft";
import {jointReview, save} from "@/api/contract/contract"
import ChoiceTemplate from "@/components/contract/choiceTemplate";
import {FlowImage} from "@/components/flowImage";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import {message} from "antd";
import {submitDepGeneralTerm} from "@/api/contract/flow";
import {Modal} from "@/components-pc";


interface Item {
    title: string;
    description: string;
    component: React.ReactNode;
    showWhen: boolean;
}

const Renewal = (props: any) => {
    const [activeTab, setActiveTab] = useState("表单"); // 默认选中“表单”标签

    const [contract, setContract] = useState<any>({});
    const [contractId, setContractId] = useState<number>(0);
    const [originalContract, setOriginalContract] = useState<any>({});

    const [loading, setLoading] = useState<boolean>(false);

    const [userSelectorVisibleForJointReview, setUserSelectorVisibleForJointReview] = useState(false);
    const [userSelectorVisible, setUserSelectorVisible] = useState(false);

    const [selectedUser, setSelectedUser] = useState<{ userId: any }>({
        userId: null,
    });
    const [userIds, setUserIds] = useState<string | null>("");

    const formsData: Item[] = [
        {
            title: "原合同信息",
            description: "原合同信息",
            component: <OriginalContractInfo
                originalContractId={11}
                defaultActive={true}
                onChange={(value) => {
                    setOriginalContract(value);
                }}/>,
            showWhen: true,
        }, {
            title: "合同续签信息",
            description: "合同续签信息",
            component: <RenewalContractInfoDraft contractName={originalContract?.name} onChange={(value) => {
                setContract(value)
            }}/>,
            showWhen: true,
        }];

    const renderContent = () => {
        switch (activeTab) {
            case "表单":
                return (
                    <div className="content-container">
                        {formsData.map((step, index) => (
                            <Fragment key={index}>
                                {step.showWhen && step.component}
                            </Fragment>
                        ))}
                    </div>
                );
            case "正文":
                return <div className={"content-content"}>
                    <ChoiceTemplate/>
                </div>;
            case "流程":
                return (
                    <FlowImage workCode="contract_draft"/>
                )
            default:
                return null;
        }
    };

    // 发往会审
    const handlerSubmitToJointReview = async () => {
        setLoading(true);
        await handlerSaveContract();
        const params = {
            contractId: contractId,
            userIds: (userIds || "").split(","),
            flag: 1,
        };
        const {success} = await jointReview(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleForJointReview(false);
        message.success("发往会审成功");
    }

    // 送呈部门负责人
    const handlerSubmitToDepartment = async () => {
        setLoading(true);
        await handlerSaveContract();
        const params = {
            contractId: contractId,
            userId: selectedUser.userId,
            flag: 1,
        };
        const {success} = await submitDepGeneralTerm(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisible(false);
        message.success("送呈部门负责人成功");
    }

    const handlerSaveContract = async () => {
        const params: {
            id?: number;
            originalContractId: number;
            name: string;
            amount: number;
            reason: string;
            attachments: any;
        } = {
            originalContractId: originalContract.id,
            name: contract.name,
            amount: contract.amount,
            reason: contract.reason,
            attachments: contract.attachments,
        };
        if (contractId && contractId > 0) {
            params.id = contractId;
        }
        save(params).then(res => {
            setContractId(res.data)
        })
    }

    const verifyInfo = async () => {
        if (originalContract) {
            message.warning("原合同不存在")
            return false;
        }
        if (!contract.name || contract.name.trim() === "") {
            message.warning("请输入续签合同名称");
            return false;
        }
        if (!contract.amount) {
            message.warning("请输入合同金额");
            return false;
        }
        if (!contract.reason || contract.reason.trim() === "") {
            message.warning("请输入续签事由");
            return false;
        }
        if (!contract.attachments || contract.attachments.length === 0) {
            message.warning("请上传附件");
            return false;
        }
        return true;
    }

    const handlerOpenSelectUserModal = async () => {
        verifyInfo().then((result) => {
            if (result) {
                setUserSelectorVisible(true);
            }
        })
    };

    const handlerOpenSelectUserForJointReviewModal = async () => {
        verifyInfo().then((result) => {
            if (result) {
                setUserSelectorVisibleForJointReview(true);
            }
        });
    };

    return (
        <div className="compliance_box">
            {/* 顶部标签页和按钮 */}
            <div className="tab-header-buttons">
                <div className="tab-header">
                    <div className={`tab-item ${activeTab === "表单" ? "active" : ""}`}
                         onClick={() => {
                             setActiveTab("表单");
                         }}>
                        表单
                    </div>
                    <div className={`tab-item ${activeTab === "正文" ? "active" : ""}`}
                         onClick={() => {
                             setActiveTab("正文");
                         }}>
                        正文
                    </div>
                    <div className={`tab-item ${activeTab === "流程" ? "active" : ""}`}
                         onClick={() => setActiveTab("流程")}>流程
                    </div>
                </div>

                {/* 右侧按钮 */}
                <div className="action-buttons">
                    <button onClick={handlerOpenSelectUserForJointReviewModal}>发往会审</button>
                    <button onClick={handlerOpenSelectUserModal}>送呈部门负责人</button>
                </div>
            </div>

            {/* 内容区域 */}
            {renderContent()}

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisibleForJointReview}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleForJointReview(false);
                }}
                onOk={handlerSubmitToJointReview}
                onClose={() => {
                    setUserSelectorVisibleForJointReview(false);
                }}
            >
                <UserChoiceSelect
                    multiple
                    onChange={setUserIds}
                    value={userIds || ""}
                />
            </Modal>

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisible}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisible(false);
                }}
                onOk={handlerSubmitToDepartment}
                onClose={() => {
                    setUserSelectorVisible(false);
                }}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>
        </div>
    );
};
export default Renewal;
