.draft-container {
  border: 1px solid #e5eaf7;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;

  .ant-form-item {
    margin-bottom: 0 !important;
  }

  .oneContractDrafting_header_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin-bottom: 15px;
    padding: 0 12px;

    .ant-form-item {
      margin-bottom: 0 !important;
    }

    .tab_item {
      .ant-tabs-tab-btn {
        padding: 5px 20px;
      }

      .ant-tabs-nav {
        margin: 0;
      }
    }

    .ant-tabs-tab-btn {
      padding: 10px 10px;
    }

    :global(.ant-form-item) {
      margin-bottom: 0 !important;
    }

    input {
      height: 40px !important;
    }
  }
}

.oneContractDrafting_body_box {
  background-color: white;
  padding: 20px 200px;
  height: calc(100vh - 210px);
}

.oneResearch_process {
  width: 100%;
  height: calc(100vh - 240px);
  background-color: white;
}
.flex_box {
  width: 100%;
  text-align: right;

  .flex_between {
    display: flex;
    justify-content: space-between;
  }
  .flex_align {
    color: green;
    display: flex;
    align-items: center;
  }
}

.container {
  display: flex;
  height: 100vh;
  width: 100%;
  position: relative;
}
