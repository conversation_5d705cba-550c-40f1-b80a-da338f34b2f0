/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description:
 * @LastEditTime: 2025-07-28 16:37:27
 * @LastEditors: wangjiantao
 */
import {Button, Space, Tabs, Modal} from "@/components-pc";
import React, {ChangeEvent, useMemo, useRef} from "react";
import "./index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import {getDictionaryTag} from "@/utils/dictionary";
import {FormOption} from "@/ui-framework";
import {Col, Form as AForm, Row} from "antd";
import {Form, FormInput, FormRadio} from "@/form-pc";
import {usePresenter} from "./hook/usePresenter";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import FormUserChoiceSelect from "@/components/user/choice/FormUserChoiceSelect";
import {DictSelect} from "@/components/dict";
import OssFileUploader from "@/components/oss/OssFileUpload";
import SupplierManagePage from "@/pages/supplier";
import Template from "@/pages/template/index";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import {getWindowParams} from "@/utils/pageofficeParams";
import {ATTACHMENT_CONFIG, IDataSource} from "./types";
import {ProCard} from "@ant-design/pro-components";
import AmountInput from "@/components/contract/amount";
import {FlowImage} from "@/components/flowImage";
import ChoiceTemplate from "@/components/contract/choiceTemplate";

const Draft = (props: any) => {
    if (getWindowParams()) {
        const prams = getWindowParams();
        localStorage.setItem("token", prams.token);
        localStorage.setItem("username", prams.username);
    }

    const {
        form,
        tableHeader,
        tabs,
        dataSource,
        onInputChange,
        visible,
        setVisible,
        fileKey,
        setFileKey,
        uploadData,
        setUploadData,
        templateVisible,
        setTemplateVisible,
        setTemplateId,
        activeKey,
        setActiveKey,
        handleSave,
        handlerOpenSubmitDepGeneralTerm,
        setFlowParams,
        flowParams,
        flowUserSelectorVisible,
        setFlowUserSelectorVisible,
        handlerSubmitDepGeneralTerm,
        poHtmlCode,
        showDialog,
        hideDialog,
        show,
        setShow,
        userIds,
        setUserIds,
        handlerSubmitJointReview,
        handlerSaveToDraft,
        loading,
    } = usePresenter();

    const options: FormOption[] = [
        {label: "是", value: "0"},
        {label: "否", value: "1"},
    ];


    // 表格数据
    const generateTableRows = (dataSource: IDataSource) => {
        const commonFields = [
            {
                label: "合同名称",
                value: dataSource.isEdit ? (
                    <FormInput
                        name="name"
                        required
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["合同名称不能为空"];
                        }}
                        onChange={(e: ChangeEvent<HTMLInputElement>) =>
                            onInputChange(e.target.value, "name")
                        }
                    />
                ) : (
                    dataSource.searchParams.name || ""
                ),
                isLabelRed: true,
            },
            {
                label: "合同甲方",
                value: dataSource.isEdit ? (
                    <FormOrgTreeSelect
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["合同甲方不能为空"];
                        }}
                        placeholder=""
                        name={"partyA"}
                        query={{}}
                        selectable={["UNIT"]}
                        onChange={(value, form) => {
                            form?.setFieldValue("performanceDeptId", null);
                            // 重新加载子字段的选项
                            form?.reloadOptions("performanceDeptId");
                            form?.setFieldValue("acceptanceDeptId", null);
                            // 重新加载子字段的选项
                            form?.reloadOptions("acceptanceDeptId");
                            onInputChange(value, "partyA");
                        }}
                    />
                ) : (
                    getDictionaryTag("carAbnormal", dataSource.searchParams.partyA)
                ),
                isLabelRed: true,
            },
            {
                label: "合同相对方",
                // partyB
                value: (
                    <AForm.Item
                        rules={[{required: true, message: "请选择合同向对方"}]}
                        name="partyB"
                    >
                        <Row justify="space-between" style={{padding: "0 12px"}}>
                            <Col span={21}>{dataSource.searchParams.partyBName}</Col>
                            {dataSource.isEdit && (
                                <Col span={3} style={{textAlign: "right"}}>
                                    <Button type="primary" onClick={() => setVisible(true)}>
                                        选择供应商
                                    </Button>
                                </Col>
                            )}
                        </Row>
                    </AForm.Item>
                ),
                isLabelRed: true,
            },
            {
                label: "合同来源",
                value: dataSource.isEdit ? (
                    <DictSelect
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["合同来源不能为空"];
                        }}
                        name="source"
                        labelCode="LeNgI92Gy8W"
                        onChange={(value) => onInputChange(value, "source")}
                    />
                ) : (
                    getDictionaryTag("LeNgI92Gy8W", dataSource.searchParams.source)
                ),
                isLabelRed: true,
            },
            {
                label: "履行单位/部门",
                colSpan: 3,
                value: dataSource.isEdit ? (
                    <FormOrgTreeSelect
                        placeholder=""
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["履行单位/部门不能为空"];
                        }}
                        name="performanceDeptId"
                        parentNodeFieldName={"partyA"}
                        query={{needDept: true}}
                        selectable={["DEPT"]}
                        onChange={(e: string) => onInputChange(e!, "performanceDeptId")}
                    />
                ) : (
                    getDictionaryTag(
                        "carAbnormal",
                        dataSource.searchParams.performanceDeptId
                    )
                ),
                valueType: (
                    <Space size={1}>
            <span
                style={{
                    color: "red",
                    marginRight: "20px",
                    fontWeight: "bold",
                    paddingLeft: 12,
                }}
            >
              经办人
            </span>
                        {dataSource.isEdit ? (
                            <FormUserChoiceSelect
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return [];
                                    }
                                    return ["经办人不能为空"];
                                }}
                                name="performanceAgentId"
                                onChange={(e: string) =>
                                    onInputChange(e!, "performanceAgentId")
                                }
                                value={dataSource.searchParams.performanceAgentId || undefined}
                            />
                        ) : (
                            getDictionaryTag(
                                "carAbnormal",
                                dataSource.searchParams.performanceAgentId
                            )
                        )}
                    </Space>
                ),
                isLabelRed: true,
            },
            {
                label: "合同验收部门",
                colSpan: 3,
                value: dataSource.isEdit ? (
                    <FormOrgTreeSelect
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["合同验收部门不能为空"];
                        }}
                        placeholder=""
                        name="acceptanceDeptId"
                        parentNodeFieldName={"partyA"}
                        query={{needDept: true}}
                        selectable={["DEPT"]}
                        onChange={(e: string) => onInputChange(e!, "acceptanceDeptId")}
                    />
                ) : (
                    getDictionaryTag(
                        "carAbnormal",
                        dataSource.searchParams.acceptanceDeptId
                    )
                ),
                valueType: (
                    <Space size={1}>
            <span
                style={{
                    color: "red",
                    marginRight: "20px",
                    fontWeight: "bold",
                    paddingLeft: 12,
                }}
            >
              经办人
            </span>
                        {dataSource.isEdit ? (
                            <FormUserChoiceSelect
                                validateFunction={async (content) => {
                                    const value = content.value;
                                    if (value) {
                                        return [];
                                    }
                                    return ["经办人不能为空"];
                                }}
                                name="acceptanceAgentId"
                                onChange={(e: string) => onInputChange(e!, "acceptanceAgentId")}
                                value={dataSource.searchParams.acceptanceAgentId || undefined}
                            />
                        ) : (
                            getDictionaryTag(
                                "carAbnormal",
                                dataSource.searchParams.acceptanceAgentId
                            )
                        )}
                    </Space>
                ),
                isLabelRed: true,
            },
            {
                label: "标的类别",
                value: dataSource.isEdit ? (
                    <DictSelect
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["标的类别不能为空"];
                        }}
                        labelCode="LeNgP003Tl2"
                        name="category"
                        onChange={(value) => onInputChange(value, "category")}
                    />
                ) : (
                    getDictionaryTag("LeNgP003Tl2", dataSource.searchParams.category)
                ),
                isLabelRed: true,
            },
            {
                label: "合同类型",
                value: dataSource.isEdit ? (
                    <DictSelect
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return [];
                            }
                            return ["合同类型不能为空"];
                        }}
                        labelCode="LeNT58Sx4gS"
                        name="type"
                        onChange={(value) => onInputChange(value, "type")}
                    />
                ) : (
                    getDictionaryTag("LeNT58Sx4gS", dataSource.searchParams.type)
                ),
                isLabelRed: true,
            },
            {
                label: "合同金额（万元）",
                value: dataSource.isEdit ? (
                    <AmountInput
                        defaultValue={
                            dataSource.searchParams.amount
                                ? dataSource.searchParams.amount
                                : undefined
                        }
                        placeholder="请输入金额"
                        style={{width: "100%"}}
                        name="amount"
                        min={0}
                        max={999999999}
                        onChange={(value) => onInputChange(String(value), "amount")}
                    />
                ) : (
                    dataSource.searchParams.amount
                ),
                isLabelRed: true,
            },
            {
                label: "是否需缴纳履约保证金",
                value: (
                    <div style={{paddingLeft: 12}}>
                        <FormRadio
                            validateFunction={async (content) => {
                                const value = content.value;
                                if (value) {
                                    return [];
                                }
                                return ["是否需缴纳履约保证金不能为空"];
                            }}
                            name="_depositRequired"
                            options={options}
                        />
                    </div>
                ),
                isLabelRed: true,
            },
        ];

        const attachmentFields = {
            label: "附件",
            children: ATTACHMENT_CONFIG[dataSource.searchParams.source || ""]?.map(
                (item: any) => renderAttachmentItem(item)
            ),
            isLabelRed: true,
        };

        return [...commonFields, attachmentFields];
    };

    const renderAttachmentItem = ({
                                      label,
                                      key,
                                      fileCategory,
                                  }: {
        label: string;
        key: string;
        fileCategory: string;
    }) => ({
        label,
        value: (
            <div style={{display: "flex", justifyContent: "flex-end"}}>
                <OssFileUploader
                    label=""
                    uploaderAccept="image/jpeg,image/jpg,application/pdf"
                    uploaderMaxCount={5}
                    bucket="document"
                    ossFileKey={fileKey[fileCategory] || ""}
                    onKeyChange={(key: string) => {
                        if (fileKey[fileCategory] === undefined) fileKey[fileCategory] = "";
                        fileKey[fileCategory] = key;
                        setFileKey({...fileKey});
                        setUploadData([
                            ...uploadData,
                            {
                                fileKey: key.split(",")[key.split(",").length - 1],
                                fileCategory,
                            },
                        ]);
                    }}
                />
            </div>
        ),
    });


    const tableRows = useMemo(
        () => generateTableRows(dataSource),
        [dataSource, uploadData]
    );

    return (
        <ProCard boxShadow>
            <div className="draft-container">
                <Form form={form} layout={"vertical"} onFinish={handleSave}>
                    <div className="oneContractDrafting_header_box">
                        <div className="tab_item">
                            <Tabs
                                className="item_tabs"
                                defaultActiveKey={activeKey}
                                items={tabs}
                                onChange={setActiveKey}
                            />
                        </div>
                        <Row style={{width: "100%"}}>
                            <Col span={16}/>
                            <Col span={4}>
                                {activeKey === "1" && (
                                    <Button type="primary" onClick={() => handlerSaveToDraft(1)}>
                                        保存表单
                                    </Button>
                                )}
                            </Col>
                            <Col span={4}>
                                <Space>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            showDialog();
                                            setShow(true);
                                        }}
                                    >
                                        发往会审
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            handlerOpenSubmitDepGeneralTerm();
                                            showDialog();
                                        }}
                                    >
                                        送呈部门负责人
                                    </Button>
                                </Space>
                            </Col>
                        </Row>
                    </div>
                    <div
                        className="oneContractDrafting_body_box"
                        style={{display: activeKey === "1" ? "block" : "none"}}
                    >
                        <div className="oneResearch_conter">
                            <GenericFormTable header={tableHeader} rows={tableRows}/>
                        </div>
                    </div>
                    {activeKey == "2" && (
                        <div className={"content-content"}>
                            <ChoiceTemplate/>
                        </div>
                    )}
                    {activeKey == "3" && (
                        <FlowImage workCode="contract_draft"/>
                    )}
                </Form>
                <Modal
                    title="选择供应商"
                    open={visible}
                    okText="选择"
                    cancelText="取消"
                    className={"flow-modal"}
                    onOk={() => setVisible(false)}
                    onClose={() => {
                        if (activeKey === "2") hideDialog();
                        setVisible(false);
                    }}
                    onCancel={() => {
                        if (activeKey === "2") hideDialog();
                        setVisible(false);
                    }}
                >
                    <SupplierManagePage
                        isComponent
                        onSelectedChange={(id: number, name: string) => {
                            onInputChange(String(id), "partyB");
                            onInputChange(name, "partyBName");
                            form.setFieldValue("partyB", id);
                        }}
                    />
                </Modal>
                <Modal
                    title="选择审批人员"
                    width={800}
                    centered
                    open={show}
                    cancelText="取消"
                    confirmLoading={loading}
                    okText="确定"
                    onCancel={() => {
                        setShow(false);
                        if (activeKey === "2") hideDialog();
                    }}
                    onOk={handlerSubmitJointReview}
                    onClose={() => {
                        setShow(false);
                        if (activeKey === "2") hideDialog();
                    }}
                >
                    <UserChoiceSelect
                        multiple
                        onChange={setUserIds}
                        value={userIds || ""}
                    />
                </Modal>

                <Modal
                    title="选择审批人员"
                    width={800}
                    centered
                    open={flowUserSelectorVisible}
                    confirmLoading={loading}
                    cancelText="取消"
                    okText="确定"
                    onCancel={() => {
                        setFlowUserSelectorVisible(false);
                        if (activeKey === "2") hideDialog();
                    }}
                    onClose={() => {
                        if (activeKey === "2") hideDialog();
                        setFlowUserSelectorVisible(false);
                    }}
                    onOk={handlerSubmitDepGeneralTerm}
                >
                    <UserChoiceSelect
                        onChange={(e: string | null) => setFlowParams({userId: e})}
                        value={flowParams.userId}
                    />
                </Modal>
            </div>
        </ProCard>
    );
};
export default Draft;
