import { ComponentBus } from "@/ui-framework";
import { ComponentType, useEffect, useState } from "react";

 export const useMicroFrontendComponents = (componentNames: string[]) => {
  // 方案1：允许undefined
  const [components, setComponents] = useState<Record<string, ComponentType<any>>>({});
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadComponents = async () => {
      const loadedComponents: Record<string, ComponentType<any>> = {};
      
      for (const name of componentNames) {
        try {
          loadedComponents[name] = await ComponentBus.getInstance().getComponent(name) as ComponentType<any>;
        } catch (error) {
          console.error(`加载组件 ${name} 失败:`, error);
          delete loadedComponents[name]; // 明确设置为undefined
        }
      }
        
        console.log(loadedComponents, 'loadedComponents');
        

      setComponents({...loadedComponents});
      setIsLoaded(true);
    };

    loadComponents();
  }, [componentNames]);

  return { components, isLoaded };
 };
