/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description:
 * @LastEditTime: 2025-07-22 08:41:24
 * @LastEditors: wangjiantao
 */
import moment from "moment";
import { message, TabsProps } from "antd";
import { useEffect, useState } from "react";
import { saveContractAttachment } from "@/api/contract/contractAttachment";
import {
  getCurrentContract,
  jointReview,
  queryContractById,
  saveContractData,
  saveToDraft,
} from "@/api/contract/contract";
import { submitDepGeneralTerm, detail } from "@/api/contract/flow";
import { wordFunctionalApi } from "@/api/pageoffice";
import { Attachment, IDataSource, ISearchParams } from "../types";
import {Form} from "@/components/form-pc";
export const usePresenter = () => {
  const form = Form.useForm();

  // start 是否可编辑状态
  const [dataSource, setDataSource] = useState<IDataSource>({
    searchParams: {
      partyB: null,
      partyA: null,
      name: "",
      source: null,
      performanceDeptId: null,
      performanceAgentId: null,
      acceptanceDeptId: null,
      acceptanceAgentId: null,
      category: null,
      type: null,
      amount: 0,
      partyBName: "",
      _depositRequired: "1",
      draftName: localStorage.getItem("username") ?? "",
      draftTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    },
    isEdit: true,
  });

  //start 上传附件
  const [uploadData, setUploadData] = useState<Attachment[]>([]);

  const [visible, setVisible] = useState(false);

  const [fileKey, setFileKey] = useState<Record<string, string>>({});

  const [templateVisible, setTemplateVisible] = useState(false);

  const [flowUserSelectorVisible, setFlowUserSelectorVisible] = useState(false);

  const [templateId, setTemplateId] = useState<number | null>(null);

  const [activeKey, setActiveKey] = useState<any>("1");


  const [poHtmlCode, setPoHtmlCode] = useState<any>("");

  const [show, setShow] = useState(false);

  const [userIds, setUserIds] = useState<string | null>("");

  const [loading, setLoading] = useState(false);

  const [isSaveWord, setIsSaveWord] = useState(false);

  const [flowParams, setFlowParams] = useState<{ userId: any }>({
    userId: null,
  });

  const [tabs, setTabs] = useState<TabsProps["items"]>([
    {
      key: "1",
      label: "表单",
      disabled: false,
    },
    {
      key: "2",
      label: "正文",
      disabled: false,
    },
    {
      key: "3",
      label: "流程",
      disabled: false,
    },
  ]);
  // 表格头数据
  const tableHeader = [
    {
      label: "起草人",
      value:
        dataSource.searchParams.draftName ?? localStorage.getItem("username"),
      isFormLabel: false,
      isTimeCell: false,
    },
    {
      label: "起草时间",
      value: dataSource.searchParams.draftTime,
      isFormLabel: false,
      isTimeCell: false,
    },
  ];

  function formatInput(value: string) {
    if (value.startsWith("-")) return;

    let formattedValue = value;

    if (
      formattedValue.length > 1 &&
      formattedValue.startsWith("0") &&
      !formattedValue.startsWith("0.")
    ) {
      formattedValue = formattedValue.replace(/^0+/, "") || "0";
    }

    if (formattedValue === ".") {
      formattedValue = "0.";
    } else if (formattedValue.startsWith(".")) {
      formattedValue = "0" + formattedValue;
    }

    const regex = /^(\d+)?(\.\d{0,6})?$/;

    if (!regex.test(formattedValue)) {
      const match = formattedValue.match(/^(\d+)?(\.\d{0,6})?/);
      if (match) {
        formattedValue = match[0];
      } else {
        return;
      }
    }

    setDataSource((prev) => ({
      ...prev,
      searchParams: {
        ...prev.searchParams,
        amount: formattedValue,
      },
    }));
  }

  const onInputChange = (value: string, fieldName: string) => {
    if (fieldName === "partyA") {
      form.setFieldValue("acceptanceDeptId", null);
      form.setFieldValue("performanceDeptId", null);
      setDataSource((prev) => ({
        ...prev,
        searchParams: {
          ...prev.searchParams,
          acceptanceDeptId: null,
          performanceDeptId: null,
        },
      }));
    }

    setDataSource((prev) => ({
      ...prev,
      searchParams: {
        ...prev.searchParams,
        [fieldName]: value,
      },
    }));
  };

  const fetchFile = async (fileId?: number) => {
    try {
      const params = {
        id: fileId || null,
      };
      setPoHtmlCode("");
      const response = await wordFunctionalApi(params);

      setPoHtmlCode(response);
      if (window.pageofficectrl) {
      }
    } catch (error) {
      console.error("获取文件失败:", error);
    }
  };

  /**
   * @description 选择合同模板callback
   * */
  const handlerSelectedTemplateAfter = async () => {
    if (!templateId) return;
    const params = {
      contractId: dataSource.searchParams.id || 7,
      templateId,
    };
    const { success } = await saveContractAttachment(params);
    setTemplateVisible(false);
    if (!success) false;
    await fetchFile(params.contractId);
  };

    /**
     * 将 JSON 对象中的每个数组 value 拼接成逗号分隔的字符串
     * @param {Object} data - 输入的 JSON 对象
     * @returns {Object} - 新对象，其中每个 value 是拼接后的字符串
     */
    function concatenateArrayValues(data: any) {
        const result:Record<string, string> = {};

        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                const valueArray = data[key];
                
                // 确保 value 是数组
                if (Array.isArray(valueArray)) {
                    // 使用 join 方法将数组元素用逗号连接
                    result[key] = valueArray.join(',');
                } else {
                    // 如果 value 不是数组，保持原样（或根据需求处理）
                    result[key] = valueArray;
                }
            }
        }

        return result;
    }

  /**
   * @description 保存后根据所反id获取保存后的数据
   * */
  const loadContractById = async (contractId: number) => {
    const { data, success } = await queryContractById(contractId);
    if (!success) return;
    data._depositRequired = !data.depositRequired ? "1" : "0";
    data.draftTime = moment(data.draftTime).format("YYYY-MM-DD HH:mm:ss");

    setDataSource((prev) => ({
      ...prev,
      searchParams: data,
    }));
    form.setFieldsValue(data);
    setUploadData([...data.attachments]);
    const _fileKey: Record<string, any> = {};

    data.attachments.forEach((i: any) => {
      if (_fileKey[i.fileCategory] === undefined) {
        _fileKey[i.fileCategory] = [];
      }
      _fileKey[i.fileCategory].push(i.fileKey);
    });
    
    setTimeout(() => {
      form.reloadOptions("performanceDeptId");
      form.reloadOptions("acceptanceDeptId");
    }, 100);
    if (JSON.stringify(_fileKey) === "{}") return;
    
    setFileKey(concatenateArrayValues(_fileKey));
  };

  /**
   * @description 保存表格信息
   * */
  const handleSave = async () => {
    form.validate().then(async (res) => {
      if (res) {
        const values = form.getFieldsValue();
        try {
          interface newDataSource extends ISearchParams {
            depositRequired?: boolean;
            attachments?: Attachment[];
          }

          let params: newDataSource = values as ISearchParams;
          params.depositRequired =
            params._depositRequired === "0" ? true : false;
          params.attachments = uploadData;

          params = {
            ...dataSource.searchParams,
            ...params,
            partyB: dataSource.searchParams.partyB,
          };

          // 调用接口保存数据
          const { data, success } = await saveContractData(params);

          if (!success) return;
          loadContractById(data);
        } catch (err) {}
      }
    });
  };

  useEffect(() => {
    const init = async () => {
      if (activeKey === "2") {
        await handlerSaveToDraft();
        if (!isSaveWord) {
          setIsSaveWord(true);
          setTimeout(() => {
            fetchFile(dataSource.searchParams.id);
          }, 1000);
        }
      }
    };
    init();
  }, [activeKey]);


  /**
   * @description 打开送呈部门负责人
   * */

  const handlerOpenSubmitDepGeneralTerm = async () =>
    setFlowUserSelectorVisible(true);

  const handlerSubmitDepGeneralTerm = async () => {
    try {
      form.validate().then(async (r) => {
        if (!r) {
          window.pageofficectrl.Enabled = false;
          setActiveKey("1");
          return;
        }

        setLoading(true);
        await handlerSaveToDraft();
        const params = {
          contractId: dataSource.searchParams.id,
          userId: flowParams.userId,
        };
        const { success } = await submitDepGeneralTerm(params);
        setLoading(false);
        if (!success) return;
        message.success("送呈部门负责人成功");
        setTimeout(() => {
          window.pageofficectrl?.CloseWindow();
        }, 1000);
      });
    } catch (error) {
    } finally {
      setFlowUserSelectorVisible(false);
      if (activeKey === "2") hideDialog();
    }
  };

  const showDialog = () => {
    window.pageofficectrl.Enabled = false;
  };

  const hideDialog = () => {
    window.pageofficectrl.Enabled = true;
  };

  /**
   * @description 发往会审
   * */
  const handlerSubmitJointReview = async () => {
    try {
      form.validate().then(async (r) => {
        if (!r) return setActiveKey("1");
        setLoading(true);

        await handlerSaveToDraft();
        const params = {
          contractId: dataSource.searchParams.id!,
          userIds: (userIds || "").split(","),
        };
        const { success } = await jointReview(params);
        setLoading(false);
        if (!success) return;
        message.success("发往会审成功");
        setTimeout(() => {
          window.pageofficectrl?.CloseWindow();
        }, 1000);
        hideDialog();
      });
    } catch (error) {
    } finally {
      setShow(false);
      setVisible(false);
      if (activeKey === "2") hideDialog();
    }
  };

  useEffect(() => {
    const tab = document.querySelectorAll(".ant-tabs-tab");

    const tag = document.createElement("div");
    tag.textContent = "";
    tag.style.background = "#ff4d4f";
    tag.style.borderRadius = "2.5px";
    tag.style.height = "5px";
    tag.style.width = "5px";
    tag.style.position = "absolute";
    tag.style.top = "20px";
    tag.style.right = "15px";
    tag.id = "tag";
    if (!templateId) {
      if (!tab.length) return;

      tab[1].appendChild(tag);
      (tab[1] as HTMLElement).style.position = "relative";
    } else {
      const existingTag = document.getElementById("tag");
      if (existingTag && existingTag.parentNode === tab[1]) {
        tab[1].removeChild(existingTag); // 直接从 tab[1] 移除
      }
      existingTag?.remove();
    }
  }, [templateId]);

  useEffect(() => {
    document.getElementById("root")!.style.height = "100vh";
    const card = document.querySelectorAll(".ant-pro-card ");

    if (!card.length) return;
    (card[0] as HTMLElement).style.height = "100%";
  }, []);

  /**
   * @description 保存草稿
   * */
  const handlerSaveToDraft = async (tag?: number) => {
    const values = form.getFieldsValue();
    interface newDataSource extends ISearchParams {
      depositRequired?: boolean;
      attachments?: Attachment[];
    }

    let params: newDataSource = values as ISearchParams;
    params.depositRequired = params._depositRequired === "0" ? true : false;
    params.attachments = uploadData;

    params = {
      ...dataSource.searchParams,
      ...params,
      partyB: dataSource.searchParams.partyB,
      name: form.getFieldValue("name"),
    };
    const { success, data } = await saveToDraft(params);
    if (!success) return;
    if (tag) message.success('保存成功')
    loadContractById(data);
  };

  useEffect(() => {
    const handlerGetCurrentContract = async () => {
      const { data } = await getCurrentContract();
      if (!data) return;
      loadContractById(data);
    };

    handlerGetCurrentContract();
  }, []);

  useEffect(() => {
    if (!poHtmlCode) return;
    window.pageofficectrl.Reload();
  }, [poHtmlCode]);

  return {
    form,
    tabs,
    tableHeader,
    setDataSource,
    dataSource,
    onInputChange,
    visible,
    setVisible,
    fileKey,
    setFileKey,
    uploadData,
    setUploadData,
    formatInput,
    templateVisible,
    setTemplateVisible,
    setTabs,
    handlerSelectedTemplateAfter,
    setTemplateId,
    templateId,
    activeKey,
    setActiveKey,
    handleSave,
    handlerOpenSubmitDepGeneralTerm,
    setFlowParams,
    flowUserSelectorVisible,
    setFlowUserSelectorVisible,
    flowParams,
    handlerSubmitDepGeneralTerm,
    poHtmlCode,
    setPoHtmlCode,
    showDialog,
    hideDialog,
    show,
    setShow,
    userIds,
    setUserIds,
    handlerSubmitJointReview,
    handlerSaveToDraft,
    loading,
    setLoading,
  };
};
