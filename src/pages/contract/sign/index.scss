.sevenContractDrafting_header_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  margin-bottom: 15px;

  .item_tabs {
    .ant-tabs-tab-btn {
      padding: 5px 20px;
    }

    .ant-tabs-nav {
      margin: 0;
    }
  }
}

.sevenContractDrafting_body_box {
  width: 100%;
  position: relative;

  .process {
    height: calc(100vh - 250px);
    background-color: white;
  }

  .compliance_icon {
    width: 40px;
    height: 80px;
    position: absolute;
    z-index: 99;
    border-radius: 10px 0 0 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #007356;
    background-color: white;
    cursor: pointer;
    border: 1px solid #007356;
    border-right: none;
  }

  .compliance_icon_open {
    left: 50%;
    transform: translateX(-70%);
  }

  .compliance_icon_close {
    right: 0;
  }

  .compliance_left_box {
    width: 100%;
    background-color: white;
    height: calc(100vh - 121px);
    overflow-y: auto;
  }

  .compliance_right_box {
    padding: 10px;
    position: relative;
    background-color: white;
  }

  .inputStyle {
    border: none;
    width: 100%;
    height: 100%;
  }

  .textareaStyle {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    resize: none;
    padding: 0 11px;
    border: none;
    outline: none;

    &:focus {
      box-shadow: none;
    }
  }

  .opinions_already_processed {
    width: 100%;
    overflow-y: auto;

    .opinions_tabs {
      height: 30px;
      background-color: #dddbdb;
      display: flex;
      position: relative;

      .summary {
        margin-left: 20px;
        font-size: var(--content-font-size);
        height: 25px;
        background-color: white;
        padding: 0 5px;
        border-radius: 5px;
        position: relative;
        cursor: pointer;
        top: 10px;

        &.summary_active {
          background-color: #999;
          color: white;
        }
      }
    }

    .opinions_content {
      font-size: var(--content-font-size);
    }
  }
}

.sevenContractDrafting_Syle {
  .ant-pro-card {
    border-radius: 0;
    border-top: 2px solid red !important;
  }

  /* 在项目的全局样式或组件样式中添加 */
  .ant-table-tbody > tr > td:first-child,
  .ant-table-thead > tr > th:first-child {
    /* 表格第一列 */
    color: red !important;
    /* 红色 */
  }

  .ant-list-item:first-child {
    /* 列表第一项 */
    color: red;
  }

  .ant-grid-col-1:first-child {
    /* Grid布局第一列 */
    color: red;
  }
}
