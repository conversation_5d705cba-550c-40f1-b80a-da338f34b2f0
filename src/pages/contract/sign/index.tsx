import { <PERSON><PERSON>, <PERSON>, Tabs, Row, Steps } from "@itm/components-pc";
import React, { Fragment, useRef, useState } from "react";
import "./index.scss";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import type { TabsProps } from "antd";
import { exhibit } from "@/assets/string";
import { ContractInfo } from "@/components/contract/info";
import { Authorize } from "@/components/contract/authorization";
import { Sign } from "@/components/contract/sign";
import CustomSplitter from "@/components/CustomSplitter";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref: React.RefObject<any>;
  showWhen: (current: number) => boolean;
}
// 定义组件状态接口
interface ComponentState {
  isShow: boolean;
  activeKey: string;
  current: number;
  isEdit: boolean;
}
const messPending = () => {
  // 初始化状态
  const [state, setState] = useState<ComponentState>({
    isShow: true,
    activeKey: "2",
    current: 1,
    isEdit: true,
  });
  const onChange = (key: string) => {
    setState((pre) => ({ ...pre, activeKey: key }));
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];
  const draftingInfoRef = useRef<any>(null);
  const authorizationRef = useRef<any>(null);
  const signInformationRef = useRef<any>(null);
  const performanceRef = useRef<any>(null);
  const performanceStatusRef = useRef<any>(null);
  const acceptanceRef = useRef<any>(null);

  const refs = [
    draftingInfoRef,
    authorizationRef,
    signInformationRef,
    performanceRef,
    performanceStatusRef,
    acceptanceRef,
  ];

  const handleTrueClick = () => {
    if (state.current >= 0 && state.current < refs.length) {
      refs[state.current].current?.isEditChange();
    }
    setState((pre) => ({ ...pre, isEdit: !pre.isEdit }));
  };

  const handleFalseClick = () => {
    handleIsClick();
    if (state.current >= 0 && state.current < refs.length) {
      refs[state.current].current?.falseEditChange();
    }
    setState((pre) => ({ ...pre, isEdit: true }));
  };
  //
  const handleIsClick = () => {
    stepsData.forEach((step) => {
      step.ref.current?.falseEditChange();
    });
  };
  const goToNextStep = () => {
    // setState((pre) => ({ ...pre, current: (pre.current !== (stepsData.length - 1) ? (pre.current + 1) : 0) }))
    // handleFalseClick()
  };

  const stepsData: StepItem[] = [
    {
      title: "合同起草信息",
      description: "This is a description.",
      component: (
        <ContractInfo ref={draftingInfoRef} contract={11} isEdit={false} />
      ),
      ref: draftingInfoRef,
      showWhen: (current) => current >= 0,
    },
    // {
    //     title: "授权委托书信息",
    //     description: "This is a description.",
    //     component: <Authorize ref={authorizationRef} contract={contract} isEdit={false}/>,
    //     ref: authorizationRef,
    //     showWhen: (current) => current >= 1,
    // },
    // {
    //     title: "合同签订信息",
    //     description: "This is a description.",
    //     component: <Sign ref={signInformationRef} isEdit={true} contract={contract} defaultActive/>,
    //     ref: signInformationRef,
    //     showWhen: (current) => current >= 2,
    // },
  ];

  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={state.activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>
          <Space>
            <Button onClick={handleTrueClick}>
              {!state.isEdit ? "测试编辑" : "测试退出编辑"}
            </Button>
            <Button>退回修改</Button>
            <Button>审核通过</Button>
            <Button onClick={goToNextStep}>点击+1</Button>
          </Space>
        </div>
      </div>
      {state.activeKey == "1" && (
        <Steps
          progressDot
          current={state.current}
          items={stepsData.map((step) => ({
            title: step.title,
            description: step.description,
          }))}
        />
      )}
      <div></div>
      <div className="sevenContractDrafting_body_box">
        {state.activeKey == "1" && (
          <>
            <CustomSplitter
              htmlContent={exhibit}
              right={
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen(state.current) && step.component}
                    </Fragment>
                  ))}
                </div>
              }
            />
          </>
        )}
        {state.activeKey == "2" && <div className="process"></div>}
      </div>
    </>
  );
};
export default messPending;
