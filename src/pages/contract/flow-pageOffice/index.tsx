/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-01 21:33:25
 * @Description:
 * @LastEditTime: 2025-07-18 15:55:59
 * @LastEditors: wangjiantao
 */
import { Button, Space, Tabs } from "@itm/components-pc";
import { Fragment, useEffect, useState } from "react";
import "./index.scss";
import { ExclamationCircleFilled, UserOutlined } from "@ant-design/icons";
import { Divider, Form, Input, message, Modal, type TabsProps } from "antd";
import GenericFormTable from "@/components/GenericFormTable";
import moment from "moment";
import {
  approvedWithJointReview,
  contractProcess,
  expertReview,
  queryContractById,
  recall,
  regulatoryWithJointReview,
  sendBackWithJointReview,
} from "@/api/contract/contract";
import { File } from "@/components/contract/info";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { ISearchParams } from "../draft/types";
import { getWindowParams } from "@/utils/pageofficeParams";
import WordFunction from "@/components/pageOffice/WordFunctional";
import { getNoticeRecordById } from "@/api/user/notice";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  status: string;
  creator: string;
  createdAt: number;
  time?: string;
  isShow: boolean;
};

interface IProps extends ISearchParams {
  sourceName: string;
  depositRequired: boolean;
  performanceDeptName: string;
  categoryName: string;
  typeName: string;
  attachments: File[];
  advice?: string;
  process?: string;
  drafterName: string;
  name: string;
  draftTime: string;
  opinions: {
    advice?: string;
    deptName?: string;
    createTime?: number;
    id?: number;
    username: string;
  }[];
}
let SUBMIT_TYPE = 1;

const _items: TabsProps["items"] = [
  {
    key: "1",
    label: "按部门汇总",
  },
  {
    key: "2",
    label: "按时间汇总",
  },
];

const FlowPageOffice = () => {
  // start 是否可编辑状态
  const [dataSource, setDataSource] = useState<IProps>();
  const [visible, setVisible] = useState(false);
  const [show, setShow] = useState(false);
  const [userId, setUserId] = useState<string | null>("");
  const [record, setRecord] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const customParams = record?.customParams
    ? JSON.parse(record.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { recordId, contractId, templateCode, processId } = customParams;

  useEffect(() => {
    if (getWindowParams()) {
      const params = getWindowParams();

      localStorage.setItem("token", params.token);
      localStorage.setItem("username", params.username);

      setRecord(params.data);
    }
  }, []);

  useEffect(() => {
    const loadContractById = async () => {
      if (!contractId) return;

      const { success, data } = await queryContractById(contractId);
      if (!success) return;
      data.draftTime = moment(data.draftTime).format("YYYY-MM-DD HH:mm:ss");

      setDataSource(data);
    };
    loadContractById();

    const loadNoticeDetail = async () => {
      if (!record) return;
      await getNoticeRecordById(record.id);
    };
    loadNoticeDetail();
  }, [record]);
  // end
  const [activeState, setActiveState] = useState<string>("1");

  const hash: Record<string, string> = {
    VeNp1ked1iC: "采购文件",
    VeNp4tRQdn6: "中标人投标文件",
    VeVRkxM4RmK: "中标通知书",
    VeVRsuuFf1M: "相关附件",
    VeVSK5c725w: "谈判纪要",
    VeVSLA74eP2: "请示批准文件",
    VeVSLjib4ym: "相关结果报告",
  };

  const categorizedFiles = (dataSource?.attachments ?? []).reduce(
    (acc: any, file: File): Record<string, File[]> => {
      const { fileCategory, ...rest } = file;
      // 只处理 hash 中存在的 fileCategory
      if (hash[fileCategory]) {
        const friendlyName = hash[fileCategory];

        if (!acc[friendlyName]) {
          acc[friendlyName] = [];
        }
        acc[friendlyName].push(rest);
      }

      return acc;
    },
    {}
  );

  const onChangeValue = (value: string) => {
    setDataSource((prevState) => {
      return {
        ...prevState!,
        advice: value,
      };
    });
  };

  const groupedByDept = dataSource?.opinions?.reduce((acc, item) => {
    const { deptName, advice, createTime, id, username } = item;
    if (!acc[deptName!]) {
      acc[deptName!] = [];
    }
    acc[deptName!].push({
      advice,
      id,
      createTime: new Date(createTime!).toLocaleString(), // 格式化时间username
      username,
    });
    return acc;
  }, {} as Record<string, any>);

  // 表格头数据
  const tableHeader = [
    {
      label: "起草人",
      value: dataSource?.drafterName,
      isformLabel: false,
      isTimeCell: true,
    },
    {
      label: "起草时间",
      value: dataSource?.draftTime,
      isformLabel: false,
      isTimeCell: false,
    },
  ];
  // 表格数据
  const tableRows = [
    {
      label: "合同名称",
      value: dataSource?.name,
      isLabelRed: true,
    },
    {
      label: "合同相对方",
      value: dataSource?.partyBName,
      isLabelRed: true,
    },
    {
      label: "合同来源",
      value: dataSource?.sourceName,
      isLabelRed: true,
    },
    {
      label: "履约部门",
      value: dataSource?.performanceDeptName,
      isLabelRed: true,
    },
    {
      label: "标的类别",
      value: dataSource?.categoryName,
      isLabelRed: true,
    },
    {
      label: "合同类型",
      value: dataSource?.typeName,
      isLabelRed: true,
    },

    {
      label: "合同金额（万元）",
      value: dataSource?.amount,
      isLabelRed: true,
    },
    {
      label: "是否需缴纳履约保证金",
      value: <>{!dataSource?._depositRequired ? "否" : "是"}</>,
      isLabelRed: true,
    },
    {
      label: "附件",
      children: Object.entries(categorizedFiles).flatMap(
        ([category, files]) => [
          {
            label: category,
            value: (files as File[]).map((file: File) => (
              <div key={file.id}>
                <a href={file.fileName}>{file.fileName}</a>
              </div>
            )),
          },
        ]
      ),
      isLabelRed: true,
    },
    {
      label: "已办意见",
      value: (
        <div className="opinions_already_processed">
          <Tabs items={_items} onChange={setActiveState} />
          {activeState == "1" ? (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {Object.entries(groupedByDept || []).map(
                ([deptName, opinions]) => (
                  <div className="opinions_content" key={deptName}>
                    <div>
                      <div>【{deptName}】</div>
                      {opinions.map((opinion: any, index: number) => (
                        <div
                          className="approval-item"
                          key={index}
                          style={{ border: "none" }}
                        >
                          <div className="approval-header">
                            <UserOutlined
                              style={{ color: "var(--primary-color)" }}
                            />
                            [审批人]{opinion.username}: {opinion.advice}
                          </div>
                          <div className="approval-time">
                            {moment(opinion.createTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )}
                          </div>
                        </div>
                      ))}
                      <Divider size="small" />
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            <div className="messages">
              <div className="approval-item">
                <div className="approval-header">
                  <UserOutlined style={{ color: "var(--primary-color)" }} />
                  [合同起草人]{dataSource?.drafterName} 《{dataSource?.name}
                  》请审核。
                </div>
                <div className="approval-time">
                  {moment(dataSource?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              {dataSource?.opinions.map((i, index) => (
                <Fragment key={index}>
                  <div className="opinions_content">
                    <div>【{i.deptName}】</div>
                    <div className="approval-item" key={index}>
                      <div className="approval-header">
                        <UserOutlined
                          style={{ color: "var(--primary-color)" }}
                        />
                        [审批人]{i.username}: {i.advice}
                      </div>
                      <div className="approval-time">
                        {moment(i.createTime).format("YYYY-MM-DD HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                </Fragment>
              ))}
            </div>
          )}
        </div>
      ),
      isLabelRed: true,
    },
    {
      label: "反馈意见",
      value: (
        <>
          <Input.TextArea
            className="textareaStyle"
            placeholder="请输入反馈意见"
            autoSize={{ minRows: 1, maxRows: 10 }} // 固定行数
            value={dataSource?.advice}
            onChange={(e) => onChangeValue(e.target.value)}
          />
        </>
      ),
      isLabelRed: true,
    },
  ];

  const [isShow, setIsShow] = useState<boolean>(true);
  const [activeKey, setActiveKey] = useState<string>("1");
  const onChange = (key: string) => {
    setActiveKey(key);
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  /**
   * @description 提交到法规部门
   * */
  //   const handlerSubmitToGeneralLegal = async () => {
  //     Modal.confirm({
  //       title: "提示",
  //       icon: <ExclamationCircleFilled />,
  //       content: "您确定要送至法规部门吗?",
  //       async onOk() {
  //         const { success } = await submitGeneralLegal({
  //       contractId,
  // advice: dataSource?.advice,
  // recordId,
  // processId,
  // userId: 1,
  //         });
  //         if (!success) return;
  //         message.success("提交成功");
  //         setTimeout(() => {}, 1000);
  //       },
  //     });
  //   };

  /**
   * @description 送法规部门
   * */
  const handlerRegulatoryWithJointReview = async () => {
    const params = {
      contractId,
      advice: dataSource?.advice,
      recordId,
      processId,
      userId: userId!,
    };
    const { success } = await regulatoryWithJointReview(params);
    if (!success) return;
    message.success("提交成功");
    setTimeout(() => {
      window.pageofficectrl.CloseWindow();
    }, 1000);
  };

  /**
   * @description 审批通过
   * */
  const handlerApproval = async () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要审核通过吗?",
      async onOk() {
        const params = {
          recordId,
          contractId,
          processId,
          advice: dataSource?.advice,
        };
        const { success } = await approvedWithJointReview(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          window.pageofficectrl.CloseWindow();
        }, 1000);
      },
      onCancel() {
        hideDialog();
      },
    });
  };

  /**
   * @description 发往法律顾问
   * */
  const handlrSubitExpertReview = async () => {
    const params = {
      recordId,
      contractId,
      processId,
      advice: dataSource?.advice,
      userId,
    };
    let http = SUBMIT_TYPE === 1 ? expertReview : contractProcess;

    const { success } = await http(params as any);
    if (!success) return;
    message.success("提交成功");
    setTimeout(() => {
      window.pageofficectrl.CloseWindow();
    }, 1000);
  };

  /**
   * @description 撤回
   * */

  const handlerRecall = () => {
    showDialog();
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要撤回吗?",
      async onOk() {
        const params = {
          contractId,
          advice: dataSource?.advice,
          recordId,
          processId,
        };
        const { success } = await recall(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          window.pageofficectrl?.CloseWindow();
        }, 1000);
      },
      onCancel() {
        hideDialog();
      },
    });
  };

  /**
   * @description 退回修改
   * */

  const handlerSendBack = () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要退回吗?",
      async onOk() {
        const params = {
          contractId,
          processId,
          advice: form.getFieldValue("advice"),
          recordId,
        };
        const { success } = await sendBackWithJointReview(params);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          setTimeout(() => {
            window.pageofficectrl?.CloseWindow();
          }, 1000);
        }, 1000);
      },
    });
  };

  useEffect(() => {
    window.pageofficectrl.Enabled = activeKey === "1";
  }, [activeKey]);

  const showDialog = () => {
    window.pageofficectrl.Enabled = false;
  };

  const hideDialog = () => {
    window.pageofficectrl.Enabled = true;
  };

  const renderButtons = () => {
    const buttonConfigs = {
      contract_draft_joint_review: [
        {
          text: "撤回",
          type: "danger",
          variant: "solid",
          onClick: () => {
            handlerRecall();
            showDialog();
          },
        },
        {
          text: "发送法律顾问",
          type: "primary",
          onClick: () => {
            showDialog();
            handleSubmit(1);
          },
        },
        {
          text: "送呈部门负责人",
          type: "primary",
          onClick: () => {
            showDialog();
            handleSubmit(2);
          },
        },
      ],
      contract_draft_joint_review_expert: [
        {
          text: "撤回",
          type: "danger",
          variant: "solid",
          onClick: () => {
            handlerRecall();
            showDialog();
          },
        },
        {
          text: "送法规部门",
          type: "primary",
          onClick: () => {
            setShow(true);
            showDialog();
          },
        },
      ],
      contract_draft_joint_review_examine: [
        {
          text: "退回修改",
          type: "danger",
          variant: "solid",
          onClick: () => {
            setOpen(true);
            showDialog();
          },
        },
        {
          text: "通过会审",
          type: "primary",
          onClick: () => {
            handlerApproval();
            showDialog();
          },
        },
      ],
    };

    const handleSubmit = (type: number) => {
      showDialog();
      setVisible(true);
      SUBMIT_TYPE = type;
    };

    const config = buttonConfigs[templateCode as keyof typeof buttonConfigs];
    if (!config) return null;

    return (
      <Space>
        {config.map((btn, index) => (
          <Button
            key={index}
            type={btn.type as any}
            color={btn.type as any}
            variant={btn.variant as any}
            onClick={btn.onClick}
          >
            {btn.text}
          </Button>
        ))}
      </Space>
    );
  };

  return (
    <>
      <div className="sixContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>{renderButtons()}</div>
      </div>
      <div className="sixContractDrafting_body_box">
        {activeKey == "1" && (
          <CustomSplitter
            left={<WordFunction fileId={contractId} />}
            right={
              <div className={`compliance_right_box`}>
                <GenericFormTable header={tableHeader} rows={tableRows} />
              </div>
            }
          />
        )}
        {activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>

      <Modal
        title="选择审批人"
        width={800}
        centered
        open={visible}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          hideDialog();
          setVisible(false);
        }}
        onOk={handlrSubitExpertReview}
      >
        <UserChoiceSelect
          multiple={false}
          onChange={setUserId}
          value={userId || undefined}
        />
      </Modal>

      <Modal
        title="选择审批人"
        width={800}
        centered
        open={show}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          hideDialog();
          setShow(false);
        }}
        onOk={handlerRegulatoryWithJointReview}
      >
        <UserChoiceSelect onChange={setUserId} value={userId || undefined} />
      </Modal>

      <Modal
        title="撤回"
        width={500}
        centered
        open={open}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          setOpen(false);
          hideDialog();
        }}
        onOk={() => {
          handlerSendBack();
        }}
      >
        <Form form={form} name="dynamic_rule">
          <Form.Item
            name="advice"
            label="退回原因"
            rules={[{ required: true, message: "请输入退回原因" }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default FlowPageOffice;
