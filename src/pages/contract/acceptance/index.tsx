import { But<PERSON>, Space, Tabs, Row } from "@itm/components-pc";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "./index.scss";
import {
  ExclamationCircleFilled,
} from "@ant-design/icons";
import { message, Modal, type TabsProps } from "antd";
import { ContractInfo } from "@/components/contract/info";
import { Authorize } from "@/components/contract/authorization";
import { Sign } from "@/components/contract/sign";
import { Acceptance } from "@/components/contract/acceptance";
import { PerformancePlan } from "@/components/contract/performance/plan";
import { PerformanceRecord } from "@/components/contract/performance/record";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import { queryContractById } from "@/api/contract/contract";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import { saveContractAcceptance } from "@/api/contract/performance";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref: React.RefObject<any>;
  showWhen: boolean;
}

export const AcceptancePage: React.FC<NoticeFormProps> = (props) => {
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  const draftingInfoRef = useRef<any>(null);
  const authorizationRef = useRef<any>(null);
  const signInformationRef = useRef<any>(null);
  const performancePlanRef = useRef<any>(null);
  const performanceRecordRef = useRef<any>(null);
  const acceptanceRef = useRef<any>(null);

  const [activeKey, setActiveKey] = useState<string>("1");

  const [expand, setExpand] = useState<boolean>(true);

  const [content, setContent] = useState<string>("");

  const [contract, setContract] = useState<any>({});

  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { contractId, noticeId, recordId } = customParams;

  useEffect(() => {
    getContractContentHtml(contractId).then((data) => {
      setContent(data.data);
    });
  }, []);

  useEffect(() => {
    queryContractById(contractId).then((res: any) => {
      setContract(res.data);
    });
  }, []);

  const stepsData: StepItem[] = [
    {
      title: "合同起草信息",
      description: "合同起草信息",
      component: <ContractInfo ref={draftingInfoRef} contract={contract} />,
      ref: draftingInfoRef,
      showWhen: true,
    },
    {
      title: "授权委托书信息",
      description: "授权委托书信息",
      component: (
        <Authorize ref={authorizationRef} contract={contract} isEdit={false} />
      ),
      ref: authorizationRef,
      showWhen: true,
    },
    {
      title: "合同签订信息",
      description: "合同签订信息",
      component: (
        <Sign ref={signInformationRef} isEdit={false} contract={contract} />
      ),
      ref: signInformationRef,
      showWhen: true,
    },
    {
      title: "合同履行计划",
      description: "合同履行计划",
      component: (
        <PerformancePlan
          ref={performancePlanRef}
          contractId={contractId}
          isEdit={false}
        />
      ),
      ref: performancePlanRef,
      showWhen: true,
    },
    {
      title: "合同履行情况",
      description: "合同履行情况",
      component: (
        <PerformanceRecord
          ref={performanceRecordRef}
          contractId={contractId}
          amount={contract?.amount}
        />
      ),
      ref: performanceRecordRef,
      showWhen: true,
    },
    {
      title: "合同验收情况",
      description: "合同验收情况",
      component: (
        <Acceptance defaultActive ref={acceptanceRef} contractId={contractId} />
      ),
      ref: acceptanceRef,
      showWhen: true,
    },
  ];

  const handlerSubmit = async () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要保存并提交数据吗?",
      async onOk() {
        const values = acceptanceRef.current?.state?.formData;
        const plans = acceptanceRef.current?.state?.performancePlanList;
        const attachments: { fileKey: string; fileCategory: string }[] = [];

        values.forEach((item: any) => {
          item.other.split(",").forEach((i: any) => {
            attachments.push({
              fileCategory: "VexUysElR7w",
              fileKey: i,
            });
          });
          item.attachments = [...item.attachments, ...attachments];
        });

        values.forEach((item: any) => {
          const attachments: { fileKey: string; fileCategory: string }[] = [];
          item.main.split(",").forEach((i: any) => {
            attachments.push({
              fileCategory: "VexV1h5Nxjs",
              fileKey: i,
            });
          });
          item.attachments = [...item.attachments, ...attachments];
        });

        const body: any = {
          contractId,
          noticeId,
          acceptances: [],
        };
        values.forEach((i: any, index: number) => {
          body.acceptances.push({
            ...i,
            planId: plans[index]?.id,
          });
        });
        const { success } = await saveContractAcceptance(body);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
      onCancel() {},
    });
  };

  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={(key: string) => {
              setActiveKey(key);
            }}
          />
        </div>
        <div>
          <Space>
            <Button type="primary" onClick={handlerSubmit}>
              归档
            </Button>
          </Space>
        </div>
      </div>
      <div className="sevenContractDrafting_body_box">
        {activeKey == "1" && (
          <>
            <CustomSplitter
              left={
                <div
                  style={{
                    transform: `scale(1.0)`,
                    transformOrigin: "top left", // 设置缩放原点为左上角
                    width: "789px",
                    height: "calc(100vh - 120px)",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: content,
                  }}
                />
              }
              right={
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen && step.component}
                    </Fragment>
                  ))}
                </div>
              }
            />
          </>
        )}
        {activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>
    </>
  );
};
export default AcceptancePage;
