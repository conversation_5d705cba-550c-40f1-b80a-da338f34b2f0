import React, {Fragment, useEffect, useRef, useState} from "react";
import {NoticeFormProps} from "@/components/user/notice/NoticeModalView";
import OriginalContractInfo from "@/components/contract/renewal/original";
import {FlowImage} from "@/components/flowImage";
import {getContractContentHtml} from "@/api/contract/contractAttachment";
import {queryContractById, queryProcessById, recall, sendBack, submitAuthorize} from "@/api/contract/contract";
import {Authorize} from "@/components/contract/authorization";
import {message, Modal, Input} from "antd";
import {ExclamationCircleFilled, LeftCircleOutlined, RightCircleOutlined} from "@ant-design/icons";
import './index.scss'
import CustomSplitter from "@/components/CustomSplitter";
import RenewalContractInfo from "@/components/contract/renewal/renewalInfo";

interface Item {
    title: string;
    description: string;
    component: React.ReactNode;
    showWhen: boolean;
}

const RenewalAuthorizePage: React.FC<NoticeFormProps> = (props) => {
    const [activeTab, setActiveTab] = useState("表单");

    const [contract, setContract] = useState<any>({});

    const [content, setContent] = useState<any>({});

    const [isContentLeftExpanded, setIsContentLeftExpanded] = useState(false);

    const [isFormLeftExpanded, setIsFormLeftExpanded] = useState(false);

    const [recallFlag, setRecallFlag] = useState<boolean>(false);

    const authorizationRef = useRef<any>(null);

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            reviewerId: 0,
            institutionId: 0,
            institutionName: "",
        };
    const {contractId, originalContractId, templateCode, recordId, processId} = customParams;

    useEffect(() => {
        getContractContentHtml(contractId).then((data) => {
            setContent(data.data);
        });
        queryContractById(contractId).then((res) => {
            setContract(res.data);
        });
    }, []);

    const formsData: Item[] = [
        {
            title: "原合同信息",
            description: "原合同信息",
            component: <OriginalContractInfo originalContractId={originalContractId} defaultActive={false}/>,
            showWhen: true,
        },
        {
            title: "合同续签信息",
            description: "合同续签信息",
            component: <RenewalContractInfo contract={contract} defaultActive={false}/>,
            showWhen: true,
        },
        {
            title: "授权委托书信息",
            description: "授权委托书信息",
            component: (
                <Authorize
                    ref={authorizationRef}
                    contract={contract}
                    isEdit={true}
                    defaultActive={true}
                />
            ),
            showWhen: true,
        },
    ]

    const renderContent = () => {
        switch (activeTab) {
            case "表单":
                return (
                    <>
                        {(!isContentLeftExpanded) && (
                            <div className="content-container">
                                {formsData.map((step, index) => (
                                    <Fragment key={index}>
                                        {step.showWhen && step.component}
                                    </Fragment>
                                ))}
                            </div>
                        )}
                        {isContentLeftExpanded && (
                            <div className="split-view-container">
                                <CustomSplitter
                                    leftDefaultSize="60%"
                                    rightDefaultSize="40%"
                                    style={{padding: 24, height: "80vh"}}
                                    left={
                                        <div
                                            style={{
                                                transform: `scale(1.0)`,
                                                transformOrigin: "top left",
                                                width: "789px",
                                                height: "100%",
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: content,
                                            }}
                                        />
                                    }
                                    right={
                                        <div className={`compliance_right_box`}>
                                            {formsData.map((step, index) => (
                                                <Fragment key={index}>
                                                    {step.showWhen && step.component}
                                                </Fragment>
                                            ))}
                                        </div>
                                    }
                                />
                            </div>
                        )}
                    </>
                );
            case "正文":
                return (
                    <>
                        {(!isFormLeftExpanded) && (
                            <div className="content-container">
                                <div
                                    style={{
                                        transform: `scale(1.0)`,
                                        transformOrigin: "top left",
                                        width: "789px",
                                        height: "100%",
                                    }}
                                    dangerouslySetInnerHTML={{
                                        __html: content,
                                    }}
                                />
                            </div>
                        )}
                        {isFormLeftExpanded && (
                            <div className="split-view-container">
                                <CustomSplitter
                                    leftDefaultSize="40%"
                                    rightDefaultSize="60%"
                                    style={{padding: 24, height: "80vh"}}
                                    left={
                                        <div className={`compliance_right_box`} style={{overflow: 'auto'}}>
                                            {formsData.map((step, index) => (
                                                <Fragment key={index}>
                                                    {step.showWhen && step.component}
                                                </Fragment>
                                            ))}
                                        </div>
                                    }
                                    right={
                                        <div
                                            style={{
                                                transform: `scale(1.0)`,
                                                transformOrigin: "top left", // 设置缩放原点为左上角
                                                width: "789px",
                                                height: "100%",
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: content,
                                            }}
                                        />
                                    }
                                />
                            </div>
                        )}
                    </>
                );
            case "流程":
                return (
                    <div className="content-container">
                        <FlowImage workCode="contract_draft" recordId={recordId}/>
                    </div>
                )
            default:
                return null;
        }
    };

    const handlerSubmitAuthorize = async () => {
        const state = authorizationRef.current?.state();

        if (!state?.serialNumber) {
            message.warning("请填写合同编号");
            return;
        }
        if (!state?.selectedAuthorize) {
            message.warning("请选择授权委托书");
            return;
        }
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要发放授权书吗?",
            async onOk() {
                const params = {
                    contractId,
                    serialNumber: state?.serialNumber,
                    authorizedId: state?.selectedAuthorize,
                    recordId,
                    processId,
                    flag: 1,
                };
                const {success} = await submitAuthorize(params);
                if (!success) return;
                message.success("发放授权书成功");
                setRecallFlag(true);
            },
        });
    };

    const handlerRecall = async () => {
        // 先进行二次确认
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要撤回吗?",
            onOk: executeRecall,
        });
    };

    const executeRecall = async () => {
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
        };
        const {success} = await recall(params);
        if (!success) {
            return;
        }
        message.success("撤回成功");
        setRecallFlag(false)
    };

// 在返回的 JSX 结构中添加悬浮按钮
    return (
        <div className="compliance_box">
            {/* 顶部标签页和按钮 */}
            <div className="tab-header-buttons">
                <div className="tab-header">
                    <div className={`tab-item ${activeTab === "表单" ? "active" : ""}`}
                         onClick={() => setActiveTab("表单")}>表单
                    </div>
                    <div className={`tab-item ${activeTab === "正文" ? "active" : ""}`}
                         onClick={() => setActiveTab("正文")}>正文
                    </div>
                    <div className={`tab-item ${activeTab === "流程" ? "active" : ""}`}
                         onClick={() => setActiveTab("流程")}>流程
                    </div>
                </div>

                {/* 右侧按钮 */}
                <div className="action-buttons">
                    <button
                        hidden={!recallFlag}
                        style={{backgroundColor: "FFA042"}}
                        onClick={handlerRecall}>
                        撤回
                    </button>
                    <button
                        hidden={recallFlag}
                        onClick={handlerSubmitAuthorize}>发放授权书
                    </button>
                </div>
            </div>

            {/* 内容区域 */}
            {renderContent()}

            {/* 左侧悬浮按钮 */}
            {activeTab === "表单" && (
                <div
                    className="toggle-content-button"
                    onClick={() => {
                        setIsContentLeftExpanded(!isContentLeftExpanded);
                    }}
                >
                    {isContentLeftExpanded ? (
                        <>
                            <span className="toggle-text">收起正文</span>
                            <LeftCircleOutlined className="toggle-icon"/>
                        </>
                    ) : (
                        <>
                            <span className="toggle-text">展开正文</span>
                            <RightCircleOutlined className="toggle-icon"/>
                        </>
                    )}
                </div>
            )}

            {/*/!* 右侧悬浮按钮 *!/*/}
            {/*{activeTab === "表单" && (*/}
            {/*    <div*/}
            {/*        className="toggle-content-right-button"*/}
            {/*        onClick={() => {*/}
            {/*            setIsContentRightExpanded(!isContentRightExpanded);*/}
            {/*            setIsContentLeftExpanded(false);*/}
            {/*        }}*/}
            {/*    >*/}
            {/*        {isContentRightExpanded ? (*/}
            {/*            <>*/}
            {/*                <RightCircleOutlined className="toggle-icon"/>*/}
            {/*                <span className="toggle-text">收起正文</span>*/}
            {/*            </>*/}
            {/*        ) : (*/}
            {/*            <>*/}
            {/*                <LeftCircleOutlined className="toggle-icon"/>*/}
            {/*                <span className="toggle-text">展开正文</span>*/}
            {/*            </>*/}
            {/*        )}*/}
            {/*    </div>*/}
            {/*)}*/}


            {activeTab === "正文" && (
                <div
                    className="toggle-content-button"
                    onClick={() => {
                        setIsFormLeftExpanded(!isFormLeftExpanded);
                    }}
                >
                    {isFormLeftExpanded ? (
                        <>
                            <span className="toggle-text">收起表单</span>
                            <LeftCircleOutlined className="toggle-icon"/>
                        </>
                    ) : (
                        <>
                            <span className="toggle-text">展开表单</span>
                            <RightCircleOutlined className="toggle-icon"/>
                        </>
                    )}
                </div>
            )}
            {/*{activeTab === "正文" && (*/}
            {/*    <div*/}
            {/*        className="toggle-content-right-button"*/}
            {/*        onClick={() => {*/}
            {/*            setIsFormRightExpanded(!isFormRightExpanded);*/}
            {/*            setIsFormLeftExpanded(false);*/}
            {/*        }}*/}
            {/*    >*/}
            {/*        {isFormRightExpanded ? (*/}
            {/*            <>*/}
            {/*                <RightCircleOutlined className="toggle-icon"/>*/}
            {/*                <span className="toggle-text">收起表单</span>*/}
            {/*            </>*/}
            {/*        ) : (*/}
            {/*            <>*/}
            {/*                <LeftCircleOutlined className="toggle-icon"/>*/}
            {/*                <span className="toggle-text">展开表单</span>*/}
            {/*            </>*/}
            {/*        )}*/}
            {/*    </div>*/}
            {/*)}*/}

        </div>
    );
};

export default RenewalAuthorizePage;