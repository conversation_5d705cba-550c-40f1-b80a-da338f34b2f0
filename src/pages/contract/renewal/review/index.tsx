import React, {Fragment, useEffect, useRef, useState} from "react";
import {NoticeFormProps} from "@/components/user/notice/NoticeModalView";
import OriginalContractInfo from "@/components/contract/renewal/original";
import RenewalContractInfoReview from "@/components/contract/renewal/renewalReview";
import {FlowImage} from "@/components/flowImage";
import {getContractContentHtml} from "@/api/contract/contractAttachment";
import {
    approval,
    approvedWithJointReview,
    contractProcess,
    expertReview,
    jointReview,
    queryContractById,
    queryProcessById, recall, regulatoryWithJointReview,
    sendBack,
    submitAuthorize, submitGeneralLegal
} from "@/api/contract/contract";
import {Authorize} from "@/components/contract/authorization";
import {message, Modal, Input} from "antd";
import {ExclamationCircleFilled, LeftCircleOutlined, RightCircleOutlined} from "@ant-design/icons";
import './index.scss'
import CustomSplitter from "@/components/CustomSplitter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";

interface Item {
    title: string;
    description: string;
    component: React.ReactNode;
    showWhen: boolean;
}

const RenewalReviewPage: React.FC<NoticeFormProps> = (props) => {
    const [activeTab, setActiveTab] = useState("表单");

    const [contract, setContract] = useState<any>({});
    const [process, setProcess] = useState<any>({});

    const [content, setContent] = useState<any>({});

    const [advice, setAdvice] = useState<string>("");
    // 控制拟办意见弹窗
    const [isAdviceModalVisible, setIsAdviceModalVisible] = useState<boolean>(false);

    const [isContentLeftExpanded, setIsContentLeftExpanded] = useState<boolean>(false);

    const [isFormLeftExpanded, setIsFormLeftExpanded] = useState<boolean>(false);

    const currentUserDeptName = localStorage.getItem("userdeptname");

    const [userSelectorVisibleToExpert, setUserSelectorVisibleToExpert] = useState<boolean>(false);
    const [userSelectorVisibleToLegalAgent, setUserSelectorVisibleToLegalAgent] = useState<boolean>(false);
    const [userSelectorVisibleToDeptManager, setUserSelectorVisibleToDeptManager] = useState<boolean>(false);
    const [userSelectorVisibleToLegalDept, setUserSelectorVisibleToLegalDept] = useState<boolean>(false);
    const [userSelectorVisibleToApproval, setUserSelectorVisibleToApproval] = useState<boolean>(false);

    const [loading, setLoading] = useState<boolean>(false);
    const [recallFlag, setRecallFlag] = useState<boolean>(false);

    const [selectedUser, setSelectedUser] = useState<{ userId: any }>({
        userId: null,
    });

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            reviewerId: 0,
            institutionId: 0,
            institutionName: "",
        };
    const {contractId, originalContractId, templateCode, recordId, processId} = customParams;

    useEffect(() => {

        queryProcessById(processId).then(res => {
            setProcess(res.data);
        })

        getContractContentHtml(contractId).then((data) => {
            setContent(data.data);
        });
        queryContractById(contractId).then((res) => {
            setContract(res.data);
        });
    }, []);

    const formsData: Item[] = [
        {
            title: "原合同信息",
            description: "原合同信息",
            component: <OriginalContractInfo originalContractId={originalContractId} defaultActive={false}/>,
            showWhen: true,
        },
        {
            title: "合同续签信息",
            description: "合同续签信息",
            component: <RenewalContractInfoReview contract={contract} onChange={(value) => {
                setAdvice(value);
            }}/>,
            showWhen: true,
        },
    ]

    const renderContent = () => {
        switch (activeTab) {
            case "表单":
                return (
                    <>
                        {(!isContentLeftExpanded) && (
                            <div className="content-container">
                                {formsData.map((step, index) => (
                                    <Fragment key={index}>
                                        {step.showWhen && step.component}
                                    </Fragment>
                                ))}
                            </div>
                        )}
                        {isContentLeftExpanded && (
                            <div className="split-view-container">
                                <CustomSplitter
                                    leftDefaultSize="60%"
                                    rightDefaultSize="40%"
                                    style={{padding: 24, height: "80vh"}}
                                    left={
                                        <div
                                            style={{
                                                transform: `scale(1.0)`,
                                                transformOrigin: "top left",
                                                width: "789px",
                                                height: "100%",
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: content,
                                            }}
                                        />
                                    }
                                    right={
                                        <div className={`compliance_right_box`}>
                                            {formsData.map((step, index) => (
                                                <Fragment key={index}>
                                                    {step.showWhen && step.component}
                                                </Fragment>
                                            ))}
                                        </div>
                                    }
                                />
                            </div>
                        )}
                    </>
                );
            case "正文":
                return (
                    <>
                        {(!isFormLeftExpanded) && (
                            <div className="content-container">
                                <div
                                    style={{
                                        transform: `scale(1.0)`,
                                        transformOrigin: "top left",
                                        width: "789px",
                                        height: "100%",
                                    }}
                                    dangerouslySetInnerHTML={{
                                        __html: content,
                                    }}
                                />
                            </div>
                        )}
                        {isFormLeftExpanded && (
                            <div className="split-view-container">
                                <CustomSplitter
                                    leftDefaultSize="40%"
                                    rightDefaultSize="60%"
                                    style={{padding: 24, height: "80vh"}}
                                    left={
                                        <div className={`compliance_right_box`} style={{overflow: 'auto'}}>
                                            {formsData.map((step, index) => (
                                                <Fragment key={index}>
                                                    {step.showWhen && step.component}
                                                </Fragment>
                                            ))}
                                        </div>
                                    }
                                    right={
                                        <div
                                            style={{
                                                transform: `scale(1.0)`,
                                                transformOrigin: "top left", // 设置缩放原点为左上角
                                                width: "789px",
                                                height: "100%",
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: content,
                                            }}
                                        />
                                    }
                                />
                            </div>
                        )}
                    </>
                );
            case "流程":
                return (
                    <div className="content-container">
                        <FlowImage workCode="contract_draft" recordId={recordId}/>
                    </div>
                )
            default:
                return null;
        }
    };

    const verifyAdvice = async () => {
        if (!advice || advice.trim() === "") {
            message.warning("请输入拟办意见");
            return false;
        }
        return true;
    }

    const handlerSubmitToExpert = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
            userId: selectedUser.userId,
            advice: advice,
            flag: 1,
        }
        const {success} = await expertReview(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleToExpert(false);
        setRecallFlag(true)
        message.success("发送法律顾问成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handlerSubmitToLegalAgent = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
            userId: selectedUser.userId,
            advice: advice,
            flag: 1,
        }
        const {success} = await regulatoryWithJointReview(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleToLegalAgent(false);
        setRecallFlag(true);
        message.success("送法规部门成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handlerSubmitToDeptManager = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
            userId: selectedUser.userId,
            advice: advice,
            flag: 1,
        }
        const {success} = await contractProcess(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleToDeptManager(false);
        setRecallFlag(true);
        message.success("送呈部门负责人成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handlerSubmitToLegalDept = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            userId: selectedUser.userId,
            advice: advice,
            flag: 1,
        }
        const {success} = await submitGeneralLegal(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleToLegalDept(false)
        message.success("送法规审核成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handlerSubmitToApproval = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            userId: selectedUser.userId,
            advice: advice,
            flag: 1,
        }
        const {success} = await approval(params);
        setLoading(false);
        if (!success) {
            return;
        }
        setUserSelectorVisibleToApproval(false)
        message.success("审核通过成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handlerApproved = async () => {
        // 先进行二次确认
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要通过会审吗?",
            onOk: executeApproved
        });
    }

    const handleSendBack = async () => {
        // 先进行二次确认
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要退回修改吗?",
            onOk: () => {
                // 如果已经填写了拟办意见，直接调用退回接口
                if (advice && advice.trim() !== "") {
                    executeSendBack();
                } else {
                    // 如果没有填写拟办意见，弹出框让用户填写
                    setIsAdviceModalVisible(true);
                }
            }
        });
    };

    const handlerRecall = async () => {
        // 先进行二次确认
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled/>,
            content: "您确定要撤回吗?",
            onOk: executeRecall,
        });
    };

    const executeApproved = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
            advice: advice,
            flag: 1
        };
        const {success} = await approvedWithJointReview(params);
        setLoading(false);
        if (!success) {
            return;
        }
        message.success("通过会审成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const executeSendBack = async () => {
        setLoading(true)
        const params = {
            contractId,
            recordId,
            processId,
            advice: advice,
            flag: 1,
        };
        const {success} = await sendBack(params);
        setLoading(false);
        if (!success) {
            return;
        }
        message.success("退回成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const executeRecall = async () => {
        setLoading(true);
        const params = {
            contractId: contractId,
            recordId: recordId,
            processId: processId,
        };
        const {success} = await recall(params);
        setLoading(false);
        if (!success) {
            return;
        }
        message.success("撤回成功");
        setTimeout(() => {
            props.setVisible(false);
        }, 1000);
    };

    const handleAdviceModalOk = async () => {
        verifyAdvice().then((res) => {
            if (res) {
                setIsAdviceModalVisible(false);
                executeSendBack();
            }
        })
    };

    const handlerOpenSelectUserModalForSubmitToExpert = async () => {
        verifyAdvice().then((res) => {
            if (res) {
                setUserSelectorVisibleToExpert(true);
            }
        })
    };

    const handlerOpenSelectUserModalForSubmitToLegalAgent = async () => {
        verifyAdvice().then((res) => {
            if (res) {
                setUserSelectorVisibleToLegalAgent(true);
            }
        })
    };

    const handlerOpenSelectUserModalForSubmitToDeptManager = async () => {
        verifyAdvice().then((res) => {
            if (res) {
                setUserSelectorVisibleToDeptManager(true);
            }
        })
    };

    const handlerOpenSelectUserModalForSubmitToLegalDept = async () => {
        setUserSelectorVisibleToLegalDept(true);
    }

    const handlerOpenSelectUserModalForApproval = async () => {
        setUserSelectorVisibleToApproval(true);
    }

// 在返回的 JSX 结构中添加悬浮按钮
    return (
        <div className="compliance_box">
            {/* 顶部标签页和按钮 */}
            <div className="tab-header-buttons">
                <div className="tab-header">
                    <div className={`tab-item ${activeTab === "表单" ? "active" : ""}`}
                         onClick={() => setActiveTab("表单")}>表单
                    </div>
                    <div className={`tab-item ${activeTab === "正文" ? "active" : ""}`}
                         onClick={() => setActiveTab("正文")}>正文
                    </div>
                    <div className={`tab-item ${activeTab === "流程" ? "active" : ""}`}
                         onClick={() => setActiveTab("流程")}>流程
                    </div>
                </div>

                {/* 右侧按钮 */}
                <div className="action-buttons">
                    <button
                        hidden={!recallFlag}
                        style={{backgroundColor: "FFA042"}}
                        onClick={handlerRecall}>
                        撤回
                    </button>
                    <button
                        hidden={
                            process?.flowNode !== "contract_draft_joint_review_examine_renewal" &&
                            process?.flowNode !== "contract_draft_manager_renewal" &&
                            process?.flowNode !== "contract_draft_legal_dept_renewal"
                        }
                        style={{backgroundColor: "red"}}
                        onClick={handleSendBack}>
                        退回修改
                    </button>
                    <button
                        hidden={
                            (process?.flowNode !== "contract_draft_joint_review_renewal" &&
                            currentUserDeptName !== "法规部") ||
                            recallFlag
                        }
                        onClick={handlerOpenSelectUserModalForSubmitToExpert}>发送法律顾问
                    </button>
                    <button
                        hidden={process?.flowNode !== "contract_draft_joint_review_expert_renewal"  || recallFlag }
                        onClick={handlerOpenSelectUserModalForSubmitToLegalAgent}>送法规部门
                    </button>
                    <button
                        hidden={process?.flowNode !== "contract_draft_joint_review_renewal" || recallFlag}
                        onClick={handlerOpenSelectUserModalForSubmitToDeptManager}>送呈部门负责人
                    </button>
                    <button
                        hidden={process?.flowNode !== "contract_draft_joint_review_examine_renewal"}
                        onClick={handlerApproved}>通过会审
                    </button>
                    <button
                        hidden={process?.flowNode !== "contract_draft_manager_renewal"}
                        onClick={handlerOpenSelectUserModalForSubmitToLegalDept}>送法规审核
                    </button>
                    <button
                        hidden={process?.flowNode !== "contract_draft_legal_dept_renewal"}
                        onClick={handlerOpenSelectUserModalForApproval}>审核通过
                    </button>
                </div>
            </div>

            {/* 内容区域 */}
            {renderContent()}

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisibleToExpert}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleToExpert(false);
                }}
                onOk={handlerSubmitToExpert}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisibleToLegalAgent}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleToLegalAgent(false);
                }}
                onOk={handlerSubmitToLegalAgent}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisibleToDeptManager}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleToDeptManager(false);
                }}
                onOk={handlerSubmitToDeptManager}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={userSelectorVisibleToLegalDept}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleToLegalDept(false);
                }}
                onOk={handlerSubmitToLegalDept}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>

            <Modal
                title="选择授权人员"
                width={800}
                centered
                open={userSelectorVisibleToApproval}
                cancelText="取消"
                confirmLoading={loading}
                okText="确定"
                onCancel={() => {
                    setUserSelectorVisibleToApproval(false);
                }}
                onOk={handlerSubmitToApproval}
            >
                <UserChoiceSelect
                    multiple
                    onChange={(e: string | null) => setSelectedUser({userId: e})}
                    value={selectedUser.userId}
                />
            </Modal>

            {/* 拟办意见弹窗 */}
            <Modal
                title="请填写拟办意见"
                centered
                open={isAdviceModalVisible}
                confirmLoading={loading}
                onOk={handleAdviceModalOk}
                onCancel={() => {
                    setIsAdviceModalVisible(false);
                }}
                okText="确定"
                cancelText="取消"
            >
                <Input.TextArea
                    rows={4}
                    placeholder="请填写拟办意见"
                    value={advice}
                    onChange={(e) => setAdvice(e.target.value)}
                />
            </Modal>

            {/* 左侧悬浮按钮 */}
            {activeTab === "表单" && (
                <div
                    className="toggle-content-button"
                    onClick={() => {
                        setIsContentLeftExpanded(!isContentLeftExpanded);
                    }}
                >
                    {isContentLeftExpanded ? (
                        <>
                            <span className="toggle-text">收起正文</span>
                            <LeftCircleOutlined className="toggle-icon"/>
                        </>
                    ) : (
                        <>
                            <span className="toggle-text">展开正文</span>
                            <RightCircleOutlined className="toggle-icon"/>
                        </>
                    )}
                </div>
            )}

            {activeTab === "正文" && (
                <div
                    className="toggle-content-button"
                    onClick={() => {
                        setIsFormLeftExpanded(!isFormLeftExpanded);
                    }}
                >
                    {isFormLeftExpanded ? (
                        <>
                            <span className="toggle-text">收起表单</span>
                            <LeftCircleOutlined className="toggle-icon"/>
                        </>
                    ) : (
                        <>
                            <span className="toggle-text">展开表单</span>
                            <RightCircleOutlined className="toggle-icon"/>
                        </>
                    )}
                </div>
            )}
        </div>
    );
};

export default RenewalReviewPage;