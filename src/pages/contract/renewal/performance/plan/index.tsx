import { Tabs } from "@/components-pc";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "./index.scss";
import { Button, type TabsProps } from "antd";
import { Authorize } from "@/components/contract/authorization";
import { PerformancePlan } from "@/components/contract/performance/plan";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import { queryContractById } from "@/api/contract/contract";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import { FlowImage } from "@/components/flowImage";
import { Sign } from "@/components/contract/sign";
import { saveOnePlan } from "@/api/contract/performance";
import moment from "moment";
import OriginalContractInfo from "@/components/contract/originalInfo";
import RenewalContractInfo from "@/components/contract/renewal/renewalInfo";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref?: React.RefObject<any>;
  showWhen: boolean;
}

const PerformancePlanPage: React.FC<NoticeFormProps> = (props) => {
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];
  const authorizationRef = useRef<any>(null);
  const signInformationRef = useRef<any>(null);
  const performancePlanRef = useRef<any>(null);

  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { contractId, noticeId, recordId } = customParams;

  const [activeKey, setActiveKey] = useState<string>("1");

  const [content, setContent] = useState<string>("");

  const [contract, setContract] = useState<any>({});

  useEffect(() => {
    queryContractById(contractId).then((res: any) => {
      setContract(res.data);
    });
  }, []);

  useEffect(() => {
    getContractContentHtml(contractId).then((data) => {
      setContent(data.data);
    });
  }, []);

  const stepsData: StepItem[] = [
    {
      title: "原合同信息",
      description: "原合同信息",
      component: <OriginalContractInfo originalContractId={contract?.originalContractId} defaultActive={false}/>,
      showWhen: true,
    },
    {
      title: "合同续签信息",
      description: "合同续签信息",
      component: <RenewalContractInfo contract={contract} defaultActive={false}/>,
      showWhen: true,
    },
    {
      title: "授权委托书信息",
      description: "授权委托书信息",
      component: (
        <Authorize ref={authorizationRef} defaultActive={false} contract={contract} isEdit={false} />
      ),
      ref: authorizationRef,
      showWhen: true,
    },
    {
      title: "合同签订信息",
      description: "合同签订信息",
      component: (
        <Sign ref={signInformationRef} defaultActive={false} contract={contract} isEdit={false} />
      ),
      ref: signInformationRef,
      showWhen: true,
    },
    {
      title: "合同履行计划",
      description: "合同履行计划",
      component: (
        <PerformancePlan
          ref={performancePlanRef}
          isEdit
          defaultActive
          noticeId={noticeId}
          contractId={contractId}
        />
      ),
      ref: performancePlanRef,
      showWhen: true,
    },
  ];
  const save = async () => {
    if (!performancePlanRef.current) return;
    const { form } = performancePlanRef.current;
    const values = await form.validateFields();
    if (!values) return;
    const fields = Object.values(form.getFieldsValue());
    fields.forEach((i: any, index: number) => {
      i.planCompletionDate = moment(i.planCompletionDate).format("YYYY-MM-DD");
      i.name = `阶段${index + 1}`;
    });
    const params = {
      plans: fields,
      contractId,
      noticeId,
    };
    const { success } = await saveOnePlan(params);
    if (!success) return;
    setTimeout(() => {
      props.setVisible(false);
    }, 1000);
  };
  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={(key: string) => {
              setActiveKey(key);
            }}
          />
        </div>
        <Button onClick={save} type="primary">
          保存
        </Button>
      </div>
      <div></div>
      <div className="sevenContractDrafting_body_box">
        {activeKey == "1" && (
          <>
            {/*<CustomSplitter*/}
            {/*  left={*/}
            {/*    <div*/}
            {/*      style={{*/}
            {/*        transform: `scale(1.0)`,*/}
            {/*        transformOrigin: "top left", // 设置缩放原点为左上角*/}
            {/*        width: "789px",*/}
            {/*        height: "calc(100vh - 120px)",*/}
            {/*      }}*/}
            {/*      dangerouslySetInnerHTML={{*/}
            {/*        __html: content,*/}
            {/*      }}*/}
            {/*    />*/}
            {/*  }*/}
            {/*  right={*/}
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen && step.component}
                    </Fragment>
                  ))}
                </div>
            {/*  }*/}
            {/*/>*/}
          </>
        )}
        {activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>
    </>
  );
};
export default PerformancePlanPage;
