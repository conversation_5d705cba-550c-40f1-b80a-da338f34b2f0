import { Button, Space, Tabs } from "@/components-pc";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "./index.scss";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { message, Modal, type TabsProps } from "antd";
import { Authorize } from "@/components/contract/authorization";
import { Sign } from "@/components/contract/sign";
import { PerformancePlan } from "@/components/contract/performance/plan";
import { PerformanceRecord } from "@/components/contract/performance/record";
import { getContractContentHtml } from "@/api/contract/contractAttachment";
import { queryContractById } from "@/api/contract/contract";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import {
  loadPerformanceRecordList,
  saveRecords,
} from "@/api/contract/performance";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";
import RenewalContractInfo from "@/components/contract/renewal/renewalInfo";
import OriginalContractInfo from "@/components/contract/originalInfo";

interface StepItem {
  title: string;
  description: string;
  component: React.ReactNode;
  ref?: React.RefObject<any>;
  showWhen: boolean;
}

// 定义组件状态接口
interface ComponentState {
  isShow: boolean;
  activeKey: string;
  current: number;
  isEdit: boolean;
}

const PerformanceRecordPage: React.FC<NoticeFormProps> = (props) => {
  // 初始化状态
  const [state, setState] = useState<ComponentState>({
    isShow: true,
    activeKey: "1",
    current: 4,
    isEdit: true,
  });
  const onChange = (key: string) => {
    setState((pre) => ({ ...pre, activeKey: key }));
  };
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "表单",
    },
    {
      key: "2",
      label: "正文",
    },
    {
      key: "3",
      label: "流程",
    },
  ];

  const authorizationRef = useRef<any>(null);

  const signInformationRef = useRef<any>(null);
  const performanceRef = useRef<any>(null);
  const performanceStatusRef = useRef<any>(null);

  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
        recordId: 0,
        reviewerId: 0,
        institutionId: 0,
        institutionName: "",
      };
  const { contractId, noticeId, recordId } = customParams;

  const [content, setContent] = useState<string>("");

  const [contract, setContract] = useState<any>({});

  const [records, setRecords] = useState<any[]>([]);

  useEffect(() => {
    getContractContentHtml(contractId).then((data) => {
      setContent(data.data);
    });
    queryContractById(contractId).then((res: any) => {
      setContract(res.data);
    });

    loadPerformanceRecordList(contractId).then((res) => {
      if (!res.success) return;
      setRecords(res.data.list);
    });
  }, []);
  const stepsData: StepItem[] = [
    {
      title: "原合同信息",
      description: "原合同信息",
      component: <OriginalContractInfo originalContractId={contract?.originalContractId} defaultActive={false}/>,
      showWhen: true,
    },
    {
      title: "合同续签信息",
      description: "合同续签信息",
      component: <RenewalContractInfo contract={contract} defaultActive={false}/>,
      showWhen: true,
    },
    {
      title: "授权委托书信息",
      description: "授权委托书信息",
      component: (
        <Authorize ref={authorizationRef} defaultActive={false} contract={contract} isEdit={false} />
      ),
      ref: authorizationRef,
      showWhen: true,
    },
    {
      title: "合同签订信息",
      description: "合同签订信息",
      component: (
        <Sign ref={signInformationRef} defaultActive={false} contract={contract} isEdit={false} />
      ),
      ref: signInformationRef,
      showWhen: true,
    },
    {
      title: "合同履行计划",
      description: "合同履行计划",
      component: (
        <PerformancePlan
          ref={performanceRef}
          contractId={contractId}
          isEdit={false}
        />
      ),
      ref: performanceRef,
      showWhen: true,
    },
    {
      title: "合同履行情况",
      description: "合同履行情况",
      component: (
        <PerformanceRecord
          ref={performanceStatusRef}
          isEdit={true}
          defaultActive
          contractId={contractId}
          amount={contract?.amount}
        />
      ),
      ref: performanceStatusRef,
      showWhen: true,
    },
  ];

  const handlerSubmit = async () => {
    Modal.confirm({
      title: "提示",
      icon: <ExclamationCircleFilled />,
      content: "您确定要保存并提交数据吗?",
      async onOk() {
        const values = performanceStatusRef.current?.state?.formData;
        const plans = performanceStatusRef.current?.state?.performancePlanList;

        values.forEach((item: any) => {
          const attachments: { fileKey: string; fileCategory: string }[] = [];
          item.attachments.split(",").forEach((i: any) => {
            attachments.push({
              fileCategory: "VexUysElR7w",
              fileKey: i,
            });
          });
          item.attachments = attachments;
        });

        const body: any = {
          contractId,
          noticeId,
          records: [],
        };
        values.forEach((i: any, index: number) => {
          body.records.push({
            ...i,
            planId: plans[index]?.id,
          });
        });
        const { success } = await saveRecords(body);
        if (!success) return;
        message.success("提交成功");
        setTimeout(() => {
          props.setVisible(false);
        }, 1000);
      },
      onCancel() {},
    });
  };

  return (
    <>
      <div className="sevenContractDrafting_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={state.activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div>
          <Space>
            {!Boolean(records?.length) && (
              <Button
                color="primary"
                variant="solid"
                onClick={async () => {
                  try {
                    const v =
                      await performanceStatusRef.current?.form?.validateFields();
                    handlerSubmit();
                  } catch (error) {
                    console.log(error);
                  }
                }}
              >
                保存
              </Button>
            )}
          </Space>
        </div>
      </div>
      <div></div>
      <div className="sevenContractDrafting_body_box">
        {state.activeKey == "1" && (
          <>
            <CustomSplitter
              left={
                <div
                  style={{
                    transform: `scale(1.0)`,
                    transformOrigin: "top left", // 设置缩放原点为左上角
                    width: "789px",
                    height: "calc(100vh - 120px)",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: content,
                  }}
                />
              }
              right={
                <div className={`compliance_right_box`}>
                  {stepsData.map((step, index) => (
                    <Fragment key={index}>
                      {step.showWhen && step.component}
                    </Fragment>
                  ))}
                </div>
              }
            />
          </>
        )}
        {state.activeKey == "2" && <FlowImage recordId={recordId} />}
      </div>
    </>
  );
};
export default PerformanceRecordPage;
