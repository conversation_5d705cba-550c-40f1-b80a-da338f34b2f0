/* ./index.css */

/* 页面容器 */
.compliance_box {
  padding: 20px;
  max-width: 1440px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 顶部标签页和按钮区域 */
.tab-header-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap; /* 小屏幕自动换行 */
  border-bottom: 1px solid #e4e4e4;
  padding-bottom: 12px;
  margin-bottom: 24px;
}

/* 切换卡容器 */
.tab-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

/* 切换卡项 */
.tab-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: color 0.2s ease;
}

.tab-item.active {
  font-weight: bold;
  color: #007bff;
}

/* 按钮区域 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 按钮通用样式 */
.action-buttons button {
  padding: 6px 12px;
  border: none;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  border-radius: 4px;
  height: 30px;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.action-buttons button:hover {
  background-color: #0056b3;
}

/* 分隔线 */
.button-separator {
  width: 1px;
  height: 20px;
  background-color: #ccc;
  margin: 0 8px;
}

/* 内容容器 */
.content-container {
  margin: 0 auto;
  width: 1200px;
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

/* 响应式设计：小屏幕适配 */
@media (max-width: 1280px) {
  .content-container {
    width: 100%;
    padding: 16px;
  }

  .tab-header-buttons {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .action-buttons {
    margin-top: 8px;
  }
}
