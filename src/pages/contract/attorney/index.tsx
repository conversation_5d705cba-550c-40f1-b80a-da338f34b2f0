import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    PageContainer,
} from "@ant-design/pro-components";
import {Form, FormSelect} from "@/components/form-pc";
import {Col, Popconfirm, Row} from "antd";
import {usePresenter} from "@/pages/contract/attorney/hooks/usePresenter";
import {<PERSON><PERSON>, Modal, ProTable} from "@itm/components-pc";
import moment from "moment";
import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import OssFileUploader from "@/components/oss/OssFileUpload";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import FormUserChoiceSelect from "@/components/user/choice/FormUserChoiceSelect";
import './index.scss';
import {ContractFileUpload} from "@/components/contract/file/upload";
import {ValidateUtils} from "@codingapi/ui-framework";

interface IProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, record: any) => void;
}

const PowerOfAttorney: React.FC<IProps> = (props) => {
    const actionRef = React.useRef<ActionType>(null);

    const [fileKey, setFileKey] = React.useState<string>("");

    const [unitId, setUnitId] = React.useState<any>(null);

    const [legalRepresentativeId, setLegalRepresentativeId] =
        React.useState<any>(null);

    const [deptId, setDeptId] = React.useState<any>(null);

    const [authorizedPersonId, setAuthorizedPersonId] = React.useState<any>(null);

    const form = Form.useForm();

    const {state, presenter} = usePresenter();

    const onSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateSelected(nodes[0] ? nodes[0] : null);
    };

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    React.useEffect(() => {
        form.reset();
        if (state.selectedDeptId > 0) {
            form.setFieldsValue({
                deptId: state.selectedDeptId,
            });
        }
    }, [state.selectedDeptId]);

    const columns: ProColumns<any>[] = [
        {
            title: "选择",
            dataIndex: "selection",
            valueType: "radio",
            search: false,
            hidden: !props.isComponent,
            width: 60,
            render: (_, record) => (
                <input
                    type="radio"
                    name="supplierSelection"
                    onClick={() => {
                        props.onSelectedChange && props.onSelectedChange(record.id, record);
                    }}
                />
            ),
        },
        {
            title: "编号",
            dataIndex: "id",
            search: false,
            width: 80,
        },
        {
            title: "单位ID",
            dataIndex: "unitId",
            search: false,
            hidden: true,
        },
        {
            title: "单位",
            dataIndex: "unitName",
            search: false,
        },
        {
            title: "法定代表人Id",
            dataIndex: "legalRepresentativeId",
            search: false,
            hidden: true,
        },
        {
            title: "法定代表人",
            dataIndex: "legalRepresentativeName",
            search: false,
        },
        {
            title: "被授权部门Id",
            dataIndex: "deptId",
            search: false,
            hidden: true,
        },
        {
            title: "被授权部门",
            dataIndex: "deptName",
            search: false,
        },
        {
            title: "被授权人Id",
            dataIndex: "authorizedPersonId",
            search: false,
            hidden: true,
        },
        {
            title: "被授权人",
            dataIndex: "authorizedPersonName",
        },
        {
            title: "上传人Id",
            dataIndex: "sysCreator",
            search: false,
            hidden: true,
        },
        {
            title: "上传人",
            dataIndex: "sysCreatorName",
            search: false,
        },
        {
            title: "上传时间",
            dataIndex: "sysCreateTime",
            search: false,
            render: (dom: React.ReactNode, record: any) => {
                const timestamp = record.sysCreateTime;
                const isValidTimestamp =
                    timestamp &&
                    !isNaN(Number(timestamp)) &&
                    moment(Number(timestamp)).isValid();
                return isValidTimestamp
                    ? moment(Number(timestamp)).format("YYYY-MM-DD HH:mm:ss")
                    : "-";
            },
        },
        {
            title: "引用次数",
            dataIndex: "referenceCount",
            search: false,
        },
        {
            title: "文件KEY",
            dataIndex: "fileUrl",
            search: false,
            hidden: true,
        },
        {
            title: "操作",
            hidden: props.isComponent,
            valueType: "option",
            render: (text, record, _, action) => [
                <a
                    key="download"
                    onClick={() =>
                        presenter.downloadFile(record.fileUrl, record.fileName)
                    }
                >
                    下载
                </a>,
                <Popconfirm
                    key="delete"
                    title="删除提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={() => presenter.delete(record.id)}
                    okText="确认"
                    cancelText="取消"
                >
                    <a key="delete">删除</a>
                </Popconfirm>,
            ],
        },
    ];

    return (
        <PageContainer>
            <Row>
                {!props.isComponent && (
                    <Col span={4} style={{height: "665px", overflow: "auto"}}>
                        <OrgTree
                            query={{needDept: true}}
                            onSelect={onSelect}
                            defaultExpandAll={true}
                        />
                    </Col>
                )}
                <Col span={props.isComponent ? 24 : 20} className={"proTable_col_box"}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        scroll={{y: 450}}
                        columns={columns}
                        //style=
                        //tableStyle={{maxHeight: "600px",minHeight: "600px", overflow: "auto"}}
                        toolBarRender={() => {
                            return props.isComponent
                                ? []
                                : [
                                    <Button
                                        type={"primary"}
                                        onClick={() => {
                                            //console.log("selectedDeptId:", state.selectedDeptId)
                                            presenter.showAddModal();
                                        }}
                                    >
                                        新增
                                    </Button>,
                                ];
                        }}
                        request={async (params, sort, filter) => {
                            const selectedDeptId = state.selectedDeptId ?? 0;
                            if (selectedDeptId != 0) {
                                const searchParams = {
                                    ...params,
                                    deptId: selectedDeptId,
                                };
                                return presenter.searchTable(searchParams, sort, filter);
                            }
                            return presenter.searchTable(params, sort, filter);
                        }}
                    />
                </Col>
            </Row>
            <Modal
                open={state.addVisible}
                title={"新增授权委托书"}
                destroyOnHidden={true}
                onCancel={() => {
                    presenter.hideAddModal();
                }}
                footer={() => (
                    <>
                        <Button
                            key="submit"
                            type="primary"
                            onClick={async () => {
                                await form.submit();
                                presenter.hideAddModal();
                            }}
                        >
                            提交
                        </Button>
                        <Button
                            key="cancel"
                            onClick={() => {
                                presenter.hideAddModal();
                            }}
                        >
                            取消
                        </Button>
                    </>
                )}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                    onFinish={async (values) => {
                        void presenter.save({...values, fileKey});
                    }}
                >
                    <FormOrgTreeSelect
                        label={"单位"}
                        placeholder={"请选择单位"}
                        name={"unitId"}
                        query={{needDept: false}}
                        selectable={["UNIT"]}
                        defaultExpandAll={true}
                        onChange={setUnitId}
                        required={true}
                    />

                    <FormUserChoiceSelect
                        label={"法定代表人"}
                        placeholder={"请选择法定代表人"}
                        name="legalRepresentativeId"
                        onChange={setLegalRepresentativeId}
                        required={true}
                    />

                    <FormOrgTreeSelect
                        label={"被授权部门"}
                        placeholder={"请选择被授权部门"}
                        name={"deptId"}
                        query={{needDept: true}}
                        selectable={["DEPT"]}
                        defaultExpandAll={true}
                        onChange={setDeptId}
                        required={true}
                    />

                    <FormUserChoiceSelect
                        label={"被授权人"}
                        placeholder={"请选择被授权人"}
                        name="authorizedPersonId"
                        onChange={setAuthorizedPersonId}
                        required={true}
                    />
                    {/*<OssFileUploader*/}
                    {/*    label="上传附件"*/}
                    {/*    uploaderAccept="application/pdf"*/}
                    {/*    uploaderMaxCount={1}*/}
                    {/*    bucket="document"*/}
                    {/*    ossFileKey={fileKey}*/}
                    {/*    onKeyChange={setFileKey}*/}
                    {/*/>*/}
                    <div>上传授权委托书</div>
                    <ContractFileUpload
                        fileType="application/pdf"
                        maxCount={1}
                        bucket="document"
                        onChange={(key) => {
                            setFileKey(key)
                        }}
                    />
                </Form>
            </Modal>
        </PageContainer>
    );
};

export default PowerOfAttorney;
