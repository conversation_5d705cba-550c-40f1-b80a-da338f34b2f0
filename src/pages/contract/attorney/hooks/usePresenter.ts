import React from "react";
import {State} from "@/pages/contract/attorney/types";
import {Presenter} from "@/pages/contract/attorney/presenter";
import {ApiImpl} from "@/pages/contract/attorney/model";


const initialState: State = {
    addVisible: false,
    selectedDeptId: 0,
    deptTree: [],
    tableRefreshVersion: 0
}

export function usePresenter() {
    const [state, dispatch] = React.useState<State>(initialState);

    const presenterRef = React.useRef<Presenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new Presenter(dispatch, new ApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}