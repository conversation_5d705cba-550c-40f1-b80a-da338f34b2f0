import {Dispatch, State, Api} from "@/pages/contract/attorney/types";
import {TreeData} from "@/api/user/org";
import {downloadOssFile} from "@/utils/download";

export class Presenter {
    private readonly dispatch: Dispatch<State>;

    private readonly api: Api;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    }

    public updateSelected = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedDeptId: node?.id ?? 0,
            }
        });
        this.refreshTable();
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public delete = (id: number) => {
        const body = {
            id: id
        }
        this.api.delete(body)
            .then((res) => {
                if (res.success) {
                    this.refreshTable();
                }
            })
            .catch(e => {
                console.log(e);
            })
    }

    public searchTable = async (params: any, sort: any, filter: any): Promise<any> => {
        const searchParams = {...params};
        const res = await this.api.loadTable(searchParams);
        const data = res.data?.list;
        return Promise.resolve({
            success: true,
            data: data,
        })
    }

    public showAddModal() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: true,
            }
        })
    }

    public hideAddModal() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: false,
            }
        })
    }

    public save(values: any) {
        void this.api.save(values).then((res) => {
            this.refreshTable();
        });
    }

    public downloadFile(fileKey: string, fileName: string) {
        downloadOssFile(fileKey).then();
    }
}
