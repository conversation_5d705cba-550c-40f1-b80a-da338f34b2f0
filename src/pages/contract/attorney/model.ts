import {Api} from "@/pages/contract/attorney/types";
import {page, save, del} from "@/api/contract/attorney";
import {generateFileAuth, downloadFile} from "@/api/oss/oss"


export class ApiImpl implements Api {
    delete(params: any): Promise<{ success: boolean }> {
        return del(params);
    }

    detail(params: any): Promise<any> {
        return Promise.resolve(undefined);
    }

    loadTable(params: any): Promise<any> {
        return page(params.current, params.pageSize, params.deptId, params.authorizedPersonName);
    }

    save(params: any): Promise<{ success: boolean }> {
        return save(params);
    }

    downloadFile(code: string,fileName:string): Promise<any> {
        return downloadFile(code,fileName)
    }

    generateFileAuth(params: any): Promise<any> {
        return generateFileAuth(params);
    }
}