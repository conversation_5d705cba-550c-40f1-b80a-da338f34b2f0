export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface State {
    addVisible: boolean; // 添加框是否可见

    selectedDeptId: number; //左侧选中的部门
    deptTree: any[];

    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新
}

export interface Api {

    loadTable: (params: any) => Promise<any>;

    detail: (params: any) => Promise<any>;

    save: (params: any) => Promise<{ success: boolean }>;

    delete: (params: any) => Promise<{ success: boolean }>;

    downloadFile: (code: string,fileName: string) => Promise<any>;

    generateFileAuth: (params: any) => Promise<any>;
}