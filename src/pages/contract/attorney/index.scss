.proTable_col_box{
  .ant-table-body {
    height: 400px;
    overflow: auto;
    border: 1px solid #eee;
  }
}


.eightContractDrafting-tree-container {
  width: 100%;
  overflow-y: auto;

  .tree-container-box {
    width: 100%;
    border-radius: 0 !important;
    //height: 100%;
    height: calc(100vh - 130px) !important;

  }

  .tree-node {
    background-color: #654321;
    padding: 4px 0;
  }

  .tree-wrapper {
    //background: #fff;
    //padding: 8px;
    //border-radius: 4px;
  }

  .custom-tree .ant-tree-node-content-wrapper {
    //font-size: 16px; /* 修改为你想要的字体大小 */
  }
}

.eightContractDrafting_col_box {
  padding: 0 !important;
  background-color: white;

  .height_search {
    background-color: white;
    padding: 15px 15px 0;

    .ant-form {
      border-bottom: 1px solid #eae2e2;
    }

    //
  }

  .table_box {
    overflow-y: auto;
    margin-top: 10px;
    padding: 10px 20px;

    .fourPopul_main_box {
      background-color: white;
      display: flex;
      width: 100%;
      justify-content: space-between;
      padding: 20px;
      font-size: 18px;
      color: #5a5a5a;
      margin-top: 10px;
      border-radius: 5px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);

      .left_main_box {
        width: 55%;
        display: flex;

        .checkbox {
          //margin-right: 30px;
        }

        .info_img {
          margin: 0 30px 10px;
          height: 100%;
          padding-top: 5px;
        }

        .introduce {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

        }

      }

      .right_main_box {
        width: 35%;
        border-left: 1px solid #ececec;
        padding-left: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .status {
          color: #37c495;

        }

        .subordinates {
          color: #63a3ff;
        }
      }
    }
  }
}