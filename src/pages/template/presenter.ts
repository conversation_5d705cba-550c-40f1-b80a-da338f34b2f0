import {Template<PERSON><PERSON>, TemplateState, Dispatch} from "@/pages/template/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<TemplateState>;

    private readonly api: TemplateApi;

    private state: TemplateState;

    public constructor(dispatch: Dispatch<TemplateState>, api: TemplateApi, state: TemplateState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: TemplateState) => {
        this.state = newStatus;
    }

    // 刷新表格数据
    public refreshTemplateTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    // 发布数据
    public publishTemplate = async (record: TemplateState) => {
        try {
            const res = await this.api.publishTemplate(record);
            if (res.success) {
                this.refreshTemplateTable();
            }
        } catch (e) {
            console.error("发布失败", e);
        }
    }

    // 删除数据
    public deleteTemplate = async (record: TemplateState) => {
        try {
            const res = await this.api.deleteTemplate(record);
            if (res.success) {
                this.refreshTemplateTable();
            }
        } catch (e) {
            console.error("删除失败", e);
        }
    }

    // 标签表格数据加载函数
    public searchTemplateTable = (params: any, sort: any, filter: any) => {
        const category = this.state.currentValueLabelCodeList;
        const categoryStr = Array.isArray(category) ? category.filter(Boolean).join(',') : category;
        const searchParams = {
            ...params,
            category: categoryStr
        }
        return this.api.loadTemplateTable(searchParams, sort, filter);


    }

    // 设置当前点击的标签数据，然后刷新表格数据
    public updateCurrentLabel = (selected: any) => {
        const selectedNodes = selected.selectedNodes
        if (selectedNodes && selectedNodes.length > 0) {
            const code = selectedNodes[0].code;
            const children = selectedNodes[0].children;
            const childCodes = children
                ? children.map((child: { code: string }) => child.code)
                : [];
            const fullCode = [code, ...childCodes];
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentValueLabelCode: code,
                    currentValueLabelCodeList: fullCode
                };
            });
        } else {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentValueLabelCode: '',
                    currentValueLabelCodeList: []
                };
            });
        }
        this.refreshTemplateTable();
    }

    // label树数据
    public loadLabelTree = () => {
        this.api.loadLabelTree().then(data => {
            this.dispatch((prevState) => {
                const dataArray = Array.isArray(data.data) ? data.data : [];
                const labelTree = this.getTreeOptions(dataArray);
                const labelSelect = this.getSelectOptions(dataArray);
                return {
                    ...prevState,
                    labelTreeData: labelTree,
                    labelSelectData: labelSelect
                }
            })
        });
    }

    public getTreeOptions = (data: any[]): TreeOption[] => {
        return data.map(item => ({
            title: item.name,
            key: item.id.toString(),
            code: item.code,
            children: item.children ? this.getTreeOptions(item.children) : undefined,
        }));
    };

    public getSelectOptions = (data: any[]): selectOption[] => {
        return data.map(item => ({
            label: item.name,
            value: item.id.toString(),
            children: item.children ? this.getSelectOptions(item.children) : undefined,
        }));
    };

    public resetUploadFile = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadedFile: []
            };
        });
    }

    public setUploadVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadVisible: v
            }
        })
    }

    public setPreviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewVisible: v
            }
        })
    }

    public setPreviewUrl = (v: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewUrl: v
            }
        })
    }

    public addUploadFile = (filename: string, base64: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadedFile: [
                    ...prevState.uploadedFile,
                    {filename, base64}
                ]
            };
        });
    }

    public handleUploadFinish = async () => {

        if (!this.state.uploadedFile || this.state.uploadedFile.length === 0) {
            return;
        }
        const formData = new FormData()
        this.state.uploadedFile.forEach(({filename, base64}) => {
            const blob = this.dataURLtoBlob(base64);
            const file = new File([blob], filename, {type: blob.type});
            formData.append("fileList", file);
        });
        formData.append("category", this.state.currentValueLabelCode)
        this.resetUploadFile();

        const res = await this.api.uploadTemplateFile(formData);
        if ((res as any)?.success) {
            this.setUploadVisible(false);
            return;
        } else {
            return;
        }
    };

    public dataURLtoBlob(dataUrl: string): Blob {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "";
        const bStr = atob(arr[1]);
        let n = bStr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bStr.charCodeAt(n);
        }
        return new Blob([u8arr], {type: mime});
    }
}