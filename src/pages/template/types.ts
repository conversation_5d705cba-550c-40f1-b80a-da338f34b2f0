export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
interface uploadedFileProps {
    filename: string;
    base64: string;
}
export interface TemplateState {

    currentValueLabelCode: string; // 当前value的labelCode

    currentValueLabelCodeList: string[]; // 当前value的labelCode

    previewVisible: boolean; //封面预览

    previewUrl: string; //封面预览

    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新

    labelTreeData: any[]; // label树结构数据

    labelSelectData: any[]; // label树结构选择数据

    uploadVisible: boolean;

    uploadedFile: uploadedFileProps[];
}


export interface TemplateApi {

    loadLabelTree: () => Promise<any>;

    loadTemplateTable: (params: any, sort: any, filter: any) => Promise<any>;

    publishTemplate: (params: any) => Promise<{ success: boolean }>;

    deleteTemplate: (params: any) => Promise<{ success: boolean }>;

    uploadTemplateFile: (formData: FormData) => Promise<any>;

}