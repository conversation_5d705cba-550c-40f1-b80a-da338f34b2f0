import React from "react";
import {TemplateState} from "@/pages/template/types";
import {ValuePresenter} from "@/pages/template/presenter";
import {TemplateApiImpl} from "@/pages/template/model";

const initialState: TemplateState = {
    currentValueLabelCode: '',
    currentValueLabelCodeList: [],
    previewVisible: false,
    previewUrl: '',
    tableRefreshVersion: 0,
    labelTreeData: [],
    labelSelectData: [],
    uploadVisible: false,
    uploadedFile: [],
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<TemplateState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new TemplateApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}