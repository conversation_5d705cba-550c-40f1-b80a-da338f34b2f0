import { DownOutlined } from "@ant-design/icons";
import { ActionType, ProColumns } from "@ant-design/pro-components";
import { PageContainer } from "@ant-design/pro-components";
import { Col, Popconfirm, Row, Tooltip, TreeProps } from "antd";
import React from "react";
import { useValuePresenter } from "@/pages/template/hooks/usePresenter";
import { Button, Modal, ProTable, Tree } from "@itm/components-pc";
import {Form, FormUploader} from "@/components/form-pc";
import { ValidateUtils } from "@codingapi/ui-framework";
import { POBrowser } from "js-pageoffice";
import moment from "moment";

interface TemplateManagePageProps {
  isComponent?: boolean;
  onSelectedChange?: (id: number, name: string) => void;
}

const TemplateManagePage: React.FC<TemplateManagePageProps> = (props) => {
  const actionRef = React.useRef<ActionType>();
  const form = Form.useForm();
  const { state, presenter } = useValuePresenter();

  // 点击标签树节点时，更新当前标签
  const onSelect: TreeProps["onSelect"] = (selectedKeys, info) => {
    presenter?.updateCurrentLabel(info);
  };

  React.useEffect(() => {
    actionRef.current?.reload();
  }, [state.tableRefreshVersion]);

  // 当左侧树选中变化时，设置表单字段值
  React.useEffect(() => {
    if (state.currentValueLabelCode) {
    }
  }, [state.currentValueLabelCode]);

  // 当组件挂载时，加载标签树数据
  React.useEffect(() => {
    presenter?.loadLabelTree();
    presenter?.updateCurrentLabel([]);
  }, [props.isComponent]);

  const columns: ProColumns<any>[] = [
    // 单选列 - 仅微应用模式下显示
    {
      title: "选择",
      dataIndex: "selection",
      valueType: "radio",
      search: false,
      hidden: !props.isComponent,
      width: 60,
      render: (_, record) => (
        <input
          type="radio"
          name="supplierSelection"
          onClick={() => {
            if (props.onSelectedChange) {
              props.onSelectedChange(record.id, record.name);
            }
          }}
        />
      ),
    },
    {
      title: "模板id",
      dataIndex: "id",
      search: false,
      hidden: true,
    },
    {
      title: "封面",
      dataIndex: "imageUrl",
      search: false,
      render: (dom: React.ReactNode, record: any): React.ReactNode => {
        const url = record.imageUrl;
        if (!url) return null;
        return (
          <img
            src={url}
            alt="封面"
            style={{ width: "50px", height: "auto" }}
            onClick={() => {
              presenter?.setPreviewUrl(url);
              presenter?.setPreviewVisible(true);
            }}
          />
        );
      },
    },
    {
      title: "模板名称",
      dataIndex: "name",
      ellipsis: true,
      render: (dom, entity, index, action, schema) => (
        <Tooltip placement="topLeft" title={entity.name}>
          <span title={entity.name} style={{ cursor: "pointer" }}>
            {entity.name?.length > 25
              ? `${entity.name.slice(0, 25)}...`
              : entity.name}
          </span>
        </Tooltip>
      ),
    },
    {
      title: "上传单位",
      dataIndex: "uploadUnit",
      search: false,
    },
    {
      title: "上传人",
      dataIndex: "uploadUser",
      search: false,
    },
    {
      title: "模板页数",
      dataIndex: "pageSize",
      search: false,
    },
    {
      title: "状态",
      dataIndex: "publishStatus",
      search: false,
      render: (dom: React.ReactNode, entity: any): React.ReactNode => {
        const text = entity.publishStatus;
        return text === 0 ? "未发布" : "已发布";
      },
    },
    {
      title: "发布时间",
      dataIndex: "publishTime",
      search: false,
      render: (dom: React.ReactNode, record: any) => {
        const timestamp = record.publishTime;
        const isValidTimestamp =
          timestamp &&
          !isNaN(Number(timestamp)) &&
          moment(Number(timestamp)).isValid();
        return isValidTimestamp
          ? moment(Number(timestamp)).format("YYYY-MM-DD HH:mm:ss")
          : "-";
      },
    },
    {
      title: "引用次数",
      dataIndex: "citeNum",
      search: false,
    },
    {
      title: "操作",
      key: "option",
      valueType: "option",
      width: 180,
      hidden: props.isComponent,
      render: (_, record) => [
        <a
          key="update"
          onClick={() => {
            const token = localStorage.getItem("token");
            const param = {
              token: token,
              id: record.id,
              path: "/template/pageoffice/word",
            };
            const paramStr = JSON.stringify(param);
            POBrowser.setHeader("Authorization", token);
            POBrowser.setProxyBaseAPI("/itm-template");
            POBrowser.openWindow(
              `${location.href}/word`,
              "width=1150px;height=900px;",
              paramStr
            );
          }}
        >
          编辑
        </a>,

        record.publishStatus !== 1 && (
          <Popconfirm
            key="publish"
            title="发布提示"
            description="确认要发布这个模板吗?"
            onConfirm={() => {
              presenter?.publishTemplate(record);
            }}
            okText="确认"
            cancelText="取消"
          >
            <a>发布</a>
          </Popconfirm>
        ),

        <Popconfirm
          key="delete"
          title="删除提示"
          description="确认要删除这个模板吗?"
          onConfirm={() => {
            presenter?.deleteTemplate(record);
          }}
          okText="确认"
          cancelText="取消"
        >
          <a key="delete" hidden={(record.children?.length ?? 0) !== 0}>
            删除
          </a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Row>
        <Col span={4}>
          {state.labelTreeData.length > 0 && (
            <Tree
              showLine
              switcherIcon={<DownOutlined />}
              defaultExpandAll={true}
              onSelect={onSelect}
              treeData={state.labelTreeData}
            />
          )}
        </Col>
        <Col span={20}>
          <ProTable
            actionRef={actionRef}
            rowKey="id"
            headerTitle="模板列表"
            scroll={{ y: 400 }}
            columns={columns}
            toolBarRender={() => {
              return props.isComponent
                ? []
                : [
                    <Button
                      key="upload"
                      type="primary"
                      onClick={() => {
                        form.reset();
                        presenter?.resetUploadFile();
                        presenter?.setUploadVisible(true);
                      }}
                    >
                      新增模板
                    </Button>,
                  ];
            }}
            request={async (params, sort, filter) => {
              return presenter?.searchTemplateTable(params, sort, filter);
            }}
          />
        </Col>
      </Row>

      <Modal
        open={state.uploadVisible}
        title="新增模板"
        onCancel={() => {
          presenter?.setUploadVisible(false);
        }}
        onOk={async () => {
          await form.submit();
          form.reset();
        }}
      >
        <Form form={form} onFinish={presenter?.handleUploadFinish}>
          <FormUploader
            name="file"
            label="选择模板"
            uploaderAccept="*"
            validateFunction={ValidateUtils.validateNotEmpty}
            onUploaderUpload={(filename, base64) => {
              presenter?.addUploadFile(filename, base64);
              return Promise.resolve({
                id: "temp-id",
                name: filename,
                url: base64,
              });
            }}
          />
        </Form>
      </Modal>

      <Modal
        open={state.previewVisible}
        footer={null}
        onCancel={() => presenter?.setPreviewVisible(false)}
        centered
        width="40%"
      >
        <img
          src={state.previewUrl}
          alt="预览图"
          style={{ width: "100%", maxHeight: "70vh", objectFit: "contain" }}
        />
      </Modal>
    </PageContainer>
  );
};

export default TemplateManagePage;
