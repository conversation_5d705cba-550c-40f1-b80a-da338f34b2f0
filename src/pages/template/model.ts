import {TemplateApi} from "./types";
import {labelTree, list, del, uploadTemplateFile, publish} from "@/api/template/template";


export class TemplateApiImpl implements TemplateApi {
    loadLabelTree(): Promise<any> {
        return labelTree();
    }

    loadTemplateTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    uploadTemplateFile(formData: FormData): Promise<any> {
        return uploadTemplateFile(formData);
    }

    async publishTemplate(params: any): Promise<{ success: boolean }> {
        try {
            const res = await publish(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }
    async deleteTemplate(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

}