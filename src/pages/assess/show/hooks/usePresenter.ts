import React from "react";
import { AssessState } from "@/pages/assess/show/types";
import { AssessPresenter } from "@/pages/assess/show/presenter";
import { AssessApiImpl } from "@/pages/assess/show/model";

const initialState: AssessState = {
    myAssessInstitutionList: [],
    alreadyAssessInstitutionList:[],
    alreadyAssessInstitutionTotal: 0,
    canAssessInstitutionList: []
}

export function useAssessPresenter() {
    const [state, dispatch] = React.useState<AssessState>(initialState);

    const presenterRef = React.useRef<AssessPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 AssessPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new AssessPresenter(dispatch, new AssessApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
} 