import { AssessApi, AssessState, Dispatch} from "@/pages/assess/show/types";

export class AssessPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AssessState>;
    private readonly api: AssessApi;
    private state: AssessState;

    public constructor(dispatch: Dispatch<AssessState>, api: AssessApi, state: AssessState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }
    public syncState = (newStatus: AssessState) => {
        this.state = newStatus;
    }

     // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
    };

    // 获取我的Assess列表
    public getMyAssessList = async () => {
        const res = await this.api.getMyAssessList();
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    myAssessInstitutionList: res.data?.list,
                }
            });
        }
    };
    // 获取已 Assess列表
    public getAlreadyAssessList = async (current: any, pageSize: any) => {
        const res = await this.api.getAlreadyAssessList(current, pageSize);
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    alreadyAssessInstitutionList: res.data?.list,
                    alreadyAssessInstitutionTotal: res.data?.total,
                }
            });
        }
    }
    // 获取可Assess列表
    public getCanAssessList = async () => {
        const res = await this.api.getCanAssessList();
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    canAssessInstitutionList: res.data?.list,
                }
            });
        }
    };
} 