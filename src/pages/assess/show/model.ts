import { AssessApi } from "./types";
import {getAlreadyAssessInstitutionList,getCanAssessInstitutionList,getMyAssessInstitutionList } from "@/api/assess/assess";

/**
 * Model层接口实现
 * AssessApiImpl 实现了AssessApi接口，提供了数据的增删改查操作
 */
export class AssessApiImpl implements AssessApi {
    async getMyAssessList(): Promise<any> {
           try {
               const res = await getMyAssessInstitutionList();
               return res;
           } catch (err) {
               throw err;
           }
       };
       async getAlreadyAssessList(current: any, pageSize: any): Promise<any> {
           try {
               const res = await getAlreadyAssessInstitutionList(current, pageSize);
               return res;
           } catch (err) {
               throw err;
           }
       };
       async getCanAssessList(): Promise<any> {
           try {
               const params = {
                   title: null
               };
               const res = await getCanAssessInstitutionList(params);
               return res;
           } catch (err) {
               throw err;
           }
       };
}