export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AssessState {
    myAssessInstitutionList: any[];
    alreadyAssessInstitutionList: any[];
    alreadyAssessInstitutionTotal: number;
    canAssessInstitutionList: any[];
}

export interface AssessApi {
    getMyAssessList: () => Promise<any>;
    getAlreadyAssessList: (current: any, pageSize: any) => Promise<any>;
    getCanAssessList: () => Promise<any>;
}
