import { AssessA<PERSON>, AssessWriteState, Dispatch, AssessFormData } from "@/pages/assess/write/types";

export class AssessPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AssessWriteState>;
    private readonly api: AssessApi;
    private state: AssessWriteState;

    public constructor(dispatch: Dispatch<AssessWriteState>, api: AssessApi, state: AssessWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AssessWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };

    // 校验评估记录数据
    public validateAssessRecord = (record: AssessFormData): string | null => {
        if (record.isAssess === undefined || record.isAssess === null) {
            return "是否评估不能为空";
        }
        if (record.isAssess === 1) {
            if (!record.assessDate) {
                return "评估时间不能为空";
            }
            if (!record.assessContent) {
                return "评估内容不能为空";
            }
            if (record.assessOpinion === undefined || record.assessOpinion === null) {
                return "评估意见不能为空";
            }
        }
        return null;
    }

    // 创建评估记录
    public createAssess = async (record: AssessFormData) => {
        try {
            const res = await this.api.saveAssess(record);
            if (res.success) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        editAble: true,
                        passVisible: true,
                        flowData: {
                            ...prevState.flowData,
                            toLeader: res.data.data.isAssess === 1 ? true : false,
                        }
                    }
                });
            }
            return res.data.data
        } catch (e) {
        }
    }
    // 暂存评估记录
    public createAssessAsDraft = async (record: AssessFormData) => {
        try {
            console.log("暂存评估记录", record);
            const res = await this.api.saveAssess(record);
            return res.data.data
        } catch (e) {
        }
    }

    // 获取制度记录详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
            return res;
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 获取评估详情
    public getAssessInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAssessDetailByInstitutionId(id);
            if (res && Object.keys(res).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        editAble: (res.recordId != null && res.recordId != undefined) ? true : false,
                        passVisible: (res.recordId != null && res.recordId != undefined) ? true : false,
                        flowData: {
                            ...prevState.flowData,
                            assessId: res.id,
                            toLeader: res.isAssess === 1 ? true : false
                        }
                    }
                });
                return res;
            } else {
                return null;
            }

        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                listData: data,
                flowData: {
                    ...prevState.flowData,
                    institutionName: data.title,
                    institutionId: data.id
                }
            }
        });
    }

    /**
     * @description 更新是否评估数据
     * */
    public onIsAssessChange = (value: number | undefined) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                formData: { ...prevState.formData, isAssess: value },
                flowData: {
                    ...prevState.flowData,
                    toLeader: (value === 1) ? true : false
                }
            }
        });
    }

    // 更新评估时间数据
    public onAssessDateChange = (value: number | undefined) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                formData: { ...prevState.formData, assessDate: value },
            }
        });
    }

    // 更新评估意见数据
    public onAssessOpinionChange = (value: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                formData: { ...prevState.formData, assessOpinion: value },
            }
        });
    }

    // 更新附件数据
    public onAttachmentKeyChange = (key: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                formData: { ...prevState.formData, fileKey: key ? key.split(',') : [] },
            }
        });
    }
    // 提交流程
    public submitAssessFlow = async (record: any, assessId: number) => {
        if (record.length > 0) {
            const newFlowData = {
                ...this.state.flowData,
                nextReviewerId: record.split(",")[0],
                assessId: assessId,
            };
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    flowData: newFlowData,
                }
            });
            await this.api.submitFlow(newFlowData);
        }

    }
    // 撤销流程
    public recallAssessReview = async () => {
        await this.api.recallAssessReview(this.state.flowData)
    }
    // 变更用户选择框状态
    public changeSelectUserVisible = (visible: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: visible,
            }
        });
    };
    // 更新撤销弹框控制键
    public setRecallReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recallReviewVisible: v
            }
        })
    }
    // 取消用户使用
    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }
} 