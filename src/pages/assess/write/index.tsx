import { Button, Space, Tabs, Row } from "@itm/components-pc";
import React, { useState, useEffect, useRef } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { Message, Modal } from "@itm/components-pc";
import { useLocation } from "react-router-dom";
import { useWritePresenter } from "./hooks/usePresenter";
import { AssessFormData } from "./types";
import GenericFormTable from "@/components/GenericFormTable";
import { Form, Radio, DatePicker, Input, GetProps } from "antd";
import OssFileUploader from "@/components/oss/OssFileUpload";
import dayjs from "dayjs";
import CustomSplitter from "@/components/CustomSplitter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { FlowImage } from "@/components/flowImage";
const messPending = () => {
  type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    return current && current < dayjs().endOf('day');
  };
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const instututionId = searchParams.get("id");

  const { state, presenter } = useWritePresenter();
  // 评估表单数据
  const defaultForm: AssessFormData = {
    institutionId: undefined,
    operatorDept: localStorage.getItem("userdeptname"),
    operatorName: localStorage.getItem("usernickname"),
    operatorDate: new Date().getTime(),
    assessContent: "",
    isAssess: undefined,
  };
  const [form, setForm] = useState<AssessFormData>(defaultForm);
  const formRef = useRef(form);

  const [activeKey, setActiveKey] = useState<string>("1");
  const [userIds, setUserIds] = useState<string | null>("");

  useEffect(() => {
    if (instututionId) {
      presenter
        .getAssessInfoDetailById(Number(instututionId))
        .then((res) => {
          if (res && Object.keys(res).length > 0) {
            setForm(res);
          } else {
            setForm(defaultForm);
          }
        })
        .catch(() => {
          setForm(defaultForm);
        });
      presenter.getInstitutionDetailById(Number(instututionId));
    }
  }, []);

  useEffect(() => {
    setForm((prev) => ({
      ...prev,
      ...state.formData,
      institutionId: state.listData?.id,
    }));
  }, [state.formData, state.listData?.id]);

  useEffect(() => {
    formRef.current = form;
  }, [form]);
  const onChange = (key: string) => {
    setActiveKey(key);
  };

  // 暂存
  const handleSaveAsDraft = async () => {
    try {
      const res = await presenter.createAssessAsDraft(formRef.current);
      if (res.id > 0) {
        setForm(res);
        Message.info("暂存成功");
      } else {
        Message.error("暂存失败，请检查提交内容后重试");
      }
    } catch (error) {
      console.error("暂存失败:", error);
    }
  };

  // 保存评估记录
  const handleSave = async (records: any) => {
    try {
      const res = await presenter.createAssess(formRef.current);
      if (res.id > 0) {
        presenter?.submitAssessFlow(records, res.id);
        presenter?.changeSelectUserVisible(false);
        Message.info("提交成功");
      } else {
        Message.error("提交失败，请检查提交内容后重试");
      }
    } catch (error) {
      console.error("保存失败:", error);
      return 33;
    }
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  const tableHeader = [
    {
      label: "经办人",
      value: form.operatorDept + "/" + form.operatorName,
    },
    {
      label: "经办时间",
      value: presenter.formatDate(form.operatorDate),
    },
  ];

  const tableRows = [
    {
      label: "制度名称",
      value: state.listData.title,
      isLabelRed: true,
    },
    {
      label: "是否评估",
      value: (
        <Form.Item
          rules={[{ required: true, message: "请选择是否评估" }]}
          style={{ marginBottom: 0 }}
        >
          <Radio.Group
            disabled={state.editAble}
            value={form.isAssess}
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "space-around",
            }}
            onChange={(e) => presenter.onIsAssessChange?.(e.target.value)}
          >
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      ),
      isLabelRed: true,
    },
    ...(form.isAssess === 1 ? [
      {
        label: "评估时间",
        value: (
          <Form.Item style={{ marginBottom: 0 }}>
            <DatePicker
              disabled={state.editAble}
              disabledDate={disabledDate}
              value={form.assessDate ? dayjs(form.assessDate) : undefined}
              onChange={(date) => {
                const timestamp = date ? date.valueOf() : undefined;
                presenter.onAssessDateChange?.(timestamp);
              }}
              style={{ height: 32, width: 200 }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        ),
        isLabelRed: true,
      },
      {
        label: "评估内容",
        children: [
          {
            label: "内容",
            value: (
              <Form.Item style={{ marginBottom: 0 }}>
                <Input.TextArea
                  readOnly={state.editAble}
                  rows={3}
                  placeholder="输入评估内容"
                  value={form.assessContent || ""}
                  onChange={(e) =>
                    setForm((prev) => ({
                      ...prev,
                      assessContent: e.target.value,
                    }))
                  }
                />
              </Form.Item>
            ),
          },
          {
            label: "上传附件",
            value: (
              <OssFileUploader
                disabled={state.editAble}
                label=""
                uploaderMaxCount={2}
                uploaderAccept={
                  ".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.zip,.rar"
                }
                bucket={"document"}
                ossFileKey={
                  Array.isArray(form.fileKey)
                    ? form.fileKey.join(",")
                    : form.fileKey || ""
                }
                onKeyChange={(key: string) =>
                  presenter.onAttachmentKeyChange?.(key)
                }
              />
            ),
          },
        ],
        isLabelRed: true,
      },
      {
        label: "经办人评估意见",
        value: (
          <Form.Item style={{ marginBottom: 0 }}>
            <Radio.Group
              disabled={state.editAble}
              value={form.assessOpinion}
              style={{
                width: "100%",
                display: "flex",
                justifyContent: "space-around",
              }}
              onChange={(e) => presenter.onAssessOpinionChange?.(e.target.value)}
            >
              <Radio value={1}>保留</Radio>
              <Radio value={2}>废止</Radio>
              <Radio value={3}>新制定</Radio>
            </Radio.Group>
          </Form.Item>
        ),
        isLabelRed: true,
      },
    ] : []),
  ];

  return (
    <div className="assess-write-container">
      <div className="twoPopul_header_box">
        <div>
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div style={{ marginRight: "30px" }}>
          <Space>
            <Button
              type="primary"
              disabled={state.passVisible}
              onClick={() => {
                handleSaveAsDraft();
              }}
            >
              保存
            </Button>
            {state.passVisible == true && (
              <Button
                hidden={!state.passVisible}
                type="primary"
                danger
                onClick={() => {
                  presenter?.setRecallReviewVisible(true);
                }}
              >
                撤回
              </Button>
            )}

            <Button
              type="primary"
              disabled={state.passVisible}
              onClick={() => {
                const errorMsg = presenter?.validateAssessRecord(
                  formRef.current
                );
                if (errorMsg) {
                  Message.error(errorMsg);
                  return;
                } else {
                  presenter?.changeSelectUserVisible(true);
                }
              }}
            >
              送呈部门负责人
            </Button>
          </Space>
        </div>
      </div>
      <div className="twoPopul_body_box">
        {activeKey == "1" && (
          <CustomSplitter
            leftDefaultSize={825}
            left={
              <div
                style={{
                  transform: `scale(1.0)`,
                  transformOrigin: "top left", // 设置缩放原点为左上角
                  width: "789px",
                  overflow: "auto",
                }}
                dangerouslySetInnerHTML={{
                  __html: state.listData?.htmlContent,
                }}
              />
            }
            right={
              <>
                <div className="tabel_edit" style={{ height: "20vh" }}>
                  <GenericFormTable header={tableHeader} rows={tableRows} />
                </div>
              </>
            }
          />
        )}
        {activeKey == "2" && (
          <div className="process">
            <FlowImage workCode={"innerInstitution_assess"} />
          </div>
        )}
      </div>
      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={state.selectUserVisible}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          presenter?.changeSelectUserVisible(false);
        }}
        onOk={() => handleSave(userIds)}
        onClose={() => {
          presenter?.changeSelectUserVisible(false);
        }}
      >
        <UserChoiceSelect
          multiple
          onChange={setUserIds}
          value={userIds || ""}
        />
      </Modal>
      <Modal
        open={state.recallReviewVisible}
        title="确认撤回吗"
        onCancel={() => presenter?.setRecallReviewVisible(false)}
        onOk={() => {
          presenter?.recallAssessReview();
          presenter?.setRecallReviewVisible(false);
          Message.success("提交成功");
        }}
      ></Modal>
    </div>
  );
};

export default messPending;
