import { AssessApi } from "./types";
import { getInstitutionDetail, save, getAssessInfoDetail, submitFlow,recallFlow } from "@/api/assess/assess";

/**
 * Model层接口实现
 * AssessApiImpl 实现了AssessApi接口，提供了数据的增删改查操作
 */
export class AssessApiImpl implements AssessApi {

    async getInstitutionDetailById(params: any): Promise<any> {
        try {
            const res = await getInstitutionDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }

    async saveAssess(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await save(params);
            return { success: true, data: res };
        } catch (err) {
            return { success: false };
        }
    }

    async getAssessDetailByInstitutionId(params: any): Promise<any> {
        try {
            const res = await getAssessInfoDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async submitFlow(params: any): Promise<any> {
        try {
            await submitFlow(params);
        } catch (err) {
            throw err;
        }
    }
    async recallAssessReview(params: any): Promise<any> {
        try {
            await recallFlow(params);
        } catch (err) {
            throw err;
        }
    }
}