
.assess-write-container {
  padding: 20px 24px;
.twoPopul_header_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;

  .item_tabs {
    .ant-tabs-tab-btn {
      padding: 5px 20px;
    }

    .ant-tabs-nav {
      margin: 0;
    }
  }

}

.twoPopul_body_box {
  width: 100%;
  position: relative;

  .process {
    height: calc(100vh - 240px);
    background-color: white;
  }
}
}

.tabel_edit {
  .form-table{
    height: 30px;
  }
}