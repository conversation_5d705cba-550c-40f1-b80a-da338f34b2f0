export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AssessWriteState {
    // 表格数据
    listData: any;
    formData: AssessFormData;
    // 切换编辑
    editAble: boolean;
    // 呈送按钮
    passVisible: boolean;
    //提交流程表格
    flowData: AssessFlowForm;
    // 选择用户模态框
    selectUserVisible: boolean;
    recallReviewVisible:boolean;
}

export interface AssessApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAssessDetailByInstitutionId: (params: any) => Promise<any>;
    saveAssess: (params: any) => Promise<{ success: boolean; data?: any }>;
    submitFlow: (params: any) => Promise<any>;
    recallAssessReview: (body: any) => Promise<any>;
}

// 评估表单数据类型
export interface AssessFormData {
    id?: number;
    institutionId?: number;
    operatorDept?: string | null;
    operatorName?: string | null;
    operatorDate?: number;
    isAssess?: number;
    assessDate?: number;
    assessContent?: string;
    assessOpinion?: number;
    fileKey?: string[];
    recordId?: any;
    processId?: string;
    reviewerId?: number;
}
export interface AssessFlowForm {
    recordId?: number;
    currentReviewerId?: number;
    nextReviewerId?: number;
    institutionId?: number;
    institutionName?: String;
    success?: boolean;
    toLeader?: boolean;
}