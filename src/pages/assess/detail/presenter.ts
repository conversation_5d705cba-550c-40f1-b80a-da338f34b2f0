import { Assess<PERSON>pi, AssessWriteState, Dispatch } from "@/pages/assess/detail/types";
import { message } from "antd";

export class AssessPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AssessWriteState>;
    private readonly api: AssessApi;
    private state: AssessWriteState;

    public constructor(dispatch: Dispatch<AssessWriteState>, api: AssessApi, state: AssessWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AssessWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };

    // 获取制度记录详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
            return res;
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 获取评估详情
    public getAssessInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAssessDetailByInstitutionId(id);
            if (res && Object.keys(res).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        assessData: res
                    }
                });
            }
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }
    // 获取评估审核记录详情
    public getAssessExamineInfos = async (id: number) => {
        try {
            const res = await this.api.getAssessExamineInfos(id);
            console.log("评估审核记录详情", res);
            if (res && Object.keys(res?.list).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        examineListData: res?.list
                    }
                });
            }
        } catch (e) {}
    }
    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionData: data,
            }
        });
    }
} 