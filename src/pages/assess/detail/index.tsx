import { Tabs } from "@itm/components-pc";
import React, { useState, useEffect } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { useWritePresenter } from "@/pages/assess/detail/hooks/usePresenter";
import GenericFormTable from "@/components/GenericFormTable";
import { Form, Radio } from "antd";
import { downloadOssFile } from "@/utils/download";
import dayjs from "dayjs";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";
import { useSearchParams } from "react-router-dom";

const AssessDetail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const assessId = searchParams.get("assessId");
  const institutionId = searchParams.get("institutionId");
  const { state, presenter } = useWritePresenter();

  const [activeKey, setActiveKey] = useState<string>("1");

  useEffect(() => {
    // 表格初始化
    presenter.getAssessInfoDetailById(Number(assessId));
    presenter.getInstitutionDetailById(Number(institutionId));
    presenter.getAssessExamineInfos(Number(assessId))
  }, []);
  const onChange = (key: string) => {
    setActiveKey(key);
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  const tableHeader = [
    {
      label: "经办人",
      value:
        state.assessData.operatorDept + "/" + state.assessData.operatorName,
    },
    {
      label: "经办时间",
      value: presenter.formatDate(state.assessData.operatorDate),
    },
  ];

  const tableRows = [
    {
      label: "制度名称",
      value: state.institutionData.title,
      isLabelRed: true,
    },
    {
      label: "是否评估",
      value: (
        <Form.Item
          style={{ marginBottom: 0 }}
        >
          <Radio.Group
            disabled
            value={state.assessData.isAssess}
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "space-around",
            }}
          >
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      ),
      isLabelRed: true,
    },
    {
      label: "评估时间",
      value: state.assessData.assessDate
        ? dayjs(state.assessData.assessDate).format("YYYY-MM-DD")
        : undefined,
      isLabelRed: true,
    },
    {
      label: "评估内容",
      children: [
        {
          label: "内容",
          value: state.assessData.assessContent || "",
        },
        {
          label: "上传附件",
          value: (
            Array.isArray(state.assessData.fileList) && state.assessData.fileList.length > 0 ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {state.assessData.fileList.map((file: any) => (
                  <span
                    key={file.fileKey}
                    style={{ color: '#1890ff', cursor: 'pointer', marginBottom: 4, wordBreak: 'break-all' }}
                    onClick={() => {
                      downloadOssFile(file.fileKey)
                    }}
                  >
                    {file.name}
                  </span>
                ))}
              </div>
            ) : (
              <span style={{ color: '#999' }}>暂无上传文件</span>
            )
          ),
        },
      ],
      isLabelRed: true,
    },
    {
      label: "经办人评估意见",
      value: (
        <Form.Item style={{ marginBottom: 0 }}>
          <Radio.Group
            disabled
            value={state.assessData.assessOpinion}
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "space-around",
            }}
          >
            <Radio value={1}>保留</Radio>
            <Radio value={2}>废止</Radio>
            <Radio value={3}>新制定</Radio>
          </Radio.Group>
        </Form.Item>
      ),
      isLabelRed: true,
    },
    {
      label: "已办意见",
      value: (
        <div>
          {state.examineListData && state.examineListData.length > 0 ? (
            state.examineListData.map((opinion: any, index: number) => (
              <div
                key={opinion.id || index}
                style={{
                  padding: "4px 0",
                  color: "#222",
                  fontSize: "15px",
                  lineHeight: "1.8",
                  wordBreak: "break-all",
                }}
              >
                {opinion.operatorDept}：
                <b style={{ fontWeight: 600 }}>[{opinion.operatorName}]</b>
                {opinion.examineSuggest}
                <span style={{ color: "#888", marginLeft: 8 }}>
                  （{presenter.formatDate(opinion.operatorDate)}）
                </span>
              </div>
            ))
          ) : (
            <div
              style={{
                padding: "10px",
                textAlign: "center",
                color: "#999",
                fontStyle: "italic",
              }}
            >
              暂无已办意见
            </div>
          )}
        </div>
      ),
      isLabelRed: true,
    }
  ];
  return (
    <>
      <div className="twoPopul_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div style={{ marginRight: '30px' }}>
        </div>
      </div>
      <div className="twoPopul_body_box">
        {activeKey == "1" && (
          <CustomSplitter
            leftDefaultSize={825}
            left={
              <div
                style={{
                  transform: `scale(1.0)`,
                  transformOrigin: "top left", // 设置缩放原点为左上角
                  width: "789px",
                  overflow: "auto",
                }}
                dangerouslySetInnerHTML={{
                  __html: state.institutionData?.htmlContent,
                }}
              />
            }
            right={
              <>
                {state.assessData.isAssess === 0 && (
                  <div className="tabel_edit" style={{ height: "20vh" }}>
                    <GenericFormTable
                      header={tableHeader}
                      rows={[...tableRows.slice(0, 2), tableRows.slice(-1)[0]]}
                    />
                  </div>

                )}
                {state.assessData.isAssess === 1 && (
                  <div style={{ height: "20vh" }} className="tabel_edit"> <GenericFormTable header={tableHeader} rows={tableRows} /> </div>

                )}
              </>
            }
          />
        )}
        {activeKey == "2" && <div className="process">
          <FlowImage recordId={state.assessData.recordId} />
        </div>}
      </div>
    </>
  );
};

export default AssessDetail;
