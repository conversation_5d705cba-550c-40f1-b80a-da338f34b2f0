export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AssessWriteState {
    // 表格数据
    institutionData: any;
    assessData: any;
    examineListData: any[];
}

export interface AssessApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAssessDetailByInstitutionId: (params: any) => Promise<any>;
    getAssessExamineInfos: (params: any) => Promise<any>;
}