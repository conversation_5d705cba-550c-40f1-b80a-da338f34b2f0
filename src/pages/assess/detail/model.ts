import { AssessApi } from "./types";
import {
    getInstitutionDetail,
    getAssessInfoDetailById,
    getAssessExamineInfos
} from "@/api/assess/assess";

/**
 * Model层接口实现
 * AssessApiImpl 实现了AssessApi接口，提供了数据的增删改查操作
 */
export class AssessApiImpl implements AssessApi {

    async getInstitutionDetailById(params: any): Promise<any> {
        try {
            const res = await getInstitutionDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }

    async getAssessDetailByInstitutionId(params: any): Promise<any> {
        try {
            const res = await getAssessInfoDetailById(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async getAssessExamineInfos(params: any): Promise<any> {
        try {
            const res = await getAssessExamineInfos(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
}