export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AssessWriteState {
    // 表格数据
    institutionData: any;
    assessData: any;
    examineListData: any[];
    formData: AssessExamineFormData;
    // 切换编辑
    editAble: boolean;
    //提交流程表格
    flowData: AssessFlowForm;
    // 选择用户模态框
    selectUserVisible: boolean;
    // 审核通过
    assessPassVisible: boolean;
    // 驳回审核
    rejectedReviewVisible: boolean;
}

export interface AssessApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAssessDetailByInstitutionId: (params: any) => Promise<any>;
    saveAssessExamine: (params: any) => Promise<{ success: boolean; data?: any }>;
    submitFlow: (params: any) => Promise<any>;
    getRecordIdStatusSum: (params: any) => Promise<any>;
    getExamineInfoByRecordId: (params1: any, params2: any) => Promise<any>;
    getAssessExamineInfos: (params: any) => Promise<any>;
}

// 评估表单数据类型
export interface AssessExamineFormData {
    id?: number;
    assessId?: number;
    operatorDept?: string | null;
    operatorName?: string | null;
    operatorDate?: number;
    examineSuggest?: string;
    recordId?: number;
}
export interface AssessFlowForm {
    recordId?: number;
    currentReviewerId?: number;
    nextReviewerId?: number;
    institutionId?: number;
    institutionName?: String;
    success?: boolean;
    toLeader?: boolean;
    nodeCode?: String;
}