import React from "react";
import { AssessWriteState } from "@/pages/assess/examine/types";
import { AssessPresenter } from "@/pages/assess/examine/presenter";
import { AssessApiImpl } from "@/pages/assess/examine/model";

const initialState: AssessWriteState = {
    // 表格数据
    institutionData: {},
    assessData:{},
    examineListData: [],
    formData: {
    },
    editAble: false,
    flowData: {},
    selectUserVisible: false,
    assessPassVisible: false,
    rejectedReviewVisible: false,
}

export function useWritePresenter() {
    const [state, dispatch] = React.useState<AssessWriteState>(initialState);

    const presenterRef = React.useRef<AssessPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 AssessPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new AssessPresenter(dispatch, new AssessApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
} 