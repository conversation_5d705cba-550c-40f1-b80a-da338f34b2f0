import { AssessA<PERSON>, AssessWriteState, <PERSON><PERSON><PERSON>, AssessExamineFormData } from "@/pages/assess/examine/types";
import { message } from "antd";

export class AssessPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AssessWriteState>;
    private readonly api: AssessApi;
    private state: AssessWriteState;

    public constructor(dispatch: Dispatch<AssessWriteState>, api: AssessApi, state: AssessWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AssessWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };

    // 更新修改状态
    public onEditAbleChange = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editAble: true,
            }
        });
    }

    // 创建评估审核记录
    public createAssessExamine = async (record: AssessExamineFormData) => {
        try {
            await this.api.saveAssessExamine(record);
        } catch (e) {
            console.error("创建失败", e);
        }
    }
    // 获取流程节点的状态
    public getRecordIdStatusSum = async (recordId: number) => {
        try {
            const res = await this.api.getRecordIdStatusSum(recordId);
            if (res > 0) {
                this.dispatch((prevState) => ({
                    ...prevState,
                    editAble: true
                }));
            }
            return res;
        } catch (e) {
            console.error("获取失败", e);
        }
    }

    // 获取当前节点的审核信息
    public getExamineInfoByRecordId = async (recordId: number, abolishId: number) => {
        try {
            const res = await this.api.getExamineInfoByRecordId(recordId, abolishId);
            return res;
        } catch (e) {
            console.error("获取失败", e);
            return null;
        }
    }
    // 获取制度记录详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
            return res;
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 获取评估详情
    public getAssessInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAssessDetailByInstitutionId(id);
            if (res && Object.keys(res).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        flowData: {
                            ...prevState.flowData,
                            assessId: res.id,
                            toLeader: res.isAssess === 1 ? true : false
                        },
                        assessData: res
                    }
                });
            }
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }
     // 获取评估审核记录详情
    public getAssessExamineInfos = async (id: number) => {
        try {
            const res = await this.api.getAssessExamineInfos(id);
            if (res && Object.keys(res?.list).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        examineListData: res?.list
                    }
                });
            }
        } catch (e) {}
    }
    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionData: data,
                flowData: {
                    ...prevState.flowData,
                    institutionName: data.title,
                    institutionId: data.id
                }
            }
        });
    }
    // 初始化时更新flowForm
    public initFlowForm = (recordId: number, currentReviewerId: number, nodeCode: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                flowData: {
                    ...prevState.flowData,
                    recordId: recordId,
                    currentReviewerId: currentReviewerId,
                    nodeCode: nodeCode
                }
            }
        });
    }
    // 提交流程
    public submitAssessFlow = async (record: any) => {
        if (record.length>0) {
             const newFlowData = {
            ...this.state.flowData,
            nextReviewerId: record.split(",")[0],
            success: true
        };
        await this.api.submitFlow(newFlowData);
        }
    }
    // 变更用户选择框状态
    public changeSelectUserVisible = (visible: boolean, nodeNoCode: string) => {
        if (nodeNoCode === "lowDeptAdmin") {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    assessPassVisible: visible,
                }
            });
        } else {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    selectUserVisible: visible,
                }
            });
        }
    };
    // 取消用户使用
    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }
    // 驳回弹窗修改
    public setRejectedReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rejectedReviewVisible: v,
            }
        });
    };
    // 驳回流程
    public rejectAssessFlow = async () => {
        const newFlowData = {
            ...this.state.flowData,
            success: false,
        };
        await this.api.submitFlow(newFlowData);
    }
    // 审核通过
    public assessPassFlow = async () => {
        const newFlowData = {
            ...this.state.flowData,
            success: true,
        };
        await this.api.submitFlow(newFlowData);
    }
    // 审核提交按钮展示
    public getReviewButtonName = (nodeNoCode: String, toLeader: boolean) => {
        if (toLeader) {
            if (!nodeNoCode) {
                return "";
            } else if (nodeNoCode === "deptManager") {
                return "送呈法规部门管理员审核";
            } else if (nodeNoCode === "lowDeptAdmin") {
                return "审核通过";
            }
        } else {
            if (!nodeNoCode) {
                return "";
            } else if (nodeNoCode === "lowDeptAdmin") {
                return "审核通过";
            }
        }
    };
} 