import { Button, Space, Tabs, Row } from "@itm/components-pc";
import React, { useState, useEffect, useRef } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { Message, Modal } from "@itm/components-pc";
import { useWritePresenter } from "@/pages/assess/examine/hooks/usePresenter";
import { AssessExamineFormData } from "@/pages/assess/examine/types";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import GenericFormTable from "@/components/GenericFormTable";
import { Form, Radio, Input } from "antd";
import { downloadOssFile } from "@/utils/download";
import dayjs from "dayjs";
import CustomSplitter from "@/components/CustomSplitter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { FlowImage } from "@/components/flowImage";

const AssessReview: React.FC<NoticeFormProps> = (props) => {
  const customParams = props.data?.customParams
    ? JSON.parse(props.data.customParams)
    : {
      recordId: 0,
      reviewerId: 0,
      assessId: 0,
      institutionId: 0,
      toLeader: false,
      nodeCode: "",
    };
  const { state, presenter } = useWritePresenter();
  // 评估表单数据
  const defaultForm: AssessExamineFormData = {
    assessId: undefined,
    operatorDept: localStorage.getItem("userdeptname"),
    operatorName: localStorage.getItem("usernickname"),
    operatorDate: new Date().getTime(),
    examineSuggest: "",
  };
  const [form, setForm] = useState<AssessExamineFormData>(defaultForm);
  const formRef = useRef(form);

  const [activeKey, setActiveKey] = useState<string>("1");
  const [userIds, setUserIds] = useState<string | null>("");

  useEffect(() => {
    if (customParams.institutionId) {
      presenter.initFlowForm(
        customParams.recordId,
        customParams.reviewerId,
        customParams.nodeCode
      );
      // recordId: 判断
      presenter.getRecordIdStatusSum(customParams.recordId);
      presenter
        .getExamineInfoByRecordId(customParams.recordId, customParams.assessId)
        .then((res) => {
          if (res != null) {
            setForm({
              id: res.id,
              assessId: res.assessId,
              operatorDept: res.operatorDept,
              operatorName: res.operatorName,
              operatorDate: res.operatorDate,
              examineSuggest: res.examineSuggest,
              recordId: res.recordId,
            });
          } else {
            setForm({
              ...form,
              recordId: customParams.recordId,
            });
          }
        });
      // 表格初始化
      presenter.getAssessExamineInfos(customParams.assessId)
      presenter.getAssessInfoDetailById(customParams.assessId);
      presenter.getInstitutionDetailById(customParams.institutionId);
    }
  }, []);

  useEffect(() => {
    setForm((prev) => ({
      ...prev,
      assessId: state.assessData.id,
    }));
  }, [state.assessData?.id]);

  useEffect(() => {
    console.log("state.examineData", form);
    formRef.current = form;
  }, [form]);
  const onChange = (key: string) => {
    setActiveKey(key);
  };

  // 保存评估记录
  const handleSave = async (condition: boolean, userIds: any | null) => {
    try {
      await presenter.createAssessExamine(formRef.current);
      if (condition == true && userIds.length > 0) {
        presenter?.submitAssessFlow(userIds);
        presenter?.changeSelectUserVisible(false, customParams.nodeCode);
        Message.info("提交成功");
        presenter?.onEditAbleChange();
        props.setVisible(false);
      }
    } catch (error) {
      console.error("保存失败:", error);
    }
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  const tableHeader = [
    {
      label: "经办人",
      value:
        state.assessData.operatorDept + "/" + state.assessData.operatorName,
    },
    {
      label: "经办时间",
      value: presenter.formatDate(state.assessData.operatorDate),
    },
  ];

  const tableRows = [
    {
      label: "制度名称",
      value: state.institutionData.title,
      isLabelRed: true,
    },
    {
      label: "是否评估",
      value: (
        <Form.Item
          style={{ marginBottom: 0 }}
        >
          <Radio.Group
            disabled
            value={state.assessData.isAssess}
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "space-around",
            }}
          >
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      ),
      isLabelRed: true,
    },
    {
      label: "评估时间",
      value: state.assessData.assessDate
        ? dayjs(state.assessData.assessDate).format("YYYY-MM-DD")
        : undefined,
      isLabelRed: true,
    },
    {
      label: "评估内容",
      children: [
        {
          label: "内容",
          value: state.assessData.assessContent || "",
        },
        {
          label: "上传附件",
          value: (
            Array.isArray(state.assessData.fileList) && state.assessData.fileList.length > 0 ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {state.assessData.fileList.map((file: any) => (
                  <span
                    key={file.fileKey}
                    style={{ color: '#1890ff', cursor: 'pointer', marginBottom: 4, wordBreak: 'break-all' }}
                    onClick={() => {
                      downloadOssFile(file.fileKey)
                    }}
                  >
                    {file.name}
                  </span>
                ))}
              </div>
            ) : (
              <span style={{ color: '#999' }}>暂无上传文件</span>
            )
          ),
        },
      ],
      isLabelRed: true,
    },
    {
      label: "经办人评估意见",
      value: (
        <Form.Item style={{ marginBottom: 0 }}>
          <Radio.Group
            disabled
            value={state.assessData.assessOpinion}
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "space-around",
            }}
          >
            <Radio value={1}>保留</Radio>
            <Radio value={2}>废止</Radio>
            <Radio value={3}>新制定</Radio>
          </Radio.Group>
        </Form.Item>
      ),
      isLabelRed: true,
    },
    {
      label: "已办意见",
      value: (
        <div>
          {state.examineListData && state.examineListData.length > 0 ? (
            state.examineListData.map((opinion: any, index: number) => (
              <div
                key={opinion.id || index}
                style={{
                  padding: "4px 0",
                  color: "#222",
                  fontSize: "15px",
                  lineHeight: "1.8",
                  wordBreak: "break-all",
                }}
              >
                {opinion.operatorDept}：
                <b style={{ fontWeight: 600 }}>[{opinion.operatorName}]</b>
                {opinion.examineSuggest}
                <span style={{ color: "#888", marginLeft: 8 }}>
                  （{presenter.formatDate(opinion.operatorDate)}）
                </span>
              </div>
            ))
          ) : (
            <div
              style={{
                padding: "10px",
                textAlign: "center",
                color: "#999",
                fontStyle: "italic",
              }}
            >
              暂无已办意见
            </div>
          )}
        </div>
      ),
      isLabelRed: true,
    },
    {
      label: "拟办意见",
      value: (
        <Form.Item style={{ marginBottom: 0 }}>
          <Input.TextArea
            readOnly={state.editAble}
            rows={3}
            placeholder="输入拟办意见"
            value={form.examineSuggest || ""}
            onChange={(e) =>
              setForm((prev) => ({ ...prev, examineSuggest: e.target.value }))
            }
          />
        </Form.Item>
      ),
      isLabelRed: true,
    },
  ];
  return (
    <>
      <div className="twoPopul_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div style={{ marginRight: '30px' }}>
          <Space>
            <Button
              disabled={state.editAble}
              type="primary"
              danger
              onClick={() => {
                if (formRef.current.examineSuggest?.length == 0) {
                  Message.error("请填写拟办意见");
                } else {
                  presenter?.setRejectedReviewVisible(true);
                }
              }}
            >
              驳回
            </Button>
            <Button
              type="primary"
              disabled={state.editAble}
              onClick={() => {
                if (formRef.current.examineSuggest?.length == 0) {
                  Message.error("请填写拟办意见");
                } else {
                  presenter?.changeSelectUserVisible(true, customParams.nodeCode);
                }
              }}
            >
              {presenter?.getReviewButtonName(
                customParams.nodeCode,
                customParams.toLeader
              )}
            </Button>
          </Space>
        </div>
      </div>
      <div className="twoPopul_body_box">
        {activeKey == "1" && (
          <CustomSplitter
            leftDefaultSize={825}
            left={
              <div
                style={{
                  transform: `scale(1.0)`,
                  transformOrigin: "top left", // 设置缩放原点为左上角
                  width: "789px",
                  overflow: "auto",
                }}
                dangerouslySetInnerHTML={{
                  __html: state.institutionData?.htmlContent,
                }}
              />
            }
            right={
              <>
                {state.assessData.isAssess === 0 && (
                  <div className="tabel_edit" style={{ height: "20vh" }}>
                    <GenericFormTable
                      header={tableHeader}
                      rows={[...tableRows.slice(0, 2), ...tableRows.slice(-2)]}
                    />
                  </div>

                )}
                {state.assessData.isAssess === 1 && (
                  <div style={{ height: "20vh" }} className="tabel_edit"> <GenericFormTable header={tableHeader} rows={tableRows} /> </div>

                )}
              </>
            }
          />
        )}
        {activeKey == "2" && <div className="process">
          <FlowImage recordId={customParams.recordId} />
        </div>}
      </div>

      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={state.selectUserVisible}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          presenter?.changeSelectUserVisible(false, customParams.nodeCode);
        }}
        onOk={() => handleSave(true, userIds)}
        onClose={() => {
          presenter?.changeSelectUserVisible(false, customParams.nodeCode);
        }}
      >
        <UserChoiceSelect
          multiple
          onChange={setUserIds}
          value={userIds || ""}
        />
      </Modal>
      <Modal
        open={state.rejectedReviewVisible}
        title="确认驳回"
        onCancel={() => presenter?.setRejectedReviewVisible(false)}
        onOk={() => {
          handleSave(false, userIds);
          presenter?.rejectAssessFlow();
          presenter?.setRejectedReviewVisible(false);
          Message.success("提交成功");
          presenter?.onEditAbleChange();
          props.setVisible(false);
        }}
      />
      <Modal
        open={state.assessPassVisible}
        title="审核通过"
        onCancel={() =>
          presenter?.changeSelectUserVisible(false, customParams.nodeCode)
        }
        onOk={() => {
          handleSave(false, userIds);
          presenter?.assessPassFlow();
          presenter?.changeSelectUserVisible(false, customParams.nodeCode);
          Message.success("提交成功");
          presenter?.onEditAbleChange();
          props.setVisible(false);
        }}
      />
    </>
  );
};

export default AssessReview;
