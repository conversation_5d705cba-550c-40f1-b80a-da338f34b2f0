import { AssessA<PERSON> } from "./types";
import {
    getInstitutionDetail,
    saveExamineInfo,
    getAssessInfoDetailById,
    submitFlow,
    countRecordIdStatus,
    getAssessExamineInfoByRecordId,
    getAssessExamineInfos
} from "@/api/assess/assess";

/**
 * Model层接口实现
 * AssessApiImpl 实现了AssessApi接口，提供了数据的增删改查操作
 */
export class AssessApiImpl implements AssessApi {

    async getInstitutionDetailById(params: any): Promise<any> {
        try {
            const res = await getInstitutionDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }

    async saveAssessExamine(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            await saveExamineInfo(params);
            return { success: true };
        } catch (err) {
            return { success: false };
        }
    }

    async getAssessDetailByInstitutionId(params: any): Promise<any> {
        try {
            const res = await getAssessInfoDetailById(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async submitFlow(params: any): Promise<any> {
        try {
            await submitFlow(params);
        } catch (err) {
            throw err;
        }
    }
    async getRecordIdStatusSum(params: any): Promise<any> {
        try {
            const res = await countRecordIdStatus(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }


    async getExamineInfoByRecordId(params1: any, params2: any): Promise<any> {
        try {
            const res = await getAssessExamineInfoByRecordId(params1, params2);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
     async getAssessExamineInfos(params: any): Promise<any> {
            try {
                const res = await getAssessExamineInfos(params);
                return res.data;
            } catch (err) {
                throw err;
            }
        }
}