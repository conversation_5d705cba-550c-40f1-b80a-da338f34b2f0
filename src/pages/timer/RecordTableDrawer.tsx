import React from "react";
import {TimerPresenter} from "@/pages/timer/presenter/TimerPresenter";
import {Drawer, List} from "antd";
import {Modal, ProTable} from "@itm/components-pc";
import {ActionType, ProColumns} from "@ant-design/pro-components";
import {TimerConfigState} from "@/pages/timer/types";

export interface TimerConfigTableProps {
    state: TimerConfigState;
    presenter: TimerPresenter;

}

const TimerTaskRecordTable: React.FC<TimerConfigTableProps> = ({state, presenter}) => {

    const actionRef = React.useRef<ActionType>();

    React.useEffect(() => {

        if (state.currentEditConfig) {
            actionRef.current?.reload();
        }

    }, [state.currentEditConfig]);

    const logsModal = React.useMemo(() => {
        return (<Modal
            data-testid={"logs-table"}
            title="日志"
            open={state.logsModalVisible}
            onCancel={presenter.hideLogsModal}
            onClose={presenter.hideLogsModal}
            destroyOnHidden={true}
            footer={false}
        >
            <List
                dataSource={state.logs}
                split={false}
                renderItem={(item) => (
                    <List.Item>
                        <span
                            style={{color: item.logLevel === 'WARN' ? 'orange' : item.logLevel === 'ERROR' ? 'red' : 'inherit'}}>
                        {'[' + item.logLevel + ']' + item.logTime + ":" + item.content}
                        </span>
                    </List.Item>
                )}
            />
        </Modal>)
    }, [state.logsModalVisible, state.logs]);

    const columns: ProColumns[] = [
        {
            title: '执行时间',
            dataIndex: 'startTime',
            search: false,
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            search: false,
        },
        {
            title: '状态',
            dataIndex: 'status',
            search: false,
            fieldProps: {
                mode: 'multiple', // 支持多选
            },
            valueEnum: {
                running: {text: '执行中'},
                finished: {text: '成功'},
                failed: {text: '失败'},
            },
            valueType: 'select',
            render: (text: any, record: any) => {
                if (record.status === 'failed') {
                    return <span style={{color: 'red'}}>{text}</span>
                } else if (record.status === 'running') {
                    return <span style={{color: 'blue'}}>{text}</span>
                } else {
                    return <span style={{color: 'green'}}>{text}</span>
                }
            }
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => {
                return [
                    <a
                        key="view"
                        onClick={() => {
                            presenter.showLogsModal(record.id);
                        }}
                    >查看执行日志</a>,
                ]
            }
        }
    ]

    return (<Drawer
        open={state.recordsDrawerVisible}
        title="执行记录"
        width={800}

        closable={{'aria-label': 'Close Button'}}
        onClose={() => presenter.hideRecordsDrawer()}
        footer={false}
    >
        <ProTable
            data-testid={"task-record-table"}
            rowKey={"id"}
            actionRef={actionRef}
            columns={columns}
            search={false}
            toolBarRender={() => {
                return []
            }}
            request={async (params, sort, filter) => {

                return presenter.searchRecordTable({...params, configId: state.currentEditConfig?.id}, sort, filter, [
                    {
                        key: "recordId",
                        type: "EQUAL"
                    },

                ]);
            }}
        />
        {logsModal}
    </Drawer>)
}

export default TimerTaskRecordTable;