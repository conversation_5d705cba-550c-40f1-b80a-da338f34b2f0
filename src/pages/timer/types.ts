export interface TimerConfigState {
    editorVisible: boolean;
    tableRefreshVersion: number;
    currentEditConfig: any;
    recordsDrawerVisible: boolean;
    logsModalVisible: boolean;
    recordId?: any;
    logs?: any[],
}

export interface TimerApi {

    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
    delete: (id: any) => Promise<any>;

    recordPage: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    getLogsByRecordId: (recordId: any) => Promise<any>;

}