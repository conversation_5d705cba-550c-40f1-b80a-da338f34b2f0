import React from "react";
import {ActionType, ProColumns} from "@ant-design/pro-components";
import {Button, Popconfirm, ProTable} from "@itm/components-pc";
import {message, Switch} from "antd";
import {TimerPresenter} from "@/pages/timer/presenter/TimerPresenter";

export interface TimerConfigTableProps {

    actionRef: React.Ref<ActionType | undefined>;
    presenter: TimerPresenter;

}

const TimerConfigTable: React.FC<TimerConfigTableProps> = ({actionRef, presenter}) => {

    const columns: ProColumns[] = [
        {
            title: '名称',
            dataIndex: 'name',
        },
        {
            title: '编码',
            dataIndex: 'code',
        },
        {
            title: 'cron表达式',
            dataIndex: 'cron',
            search: false,
        },
        {
            title: '服务名',
            dataIndex: 'serviceName',
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.enabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.enabled) {
                            presenter.disableTimer(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enableTimer(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.enabled} />
                </Popconfirm>

            ]
        },
        {
          title:"执行记录",
          render: (_: any, record: any) => [
              <a
                  key="log"
                  onClick={() => {
                      presenter.showRecordsDrawer(record)
                  }}
              >
                  查看执行记录
              </a>
          ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="deleteConfirm"
                    title={"确认要删除吗？"}
                    onConfirm={() => {
                        presenter.deleteTimer(record.id).then(() => message.success("删除成功"));
                    }}
                             >
                <a
                    key="delete"
                >
                    删除
                </a>
                </Popconfirm>,
            ]
        }
    ]

    return (
        <ProTable
            data-testid={"user-table"}
            rowKey={"id"}
            actionRef={actionRef}
            columns={columns}
            toolBarRender={() => {
                return [
                    <Button
                        data-testid={"flow-add-btn"}
                        type={"primary"}
                        onClick={() => {
                            presenter.showEditModal(null);
                        }}
                    >新增</Button>
                ]
            }}
            request={async (params, sort, filter) => {

                return presenter.searchConfigTable({...params}, sort, filter, [
                    {
                        key: "name",
                        type: "LIKE"
                    },
                    {
                        key: "code",
                        type: "LIKE"
                    },
                    {
                        key: "serviceName",
                        type: "LIKE"
                    },
                ]);
            }}
        />
    );

};

export default TimerConfigTable;