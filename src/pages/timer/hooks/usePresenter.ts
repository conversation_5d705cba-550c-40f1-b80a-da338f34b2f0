
import {useEffect, useRef, useState} from "react";
import {TimerConfigState} from "@/pages/timer/types";
import {TimerPresenter} from "@/pages/timer/presenter/TimerPresenter";
import {TimerApiImpl} from "@/pages/timer/model/TimerModel";

const initialState: TimerConfigState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    currentEditConfig: null,
    recordsDrawerVisible: false,
    logsModalVisible: false,

};

export function useTimerPresenter() {

    const [state, dispatch] = useState<TimerConfigState>(initialState);
    const presenterRef = useRef<TimerPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new TimerPresenter(state, new TimerApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}