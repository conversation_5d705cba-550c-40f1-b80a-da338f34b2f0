import {Timer<PERSON>pi} from "@/pages/timer/types";
import {
    deleteTimerConfig,
    disableTimerConfig,
    enableTimerConfig,
    queryTimerConfigPage,
    queryTimerTaskLogsByRecordId,
    queryTimerTaskRecordPage,
    saveTimerConfig
} from "@/api/timer/timer";

export class TimerApiImpl implements TimerApi {

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryTimerConfigPage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveTimerConfig(params);
    }

    disable(id: any): any {
        return disableTimerConfig(id);
    }

    enable(id: any): any {
        return enableTimerConfig(id);
    }

    delete(id: any): any {
        return deleteTimerConfig(id);
    }

    recordPage(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryTimerTaskRecordPage(params, sort, filter, match);
    }

    getLogsByRecordId(recordId: any): Promise<any> {
        return queryTimerTaskLogsByRecordId(recordId);
    }


}