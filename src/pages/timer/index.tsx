import React from "react";
import {ActionType, PageContainer} from "@ant-design/pro-components";
import {Form} from "@/components/form-pc";
import {Modal, Row} from "@itm/components-pc";
import {useTimerPresenter} from "@/pages/timer/hooks/usePresenter";
import TimerConfigTable from "@/pages/timer/ConfigTable";
import TimerConfigForm from "@/pages/timer/ConfigForm";
import RecordTableDrawer from "@/pages/timer/RecordTableDrawer";

const TimerManagePage = () => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();

    const {state, presenter} = useTimerPresenter();

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    React.useEffect(() => {

        if (state.currentEditConfig) {
            form.setFieldsValue(state.currentEditConfig)
        } else {
            form.reset();
        }

    }, [state.currentEditConfig])

    return (
        <PageContainer>
            <Row>
                <Row.Col span={24}>
                    <TimerConfigTable actionRef={actionRef} presenter={presenter}/>
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑定时器配置"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter?.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter?.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <TimerConfigForm form={form} state={state} presenter={presenter}/>
                    </Modal>
                </Row.Col>
            </Row>
            <RecordTableDrawer state={state} presenter={presenter} />
        </PageContainer>
    )
}


export default TimerManagePage;