import {Dispatch} from "@/utils/mvvm";
import {TimerApi, TimerConfigState} from "@/pages/timer/types";


export class TimerPresenter {

    private readonly dispatch: Dispatch<TimerConfigState>;

    private readonly api: TimerApi;

    private state: TimerConfigState;

    public constructor(state: TimerConfigState, api: TimerApi, dispatch: Dispatch<TimerConfigState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: TimerConfigState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public showEditModal = (config: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditConfig: config
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditConfig: null
            }
        });
    }

    public showRecordsDrawer = (config: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recordsDrawerVisible: true,
                currentEditConfig: config
            }
        });
    }

    public hideRecordsDrawer = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recordsDrawerVisible: false,
                currentEditConfig: null
            }
        });
    }

    public showLogsModal = (recordId: any) => {

        this.api.getLogsByRecordId(recordId).then(res => {
            if (res.success) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        logsModalVisible: true,
                        recordId: recordId,
                        logs: res.data
                    }
                });
            }
        }).catch(e => console.log(e));

    }

    public hideLogsModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                logsModalVisible: false,
                recordId: null,
                logs: []
            }
        });
    }

    public saveTimer = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public enableTimer = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableTimer = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public deleteTimer = async (id: any) => {
        const res = await this.api.delete(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchConfigTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }

    public searchRecordTable = (params: any, sort: any, filter: any, match: any[]) => {
        if (params.status) {
            // 如果是数组，用逗号分隔
            params.status = Array.isArray(params.status) ? params.status.join(",") : params.status;
        }
        return this.api.recordPage(params, sort, filter, match);
    }

}