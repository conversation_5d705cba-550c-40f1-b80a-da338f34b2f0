import React from "react";
import {TimerPresenter} from "@/pages/timer/presenter/TimerPresenter";
import {FormInstance, ValidateUtils} from "@codingapi/ui-framework";
import {TimerConfigState} from "@/pages/timer/types";
import {Form, FormInput, FormSwitch} from "@/components/form-pc";
import {message} from "antd";


export interface TimerConfigFormProps {
    form: FormInstance;
    presenter: TimerPresenter;
    state: TimerConfigState;

}

const TimerConfigForm: React.FC<TimerConfigFormProps> = ({form, state, presenter}) => {

    return (
        <Form
            form={form}
            layout={"vertical"}
            onFinish={(values) => presenter.saveTimer(values).then(() => {
                message.success("保存成功")
            }).catch(e => console.log(e))}
        >
            <FormInput
                name={"id"}
                hidden={true}
            />

            <FormInput
                name={"name"}
                label={"名称"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            <FormInput
                name={"code"}
                label={"编码"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            <FormInput
                name={"cron"}
                label={"cron表达式"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            <FormInput
                name={"serviceName"}
                label={"服务名"}
                tooltip={"服务发现上注册的各服务名称，如：itm-user"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            <FormInput
                name={"extra"}
                label={"额外参数"}
                tooltip={"字符串格式，在运行时可在TaskInfo的params字段中获取"}
            />

            <FormInput
                name={"maxRetryTimes"}
                label={"最大重试次数"}
                tooltip={"当执行出现异常或业务逻辑中判断失败后进行重试，为0则不重试，默认为0"}
                inputType={"number"}
                validateFunction={async (content) => {

                    if (content.value < 0) {
                        return ["最大重试次数不能小于0"];
                    }

                    return [];
                }}
            />

            <FormSwitch
                name={"enabled"}
                label={"是否启用"}
            />

        </Form>
    );


};

export default TimerConfigForm;