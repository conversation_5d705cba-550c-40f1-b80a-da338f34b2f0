import React, {useMemo} from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {Col, Row} from "antd";
import {useValuePresenter} from "@/pages/supervise/numberMaintenanceInfo/hooks/usePresenter";
import {Button, ProTable} from "@/components-pc";
import {Form, FormSelect} from "@/form-pc";
import {useLocation} from "react-router-dom";

interface LocationState {
    selectedRow: {
        orgCode: string;
    };
    status: string;
}

interface SupplierManagePageProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, name: string) => void;
}

const SupplierManagePage: React.FC<SupplierManagePageProps> = (props) => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();
    const {state, presenter} = useValuePresenter();

    const location = useLocation() as { state: LocationState };

    const orgCode = location.state?.selectedRow?.orgCode;
    const status = location.state?.status;

    React.useEffect(() => {
        console.log(status)
    }, [props.isComponent]);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    const columns: ProColumns<any>[] = [
        {
            title: "序号",
            search: false,
            width: 30,
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "单位名称",
            dataIndex: "orgName",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "检查证号",
            dataIndex: "certificateNumber",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "使用情况",
            dataIndex: "status",
            hidden: props.isComponent,
            // 渲染表格中显示的文本（将状态值转换为中文显示）
            render: (text) => {
                // 定义状态映射关系
                const statusMap = {
                    "1": "未启用",
                    "2": "未使用",
                    "3": "申请中",
                    "4": "使用中",
                    "5": "作废"
                };
                return statusMap[text] || '-';
            },
            // 搜索配置
            search: !props.isComponent ? {
                transform: (value) => ({status: value})
            } : false,
            // 自定义筛选表单项，使用下拉选择器
            renderFormItem: (schema, config) => {
                // 状态选项列表
                const statusOptions = [
                    {label: "未启用", value: "1"},
                    {label: "未使用", value: "2"},
                    {label: "申请中", value: "3"},
                    {label: "使用中", value: "4"},
                    {label: "作废", value: "5"}
                ];
                return (
                    <FormSelect
                        {...config}
                        name="status"
                        options={[
                            {label: '', value: ''},
                            ...statusOptions
                        ]}
                        placeholder="使用情况"
                    />
                );
            }
        },
        {
            title: "使用人",
            dataIndex: "userName",
            search: true,
            hidden: props.isComponent,
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            hidden: props.isComponent,
        },
    ];

    return (

        <PageContainer>
            <Row>
                <Col span={24}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="号段详情列表"
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                !props.isComponent && (
                                    <Button
                                        key="upload"
                                        type="primary"
                                        onClick={() => {
                                            presenter?.export();
                                        }}
                                    >
                                        导出
                                    </Button>
                                ),
                            ].filter(Boolean)
                        }}
                        request={async (params, sort, filter) => {
                            const queryParams = {
                                ...params,
                                orgCode: orgCode || null,
                                status: status || null,
                            };
                            return presenter?.searchTable(queryParams, sort, filter);
                        }}
                    />
                </Col>
            </Row>


        </PageContainer>
    );
};

export default SupplierManagePage;
