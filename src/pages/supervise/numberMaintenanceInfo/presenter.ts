import {SupplierApi, SupplierState, Dispatch} from "@/pages/supervise/numberMaintenanceInfo/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<SupplierState>;

    private readonly api: SupplierApi;

    private state: SupplierState;

    public constructor(dispatch: Dispatch<SupplierState>, api: SupplierApi, state: SupplierState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: SupplierState) => {
        this.state = newStatus;
    }

    // 刷新表格数据
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1,
                searchParams: null,
            }
        })
    }

    // 标签表格数据加载函数
    public searchTable = (params: any, sort: any, filter: any) => {
        const {current, pageSize, ...restParams} = params;

        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchParams: restParams,
            };
        });
        return this.api.loadTable(params, sort, filter);
    }


    public export = () => {
        return this.api.exportList(this.state.searchParams);
    }


}
