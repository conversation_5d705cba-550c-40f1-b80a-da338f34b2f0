import {SupplierApi, SupplierState, Dispatch} from "@/pages/supervise/numberMaintenance/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<SupplierState>;

    private readonly api: SupplierApi;

    private state: SupplierState;

    public constructor(dispatch: Dispatch<SupplierState>, api: SupplierApi, state: SupplierState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: SupplierState) => {
        this.state = newStatus;
    }

    // 刷新表格数据
    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1,
                searchParams: null,
            }
        })
    }

    // 删除数据
    public delete = async (record: SupplierState) => {
        try {
            const res = await this.api.delete(record);
            if (res.success) {
                this.refreshTable();
            }
        } catch (e) {
            console.error("删除失败", e);
        }
    }

    // 标签表格数据加载函数
    public searchTable = (params: any, sort: any, filter: any) => {
        const {current, pageSize, ...restParams} = params;

        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchParams: restParams,
            };
        });
        return this.api.loadTable(params, sort, filter);
    }

    public getInfo = async (id: number) => {
        const params = {
            id
        }
        const res = await this.api.getInfo(params);
        if (res.success) {
            return res.data;
        }
    }


    public setAddVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                addVisible: v
            }
        })
    }

    public add = async (fieldsValue: any) => {
        const params = fieldsValue.supplier
        await this.api.addOrUpdate(params);
        this.setAddVisible(false)
        this.refreshTable();
    };


    // 删除数据
    public init = async (record: SupplierState) => {
        try {
            const res = await this.api.init(record);
            if (res.success) {
                this.refreshTable();
            }
        } catch (e) {
            console.error("初始化失败", e);
        }
    }



}
