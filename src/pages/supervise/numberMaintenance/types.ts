export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface SupplierState {
    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新
    searchParams: any;
    addVisible: boolean;
}


export interface SupplierApi {

    loadTable: (params: any, sort: any, filter: any) => Promise<any>;

    getInfo: (params: any) => Promise<any>;

    delete: (params: any) => Promise<{ success: boolean }>;

    addOrUpdate: (formData: FormData) => Promise<any>;

    init: (params: any) => Promise<{ success: boolean }>;


}
