import React, {useMemo} from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {Col, Popconfirm, Row} from "antd";
import {useValuePresenter} from "@/pages/supervise/numberMaintenance/hooks/usePresenter";
import {Button, Modal, ProTable} from "@/components-pc";
import {Form, FormInput, FormTextArea} from "@/form-pc";
import OrgTree from "@/components/user/org/OrgTree";
import {useNavigate} from "react-router-dom";

interface SupplierManagePageProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, name: string) => void;
}

const SupplierManagePage: React.FC<SupplierManagePageProps> = (props) => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();
    const {state, presenter} = useValuePresenter();

    const navigate = useNavigate();

    React.useEffect(() => {


    }, [props.isComponent]);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    const columns: ProColumns<any>[] = [
        {
            title: "序号",
            search: false,
            width: 100,
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "单位名称",
            dataIndex: "orgName",
            search: true,
            hidden: props.isComponent,
        },
        {
            title: "起始编号",
            dataIndex: "startNumber",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "结束编号",
            dataIndex: "endNumber",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "号段总个数",
            dataIndex: "numberCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                            selectedRow: record,
                        }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "未启用个数",
            dataIndex: "notEnabledCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                            selectedRow: record,
                            status: 1,
                        }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "未使用个数",
            dataIndex: "unUsedCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                                selectedRow: record,
                                status: 2,
                            }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "申请中个数",
            dataIndex: "applyingCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                                selectedRow: record,
                                status: 3,
                            }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "使用中个数",
            dataIndex: "usedCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                                selectedRow: record,
                                status: 4,
                            }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "作废个数",
            dataIndex: "invalidCount",
            search: false,
            hidden: props.isComponent,
            render: (text, record) => (
                <a
                    key="publish"
                    onClick={() => {
                        navigate(`/supervise/numberMaintenanceInfo`, {state: {
                                selectedRow: record,
                                status: 5,
                            }});
                    }}
                >
                    {text}
                </a>
            )
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            hidden: props.isComponent,
            render: (_, record) => [
                <a
                    key="edit"
                    onClick={async () => {
                        try {
                            const detail = await presenter?.getInfo(record.id);
                            if (detail) {
                                form.reset();
                                form.setFieldsValue({supplier: detail});
                                presenter?.setAddVisible(true); // 打开新增/编辑弹窗
                            }
                        } catch (e) {
                            console.error("加载详情失败", e);
                        }
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title="提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={() => {
                        presenter?.delete(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,

                <Popconfirm
                    key="delete"
                    title="提示"
                    description="确认要初始化吗?"
                    onConfirm={() => {
                        presenter?.init(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        初始化
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    return (

        <PageContainer>
            <Row>
                <Col span={24}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="号段列表"
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                !props.isComponent && ( // 添加条件判断
                                    <Button
                                        key="upload"
                                        type="primary"
                                        onClick={() => {
                                            form.reset();
                                            presenter?.setAddVisible(true)
                                        }}
                                    >
                                        新增
                                    </Button>
                                ),
                            ].filter(Boolean)
                        }}
                        request={async (params, sort, filter) => {
                            return presenter?.searchTable(params, sort, filter);
                        }}
                    />
                </Col>
            </Row>


            <Modal
                width={1000}
                centered={true}
                open={state.addVisible}
                title={form.getFieldValue(['supplier', 'id']) ? "编辑" : "新增"}
                onCancel={() => {
                    presenter?.setAddVisible(false);
                }}
                onOk={async () => {
                    if (!await form.validate()) {
                        return "";
                    }
                    await form.submit();
                    presenter?.add(form.getFieldsValue());
                    form.reset();
                }}
            >

                <Form
                    form={form}
                    layout={"vertical"}
                >
                    <FormInput
                        name={["supplier", "id"]}
                        hidden={true}
                    />
                    <FormInput
                        required={true}
                        disabled={!!form.getFieldValue(['supplier', 'id'])}
                        name={["supplier", "orgCode"]}
                        label={"组织机构"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return []
                            }
                            return ['组织机构不能为空']
                        }}
                    />
                    <FormInput
                        required={true}
                        name={["supplier", "startNumber"]}
                        disabled={!!form.getFieldValue(['supplier', 'id'])}
                        label={"起始编码"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return []
                            }
                            return ['起始编码不能为空']
                        }}
                    />
                    <FormInput
                        required={true}
                        name={["supplier", "endNumber"]}
                        label={"结束编码"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value) {
                                return []
                            }
                            return ['结束编码不能为空']
                        }}
                    />
                    <FormTextArea
                        required={false}
                        name={["supplier", "remark"]}
                        label={"备注"}
                    />

                </Form>

            </Modal>


        </PageContainer>
    );
};

export default SupplierManagePage;
