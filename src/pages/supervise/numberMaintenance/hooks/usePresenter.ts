import React from "react";
import {SupplierState} from "@/pages/supervise/numberMaintenance/types";
import {ValuePresenter} from "@/pages/supervise/numberMaintenance/presenter";
import {SupplierApiImpl} from "@/pages/supervise/numberMaintenance/model";

const initialState: SupplierState = {
    tableRefreshVersion: 0, // 表格刷新版本号，用于表格刷新
    searchParams: {},
    addVisible: false,
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<SupplierState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new SupplierApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}
