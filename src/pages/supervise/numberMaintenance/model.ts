import {SupplierApi} from "./types";
import {
    list,
    del,
    addOrUpdate,
    getInfo,
    init
} from "@/api/supervise/numberMaintenance";


export class SupplierApiImpl implements SupplierApi {

    loadTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    getInfo(params: any): Promise<any> {
        return getInfo(params);
    }

    addOrUpdate(formData: FormData): Promise<any> {
        return addOrUpdate(formData);
    }

    async delete(params: any): Promise<{ success: boolean }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }


    async init(params: any): Promise<{ success: boolean }> {
        try {
            const res = await init(params);
            return {success: true};
        } catch (err) {
            return {success: false};
        }
    }

}
