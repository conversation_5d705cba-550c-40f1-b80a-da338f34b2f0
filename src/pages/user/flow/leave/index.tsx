import React, {useEffect, useState} from "react";
import {ActionType, PageContainer, ProTable} from "@ant-design/pro-components";
import {list} from "@/api/user/leave";
import {Button} from "antd";
import LeaveForm from "@/pages/user/flow/leave/LeaveForm";
import {FlowModelView} from "@/components/flow-pc";
import {getSelfDetail} from "@/api/user/self";

const LeavePage = () => {

    const [visible, setVisible] = React.useState(false);

    const [selfInfo, setSelfInfo] = useState<any>();

    const username = localStorage.getItem('username');

    useEffect(() => {
        getSelfDetail().then(res => {
            setSelfInfo(res.data);
        })
    }, []);

    const actionRef = React.useRef<ActionType>();

    useEffect(() => {
        actionRef.current?.reload();
    }, [visible]);

    const columns = [
        {
            title: '编号',
            dataIndex: 'id',
            search: false
        },
        {
            title: '说明',
            dataIndex: 'desc',
        },
        {
            title: '请假天数',
            dataIndex: 'days',
        },
        {
            title: '请假人',
            dataIndex: 'username',
        },
        {
            title: '状态',
            dataIndex: 'status',
        }
    ] as any[];


    return (
        <PageContainer>
            <ProTable
                actionRef={actionRef}
                toolBarRender={() => [
                    <Button
                        type={"primary"}
                        onClick={() => {
                            setVisible(true);
                        }}
                    >发起请假</Button>
                ]}
                columns={columns}
                search={false}
                rowKey={"id"}
                request={async (params, sort, filter) => {
                    return list(params, sort, filter, []);
                }}
            />

            <FlowModelView
                visible={visible}
                setVisible={setVisible}
                view={LeaveForm}
                workCode={"leave"}
                formParams={{
                    clazzName: 'com.codingapi.itm.user.infra.form.LeaveForm',
                    flowBindData: {
                        username: username,
                        stepOperators: {
                            start: [
                                {
                                    userId: selfInfo?.id,
                                    name: selfInfo?.name,
                                    fixed: false,
                                }
                            ]
                        }
                    }
                }}
            />

        </PageContainer>
    )
}

export default LeavePage;
