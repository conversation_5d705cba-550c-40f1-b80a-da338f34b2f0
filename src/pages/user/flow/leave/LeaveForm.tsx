import React, {useContext, useEffect} from "react";
import {FlowFormViewProps, ValidateUtils} from "@codingapi/ui-framework";
import {Form, FormInput, FormTextArea} from "@/components/form-pc";
import {FlowViewReactContext} from "@/components/flow-pc";

const LeaveForm: React.FC<FlowFormViewProps> = (props) => {

    useEffect(() => {
        if (props.dataVersion && props.data) {
            props.form?.setFieldsValue({
                ...props.data
            });
        }
    }, [props.dataVersion]);

    const flowViewContext = useContext(FlowViewReactContext);

    return (
        <Form
            form={props.form}
            layout={"vertical"}
        >

            <FormInput
                name={["flowBindData", "id"]}
                hidden={true}
            />

            <FormInput
                name={["flowBindData", "username"]}
                hidden={true}
            />

            <FormInput
                name={["flowBindData", "days"]}
                label={"请假天数"}
                inputType={"number"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            <FormTextArea
                name={["flowBindData", "desc"]}
                label={"请假原因"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
            />

            {/*<FormStepOperatorsSelect*/}
            {/*    name={["flowBindData", "stepOperators"]}*/}
            {/*    workCode={flowViewContext?.flowRecordContext?.getWorkCode()}*/}
            {/*    recordId={flowViewContext?.flowRecordContext?.getCurrentFlowRecord()?.id}*/}
            {/*    required={true}*/}
            {/*    notEditableNodes={["start", "over", flowViewContext?.flowRecordContext.getNodeCode()]}*/}
            {/*    defaultTips={<Space style={{paddingBottom: "4px", "color": "grey"}}>*/}
            {/*        <InfoCircleFilled style={{color: "var(--primary-color)"}}/>请选择后续节点审批人员</Space>}*/}
            {/*/>*/}

        </Form>
    )
}

export default LeaveForm;
