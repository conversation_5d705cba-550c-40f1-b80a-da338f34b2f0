import {
    disableUser,
    enableUser,
    findRoleIdsByUserId,
    queryUserPage,
    resetUserPassword,
    saveUser,
    saveUserRoleRelations
} from "@/api/user/user";
import {UserApi} from "@/pages/user/user/type";
import {queryLineList} from "@/api/user/line";
import {queryPostList} from "@/api/user/post";


export class UserApiImpl implements UserApi {
    disable(id: any): any {
        return disableUser(id);
    }

    enable(id: any): any {
        return enableUser(id);
    }

    resetPwd(id: any): any {
        return resetUserPassword(id)
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryUserPage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveUser(params);
    }

    queryAllLine(): Promise<any> {
        return queryLineList();
    }

    queryPostByDeptNodeId(deptNodeId: any): Promise<any> {
        return queryPostList(deptNodeId);
    }

    findRelatedRoleIds(id: any): Promise<any> {
        return findRoleIdsByUserId(id);
    }

    saveUserRoles(userId: any, roleIds: any[]): Promise<any> {
        return saveUserRoleRelations(userId, roleIds);
    }

}