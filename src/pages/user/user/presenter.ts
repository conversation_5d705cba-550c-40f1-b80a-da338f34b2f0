import {UserApi, UserState} from "@/pages/user/user/type";
import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/org";
import {FormOption} from "@codingapi/ui-framework";

export default class UserPresenter {
    private readonly api: UserApi;
    private readonly dispatch: Dispatch<UserState>;
    private state: UserState;

    public constructor(state: UserState, api: UserApi, dispatch: Dispatch<UserState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: UserState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public updateCurrentOrg = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public showEditModal = (record: any) => {

        if (record == null) {
            const currentTreeNode = this.state.currentTreeNode;
            const unitNodeId = currentTreeNode?.type == "UNIT" ? currentTreeNode.nodeId : currentTreeNode?.unitNodeId
            record = {
                deptNodeId: currentTreeNode?.type == "DEPT" ? currentTreeNode.nodeId : null,
                unitNodeId: unitNodeId
            }
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public showSelectRoleModal = async (id: number) => {
        const res = await this.api.findRelatedRoleIds(id);

        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    selectRoleVisible: true,
                    currentEditUserId: id,
                    selectedRoleIds: res.data
                }
            });
        } else {
            throw new Error(res.message)
        }

    }

    public hideSelectRoleModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectRoleVisible: false,
                currentEditUserId: null,
                selectedRoleIds: []
            }
        });
    }

    public updateSelectedRole = (selectedKeys: any[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedRoleIds: selectedKeys
            }
        });
    }

    public saveUserRoles = async () => {

        const res = await this.api.saveUserRoles(this.state.currentEditUserId, this.state.selectedRoleIds);
        if (res.success) {
            this.hideSelectRoleModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public saveUser = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public enableUser = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableUser = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public resetPwd = async (id: any) => {
        const res = await this.api.resetPwd(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public queryPostFormSelect = async (deptNodeId: any): Promise<FormOption[]> => {
        const res = await this.api.queryPostByDeptNodeId(deptNodeId)
        const treeDataNode: FormOption[] = [];
        res.data.forEach((item: any) => {
            const treeNode = {
                value: item.id,
                label: item.name,
                disabled: !item.sysEnabled
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    public queryLineFormSelect = async (): Promise<FormOption[]> => {
        const res = await this.api.queryAllLine()
        const treeDataNode: FormOption[] = [];
        res.data.forEach((item: any) => {
            const treeNode = {
                value: item.id,
                label: item.name,
                disabled: !item.sysEnabled
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }
}