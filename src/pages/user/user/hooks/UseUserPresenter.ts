import {UserState} from "@/pages/user/user/type";
import {useEffect, useRef, useState} from "react";
import UserPresenter from "@/pages/user/user/presenter";
import {UserApiImpl} from "@/pages/user/user/model";


const initialState: UserState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    currentEditRecord: null,
    currentTreeNode: null,
    currentEditUserId: null,
    selectRoleVisible: false,
    selectedRoleIds: [],
}

export function useUserPresenter() {

    const [state, dispatch] = useState<UserState>(initialState);
    const presenterRef = useRef<UserPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new UserPresenter(state, new UserApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}