import {TreeData} from "@/api/user/org";

export interface UserState {
    editorVisible: boolean;
    tableRefreshVersion: number;
    currentEditRecord: any;
    currentTreeNode: TreeData | null;
    currentEditUserId: any;
    selectRoleVisible: boolean;
    selectedRoleIds: (string | number)[];

}

export interface UserApi {
    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
    resetPwd: (id: any) => Promise<any>;

    queryPostByDeptNodeId: (deptNodeId: any) => Promise<any>;
    queryAllLine: () => Promise<any>;
    findRelatedRoleIds: (id: any) => Promise<any>;
    saveUserRoles: (userId: any, roleIds: any[]) => Promise<any>;
}