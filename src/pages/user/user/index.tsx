import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {message, Switch,} from "antd";
import React, {useEffect, useMemo} from "react";
import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import {ValidateUtils,} from "@codingapi/ui-framework";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import {useUserPresenter} from "@/pages/user/user/hooks/UseUserPresenter";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import RoleList from "@/components/user/role/RoleList";
import {Form, FormInput, FormRadio, FormSelect, FormUploader} from "@/components/form-pc";

const UserManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useUserPresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);


    const columns: ProColumns[] = [
        {
            title: '姓名',
            dataIndex: 'name',
        },
        {
            title: '账号',
            dataIndex: 'account',
        },
        {
            title: '员工编号',
            dataIndex: 'userNo',
        },
        {
            title: '岗位',
            dataIndex: 'postName',
            search: false
        },
        {
            title: '部门下顺序号',
            dataIndex: 'sysIndex',
            search: false
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.sysEnabled) {
                            presenter.disableUser(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enableUser(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
                </Popconfirm>
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
                <a
                    key="roles"
                    onClick={() => {
                        presenter?.showSelectRoleModal(record.id)
                    }}
                >
                    设置角色
                </a>,
                <Popconfirm
                    key="resetPwd"
                    title={"确认重置?"}
                    onConfirm={async () => {
                        presenter.resetPwd(record.id).then(() => message.success("重置成功"))
                            .catch(e => {
                                message.error(e.message)
                            })
                    }}
                >
                    <a
                        key="resetPwd"
                    >
                        重置密码
                    </a>
                </Popconfirm>,
            ]
        }
    ]

    const treeSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateCurrentOrg(nodes[0] ? nodes[0] : null)
    }

    const orgTreeComponent = useMemo(() => {
        return (<OrgTree query={{needDept: true}} onSelect={treeSelect} defaultExpandAll={false}/>)
    }, []);

    const handlerSave = async (values: any) => {
        presenter.saveUser(values).then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    const handleSaveUserRole = () => {
        presenter?.saveUserRoles().then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    return (
        <PageContainer>
            <Row>
                <Row.Col span={4}>
                    {orgTreeComponent}
                </Row.Col>
                <Row.Col span={20}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        presenter.showEditModal(null)
                                    }}
                                >新增</Button>,

                            ]
                        }}
                        request={async (params, sort, filter) => {
                            return presenter.searchTable({
                                ...params,
                                unitNodeId: state.currentTreeNode?.type == "DEPT" ? state.currentTreeNode.unitNodeId : state.currentTreeNode?.nodeId,
                                deptNodeId: state.currentTreeNode?.type == "DEPT" ? state.currentTreeNode.nodeId : null
                            }, sort, filter, [
                                {
                                    key: "name",
                                    type: "LIKE"
                                },
                                {
                                    key: "account",
                                    type: "LIKE"
                                },
                                {
                                    key: "userNo",
                                    type: "LIKE"
                                }
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑用户"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={handlerSave}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"name"}
                                label={"姓名"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"namePy"}
                                label={"姓名拼音"}
                            />

                            <FormInput
                                name={"account"}
                                label={"账号"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"type"}
                                label={"人员类型"}
                            />

                            <FormUploader
                                required={false}
                                name={"avatar"}
                                label={"头像"}
                                uploaderMaxCount={1}
                                onUploaderUpload={(filename, base64) => {
                                    // 获取表单中的id
                                    // 走上传文件
                                    return new Promise<{ id: string; name: string; url: string } | null>(resolve => {
                                        resolve({
                                            id: "1",
                                            name: "1",
                                            url: "1"
                                        })
                                    });
                                }}
                                onUploaderLoad={(ids) => {
                                    return new Promise<{ id: string; name: string; url: string }[]>(resolve => {
                                        resolve([{
                                            id: "1",
                                            name: "1",
                                            url: "1"
                                        }])
                                    });
                                }}
                            />

                            <FormRadio
                                name={"sex"}
                                label={"性别"}
                                options={[
                                    {
                                        label: "男",
                                        value: "1"
                                    },
                                    {
                                        label: "女",
                                        value: "0"
                                    }
                                ]}
                            />

                            <FormInput
                                name={"userNo"}
                                label={"员工编号"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"certNo"}
                                label={"证件号码"}
                            />

                            <FormInput
                                name={"telephone"}
                                label={"手机号"}
                            />

                            <FormInput
                                name={"officeTel"}
                                label={"办公室电话"}
                            />

                            <FormOrgTreeSelect label={"所属单位"} name={"unitNodeId"} query={{}} selectable={["UNIT"]}
                                               defaultExpandAll={false}
                                               onChange={(value, form) => {
                                                   console.log(form)
                                                   form?.setFieldValue('deptNodeId', null)
                                                   form?.setFieldValue('postId', null)
                                                   form?.reloadOptions("deptNodeId")
                                                   form?.reloadOptions("postId")
                                               }}
                            />
                            <FormOrgTreeSelect label={"所属部门"} name={"deptNodeId"} query={{needDept: true}}
                                               parentNodeFieldName={"unitNodeId"}
                                               selectable={["DEPT"]} defaultExpandAll={false}
                                               onChange={(value, form) => {
                                                   form?.setFieldValue('postId', null)
                                                   form?.reloadOptions("postId")
                                               }}
                            />

                            <FormSelect
                                required={true}
                                name={"postId"}
                                label={"岗位"}
                                loadOptions={(form) => {
                                    return presenter.queryPostFormSelect(form?.getFieldValue("deptNodeId"))
                                }}
                            />

                            <FormSelect
                                name={"lineId"}
                                label={"条线"}
                                loadOptions={(form) => {
                                    return presenter.queryLineFormSelect()
                                }}
                            />
                            <FormInput
                                name={"sysIndex"}
                                label={"部门下顺序号"}
                            />
                        </Form>
                    </Modal>
                    <Modal
                        data-testid={"role-select-modal"}
                        title="选择角色"
                        open={state.selectRoleVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter?.hideSelectRoleModal()
                        }}
                        onCancel={() => {
                            presenter?.hideSelectRoleModal()
                        }}
                        onOk={handleSaveUserRole}
                    >
                        <RoleList
                            onSelect={(selectedKeys: any, selectedNodes: any) => {
                                presenter?.updateSelectedRole(selectedKeys)
                            }}
                            selectedRoleIds={state.selectedRoleIds}
                        />
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default UserManagePage