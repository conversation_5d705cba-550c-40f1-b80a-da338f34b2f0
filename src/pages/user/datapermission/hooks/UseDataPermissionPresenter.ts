import {useEffect, useRef, useState} from "react";
import {DataPermissionState} from "@/pages/user/datapermission/type";
import DataPermissionPresenter from "@/pages/user/datapermission/presenter";
import {DataPermissionApiImpl} from "@/pages/user/datapermission/model";


const initialState: DataPermissionState = {
    tableRefreshVersion: 0,
    currentTreeNode: null,
    currentEditUserId: null,
    permittedOrgNodeIds: [],
}

export function useDataPermissionPresenter() {

    const [state, dispatch] = useState<DataPermissionState>(initialState);
    const presenterRef = useRef<DataPermissionPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new DataPermissionPresenter(state, new DataPermissionApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}