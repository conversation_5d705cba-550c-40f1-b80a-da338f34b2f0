import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import React, {Key, useEffect, useMemo, useState} from "react";
import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import {Button, ProTable, Row} from "@itm/components-pc";
import {useDataPermissionPresenter} from "@/pages/user/datapermission/hooks/UseDataPermissionPresenter";
import {Empty, message, Popconfirm} from "antd";


const DataPermissionManagePage = () => {

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useDataPermissionPresenter();

    const [selectedRecord, setSelectedRecord] = useState<Key[]>([]);

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    const columns: ProColumns[] = [
        {
            title: '姓名',
            dataIndex: 'name',
            search: false,
        },
        {
            title: '账号',
            dataIndex: 'account',
            search: false,
        },
        {
            title: '员工编号',
            dataIndex: 'userNo',
            search: false,
        },
    ]

    const treeSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateCurrentOrg(nodes[0] ? nodes[0] : null)
    }

    const orgTreeComponent = useMemo(() => {
        return (<OrgTree query={{needDept: true}} onSelect={treeSelect} defaultExpandAll={false}/>)
    }, []);

    const permissionTreeComponent = useMemo(() => {
        return (<OrgTree query={{needDept: true}}
                         onSelect={() => {
                         }}
                         defaultExpandAll={false} checkable={true}
                         checkedKeys={state.permittedOrgNodeIds}
                         onCheck={(checked) => {
                             console.log(checked)
                             presenter.setSelectedOrgNodeIds(checked as any[])
                         }}/>)
    }, [state.currentEditUserId,state.permittedOrgNodeIds]);


    return (
        <PageContainer>
            <Row>
                <Row.Col span={4}>
                    {orgTreeComponent}
                </Row.Col>
                <Row.Col span={14}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        onRow={(record) => {
                            return {
                                onClick: () => {
                                    presenter.editUserPermission(record.id)
                                }
                            };
                        }}
                        rowSelection={{
                            type: "radio",
                            selectedRowKeys: state.currentEditUserId ? [state.currentEditUserId] : [],
                            onChange: (selectedRowKeys) => {
                                presenter.editUserPermission(selectedRowKeys.length > 0 ? selectedRowKeys[0] : null)
                            }
                        }}
                        actionRef={actionRef}
                        search={false}
                        columns={columns}
                        request={async (params, sort, filter) => {
                            return presenter.searchTable({
                                ...params,
                                unitNodeId: state.currentTreeNode?.type == "DEPT" ? state.currentTreeNode.unitNodeId : state.currentTreeNode?.nodeId,
                                deptNodeId: state.currentTreeNode?.type == "DEPT" ? state.currentTreeNode.nodeId : null
                            }, sort, filter, []);
                        }}
                    />
                </Row.Col>
                <Row.Col span={6}>

                    {state.currentEditUserId ?
                        <>
                            <Popconfirm
                                key="enableConfirm"
                                title={"确认要更新数据权限吗?"}
                                onConfirm={() => {
                                    presenter.saveUserPermission().then(()=>message.success("保存成功"))
                                }}>
                                <Button type={"primary"}>保存</Button>
                            </Popconfirm>
                            {permissionTreeComponent}
                        </> :
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>}
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default DataPermissionManagePage