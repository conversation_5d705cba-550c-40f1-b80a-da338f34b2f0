import {TreeData} from "@/api/user/org";

export interface DataPermissionState {
    tableRefreshVersion: number;
    currentTreeNode: TreeData | null;
    currentEditUserId: any;
    permittedOrgNodeIds: any[];
}

export interface DataPermissionApi {
    userPage: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    detailByUserId: (userId: any) => Promise<any>;
}