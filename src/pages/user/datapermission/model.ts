import {DataPermissionApi} from "@/pages/user/datapermission/type";
import {getDataPermissionDetailByUserId, saveDataPermission} from "@/api/user/datapermission";
import {queryUserPage} from "@/api/user/user";


export class DataPermissionApiImpl implements DataPermissionApi {
    detailByUserId(userId: any): Promise<any> {
        return getDataPermissionDetailByUserId(userId);
    }

    save(params: any): Promise<any> {
        return saveDataPermission(params);
    }

    userPage(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryUserPage(params, sort, filter, match);
    }


}