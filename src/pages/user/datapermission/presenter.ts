import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/org";
import {DataPermissionApi, DataPermissionState} from "@/pages/user/datapermission/type";

export default class DataPermissionPresenter {
    private readonly api: DataPermissionApi;
    private readonly dispatch: Dispatch<DataPermissionState>;
    private state: DataPermissionState;

    public constructor(state: DataPermissionState, api: DataPermissionApi, dispatch: Dispatch<DataPermissionState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: DataPermissionState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public updateCurrentOrg = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public editUserPermission = (userId: any) => {

        if (!userId) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    currentEditUserId: null,
                    permittedOrgNodeIds: []
                }
            });
            return
        }

        this.api.detailByUserId(userId).then((res) => {

            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    permittedOrgNodeIds: res.data?.orgNodeIds ? res.data.orgNodeIds : [],
                    currentEditUserId: userId
                }
            });

        });

    }

    public setSelectedOrgNodeIds = (orgNodeIds: any[]) => {
        console.log(orgNodeIds)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                permittedOrgNodeIds: orgNodeIds
            }
        });

    }

    public saveUserPermission = () => {

       return  this.api.save({
            userId: this.state.currentEditUserId,
            orgNodeIds: this.state.permittedOrgNodeIds
        })

    }


    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.userPage(params, sort, filter, match);
    }
}