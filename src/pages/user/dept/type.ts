import {TreeData} from "@/api/user/org";

export interface DeptState {
    editorVisible: boolean;
    tableRefreshVersion: number;
    treeRefreshVersion: number;
    currentEditRecord: any;
    currentTreeNode: TreeData | null;
}

export interface DeptApi {
    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
}