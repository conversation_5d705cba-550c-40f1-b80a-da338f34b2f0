import {DeptState} from "@/pages/user/dept/type";
import {useEffect, useRef, useState} from "react";
import DeptPresenter from "@/pages/user/dept/presenter";
import {DeptApiImpl} from "@/pages/user/dept/model";


const initialState: DeptState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    treeRefreshVersion: 0,
    currentEditRecord: null,
    currentTreeNode: null
}

export function useDeptPresenter() {

    const [state, dispatch] = useState<DeptState>(initialState);
    const presenterRef = useRef<DeptPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new DeptPresenter(state, new DeptApiImpl(), dispatch);
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}