import {disableDept, enableDept, queryDeptPage, saveDept} from "@/api/user/dept";
import {DeptApi} from "@/pages/user/dept/type";


export class DeptApiImpl implements DeptApi {
    disable(id: any): any {
        return disableDept(id);
    }

    enable(id: any): any {
        return enableDept(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryDeptPage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveDept(params);
    }

}