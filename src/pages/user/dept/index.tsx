import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import {message, Switch} from "antd";
import React, {useEffect, useMemo} from "react";
import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {ValidateUtils} from "@codingapi/ui-framework";
import FormOrgTreeSelect, {DisplayType} from "@/components/user/org/FormOrgTreeSelect";
import {useDeptPresenter} from "@/pages/user/dept/hooks/UseDeptPresenter";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {Form, FormInput} from "@/components/form-pc";

const DeptManagePage = () => {
  const form = Form.useForm();

  const actionRef = React.useRef<ActionType>();

  const { state, presenter } = useDeptPresenter();

  useEffect(() => {
    actionRef.current?.reload();
  }, [state.tableRefreshVersion]);

  useEffect(() => {
    if (state.currentEditRecord) {
      form.setFieldsValue(state.currentEditRecord);
    } else {
      form.reset();
    }
  }, [form, state.currentEditRecord]);

  const columns: ProColumns[] = [
    {
      title: "部门名称",
      dataIndex: "name",
    },
    {
      title: "部门编码",
      dataIndex: "code",
    },
    {
      title: "顺序号",
      dataIndex: "sysIndex",
    },
    {
      title: "调整启用状态",
      valueType: "option",
      render: (_: any, record: any) => [
        <Popconfirm
            key="enableConfirm"
            title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
            onConfirm={async () => {
              if (record.sysEnabled) {
                presenter.disableLDept(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
              } else {
                presenter.enableDept(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
              }
            }}
        >
          <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
        </Popconfirm>
      ],
    },
    {
      title: "操作",
      valueType: "option",
      render: (_: any, record: any) => [
        <a
          key="editable"
          onClick={() => {
            presenter.showEditModal(record);
          }}
        >
          编辑
        </a>,
      ],
    },
  ];

  const treeSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
    presenter.updateCurrentOrg(nodes[0] ? nodes[0] : null);
  };

  const orgTreeComponent = useMemo(() => {
    return (
      <OrgTree
        key={state.treeRefreshVersion}
        query={{ needDept: true }}
        onSelect={treeSelect}
        defaultSelectedKeys={
          state.currentTreeNode ? [state.currentTreeNode.nodeId] : []
        }
        defaultExpandAll={false}
      />
    );
  }, [state.treeRefreshVersion]);

  const handlerSave = async (values: any) => {
    presenter
      .saveDept(values)
      .then(() => message.success("保存成功"))
      .catch((e) => {
        message.error(e.message);
      });
  };

  return (
    <PageContainer>
      <Row>
        <Row.Col span={4}>{orgTreeComponent}</Row.Col>
        <Row.Col span={20}>
          <ProTable
            data-testid={"user-table"}
            rowKey={"id"}
            actionRef={actionRef}
            columns={columns}
            toolBarRender={() => {
              return [
                <Button
                  data-testid={"flow-add-btn"}
                  type={"primary"}
                  onClick={() => {
                    presenter.showEditModal(null);
                  }}
                >
                  新增
                </Button>,
              ];
            }}
            request={async (params, sort, filter) => {
              return presenter.searchTable(
                {
                  ...params,
                  parentNodeId: state.currentTreeNode?.nodeId,
                },
                sort,
                filter,
                [
                  {
                    key: "name",
                    type: "LIKE",
                  },
                  {
                    key: "code",
                    type: "LIKE",
                  },
                ]
              );
            }}
          />
          <Modal
            data-testid={"user-editor"}
            title="编辑用户"
            open={state.editorVisible}
            destroyOnHidden={true}
            onClose={() => {
              presenter.hideEditModal();
            }}
            onCancel={() => {
              presenter.hideEditModal();
            }}
            onOk={async () => {
              await form.submit();
            }}
          >
            <Form form={form} layout={"vertical"} onFinish={handlerSave}>
              <FormInput name={"id"} hidden={true} />

              <FormInput
                name={"name"}
                label={"部门名称"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
              />

              <FormInput
                  name={"shortName"}
                  label={"部门简称"}
                  required={true}
                  validateFunction={ValidateUtils.validateNotEmpty}
              />

              <FormInput
                name={"code"}
                label={"部门编码"}
                required={true}
                validateFunction={ValidateUtils.validateNotEmpty}
              />

              <FormInput
                  name={"type"}
                  label={"部门类型"}
              />

              {/*<AntdForm.Item label={"所属单位"} name={"unitNodeId"} >*/}
              {/*    <OrgTreeSelect query={{}} selectable={["UNIT"]} treeDefaultExpandAll={true} />*/}
              {/*</AntdForm.Item>*/}
              {/*<AntdForm.Item label={"上级部门"} name={"parentNodeId"} >*/}
              {/*    <OrgTreeSelect query={{needDept:true}} selectable={["DEPT"]} treeDefaultExpandAll={true} />*/}
              {/*</AntdForm.Item>*/}
              <FormOrgTreeSelect
                required={true}
                label={"所属单位"}
                display={DisplayType.shortName}
                name={"unitNodeId"}
                query={{}}
                selectable={["UNIT"]}
                defaultExpandAll={false}
                onChange={(value, form) => {
                  form?.setFieldValue("parentNodeId", null);
                  // 重新加载子字段的选项
                  form?.reloadOptions("parentNodeId");
                }}
              />
              <FormOrgTreeSelect
                label={"上级部门"}
                name={"parentNodeId"}
                parentNodeFieldName={"unitNodeId"}
                query={{ needDept: true }}
                selectable={["DEPT"]}
                defaultExpandAll={false}
              />
              <FormInput name={"sysIndex"} label={"顺序号"} />
            </Form>
          </Modal>
        </Row.Col>
      </Row>
    </PageContainer>
  );
};

export default DeptManagePage;
