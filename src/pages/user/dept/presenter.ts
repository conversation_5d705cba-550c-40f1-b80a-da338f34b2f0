import {Dept<PERSON>pi, DeptState} from "@/pages/user/dept/type";
import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/org";

export default class DeptPresenter {
    private readonly api: DeptApi;
    private readonly dispatch: Dispatch<DeptState>;
    private state: DeptState;

    public constructor(state: DeptState, api: DeptApi, dispatch: Dispatch<DeptState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: DeptState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public refreshTree = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                treeRefreshVersion: prevState.treeRefreshVersion + 1
            }
        })
    }

    public updateCurrentOrg = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public showEditModal = (record: any) => {

        if (record == null) {
            record = {
                parentNodeId: this.state.currentTreeNode?.type == "DEPT" ? this.state.currentTreeNode?.nodeId : null,
                unitNodeId: this.state.currentTreeNode?.type == "UNIT" ? this.state.currentTreeNode?.nodeId : this.state.currentTreeNode?.unitNodeId
            }
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public saveDept = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
            this.refreshTree();
        } else {
            throw new Error(res.message)
        }
    }

    public enableDept = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableLDept = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }
}