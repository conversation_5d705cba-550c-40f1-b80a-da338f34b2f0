import {PostApi, PostState} from "@/pages/user/post/type";
import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/org";

export default class PostPresenter {
    private readonly api: PostApi;
    private readonly dispatch: Dispatch<PostState>;
    private state: PostState;

    public constructor(state: PostState, api: PostApi, dispatch: Dispatch<PostState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: PostState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public updateCurrentOrg = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public showEditModal = (record: PostState | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record ? record : {deptNodeId: this.state.currentTreeNode?.nodeId}
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public savePost = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public enablePost = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disablePost = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }
}