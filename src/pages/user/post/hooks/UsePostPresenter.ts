import {PostState} from "@/pages/user/post/type";
import {useEffect, useRef, useState} from "react";
import PostPresenter from "@/pages/user/post/presenter";
import {PostApiImpl} from "@/pages/user/post/model";


const initialState: PostState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    currentEditRecord: null,
    currentTreeNode: null
}

export function usePostPresenter() {

    const [state, dispatch] = useState<PostState>(initialState);
    const presenterRef = useRef<PostPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new PostPresenter(state, new PostApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}