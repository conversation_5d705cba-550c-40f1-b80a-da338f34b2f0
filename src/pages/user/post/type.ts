import {TreeData} from "@/api/user/org";

export interface PostState {
    editorVisible: boolean;
    tableRefreshVersion: number;
    currentEditRecord: any;
    currentTreeNode: TreeData | null;
}

export interface PostApi {
    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
}