import {disablePost, enablePost, queryPostPage, savePost} from "@/api/user/post";
import {PostA<PERSON>} from "@/pages/user/post/type";


export class PostApiImpl implements PostApi {
    disable(id: any): any {
        return disablePost(id);
    }

    enable(id: any): any {
        return enablePost(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryPostPage(params, sort, filter, match);
    }

    save(params: any): any {
        return savePost(params);
    }

}