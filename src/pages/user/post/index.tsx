import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import {message, Switch} from "antd";
import React, {useEffect, useMemo} from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {ValidateUtils} from "@codingapi/ui-framework";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import {usePostPresenter} from "@/pages/user/post/hooks/UsePostPresenter";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {Form, FormInput, FormSwitch} from "@/components/form-pc";

const PostManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = usePostPresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);

    const columns: ProColumns<any>[] = [
        {
            title: '岗位名称',
            dataIndex: 'name',
        },
        {
            title: '岗位编码',
            dataIndex: 'code',
        },
        {
            title: '顺序号',
            dataIndex: 'sysIndex',
            search: false
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.sysEnabled) {
                            presenter.disablePost(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enablePost(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
                </Popconfirm>
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
            ]
        }
    ]

    const treeSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateCurrentOrg(nodes[0] ? nodes[0] : null);
    }

    const orgTreeComponent = useMemo(() => {
        return (<OrgTree query={{needDept: true}} onSelect={treeSelect} defaultExpandAll={false}/>)
    }, []);

    const handlerSave = async (values: any) => {
        presenter.savePost(values)
            .then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    return (
        <PageContainer>
            <Row>
                <Row.Col span={4}>
                    {orgTreeComponent}
                </Row.Col>
                <Row.Col span={20}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        if (state.currentTreeNode?.type != "DEPT") {
                                            message.warning("请在左侧树中选择部门");
                                            return
                                        }

                                        presenter.showEditModal(null)

                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {
                            if (state.currentTreeNode?.type != 'DEPT') {
                                // 不是部门就返回空
                                return {
                                    data: [],
                                    success: true,
                                    total: 0
                                }
                            }

                            return presenter.searchTable({
                                ...params,
                                deptNodeId: state.currentTreeNode?.nodeId
                            }, sort, filter, [
                                {
                                    key: "name",
                                    type: "LIKE"
                                },
                                {
                                    key: "code",
                                    type: "LIKE"
                                }
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑岗位"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={handlerSave}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"name"}
                                label={"岗位名称"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"code"}
                                label={"岗位编码"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"type"}
                                label={"岗位类型"}
                            />

                            <FormSwitch
                                name={"deptLeader"}
                                label={"是否部门负责人"}
                            />

                            <FormOrgTreeSelect required={true} label={"所属部门"} name={"deptNodeId"}
                                               query={{needDept: true}}
                                               selectable={["DEPT"]}
                                               defaultExpandAll={false}/>
                            <FormInput
                                name={"sysIndex"}
                                label={"顺序号"}
                            />
                        </Form>
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default PostManagePage