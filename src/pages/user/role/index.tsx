import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {message} from "antd";
import React, {useEffect} from "react";
import {ValidateUtils} from "@codingapi/ui-framework";
import {<PERSON><PERSON>, FormStepper, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import MenuTree from "@/components/user/menu/MenuTree";
import {useRolePresenter} from "@/pages/user/role/hooks/UseRolePresenter";
import {Form, FormInput} from "@/components/form-pc";
import FormUserChoiceSelect from "@/components/user/choice/FormUserChoiceSelect";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {SelectableUser} from "@/components/user/choice/type";

const RoleManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useRolePresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);

    const columns: ProColumns[] = [
        {
            title: '名称',
            dataIndex: 'name',
        },
        {
            title: '编码',
            dataIndex: 'code',
        },
        {
            title: '顺序号',
            dataIndex: 'sysIndex',
            search: false
        },
        {
            title: '角色人员',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter?.showSelectUserModal(record.id)
                    }}
                >
                    {record.userCount}
                </a>,
            ]
        },
        {
            title: '功能授权',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter?.showSelectMenuModal(record.id)
                    }}
                >
                    功能授权
                </a>,
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [
                <a
                    key="editable"
                    onClick={() => {
                        presenter?.showEditModal(record)
                    }}
                >
                    编辑
                </a>,

                <Popconfirm
                    key="delete"
                    title={"确认删除?"}
                    onConfirm={() => {
                        presenter?.deleteRole(record.id).then(() => message.success("删除成功"))
                            .catch(e => {
                                message.error(e.message)
                            })
                    }}
                >
                    <a
                        key="delete"
                    >
                        删除
                    </a>
                </Popconfirm>
            ]
        }
    ]


    return (
        <PageContainer>
            <Row>
                <Row.Col span={24}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        presenter?.showEditModal(null)
                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {
                            return presenter?.searchTable({...params}, sort, filter, [
                                {
                                    key: "name",
                                    type: "LIKE"
                                },
                                {
                                    key: "code",
                                    type: "LIKE"
                                }
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑角色"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={async (values) => {
                                presenter?.saveRole(values)
                                    .then(() => message.success("保存成功"))
                                    .catch(e => {
                                        message.error(e.message)
                                    })
                            }}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"name"}
                                label={"角色名称"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"code"}
                                label={"角色编码"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />
                            <FormStepper
                                name={"sysIndex"}
                                label={"顺序号"}
                                placeholder={"请输入顺序号"}
                                validateFunction={(content) => {
                                    return new Promise((resolve, reject) => {
                                        if ((content.value || content.value === 0) && !/^[1-9]\d*$/.test(content.value)) {
                                            resolve(["请输入正整数"])
                                        } else {
                                            resolve([])
                                        }
                                    })
                                }}
                            />
                            <FormUserChoiceSelect name={"userIds"} label={"人员"} selectMultiple={true}/>
                        </Form>
                    </Modal>
                    <UserChoiceModal
                        visible={state.selectUserVisible}
                        hide={presenter?.hideSelectUserModal}
                        multiple={true}
                        onFinish={(records: SelectableUser[]) => {
                            presenter?.saveRoleUsers(records).then(() => message.success("保存成功"))
                                .catch(e => {
                                    message.error(e.message)
                                })
                        }}
                        selectedUsers={state.currentSelectedUser}
                    />
                    <Modal
                        data-testid={"menu-select-modal"}
                        title="选择菜单"
                        open={state.selectMenuVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter?.hideSelectMenuModal()
                        }}
                        onCancel={() => {
                            presenter?.hideSelectMenuModal()
                        }}
                        onOk={() => {
                            presenter?.saveRoleMenus().then(() => message.success("保存成功"))
                                .catch(e => {
                                    message.error(e.message)
                                })
                        }}
                    >
                        <MenuTree
                            multiSelect={true}
                            onSelect={(selectedKeys: any, selectedNodes: any) => {
                                presenter?.updateSelectedMenu(selectedKeys)
                            }}
                            selectedKeys={state.currentSelectedMenu}
                            defaultExpandAll={true}
                        />
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default RoleManagePage