import {<PERSON><PERSON><PERSON>} from "@/pages/user/role/types";
import {
    deleteRole,
    disableRole,
    enableRole,
    findMenuIdsByRoleId,
    findUsersByRoleId,
    queryRolePage,
    saveRole,
    saveRoleMenuRelations,
    saveRoleUserRelations
} from "@/api/user/role";


export class Role<PERSON>piImpl implements Role<PERSON><PERSON> {
    disable(id: any): any {
        return disableRole(id);
    }

    enable(id: any): any {
        return enableRole(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryRolePage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveRole(params);
    }

    delete(id: any): any {
        return deleteRole(id);
    }

    findRelatedMenuIds(id: any): any {
        return findMenuIdsByRoleId(id);
    }

    findRelatedUsers(id: any): any {
        return findUsersByRoleId(id);
    }

    saveRoleUsers(roleId: any, userIds: any[]): any {
        return saveRoleUserRelations(roleId, userIds);
    }

    saveRoleMenus(roleId: any, menuIds: any[]): any {
        return saveRoleMenuRelations(roleId, menuIds);
    }

}