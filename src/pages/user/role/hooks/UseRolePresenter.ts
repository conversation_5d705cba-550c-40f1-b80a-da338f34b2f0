import {RoleState} from "@/pages/user/role/types";
import {RolePresenter} from "@/pages/user/role/presenter";
import {RoleApiImpl} from "@/pages/user/role/model";
import {useEffect, useRef, useState} from "react";

const initialState: RoleState = {
    editorVisible: false,
    selectUserVisible: false,
    selectMenuVisible: false,
    tableRefreshVersion: 0,
    currentEditRecord: null,
    currentRoleId: null,
    currentSelectedUser: [],
    currentSelectedMenu: []

};

export function useRolePresenter() {

    const [state, dispatch] = useState<RoleState>(initialState);
    const presenterRef = useRef<RolePresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new RolePresenter(state, new RoleApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}