import {<PERSON><PERSON><PERSON>, RoleState} from "@/pages/user/role/types";
import {Dispatch} from "@/utils/mvvm";


export class RolePresenter {

    private readonly dispatch: Dispatch<RoleState>;

    private readonly api: <PERSON><PERSON><PERSON>;

    private state: RoleState;

    public constructor(state: RoleState, api: RoleApi, dispatch: Dispatch<RoleState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: RoleState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public showEditModal = (record: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public showSelectUserModal = async (id: number) => {
        const res = await this.api.findRelatedUsers(id);

        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    selectUserVisible: true,
                    currentRoleId: id,
                    currentSelectedUser: res.data
                }
            });
        } else {
            throw new Error(res.message)
        }

    }

    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }

    public updateSelectedUser = (selectedKeys: any[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentSelectedUser: selectedKeys
            }
        });
    }

    public showSelectMenuModal = async (id: number) => {
        const res = await this.api.findRelatedMenuIds(id);
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    selectMenuVisible: true,
                    currentRoleId: id,
                    currentSelectedMenu: res.data
                }
            });
        } else {
            throw new Error(res.message)
        }
    }

    public updateSelectedMenu = (selectedKeys: any[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentSelectedMenu: selectedKeys
            }
        });
    }

    public hideSelectMenuModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectMenuVisible: false,
                currentRoleId: null,
                currentSelectedMenu: []
            }
        });
    }

    public saveRole = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public saveRoleUsers = async (records: any[]) => {

        // 从records中提取id字段
        const userIds = records.map((item) => {
            return item.id;
        })

        const res = await this.api.saveRoleUsers(this.state.currentRoleId, userIds);
        if (res.success) {
            this.hideSelectUserModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public saveRoleMenus = async () => {

        const res = await this.api.saveRoleMenus(this.state.currentRoleId, this.state.currentSelectedMenu);
        if (res.success) {
            this.hideSelectMenuModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public enableRole = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableRole = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public deleteRole = async (id: any) => {
        const res = await this.api.delete(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }

}