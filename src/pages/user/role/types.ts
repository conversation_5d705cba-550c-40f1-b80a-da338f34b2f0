export interface RoleState {
    editorVisible: boolean;
    selectUserVisible: boolean;
    selectMenuVisible: boolean;
    tableRefreshVersion: number;
    currentEditRecord: any;
    currentRoleId: any;
    currentSelectedUser: any[];
    currentSelectedMenu: any[];
}

export interface RoleApi {

    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
    delete: (id: any) => Promise<any>;
    findRelatedMenuIds: (id: any) => Promise<any>;
    saveRoleUsers: (roleId: any, userIds: any[]) => Promise<any>;
    findRelatedUsers: (id: any) => Promise<any>;
    saveRoleMenus: (roleId: any, menuIds: any[]) => Promise<any>;

}