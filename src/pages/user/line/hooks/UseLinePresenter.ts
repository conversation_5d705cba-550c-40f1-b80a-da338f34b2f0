import {LineState} from "@/pages/user/line/types";
import {LinePresenter} from "@/pages/user/line/presenter";
import {LineApiImpl} from "@/pages/user/line/model";
import {useEffect, useRef, useState} from "react";

const initialState: LineState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    currentEditRecord: null,

};

export function useLinePresenter() {

    const [state, dispatch] = useState<LineState>(initialState);
    const presenterRef = useRef<LinePresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new LinePresenter(state, new LineApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}