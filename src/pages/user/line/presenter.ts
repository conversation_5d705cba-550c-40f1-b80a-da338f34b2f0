import {Line<PERSON><PERSON>, LineState} from "@/pages/user/line/types";
import {Dispatch} from "@/utils/mvvm";


export class LinePresenter {

    private readonly dispatch: Dispatch<LineState>;

    private readonly api: LineApi;

    private state: LineState;

    public constructor(state: LineState, api: LineApi, dispatch: Dispatch<LineState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: LineState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public showEditModal = (record: LineState | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public saveLine = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public enableLine = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableLine = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }

}