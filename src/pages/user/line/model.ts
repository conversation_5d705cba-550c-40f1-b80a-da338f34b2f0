import {LineApi} from "@/pages/user/line/types";
import {disableLine, enableLine, queryLinePage, saveLine} from "@/api/user/line";


export class LineApiImpl implements LineApi {
    disable(id: any): any {
        return disableLine(id);
    }

    enable(id: any): any {
        return enableLine(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryLinePage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveLine(params);
    }

}