import {message, Switch} from "antd";
import React, {useEffect} from "react";
import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {ValidateUtils} from "@codingapi/ui-framework";
import {useLinePresenter} from "@/pages/user/line/hooks/UseLinePresenter";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {Form, FormInput} from "@/components/form-pc";

const LineManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useLinePresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);


    const columns: ProColumns[] = [
        {
            title: '条线名称',
            dataIndex: 'name',
        },
        {
            title: '条线编码',
            dataIndex: 'code',
        },
        {
            title: '顺序号',
            dataIndex: 'sysIndex',
            search: false
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.sysEnabled) {
                            presenter.disableLine(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enableLine(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
                </Popconfirm>
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
            ]
        }
    ]

    const handlerSave = async (values: any) => {
        presenter.saveLine(values)
            .then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    return (
        <PageContainer>
            <Row>
                <Row.Col span={24}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        presenter.showEditModal(null);
                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {

                            return presenter.searchTable({...params}, sort, filter, [
                                {
                                    key: "name",
                                    type: "LIKE"
                                },
                                {
                                    key: "code",
                                    type: "LIKE"
                                },
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑条线"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={handlerSave}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"name"}
                                label={"条线名称"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormInput
                                name={"code"}
                                label={"条线编码"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />
                            <FormInput
                                name={"sysIndex"}
                                label={"顺序号"}
                            />
                        </Form>
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default LineManagePage