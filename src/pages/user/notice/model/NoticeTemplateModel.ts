import {NoticeTemplateApi} from "@/pages/user/notice/types";
import {deleteNoticeTemplate, queryNoticeTemplatePage, saveNoticeTemplate} from "@/api/user/notice";


export class NoticeTemplateApiImpl implements NoticeTemplateApi {
    delete(id: any): any {
        return deleteNoticeTemplate(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryNoticeTemplatePage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveNoticeTemplate(params);
    }

}