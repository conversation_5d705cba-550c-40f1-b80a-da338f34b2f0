import {message} from "antd";
import React, {useEffect} from "react";
import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {ValidateUtils} from "@codingapi/ui-framework";
import {But<PERSON>, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {Form, FormInput, FormSelect, FormTextArea} from "@/components/form-pc";
import {useNoticeTemplatePresenter} from "@/pages/user/notice/hooks/UseNoticeTemplatePresenter";

const NoticeTemplateManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useNoticeTemplatePresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);


    const columns: ProColumns[] = [
        {
            title: '模板编码',
            dataIndex: 'code',
        },
        {
            title: '类型',
            dataIndex: 'type',
            valueEnum: {
                FLOW_TODO: {text: '流程待办'},
                FLOW_NOTICE: {text: '流程通知'},
                CUSTOM_TODO: {text: '自定义待办'},
                CUSTOM_NOTICE: {text: '自定义通知'},
                WARN: {text: '预警'},
            },
            valueType: 'select',
        },
        {
            title: '标题格式',
            dataIndex: 'titleFormat',
        },
        {
            title: '组件标识',
            dataIndex: 'componentKey',
            search: false
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title={"确认删除?"}
                    onConfirm={() => {
                        presenter?.deleteTodoTemplate(record.id).then(() => message.success("删除成功"))
                            .catch(e => {
                                message.error(e.message)
                            })
                    }}
                >
                    <a
                        key="delete"
                    >
                        删除
                    </a>
                </Popconfirm>
            ]
        }
    ]

    const handlerSave = async (values: any) => {
        presenter.saveTodoTemplate(values)
            .then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    return (
        <PageContainer>
            <Row>
                <Row.Col span={24}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        presenter.showEditModal(null);
                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {

                            return presenter.searchTable({...params}, sort, filter, [
                                {
                                    key: "code",
                                    type: "LIKE"
                                },
                                {
                                    key: "titleFormat",
                                    type: "LIKE"
                                },
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑模板"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={handlerSave}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"code"}
                                label={"模板编码"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />
                            <FormSelect
                                name={"type"}
                                label={"类型"}
                                required={true}
                                options={[
                                    {label: '流程待办', value: 'FLOW_TODO'},
                                    {label: '流程通知', value: 'FLOW_NOTICE'},
                                    {label: '自定义待办', value: 'CUSTOM_TODO'},
                                    {label: '自定义通知', value: 'CUSTOM_NOTICE'},
                                    {label: '预警', value: 'WARN'},
                                ]}
                            />
                            <FormTextArea
                                name={"titleFormat"}
                                label={"标题格式"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />
                            <FormTextArea
                                name={"contentFormat"}
                                label={"内容格式"}
                            />
                            <FormInput
                                name={"componentKey"}
                                label={"组件标识"}
                            />
                        </Form>
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default NoticeTemplateManagePage