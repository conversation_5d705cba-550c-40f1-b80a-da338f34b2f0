import {NoticeR<PERSON>ord<PERSON>pi} from "@/pages/user/notice/record/types";
import {
    getSelfNotDoneTodoCount,
    getSelfNoticePage,
    getSelfNotReadNoticeCount,
    getSelfNotReadWarnCount,
    getSelfTodoPage, getSelfWarnPage
} from "@/api/user/self";


export class NoticeRecordApiImpl implements NoticeRecordApi {
    selfNotDoneTodoCount(): Promise<any> {
        return getSelfNotDoneTodoCount();
    }

    selfNotReadNoticeCount(): Promise<any> {
        return getSelfNotReadNoticeCount();
    }

    selfNotReadWarnCount(): Promise<any> {
        return getSelfNotReadWarnCount();
    }

    selfNoticePage(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return getSelfNoticePage(params, sort, filter, match)
    }

    selfWarnPage(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return getSelfWarnPage(params, sort, filter, match)
    }

    selfTodoPage(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return getSelfTodoPage(params, sort, filter, match)
    }

}