import {Badge, Tabs} from "antd";
import React, {useEffect, useState} from "react";
import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {Button, ProTable} from "@itm/components-pc";
import NoticeModalView from "@/components/user/notice/NoticeModalView";
import {useNoticeRecordPresenter} from "@/pages/user/notice/record/hooks/UseNoticeRecordPresenter";
import {testSendNotice, testSendTodo} from "@/api/user/notice";
import {POBrowser} from "js-pageoffice";
import {useLocation} from "react-router-dom";

const NoticeCenterPage = () => {
    const actionRef = React.useRef<ActionType>();

    const [tabActiveKey, setTabsActiveKey] = useState<string>("todo");

    const location = useLocation();
    useEffect(() => {
        const query = new URLSearchParams(location.search);
        const type = query.get("type");
        const tab = type ? type : "todo";
        setTabsActiveKey(tab);
    }, [location.search]);

    const {state, presenter} = useNoticeRecordPresenter();

    useEffect(() => {
        actionRef.current?.reload();
        presenter?.reloadCount();
    }, [state.tableRefreshVersion]);

    const todoColumns: ProColumns[] = [
        {
            title: "标题",
            dataIndex: "title",
            render: (_: any, record: any) => [
                <a
                    key="detail"
                    onClick={() => {
                        var parse = JSON.parse(record.customParams);
                        const templateCode = parse?.templateCode;

                        POBrowser.setProxyBaseAPI("/itm-legal-review-contract");
                        POBrowser.setHeader("token", localStorage.getItem("token"));
                        const params = {
                            token: localStorage.getItem("token"),
                            username: localStorage.getItem("username"),
                            data: record,
                        };
                        const codes = [
                            "contract_draft_joint_review_expert",
                            "contract_draft_joint_review",
                            "contract_draft_joint_review_examine",
                        ];
                        if (codes.includes(templateCode)) {
                            POBrowser.openWindowModeless(
                                `/flow-page-office`,
                                `width=${screen.width}px;height=${screen.height}px;`,
                                JSON.stringify(params)
                            );
                        } else if (templateCode == "contract_draft_modify") {
                            POBrowser.openWindowModeless(
                                `/contract/update`,
                                `width=${screen.width}px;height=${screen.height}px;`,
                                JSON.stringify(params)
                            );
                        } else {
                            presenter?.showModal(record);
                        }
                    }}
                >
                    {record.title}
                </a>,
            ],
        },
        {
            title: "发送人",
            dataIndex: "senderName",
            search: false,
        },
        {
            title: "创建时间",
            dataIndex: "sysCreateTime",
            search: false,
        },
        {
            title: "状态",
            dataIndex: "status",
            fieldProps: {
                mode: "multiple", // 支持多选
            },
            valueEnum: {
                UNREAD: {text: "未读"},
                READ: {text: "已读"},
                DONE: {text: "已办"},
            },
            initialValue: ["UNREAD", "READ"],
            valueType: "select",
            render: (text: any, record: any) => {
                if (record.status === "UNREAD") {
                    return <span style={{color: "red"}}>{text}</span>;
                } else if (record.status === "READ") {
                    return <span style={{color: "blue"}}>{text}</span>;
                } else {
                    return <span style={{color: "green"}}>{text}</span>;
                }
            },
        },
        {
            title: "操作",
            valueType: "option",
            render: (_: any, record: any) => {
                // 如果不是待办，不展示办理
                return record.status === "DONE"
                    ? []
                    : [
                        <a
                            key="handle"
                            onClick={() => {
                                var parse = JSON.parse(record.customParams);
                                const templateCode = parse?.templateCode;

                                POBrowser.setProxyBaseAPI("/itm-legal-review-contract");
                                POBrowser.setHeader("token", localStorage.getItem("token"));
                                const params = {
                                    token: localStorage.getItem("token"),
                                    username: localStorage.getItem("username"),
                                    data: record,
                                };
                                const codes = [
                                    "contract_draft_joint_review_expert",
                                    "contract_draft_joint_review",
                                    "contract_draft_joint_review_examine",
                                ];
                                if (codes.includes(templateCode)) {
                                    POBrowser.openWindowModeless(
                                        `/flow-page-office`,
                                        `width=${screen.width}px;height=${screen.height}px;`,
                                        JSON.stringify(params)
                                    );
                                } else if (templateCode == "contract_draft_modify") {
                                    POBrowser.openWindowModeless(
                                        `/contract/update`,
                                        `width=${screen.width}px;height=${screen.height}px;`,
                                        JSON.stringify(params)
                                    );
                                } else {
                                    presenter?.showModal(record);
                                }
                            }}
                        >
                            办理
                        </a>,
                    ];
            },
        },
    ];

    const noticeColumns: ProColumns[] = [
        {
            title: "标题",
            dataIndex: "title",
            render: (_: any, record: any) => [
                <a
                    key="detail"
                    onClick={() => {
                        // 如果是待办，跳转到办理页面，如果是通知，打开内容或者办理页面
                        presenter?.showModal(record);
                    }}
                >
                    {record.title}
                </a>,
            ],
        },
        {
            title: "发送人",
            dataIndex: "senderName",
            search: false,
        },
        {
            title: "创建时间",
            dataIndex: "sysCreateTime",
            search: false,
        },
        {
            title: "状态",
            dataIndex: "status",
            fieldProps: {
                mode: "multiple", // 支持多选
            },
            valueEnum: {
                UNREAD: {text: "未读"},
                READ: {text: "已读"},
            },
            initialValue: ["UNREAD"],
            valueType: "select",
            render: (text: any, record: any) => {
                if (record.status === "UNREAD") {
                    return <span style={{color: "red"}}>{text}</span>;
                } else {
                    return <span style={{color: "green"}}>{text}</span>;
                }
            },
        },
    ];

    const tabs = [
        {
            label: (
                <Badge
                    size="small"
                    count={state.notDoneTodoCount}
                    overflowCount={99}
                    offset={[5, 0]}
                >
                    <span>我的待办</span>
                </Badge>
            ),
            key: "todo",
            children: (
                <ProTable
                    data-testid={"todo-table"}
                    rowKey={"id"}
                    actionRef={actionRef}
                    columns={todoColumns}
                    toolBarRender={() => {
                        return [
                            <Button
                                data-testid={"flow-add-btn"}
                                type={"primary"}
                                onClick={() => {
                                    testSendTodo().then(() => {
                                        actionRef.current?.reload();
                                        presenter?.reloadCount();
                                    });
                                }}
                            >
                                测试发待办
                            </Button>,
                        ];
                    }}
                    request={async (params, sort, filter) => {
                        return presenter?.searchTodoTable({...params}, sort, filter, [
                            {
                                key: "title",
                                type: "LIKE",
                            },
                            {
                                key: "status",
                                type: "IN",
                            },
                        ]);
                    }}
                />
            ),
        },
        {
            label: (
                <Badge
                    size="small"
                    count={state.notReadNoticeCount}
                    overflowCount={99}
                    offset={[5, 0]}
                >
                    <span>我的通知</span>
                </Badge>
            ),
            key: "notice",
            children: (
                <ProTable
                    data-testid={"notice-table"}
                    rowKey={"id"}
                    actionRef={actionRef}
                    columns={noticeColumns}
                    toolBarRender={() => {
                        return [
                            <Button
                                data-testid={"flow-add-btn"}
                                type={"primary"}
                                onClick={() => {
                                    testSendNotice().then(() => {
                                        actionRef.current?.reload();
                                        presenter?.reloadCount();
                                    });
                                }}
                            >
                                测试发通知
                            </Button>,
                        ];
                    }}
                    request={async (params, sort, filter) => {
                        return presenter?.searchNoticeTable({...params}, sort, filter, [
                            {
                                key: "title",
                                type: "LIKE",
                            },
                            {
                                key: "status",
                                type: "IN",
                            },
                        ]);
                    }}
                />
            ),
        },
        {
            label: (
                <Badge
                    size="small"
                    count={state.notReadWarnCount}
                    overflowCount={99}
                    offset={[5, 0]}
                >
                    <span>我的预警</span>
                </Badge>
            ),
            key: "warn",
            children: (
                <ProTable
                    data-testid={"warn-table"}
                    rowKey={"id"}
                    actionRef={actionRef}
                    columns={noticeColumns}
                    toolBarRender={() => {
                        return [];
                    }}
                    request={async (params, sort, filter) => {
                        return presenter?.searchWarnTable({...params}, sort, filter, [
                            {
                                key: "title",
                                type: "LIKE",
                            },
                            {
                                key: "status",
                                type: "IN",
                            },
                        ]);
                    }}
                />
            ),
        },
    ];

    return (
        <PageContainer>
            <Tabs items={tabs} activeKey={tabActiveKey} onChange={(key) => setTabsActiveKey(key)}/>
            <NoticeModalView
                visible={state.modalVisible}
                setVisible={presenter?.setModalVisible}
                id={state.currentHandleRecord?.id}
                componentKey={state.currentHandleRecord?.componentKey}
            />
        </PageContainer>
    );
};

export default NoticeCenterPage;
