import {NoticeRecordState} from "@/pages/user/notice/record/types";
import {NoticeRecordPresenter} from "@/pages/user/notice/record/presenter";
import {NoticeRecordApiImpl} from "@/pages/user/notice/record/model";
import {useEffect, useRef, useState} from "react";

const initialState: NoticeRecordState = {
    modalVisible: false,
    tableRefreshVersion: 0,
    currentHandleRecord: null,
    notDoneTodoCount: 0,
    notReadNoticeCount: 0,
    notReadWarnCount: 0,
};

export function useNoticeRecordPresenter() {

    const [state, dispatch] = useState<NoticeRecordState>(initialState);
    const presenterRef = useRef<NoticeRecordPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new NoticeRecordPresenter(state, new NoticeRecordApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}