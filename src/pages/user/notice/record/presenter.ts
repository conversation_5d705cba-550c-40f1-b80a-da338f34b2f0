import {NoticeR<PERSON>ord<PERSON><PERSON>, NoticeRecordState} from "@/pages/user/notice/record/types";
import {Dispatch} from "@/utils/mvvm";


export class NoticeRecordPresenter {

    private readonly dispatch: Dispatch<NoticeRecordState>;

    private readonly api: NoticeRecordApi;

    private state: NoticeRecordState;

    public constructor(state: NoticeRecordState, api: NoticeRecordApi, dispatch: Dispatch<NoticeRecordState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: NoticeRecordState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public showModal = (record: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                modalVisible: true,
                currentHandleRecord: record
            }
        });
    }

    public setModalVisible = (visible: boolean) => {
        visible ? this.showModal(null) : this.hideModal()
    }

    public hideModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                modalVisible: false,
                currentHandleRecord: null,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        });
    }

    public async reloadCount() {
        const [notDoneTodoCount, notReadNoticeCount, notReadWarnCount] = await Promise.all([
            this.api.selfNotDoneTodoCount(),
            this.api.selfNotReadNoticeCount(),
            this.api.selfNotReadWarnCount()
        ]);

        this.dispatch((prevState) => {
            return {
                ...prevState,
                notDoneTodoCount: notDoneTodoCount.data,
                notReadNoticeCount: notReadNoticeCount.data,
                notReadWarnCount: notReadWarnCount.data
            }
        });
    }

    public searchTodoTable = (params: any, sort: any, filter: any, match: any[]) => {
        if (params.status) {
            // 如果是数组，用逗号分隔
            params.status = Array.isArray(params.status) ? params.status.join(",") : params.status;
        }
        return this.api.selfTodoPage(params, sort, filter, match);
    }

    public searchNoticeTable = (params: any, sort: any, filter: any, match: any[]) => {
        if (params.status) {
            // 如果是数组，用逗号分隔
            params.status = Array.isArray(params.status) ? params.status.join(",") : params.status;
        }
        return this.api.selfNoticePage(params, sort, filter, match);
    }

    public searchWarnTable = (params: any, sort: any, filter: any, match: any[]) => {
        if (params.status) {
            // 如果是数组，用逗号分隔
            params.status = Array.isArray(params.status) ? params.status.join(",") : params.status;
        }
        return this.api.selfWarnPage(params, sort, filter, match);
    }

    public getNotDoneTodoCount = () => {
        return this.api.selfNotDoneTodoCount();
    }

    public getNotReadNoticeCount = () => {
        return this.api.selfNotReadNoticeCount();
    }

    public getNotReadWarnCount = () => {
        return this.api.selfNotReadNoticeCount();
    }

}