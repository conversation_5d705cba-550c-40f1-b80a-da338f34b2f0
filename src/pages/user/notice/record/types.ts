/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description: 
 * @LastEditTime: 2025-07-08 08:41:16
 * @LastEditors: wangjiantao
 */
export interface NoticeRecordState {
    modalVisible: boolean;
    tableRefreshVersion: number;
    currentHandleRecord: any;

    notDoneTodoCount: number;
    notReadNoticeCount: number;
    notReadWarnCount: number;
}

export interface NoticeRecordApi {
    selfTodoPage: (
        params: any,
        sort: any,
        filter: any,
        match: any[]
    ) => Promise<any>;
    selfNoticePage: (
        params: any,
        sort: any,
        filter: any,
        match: any[]
    ) => Promise<any>;
    selfWarnPage: (
        params: any,
        sort: any,
        filter: any,
        match: any[]
    ) => Promise<any>;
    selfNotDoneTodoCount: () => Promise<any>;
    selfNotReadNoticeCount: () => Promise<any>;
    selfNotReadWarnCount: () => Promise<any>;
}

export const BUTTON_TEXTS = {
    SUBMIT_TO_LEADER: "送呈主要领导",
    SUBMIT_TO_MANAGER: "送呈分管领导",
    SUBMIT_TO_LEGAL: "送法规部门",
    APPROVE: "审核通过",
};

export const TEMPLATE_CODES = {
    CONTRACT_DRAFT_LEADER: "contract_draft_leader",
    REGULATORY_REVIEW: "regulatory_review",
    CONTRACT_DRAFT_LEGAL_DEPT: "contract_draft_legal_dept",
};
