import {NoticeTemplateApi, NoticeTemplateState} from "@/pages/user/notice/types";
import {Dispatch} from "@/utils/mvvm";


export class NoticeTemplatePresenter {

    private readonly dispatch: Dispatch<NoticeTemplateState>;

    private readonly api: NoticeTemplateApi;

    private state: NoticeTemplateState;

    public constructor(state: NoticeTemplateState, api: NoticeTemplateApi, dispatch: Dispatch<NoticeTemplateState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: NoticeTemplateState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public showEditModal = (record: NoticeTemplateState | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public saveTodoTemplate = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public deleteTodoTemplate = async (id: any) => {
        const res = await this.api.delete(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }
    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }

}