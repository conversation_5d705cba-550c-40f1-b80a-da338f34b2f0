import {NoticeTemplateState} from "@/pages/user/notice/types";
import {useEffect, useRef, useState} from "react";
import {NoticeTemplatePresenter} from "@/pages/user/notice/presenter/NoticeTemplatePresenter";
import {NoticeTemplateApiImpl} from "@/pages/user/notice/model/NoticeTemplateModel";

const initialState: NoticeTemplateState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    currentEditRecord: null,

};

export function useNoticeTemplatePresenter() {

    const [state, dispatch] = useState<NoticeTemplateState>(initialState);
    const presenterRef = useRef<NoticeTemplatePresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new NoticeTemplatePresenter(state, new NoticeTemplateApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}