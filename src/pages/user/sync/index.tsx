import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from "@itm/components-pc";
import {Descriptions, message} from "antd";
import React from "react";
import {ActionType, PageContainer, ProTable} from "@ant-design/pro-components";
import {list} from "@/api/user/sync";


const SyncManagePage = () => {

    const [detailVisible, setDetailVisible] = React.useState(false);

    const actionRef = React.useRef<ActionType>();

    const [detail, setDetail] = React.useState<any>({})


    const columns = [
        {
            title: '同步时间',
            dataIndex: 'syncTime',

        },
        {
            title: '同步结果',
            dataIndex: 'syncResult',
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [
                <a
                    key="editable"
                    onClick={() => {
                        setDetail(record)
                        setDetailVisible(true);
                    }}
                >
                    详情
                </a>
            ]
        }
    ]


    return (
        <PageContainer>
            <Row>
                <Row.Col span={24}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        message.success("同步成功");
                                        actionRef.current?.reload();
                                    }}
                                >手动同步</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {
                            return list({...params}, sort, filter, [
                                {
                                    key: "title",
                                    type: "LIKE"
                                }
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="同步详情"
                        open={detailVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            setDetailVisible(false)
                        }}
                        onCancel={() => {
                            setDetailVisible(false)
                        }}
                    >
                        <Descriptions title="人员">
                            <Descriptions.Item label="新增">{detail.syncDetail?.user.add}</Descriptions.Item>
                            <Descriptions.Item label="修改">{detail.syncDetail?.user.update}</Descriptions.Item>
                            <Descriptions.Item label="删除">{detail.syncDetail?.user.delete}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions title="单位">
                            <Descriptions.Item label="新增">{detail.syncDetail?.unit.add}</Descriptions.Item>
                            <Descriptions.Item label="修改">{detail.syncDetail?.unit.update}</Descriptions.Item>
                            <Descriptions.Item label="删除">{detail.syncDetail?.unit.delete}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions title="部门">
                            <Descriptions.Item label="新增">{detail.syncDetail?.dept.add}</Descriptions.Item>
                            <Descriptions.Item label="修改">{detail.syncDetail?.dept.update}</Descriptions.Item>
                            <Descriptions.Item label="删除">{detail.syncDetail?.dept.delete}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions title="岗位">
                            <Descriptions.Item label="新增">{detail.syncDetail?.post.add}</Descriptions.Item>
                            <Descriptions.Item label="修改">{detail.syncDetail?.post.update}</Descriptions.Item>
                            <Descriptions.Item label="删除">{detail.syncDetail?.post.delete}</Descriptions.Item>
                        </Descriptions>
                        <Descriptions title="条线">
                            <Descriptions.Item label="新增">{detail.syncDetail?.line.add}</Descriptions.Item>
                            <Descriptions.Item label="修改">{detail.syncDetail?.line.update}</Descriptions.Item>
                            <Descriptions.Item label="删除">{detail.syncDetail?.line.delete}</Descriptions.Item>
                        </Descriptions>
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default SyncManagePage