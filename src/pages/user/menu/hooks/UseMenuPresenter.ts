import {MenuState} from "@/pages/user/menu/type";
import {useEffect, useRef, useState} from "react";
import MenuPresenter from "@/pages/user/menu/presenter";
import {MenuApiImpl} from "@/pages/user/menu/model";


const initialState: MenuState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    treeRefreshVersion: 0,
    currentEditRecord: null,
    currentTreeNode: null,
}

export function useMenuPresenter() {

    const [state, dispatch] = useState<MenuState>(initialState);
    const presenterRef = useRef<MenuPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new MenuPresenter(state, new MenuApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}