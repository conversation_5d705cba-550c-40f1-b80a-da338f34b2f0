import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {Form as AntdForm, message, Switch} from "antd";
import React, {useEffect, useMemo} from "react";
import {useMenuPresenter} from "@/pages/user/menu/hooks/UseMenuPresenter";
import MenuTree, {MenuTreeSelectCallBack} from "@/components/user/menu/MenuTree";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {ValidateUtils} from "@codingapi/ui-framework";
import MenuTreeFormSelect from "@/components/user/menu/MenuTreeFormSelect";
import {Form, FormInput, FormSelect} from "@/components/form-pc";
import FormTempImageUpload from "@/components/oss/FormTempImageUpload";

const MenuManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useMenuPresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);

    const columns: ProColumns<any>[] = [
        {
            title: '菜单名称',
            dataIndex: 'title',
        },
        {
            title: '菜单编码',
            dataIndex: 'code',
        },
        {
            title: '菜单类型',
            dataIndex: 'type',
            valueEnum: {
                HOME: {text: '首页'},
                AREA: {text: '区域'},
                MENU: {text: '菜单'},
                APP: {text: '应用'},
            },
            valueType: 'select',
        },
        {
            title: '菜单地址',
            dataIndex: 'url',
        },
        {
            title: '页面路径',
            dataIndex: 'pagePath',
        },
        {
            title: '顺序号',
            dataIndex: 'sysIndex',
            search: false
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.sysEnabled) {
                            presenter.disableMenu(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enableMenu(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
                </Popconfirm>
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title={"确认删除?"}
                    onConfirm={async () => {
                        await handlerDelete(record.id);
                    }}
                >
                    <a
                        key="delete"
                    >
                        删除
                    </a>
                </Popconfirm>
            ]
        }
    ]

    const treeSelect: MenuTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateCurrentTree(nodes[0] ? nodes[0] : null)
    }

    const menuTreeComponent = useMemo(() => {
        return (<MenuTree key={state.treeRefreshVersion} onSelect={treeSelect}
                          selectedKeys={state.currentTreeNode ? [state.currentTreeNode.nodeId] : []}
                          defaultExpandAll={true}/>)
    }, [state.treeRefreshVersion]);
    const handlerSave = async (values: any) => {
        presenter?.saveMenu(values)
            .then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    const handlerDelete = async (id: any) => {
        presenter?.deleteMenu(id).then(() => message.success("删除成功"))
            .catch(e => {
                message.error(e.message)
            })
    }


    return (<PageContainer>
        <Row>
            <Row.Col span={4}>
                {menuTreeComponent}
            </Row.Col>
            <Row.Col span={20}>
                <ProTable
                    data-testid={"menu-table"}
                    rowKey={"id"}
                    actionRef={actionRef}
                    columns={columns}
                    toolBarRender={() => {
                        return [
                            <Button
                                data-testid={"flow-add-btn"}
                                type={"primary"}
                                onClick={() => {
                                    presenter.showEditModal({parentNodeId: state.currentTreeNode?.nodeId})
                                }}
                            >新增</Button>
                        ]
                    }}
                    request={async (params, sort, filter) => {
                        return presenter.searchTable({
                            ...params,
                            parentNodeId: state.currentTreeNode?.nodeId
                        }, sort, filter, [
                            {
                                key: "title",
                                type: "LIKE"
                            },
                            {
                                key: "code",
                                type: "LIKE"
                            },
                            {
                                key: "type",
                                type: "EQUAL"
                            },
                            {
                                key: "url",
                                type: "LIKE"
                            },
                            {
                                key: "pagePath",
                                type: "LIKE"
                            }
                        ]);
                    }}
                />
                <Modal
                    data-testid={"menu-editor"}
                    title="编辑菜单"
                    open={state.editorVisible}
                    destroyOnHidden={true}
                    onClose={() => {
                        presenter.hideEditModal()
                    }}
                    onCancel={() => {
                        presenter.hideEditModal()
                    }}
                    onOk={async () => {
                        await form.submit();
                    }}
                >
                    <Form
                        form={form}
                        layout={"vertical"}
                        onFinish={handlerSave}
                    >
                        <FormInput
                            name={"id"}
                            hidden={true}
                        />
                        <MenuTreeFormSelect name={"parentNodeId"} label={"上级菜单"}
                                            defaultExpandAll={true}
                                            itemProps={{allowClear: true}}
                        />
                        <FormInput
                            name={"title"}
                            label={"菜单名称"}
                            required={true}
                            validateFunction={ValidateUtils.validateNotEmpty}
                        />
                        <FormInput
                            name={"code"}
                            label={"菜单编码"}
                            required={true}
                            validateFunction={ValidateUtils.validateNotEmpty}
                        />
                        <FormSelect
                            name={"type"}
                            label={"菜单类型"}
                            required={true}
                            options={[
                                {label: '首页', value: 'HOME'},
                                {label: '模块', value: 'MODULE'},
                                {label: '菜单', value: 'MENU'},
                                {label: '应用', value: 'APP'},
                            ]}
                            validateFunction={ValidateUtils.validateNotEmpty}
                        />
                        <FormSelect
                            name={"display"}
                            label={"展示方式"}
                            options={[
                                {label: '伸缩', value: 'EXPAND'},
                                {label: '弹出', value: 'POPUP'},
                            ]}
                        />
                        <FormInput
                            name={"image"}
                            label={"菜单图片"}
                        />
                        <FormInput
                            name={"url"}
                            label={"菜单地址"}
                        />
                        <FormInput
                            name={"pagePath"}
                            label={"页面路径"}
                        />
                        <FormInput
                            name={"sysIndex"}
                            label={"顺序号"}
                        />
                    </Form>
                </Modal>
            </Row.Col>
        </Row>
    </PageContainer>);
};

export default MenuManagePage;