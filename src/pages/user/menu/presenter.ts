import {MenuApi, MenuState} from "@/pages/user/menu/type";
import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/menu";

export default class MenuPresenter {
    private readonly api: MenuApi;
    private readonly dispatch: Dispatch<MenuState>;
    private state: MenuState;

    public constructor(state: MenuState, api: MenuApi, dispatch: Dispatch<MenuState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: MenuState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public refreshTree = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                treeRefreshVersion: prevState.treeRefreshVersion + 1
            }
        })
    }

    public updateCurrentTree = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public showEditModal = (record: any) => {

        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record,
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public deleteMenu = async (id: any) => {
        const res = await this.api.delete(id)
        if (res.success) {
            this.refreshTable();
            this.refreshTree();

        } else {
            throw new Error(res.message)
        }
    }

    public saveMenu = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
            this.refreshTree();
        } else {
            throw new Error(res.message)
        }
    }

    public enableMenu = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableMenu = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }
}