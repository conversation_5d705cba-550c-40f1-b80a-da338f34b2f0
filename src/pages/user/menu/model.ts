import {deleteMenu, disableMenu, enableMenu, queryMenuPage, saveMenu} from "@/api/user/menu";
import {MenuApi} from "@/pages/user/menu/type";


export class MenuApiImpl implements MenuApi {
    disable(id: any): any {
        return disableMenu(id);
    }

    enable(id: any): any {
        return enableMenu(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryMenuPage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveMenu(params);
    }

    delete(id: any): any {
        return deleteMenu(id);
    }

}