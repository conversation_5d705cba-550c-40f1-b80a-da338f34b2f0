import {UnitState} from "@/pages/user/unit/type";
import {useEffect, useRef, useState} from "react";
import UnitPresenter from "@/pages/user/unit/presenter";
import {UnitApiImpl} from "@/pages/user/unit/model";


const initialState: UnitState = {
    editorVisible: false,
    tableRefreshVersion: 0,
    treeRefreshVersion: 0,
    currentEditRecord: null,
    currentTreeNode: null
}

export function useUnitPresenter() {

    const [state, dispatch] = useState<UnitState>(initialState);
    const presenterRef = useRef<UnitPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new UnitPresenter(state, new UnitApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}