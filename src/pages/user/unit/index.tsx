import OrgTree, {OrgTreeSelectCallBack} from "@/components/user/org/OrgTree";
import {message, Switch} from "antd";
import React, {useEffect, useMemo} from "react";
import {ActionType, PageContainer, ProColumns,} from "@ant-design/pro-components";
import {Button, Modal, Popconfirm, ProTable, Row} from "@itm/components-pc";
import {ValidateUtils} from "@codingapi/ui-framework";
import {useUnitPresenter} from "@/pages/user/unit/hooks/UseUnitPresenter";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import {Form, FormInput} from "@/components/form-pc";

const UnitManagePage = () => {

    const form = Form.useForm();

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useUnitPresenter();

    useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    useEffect(() => {
        if (state.currentEditRecord) {
            form.setFieldsValue(state.currentEditRecord)
        } else {
            form.reset()
        }
    }, [form, state.currentEditRecord]);


    const columns: ProColumns[] = [
        {
            title: '单位名称',
            dataIndex: 'name',
        },
        {
            title: '单位编码',
            dataIndex: 'code',
        },
        {
            title: '顺序号',
            dataIndex: 'sysIndex',
        },
        {
            title: '调整启用状态',
            valueType: 'option',
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        if (record.sysEnabled) {
                            presenter.disableUnit(record.id).then(() => message.success("禁用成功")).catch(e => console.log(e.message))
                        } else {
                            presenter.enableUnit(record.id).then(() => message.success("启用成功")).catch(e => console.log(e.message))
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked value={record.sysEnabled} />
                </Popconfirm>
            ]
        },
        {
            title: '操作',
            valueType: 'option',
            render: (_: any, record: any) => [

                <a
                    key="editable"
                    onClick={() => {
                        presenter.showEditModal(record)
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title={"确认删除?"}
                    onConfirm={async () => {
                        await handlerDelete(record.id);
                    }}
                >
                    <a
                        key="delete"
                    >
                        删除
                    </a>
                </Popconfirm>
            ]
        }
    ]

    const treeSelect: OrgTreeSelectCallBack = (selectedKeys, nodes) => {
        presenter.updateCurrentOrg(nodes[0] ? nodes[0] : null)
    }

    const orgTreeComponent = useMemo(() => {
        return (<OrgTree key={state.treeRefreshVersion} query={{}} onSelect={treeSelect}
                         defaultSelectedKeys={state.currentTreeNode ? [state.currentTreeNode.nodeId] : []}
                         defaultExpandAll={false}/>)
    }, [state.treeRefreshVersion]);

    const handlerSave = async (values: any) => {
        presenter.saveUnit(values)
            .then(() => message.success("保存成功"))
            .catch(e => {
                message.error(e.message)
            })

    }

    const handlerDelete = async (id: any) => {
        presenter.deleteUnit(id).then(() => message.success("删除成功"))
            .catch(e => {
                message.error(e.message)
            })
    }

    return (
        <PageContainer>
            <Row>
                <Row.Col span={4}>
                    {orgTreeComponent}
                </Row.Col>
                <Row.Col span={20}>
                    <ProTable
                        data-testid={"user-table"}
                        rowKey={"id"}
                        actionRef={actionRef}
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                <Button
                                    data-testid={"flow-add-btn"}
                                    type={"primary"}
                                    onClick={() => {
                                        presenter.showEditModal({parentNodeId: state.currentTreeNode?.nodeId})
                                    }}
                                >新增</Button>
                            ]
                        }}
                        request={async (params, sort, filter) => {
                            return presenter.searchTable({
                                ...params,
                                parentNodeId: state.currentTreeNode?.nodeId
                            }, sort, filter, [
                                {
                                    key: "name",
                                    type: "LIKE"
                                },
                                {
                                    key: "code",
                                    type: "LIKE"
                                }
                            ]);
                        }}
                    />
                    <Modal
                        data-testid={"user-editor"}
                        title="编辑用户"
                        open={state.editorVisible}
                        destroyOnHidden={true}
                        onClose={() => {
                            presenter.hideEditModal()
                        }}
                        onCancel={() => {
                            presenter.hideEditModal()
                        }}
                        onOk={async () => {
                            await form.submit();
                        }}
                    >
                        <Form
                            form={form}
                            layout={"vertical"}
                            onFinish={handlerSave}
                        >
                            <FormInput
                                name={"id"}
                                hidden={true}
                            />

                            <FormInput
                                name={"name"}
                                label={"单位名称"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />
                            <FormInput
                                name={"fullName"}
                                label={"单位全称"}
                            />
                            <FormInput
                                name={"shortName"}
                                label={"单位简称"}
                            />

                            <FormInput
                                name={"code"}
                                label={"单位编码"}
                                required={true}
                                validateFunction={ValidateUtils.validateNotEmpty}
                            />

                            <FormOrgTreeSelect label={"上级单位"} name={"parentNodeId"}
                                               query={{needDept: false}}
                                               selectable={["UNIT"]} defaultExpandAll={false}/>
                            <FormInput
                                name={"sysIndex"}
                                label={"顺序号"}
                            />
                        </Form>
                    </Modal>
                </Row.Col>
            </Row>
        </PageContainer>
    )
}

export default UnitManagePage