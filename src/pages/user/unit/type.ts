import {TreeData} from "@/api/user/org";

export interface UnitState {
    editorVisible: boolean;
    tableRefreshVersion: number;
    treeRefreshVersion: number;
    currentEditRecord: any;
    currentTreeNode: TreeData | null;
}

export interface UnitApi {
    page: (params: any, sort: any, filter: any, match: any[]) => Promise<any>;
    save: (params: any) => Promise<any>;
    delete: (id: any) => Promise<any>;
    enable: (id: any) => Promise<any>;
    disable: (id: any) => Promise<any>;
}