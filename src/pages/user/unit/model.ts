import {deleteUnit, disableUnit, enableUnit, queryUnitPage, saveUnit} from "@/api/user/unit";
import {UnitApi} from "@/pages/user/unit/type";


export class UnitApiImpl implements UnitApi {
    disable(id: any): any {
        return disableUnit(id);
    }

    enable(id: any): any {
        return enableUnit(id);
    }

    page(params: any, sort: any, filter: any, match: any[]): Promise<any> {
        return queryUnitPage(params, sort, filter, match);
    }

    save(params: any): any {
        return saveUnit(params);
    }

    delete(id: any): any {
        return deleteUnit(id);
    }

}