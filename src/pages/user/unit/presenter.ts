import {UnitApi, UnitState} from "@/pages/user/unit/type";
import {Dispatch} from "@/utils/mvvm";
import {TreeData} from "@/api/user/org";

export default class UnitPresenter {
    private readonly api: UnitApi;
    private readonly dispatch: Dispatch<UnitState>;
    private state: UnitState;

    public constructor(state: UnitState, api: UnitApi, dispatch: Dispatch<UnitState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: UnitState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public refreshTree = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                treeRefreshVersion: prevState.treeRefreshVersion + 1
            }
        })
    }

    public updateCurrentOrg = (node: TreeData | null) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                currentTreeNode: node
            }
        });
        this.refreshTable();
    }

    public showEditModal = (record: any) => {

        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: true,
                currentEditRecord: record
            }
        });
    }

    public hideEditModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editorVisible: false,
                currentEditRecord: null
            }
        });
    }

    public deleteUnit = async (id: any) => {
        const res = await this.api.delete(id)
        if (res.success) {
            this.refreshTable();
            this.refreshTree();

        } else {
            throw new Error(res.message)
        }
    }

    public saveUnit = async (record: any) => {
        const res = await this.api.save(record);
        if (res.success) {
            this.hideEditModal();
            this.refreshTable();
            this.refreshTree();
        } else {
            throw new Error(res.message)
        }
    }

    public enableUnit = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public disableUnit = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message)
        }

    }

    public searchTable = (params: any, sort: any, filter: any, match: any[]) => {
        return this.api.page(params, sort, filter, match);
    }
}