import React from "react";
import {InterInstitutionState} from "@/pages/collect/inner/types";
import {InterInstitutionPresenter} from "@/pages/collect/inner/presenter";
import {InterInstitutionApiImpl} from "@/pages/collect/inner/model";

// state状态的初始值
const initialState: InterInstitutionState = {
    searchTitleName: "",
    isMy: true,
    activeTab: "draft",
    searchPublish: [1,2],
    uploadVisible: false,
    deleteVisible: false,
    deleteId: 0,
    uploadFileKey: '',
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInterInstitutionPresenter() {
    const [state, dispatch] = React.useState<InterInstitutionState>(initialState);

    const presenterRef = React.useRef<InterInstitutionPresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InterInstitutionPresenter(state, dispatch, new InterInstitutionApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };

}
