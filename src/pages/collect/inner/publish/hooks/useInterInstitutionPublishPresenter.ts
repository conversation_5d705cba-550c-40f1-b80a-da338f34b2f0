import React from "react";
import {InterInstitutionPublishState} from "@/pages/collect/inner/publish/types";
import {InterInstitutionPublishPresenter} from "@/pages/collect/inner/publish/presenter";
import {InterInstitutionPublishApiImpl} from "@/pages/collect/inner/publish/model";

// state状态的初始值
const initialState: InterInstitutionPublishState = {
    loading: false,
    alreadySubmit: false,
    selectUserVisible: false,
    currentRoleId: null,
    currentSelectedUser: [],
    selectedInstitutionDetail: {
        recordId: 0,
        currentNodeCode: '',
        id: 0,
        title: '',
        number: '',
        pushOrgId: '',
        pushTime: 0,
        implTime: 0,
        status: 0,
        htmlContent: ''
    },
    viewType: 'html',
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInterInstitutionPublishPresenter() {
    const [state, dispatch] = React.useState<InterInstitutionPublishState>(initialState);

    const presenterRef = React.useRef<InterInstitutionPublishPresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InterInstitutionPublishPresenter(state, dispatch, new InterInstitutionPublishApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };

}
