/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface InterInstitutionPublishState {
    loading: boolean;
    alreadySubmit: boolean;
    selectUserVisible: boolean;
    currentRoleId: any;
    currentSelectedUser: any[];
    selectedInstitutionDetail: {
        recordId: number;
        currentNodeCode: string;
        id: number;
        title: string;
        number: string;
        pushOrgId: string;
        pushTime: number;
        implTime: number;
        status: number;
        htmlContent?: string;
    },
    viewType: string;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface InterInstitutionPublishApi {
    savePublishFormData: (body: any) => Promise<any>;
    getInstitutionDetail: (id: number) => Promise<any>;
    institutionReview: (body: any) => Promise<any>;

}
