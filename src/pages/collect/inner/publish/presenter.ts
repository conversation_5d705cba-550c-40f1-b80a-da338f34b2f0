import {InterInstitutionPublishApi, Dispatch, InterInstitutionPublishState} from "./types";
import {SelectableUser} from "@/components/user/choice/type";

export class InterInstitutionPublishPresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<InterInstitutionPublishState>;
    // 传入的Model对象
    private readonly api: InterInstitutionPublishApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: InterInstitutionPublishState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: InterInstitutionPublishState, dispatch: Dispatch<InterInstitutionPublishState>, api: InterInstitutionPublishApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('InterInstitutionPublishPresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: InterInstitutionPublishState) => {
        this.state = newState;
    }

    public savePublishFormData = (formData: any, institutionId: any) => {
        const flatValues = {
            id:institutionId,
            number: formData.doc.number,
            pushOrgId: formData.doc.pushOrgId,
            pushTime: formData.doc.pushTime,
            implTime: formData.doc.implTime,
            status: formData.doc.status
        };
        return this.api.savePublishFormData(flatValues);
    }
    public getInstitutionDetail = async (id: number) => {
        const res = await this.api.getInstitutionDetail(id);
        this.dispatch((prevState) => ({
            ...prevState,
            alreadySubmit: res.data.currentNodeCode != null && res.data.currentNodeCode != 'start',
            selectedInstitutionDetail: {
                recordId: res.data.recordId,
                currentNodeCode: res.data.currentNodeCode,
                id: res.data.id,
                title: res.data.title,
                number: res.data.number,
                pushOrgId: res.data.pushOrgId,
                pushTime: res.data.pushTime,
                implTime: res.data.implTime,
                status: res.data.status,
                htmlContent: res.data.htmlContent },
            loading: true,
        }));
    }

    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }

    public setPublishVisible =  (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: v,
            }
        });
    }
    public setViewType = (type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                viewType: type
            }
        })
    }

    public institutionReview = async (records: SelectableUser[]) => {
        const body = {
            reviewerId: records[0].id,
            institutionId: this.state.selectedInstitutionDetail.id,
            institutionName: this.state.selectedInstitutionDetail.title
        }
        await this.api.institutionReview(body)
    }

    public alreadySubmit = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: true
            }
        })
    }

}
