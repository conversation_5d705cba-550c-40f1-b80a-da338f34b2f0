import React, { useEffect } from "react";
import { DatePicker, Form, Select, Tabs } from "antd";
import { Button, Message } from "@itm/components-pc";
import { useNavigate } from "react-router-dom";
import { useInterInstitutionPublishPresenter } from "@/pages/collect/inner/publish/hooks/useInterInstitutionPublishPresenter";
import { useLocation } from "react-router-dom";
import { SelectableUser } from "@/components/user/choice/type";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {FormInput, FormRadio} from "@/components/form-pc";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import dayjs from "dayjs";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";
import "./index.scss";

const { Option } = Select;

interface LocationState {
  selectedRow: {
    id: number;
  };
}

const InnerInstitutionDetail: React.FC = () => {
  const { state, presenter } = useInterInstitutionPublishPresenter();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const location = useLocation() as { state: LocationState };
  const institutionId = location.state?.selectedRow?.id;

  useEffect(() => {
    if (institutionId) {
      presenter?.getInstitutionDetail(institutionId);
    }
  }, [institutionId]);

  useEffect(() => {
    if (state.selectedInstitutionDetail) {
      const formattedValues = {
        doc: {
          number: state.selectedInstitutionDetail.number,
          pushOrgId: state.selectedInstitutionDetail.pushOrgId,
          // 确保日期值转换为 Day.js 对象
          pushTime: state.selectedInstitutionDetail.pushTime
            ? dayjs(state.selectedInstitutionDetail.pushTime)
            : null,
          implTime: state.selectedInstitutionDetail.implTime
            ? dayjs(state.selectedInstitutionDetail.implTime)
            : null,
          status: String(state.selectedInstitutionDetail.status),
        },
      };
      form.setFieldsValue(formattedValues);
    }
  }, [state.selectedInstitutionDetail]);

  return (
    <div className="collect-publish-container">
      <div className="twoPopul_header_box">
        <Tabs
          activeKey={state.viewType}
          className="item_tabs"
          onChange={(key) =>
            presenter?.setViewType(key as "html" | "flowchart")
          }
          items={[
            {
              key: "html",
              label: "正文",
            },
            {
              key: "flowchart",
              label: "流程",
            },
          ]}
        />
        <div style={{ textAlign: "right" }}>
          {state.loading && !state.alreadySubmit && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  presenter?.setPublishVisible(true);
                }}
              >
                提交
              </Button>
              <Button
                type="primary"
                style={{ marginLeft: "8px" }}
                onClick={async () => {
                  const values = form.getFieldsValue();
                  if (values) {
                    const processedValues = {
                      ...values,
                      doc: {
                        ...values.doc,
                        pushTime: values.doc.pushTime
                          ? dayjs(values.doc.pushTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )
                          : null,
                        implTime: values.doc.implTime
                          ? dayjs(values.doc.implTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )
                          : null,
                      },
                    };
                    const res = await presenter?.savePublishFormData(
                      processedValues,
                      institutionId
                    );
                    if ((res as any)?.success) {
                      Message.success("保存成功");
                    } else {
                      Message.error("保存失败");
                    }
                  }
                }}
              >
                保存
              </Button>
            </>
          )}
          <Button
            style={{ marginLeft: "8px" }}
            onClick={() => navigate(-1)} // 返回上一页
          >
            关闭
          </Button>
        </div>
      </div>
      {state.viewType === "html" ? (
        <CustomSplitter
          leftDefaultSize={825}
          left={
            <div
              style={{
                transform: `scale(1.0)`,
                transformOrigin: "top left",
                width: "789px",
                overflow: "auto",
              }}
              dangerouslySetInnerHTML={{
                __html: state.selectedInstitutionDetail?.htmlContent || "",
              }}
            />
          }
          right={
            <>
              <div style={{ padding: '10px' }}>
                <Form form={form} layout="vertical">
                  <FormInput
                    name={["doc", "number"]}
                    label="制度文号"
                    placeholder="请输入文号"
                  />
                  <FormOrgTreeSelect
                    placeholder="请选择发文部门/单位"
                    label={"发文部门/单位"}
                    name={["doc", "pushOrgId"]}
                    query={{ needDept: true }}
                    selectable={["UNIT","DEPT"]}
                    defaultExpandAll={false}
                  />
                  <Form.Item name={["doc", "pushTime"]} label="发文时间">
                    <DatePicker style={{ width: "100%" }} />
                  </Form.Item>
                  <Form.Item name={["doc", "implTime"]} label="实施时间">
                    <DatePicker style={{ width: "100%" }} />
                  </Form.Item>
                  <FormRadio
                    name={["doc", "status"]}
                    label="制度状态"
                    options={[
                      { label: "现行制度", value: "3" },
                      { label: "暂行制度", value: "2" },
                      { label: "废止制度", value: "4" },
                    ]}
                  />
                </Form>
              </div>
            </>
          }
        />
      ) : (
        <FlowImage
          recordId={state.selectedInstitutionDetail.recordId}
          workCode="institution_inner_collect_review"
        />
      )}

      <UserChoiceModal
        visible={state.selectUserVisible}
        hide={presenter?.hideSelectUserModal ?? (() => {})}
        onFinish={async (records: SelectableUser[]) => {
          const values = form.getFieldsValue(); // 获取表单数据
          const processedValues = {
            ...values,
            doc: {
              ...values.doc,
              pushTime: values.doc.pushTime
                ? dayjs(values.doc.pushTime).format("YYYY-MM-DD HH:mm:ss")
                : null,
              implTime: values.doc.implTime
                ? dayjs(values.doc.implTime).format("YYYY-MM-DD HH:mm:ss")
                : null,
            },
          };
          const res = await presenter?.savePublishFormData(
            processedValues,
            institutionId
          );
          if (res.success) {
            presenter?.institutionReview(records);
            presenter?.setPublishVisible(false);
            presenter?.alreadySubmit();
            Message.success("提交成功");
          } else {
            Message.error("提交失败");
          }
        }}
        selectedUsers={state.currentSelectedUser}
      />
    </div>
  );
};

export default InnerInstitutionDetail;
