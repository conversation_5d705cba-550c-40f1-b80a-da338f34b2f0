import {InterInstitutionPublishApi} from "./types";
import {
    savePublishFormData, getInstitutionDetail, institutionReview
} from "@/api/collect/institution";

/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InterInstitutionPublishApiImpl implements InterInstitutionPublishApi {
    savePublishFormData(body: any): Promise<any> {
        return savePublishFormData(body);
    }

    getInstitutionDetail(id: number): Promise<any> {
        return getInstitutionDetail(id);
    }

    institutionReview(body: any): Promise<any> {
        return institutionReview(body);
    }



}