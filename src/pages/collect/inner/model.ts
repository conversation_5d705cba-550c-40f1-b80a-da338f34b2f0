import {InterInstitutionApi} from "./types";
import {
    deleteInterInstitution,
    interList, uploadInterInstitutionFile

} from "@/api/collect/institution";

/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InterInstitutionApiImpl implements InterInstitutionApi {

    loadInterInstitutionTable(params: any, sort: any, filter: any): Promise<any> {
        return interList(params, sort, filter, []);
    }

    uploadInterInstitutionFile(body: any): Promise<any> {
        return uploadInterInstitutionFile(body);
    }

    deleteInterInstitution(formData: { id: number }): Promise<any> {
        return deleteInterInstitution(formData);
    }
}