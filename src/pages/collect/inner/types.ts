/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;
interface uploadedFileProps {
    filename: string;
    base64: string;
}
/**
 * 数据状态类型的定义
 */
export interface InterInstitutionState {
    searchTitleName: string;
    isMy: boolean;
    searchPublish: number[];
    activeTab: string;
    uploadVisible: boolean;
    deleteVisible: boolean;
    deleteId: number;
    uploadFileKey: string;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface InterInstitutionApi {
    loadInterInstitutionTable: (params: any, sort: any, filter: any) => Promise<any>;
    uploadInterInstitutionFile: (body: any) => Promise<any>;
    deleteInterInstitution: (formData: { id: number }) => Promise<any>;
}
