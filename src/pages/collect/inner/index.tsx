import React, {useRef} from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import {Radio} from "antd";
import {useNavigate} from 'react-router-dom';
import {Button, Modal, Input, Message, Tabs} from "@itm/components-pc";
import moment from "moment/moment";
import {useInterInstitutionPresenter} from "@/pages/collect/inner/hooks/useOuterInstitutionPresenter";
import OssFileUploader from "@/components/oss/OssFileUpload";

const {TabPane} = Tabs;

const CollectPage: React.FC = () => {
    const navigate = useNavigate();
    const actionRef = useRef<ActionType>();
    const {state, presenter} = useInterInstitutionPresenter();

    React.useEffect(() => {
        actionRef.current?.reload();
    }, []);

    const handleSearch = () => {
        actionRef.current?.reload();
    };

    const handleReset = () => {
        presenter?.resetSearch();
        actionRef.current?.reload();
    };

    const handleDeleteOk = async (id: number) => {

        try {
            const res = await presenter?.deleteInterInstitution(id);
            console.log('删除结果：', res)
            if ((res as any)?.success) {
                Message.success('删除成功');
                actionRef.current?.reload();
            } else {
                Message.error('删除失败');
            }
        } catch (error) {
            Message.error('请求异常，请重试');
            console.error('删除制度失败:', error);
        }

        presenter?.setDeleteVisible(false);
    };

    const columns = [
        {
            title: "序号",
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "id",
            dataIndex: "id",
            hidden: true,

        },
        {
            title: "制度标题",
            dataIndex: "title",
            render: (text: string) => (
                <span title={text} style={{cursor: 'pointer'}}>
                {text?.length > 20 ? `${text.slice(0, 20)}...` : text}
           </span>
            ),
        },
        {title: "制度文号", dataIndex: "number"},
        {title: "制度类型", dataIndex: "type", hidden: true},
        {title: "层级", dataIndex: "level", hidden: true},
        {
            title: "发文时间",
            dataIndex: "pushTime",
            render: (timestamp: number | string | null | undefined) => {
                const isValidTimestamp = timestamp && !isNaN(Number(timestamp)) && moment(Number(timestamp)).isValid();
                return isValidTimestamp ? moment(Number(timestamp)).format("YYYY-MM-DD HH:mm:ss") : "-";
            },
        },
        {
            title: "制度状态",
            dataIndex: "status",
            render: (status: number) => {
                switch (status) {
                    case 3:
                        return "现行制度";
                    case 2:
                        return "暂行制度";
                    case 4:
                        return "废止制度";
                    default:
                        return "-";
                }
            },
        },
        {title: "公开", dataIndex: "isPublic", hidden: true},
        {title: "发文单位", dataIndex: "pushOrgName"},
        {title: "创建人", dataIndex: "createUserName"},
        {
            title: "发布状态",
            dataIndex: "publishStatus",
            render: (status: number) => {
                switch (status) {
                    case 1:
                        return "未发布";
                    case 2:
                        return "审核中";
                    case 3:
                        return "已发布";
                    default:
                        return "-";
                }
            },
        },
        {
            title: "操作",
            valueType: "option",
            render: (text: any, record: any) => {
                const actions = [];
                // 根据 publishStatus 切换按钮名称
                const buttonLabel = record.publishStatus === 1 ? '发布' : '详情';
                actions.push(
                    <a
                        key="publish"
                        onClick={() => {
                            navigate(`/collect/inner/publish`, {state: {selectedRow: record}});
                        }}
                    >
                        {buttonLabel}
                    </a>
                );
                if (record.publishStatus === 1){
                    actions.push(
                        <a key="delete" onClick={() => {
                            presenter?.setDeleteId(record.id)
                            presenter?.setDeleteVisible(true)
                        }
                        }>
                            删除
                        </a>
                    );
                }

                return actions;
            },
        },
    ] as any[];


    return (
        <PageContainer>
            <Tabs
                activeKey={state.activeTab}
                onChange={(key) => {
                    presenter?.setActiveTab(key)
                    if (key === 'draft') {
                        presenter?.setPublishStatus([1, 2])
                    } else {
                        presenter?.setPublishStatus([3])
                    }
                    presenter?.setIsMy(true);
                }}
                style={{marginTop: 16}}
            >
                <TabPane tab="草稿" key="draft"/>
                <TabPane tab="已发布" key="published"/>
            </Tabs>

            <ProTable
                key={state.activeTab}
                actionRef={actionRef}
                toolBarRender={() => {
                    const buttons = [];

                    if (state.activeTab === 'draft') {
                        buttons.push(
                            <Button
                                key="upload"
                                type="primary"
                                onClick={() => {
                                    presenter?.resetUploadFile()
                                    presenter?.setUploadVisible(true)
                                }}
                            >
                                制度上传
                            </Button>
                        );
                    }

                    buttons.push(
                        <React.Fragment key="search-options">
                            <span style={{marginRight: 8, fontWeight: 500}}>数据来源：</span>
                            <Radio.Group
                                value={state.isMy ? "mine" : "all"}
                                onChange={(e) => {
                                    const isMine = e.target.value === 'mine';
                                    presenter?.setIsMy(isMine);
                                    actionRef.current?.reload();
                                }}
                                style={{marginRight: 8}}
                            >
                                <Radio value="mine">我的</Radio>
                                <Radio value="all">全部</Radio>
                            </Radio.Group>

                            <Input
                                key="fileName"
                                placeholder="请输入关键词"
                                value={state.searchTitleName}
                                onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                                style={{width: 200, marginRight: 8}}
                            />
                        </React.Fragment>
                    );

                    buttons.push(
                        <Button key="search" type="primary" onClick={handleSearch}>
                            搜索
                        </Button>,
                        <Button key="reset" onClick={handleReset}>
                            重置
                        </Button>
                    );

                    return buttons;
                }}

                columns={columns}
                search={false}
                request={async (params, sort, filter) => {
                    return presenter?.searchInterInstitutionTable(params, sort, filter);
                }}
            />

            <Modal
                open={state.uploadVisible}
                title="上传文件"
                onCancel={() => {
                    presenter?.setUploadVisible(false);
                }}
                onOk={async () => {
                    presenter?.uploadInterInstitutionFile().then(() => {
                        Message.success("上传成功")
                        presenter?.setUploadVisible(false);
                    });
                }}
            >
                <OssFileUploader
                    label="选择文件"
                    uploaderMaxCount={100}
                    uploaderAccept={".docx"}
                    bucket={"document"} // 如有实际bucket请替换
                    ossFileKey={state.uploadFileKey}
                    onKeyChange={(key: string) => presenter?.setUploadFileKey(key)}
                />
            </Modal>
            <Modal
                title="确认删除"
                open={state.deleteVisible}
                onOk={() => handleDeleteOk(state.deleteId)}
                onCancel={() => presenter?.setDeleteVisible(false)}
            >
            </Modal>
        </PageContainer>
    );
};

export default CollectPage;
