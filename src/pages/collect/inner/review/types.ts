/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface InterInstitutionReviewState {
    alreadySubmit: boolean;
    reviewPassVisible: boolean;
    reviewRejectVisible: boolean;
    selectedInstitutionDetail: {
        recordId: number;
        currentNodeCode: string;
        number: string;
        pushOrgId: string;
        pushTime: number;
        implTime: number;
        status: number;
        htmlContent?: string;
    },
    viewType: string;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface InterInstitutionReviewApi {
    getInstitutionDetail: (id: number) => Promise<any>;
    getFlowRecordDetail: (id?: any, workCode?: any) => Promise<any>,
    passInstitutionReview: (body: any) => Promise<any>;
    rejectedInstitutionReview: (body: any) => Promise<any>;
}
