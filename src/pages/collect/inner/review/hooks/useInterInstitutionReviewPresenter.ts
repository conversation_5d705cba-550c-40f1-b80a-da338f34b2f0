import React from "react";
import {InterInstitutionReviewState} from "@/pages/collect/inner/review/types";
import {InterInstitutionReviewPresenter} from "@/pages/collect/inner/review/presenter";
import {InterInstitutionReviewApiImpl} from "@/pages/collect/inner/review/model";

// state状态的初始值
const initialState: InterInstitutionReviewState = {
    alreadySubmit: false,
    reviewPassVisible: false,
    reviewRejectVisible: false,
    selectedInstitutionDetail: {
        recordId: 0,
        currentNodeCode: '',
        number: '',
        pushOrgId: '',
        pushTime: 0,
        implTime: 0,
        status: 0,
        htmlContent: ''
    },
    viewType: 'html'
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useInterInstitutionReviewPresenter() {
    const [state, dispatch] = React.useState<InterInstitutionReviewState>(initialState);

    const presenterRef = React.useRef<InterInstitutionReviewPresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InterInstitutionReviewPresenter(state, dispatch, new InterInstitutionReviewApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };

}
