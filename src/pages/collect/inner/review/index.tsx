import React, {useEffect} from "react";
import {DatePicker, Form, Select, Space, Tabs} from "antd";
import {Button, Message, Modal} from "@itm/components-pc";
import {
    useInterInstitutionReviewPresenter
} from "@/pages/collect/inner/review/hooks/useInterInstitutionReviewPresenter";
import {FormInput, FormRadio} from "@/components/form-pc";
import FormOrgTreeSelect from "@/components/user/org/FormOrgTreeSelect";
import {NoticeFormProps} from "@/components/user/notice/NoticeModalView";
import CustomSplitter from "@/components/CustomSplitter";
import dayjs from "dayjs";
import {FlowImage} from "@/components/flowImage";

const {Option} = Select;

const InnerInstitutionReview: React.FC<NoticeFormProps> = (props) => {
    const {state, presenter} = useInterInstitutionReviewPresenter();
    const [form] = Form.useForm();

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            currentNodeCode: "",
            reviewerId: 0,
            institutionId: 0,
            institutionName: "",
        };
    const recordId = customParams.recordId;
    const currentNodeCode = customParams.currentNodeCode;
    const reviewerId = customParams.reviewerId;
    const institutionId = customParams.institutionId;
    const institutionName = customParams.institutionName;

    useEffect(() => {
        if (institutionId) {
            presenter?.getInstitutionDetail(institutionId);
            presenter?.getFlowRecordDetail(recordId);
        }
    }, [institutionId]);

    useEffect(() => {
        if (state.selectedInstitutionDetail) {
            const formattedValues = {
                doc: {
                    number: state.selectedInstitutionDetail.number,
                    pushOrgId: state.selectedInstitutionDetail.pushOrgId,
                    // 确保日期值转换为 Day.js 对象
                    pushTime: state.selectedInstitutionDetail.pushTime
                        ? dayjs(state.selectedInstitutionDetail.pushTime)
                        : null,
                    implTime: state.selectedInstitutionDetail.implTime
                        ? dayjs(state.selectedInstitutionDetail.implTime)
                        : null,
                    status: String(state.selectedInstitutionDetail.status),
                },
            };
            form.setFieldsValue(formattedValues);
        }
    }, [state.selectedInstitutionDetail]);

    return (
        <>
            <div className="sevenContractDrafting_header_box">
                <Tabs
                    activeKey={state.viewType}
                    className="item_tabs"
                    onChange={(key) =>
                        presenter?.setViewType(key as "html" | "flowchart")
                    }
                    items={[
                        {
                            key: "html",
                            label: "正文",
                        },
                        {
                            key: "flowchart",
                            label: "流程",
                        },
                    ]}
                />
                <Space>
                    <Button
                        style={{
                            backgroundColor: "#c71114",
                            borderColor: "#c71114",
                            color: "#ffffff",
                        }}
                        type="primary"
                        disabled={state.alreadySubmit}
                        onClick={() => {
                            presenter?.reviewRejectVisible(true);
                        }}
                    >
                        驳回
                    </Button>
                    <Button
                        type="primary"
                        disabled={state.alreadySubmit}
                        style={{marginLeft: "8px"}}
                        onClick={() => {
                            presenter?.reviewPassVisible(true);
                        }}
                    >
                        通过
                    </Button>
                </Space>
            </div>

            {state.viewType === "html" ? (
                <CustomSplitter
                    leftDefaultSize={825}
                    style={{padding: 12}}
                    left={
                        <div
                            style={{
                                transform: `scale(1.0)`,
                                transformOrigin: "top left", // 设置缩放原点为左上角
                                width: "789px",
                                height: "calc(100vh - 120px)",
                            }}
                            dangerouslySetInnerHTML={{
                                __html: state.selectedInstitutionDetail?.htmlContent || "",
                            }}
                        />
                    }
                    right={
                        <>
                            <div style={{marginLeft: "10px"}}>
                                <Form form={form} layout="vertical">
                                    <FormInput
                                        name={["doc", "number"]}
                                        label="制度文号"
                                        placeholder="请输入文号"
                                    />
                                    <FormOrgTreeSelect
                                        label={"发文部门/单位"}
                                        name={["doc", "pushOrgId"]}
                                        query={{needDept: true}}
                                        selectable={["UNIT"]}
                                        defaultExpandAll={false}
                                    />
                                    <Form.Item name={["doc", "pushTime"]} label="发文时间">
                                        <DatePicker style={{width: "100%"}}/>
                                    </Form.Item>
                                    <Form.Item name={["doc", "implTime"]} label="实施时间">
                                        <DatePicker style={{width: "100%"}}/>
                                    </Form.Item>
                                    <FormRadio
                                        name={["doc", "status"]}
                                        label="制度状态"
                                        options={[
                                            {label: "现行制度", value: "3"},
                                            {label: "暂行制度", value: "2"},
                                            {label: "废止制度", value: "4"},
                                        ]}
                                    />
                                </Form>
                            </div>
                        </>
                    }
                />
            ) : (
                <FlowImage recordId={recordId}/>
            )}
            <Modal
                open={state.reviewPassVisible}
                title="确认通过"
                onCancel={() => presenter?.reviewPassVisible(false)}
                onOk={async () => {
                    await presenter?.reviewPass(
                        recordId,
                        reviewerId,
                        institutionId,
                        institutionName
                    );
                    presenter?.reviewPassVisible(false);
                    presenter?.setAlreadySubmit();
                    Message.success("提交成功");
                }}
            ></Modal>
            <Modal
                open={state.reviewRejectVisible}
                title="确认驳回"
                onCancel={() => presenter?.reviewRejectVisible(false)}
                onOk={async () => {
                    await presenter?.reviewReject(
                        recordId,
                        reviewerId,
                        institutionId,
                        institutionName
                    );
                    presenter?.reviewRejectVisible(false);
                    presenter?.setAlreadySubmit();
                    Message.success("驳回成功");
                }}
            ></Modal>
        </>
    );
};

export default InnerInstitutionReview;
