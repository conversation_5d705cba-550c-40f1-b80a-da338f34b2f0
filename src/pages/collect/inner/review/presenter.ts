import {InterInstitutionReviewApi, Dispatch, InterInstitutionReviewState} from "./types";
import {SelectableUser} from "@/components/user/choice/type";

export class InterInstitutionReviewPresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<InterInstitutionReviewState>;
    // 传入的Model对象
    private readonly api: InterInstitutionReviewApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: InterInstitutionReviewState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: InterInstitutionReviewState, dispatch: Dispatch<InterInstitutionReviewState>, api: InterInstitutionReviewApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('InterInstitutionReviewPresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: InterInstitutionReviewState) => {
        this.state = newState;
    }

    public getInstitutionDetail = async (id: number) => {
        const res = await this.api.getInstitutionDetail(id);
        this.dispatch((prevState) => ({
            ...prevState,
            selectedInstitutionDetail: {
                recordId: 0,
                currentNodeCode: res.data.currentNodeCode,
                number: res.data.number,
                pushOrgId: res.data.pushOrgId,
                pushTime: res.data.pushTime,
                implTime: res.data.implTime,
                status: res.data.status,
                htmlContent: res.data.htmlContent
            }
        }));
    }

    public getFlowRecordDetail = async (id: number) => {
        const res = await this.api.getFlowRecordDetail(id);
        console.log( res)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: res.data?.flowRecord.done
            }
        })
    }

    public setViewType = (type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                viewType: type
            }
        })
    }

    public reviewPassVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                reviewPassVisible: v
            }
        })
    }

    public reviewRejectVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                reviewRejectVisible: v
            }
        })
    }

    public reviewPass = async (recordId: number, reviewerId: number, institutionId: number, institutionName: string) => {
        const body = {
            recordId: recordId,
            reviewerId: reviewerId,
            institutionId: institutionId,
            institutionName: institutionName
        }
        await this.api.passInstitutionReview(body)
    }

    public reviewReject = async (recordId: number, reviewerId: number, institutionId: number, institutionName: string) => {
        const body = {
            recordId: recordId,
            reviewerId: reviewerId,
            institutionId: institutionId,
            institutionName: institutionName
        }
        await this.api.rejectedInstitutionReview(body)
    }

    public setAlreadySubmit = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: true
            }
        })
    }

}
