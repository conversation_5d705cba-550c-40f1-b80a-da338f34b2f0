import {InterInstitutionReviewApi} from "./types";
import {
    getInstitutionDetail,
    passInstitutionReview,
    rejectedInstitutionReview,
} from "@/api/collect/institution";
import {detail} from "@/api/user/flow";

/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class InterInstitutionReviewApiImpl implements InterInstitutionReviewApi {
    getInstitutionDetail(id: number): Promise<any> {
        return getInstitutionDetail(id);
    }
    getFlowRecordDetail(id?: any, workCode?: any): Promise<any> {
        return detail(id, workCode);
    }

    passInstitutionReview(body: any): Promise<any> {
        return passInstitutionReview(body);
    }

    rejectedInstitutionReview(body: any): Promise<any> {
        return rejectedInstitutionReview(body);
    }


}