import {InterInstitutionApi, Dispatch, InterInstitutionState} from "./types";

export class InterInstitutionPresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<InterInstitutionState>;
    // 传入的Model对象
    private readonly api: InterInstitutionApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: InterInstitutionState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: InterInstitutionState, dispatch: Dispatch<InterInstitutionState>, api: InterInstitutionApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
    }

    // 更新状态数据
    public syncState = (newState: InterInstitutionState) => {
        this.state = newState;
    }
    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: "",
            }
        })
    }
    public setSearchFileName = (title: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: title
            }
        })
    }
    public searchInterInstitutionTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params,
            title: this.state.searchTitleName,
            isMy: this.state.isMy,
            publishStatus: this.state.searchPublish.join(','),
        }

        return this.api.loadInterInstitutionTable(searchParams, sort, filter);
    }

    public setActiveTab = (v: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                activeTab: v
            }
        })
    }
    public setPublishStatus = (v: number[]) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchPublish: v
            }
        })
    }

    public setIsMy = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                isMy: v
            }
        })
    }
    public setUploadVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadVisible: v
            }
        })
    }

    public resetUploadFile = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadFileKey: ''
            };
        });
    }

    public setUploadFileKey = (uploadFileKey: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadFileKey: uploadFileKey
            };
        });
    }

    public uploadInterInstitutionFile = async () => {
        const body = {
            originFileKeyList: (this.state.uploadFileKey || '').split(','),
        }
        await this.api.uploadInterInstitutionFile(body);
    }

    public dataURLtoBlob(dataUrl: string): Blob {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "";
        const bStr = atob(arr[1]);
        let n = bStr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bStr.charCodeAt(n);
        }
        return new Blob([u8arr], {type: mime});
    }

    public setDeleteVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                deleteVisible: v
            }
        })
    }
    public setDeleteId = (v: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                deleteId: v
            }
        })
    }
    public deleteInterInstitution = async (id: number): Promise<any> => {
        const formData = {
            id: id,
        };
        return await this.api.deleteInterInstitution(formData);
    };

}
