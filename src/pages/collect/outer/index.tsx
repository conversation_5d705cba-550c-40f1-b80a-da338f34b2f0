import React from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import {Button, Input,} from "@itm/components-pc";
import {useOuterInstitutionPresenter} from "@/pages/collect/outer/hooks/useOuterInstitutionPresenter";
import moment from 'moment';

const CollectPage = () => {

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useOuterInstitutionPresenter();

    React.useEffect(() => {
        actionRef.current?.reload();
        const loadRefreshStatus = async () => {
            const res = await presenter?.searchRefreshStatus();
            presenter?.setIsRefreshing((res as any)?.data);
        };

        loadRefreshStatus();

        // 设置定时器，每秒调用一次
        const intervalId = setInterval(() => {
            if (state.isRefreshing) {
                loadRefreshStatus();
            }
        }, 2000);

        // 组件卸载时清除定时器
        return () => {
            clearInterval(intervalId);
        };
    }, [state.isRefreshing]);

    const handleSearch = () => {
        actionRef.current?.reload();
    };

    const handleReset = () => {
        presenter?.resetSearch()
        actionRef.current?.reload();
    };

    const handleRefresh = async () => {
        const refreshing = await presenter?.searchRefreshStatus()
        if (!refreshing) return;
        presenter?.setIsRefreshing(true);
        await presenter?.refreshOuterInstitution();
    };


    const columns = [
        {
            title: "序号",
            width: 30,
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "制度标题",
            width: 500,
            dataIndex: "title",
            render: (text: string) => (
                <span title={text} style={{cursor: 'pointer'}}>
                {text?.length > 30 ? `${text.slice(0, 30)}...` : text}
           </span>
            ),
        },
        {
            title: "制定机关",
            width: 300,
            dataIndex: "pushOrgName",
            render: (text: string) => (
                <span title={text} style={{cursor: 'pointer'}}>
                {text?.length > 15 ? `${text.slice(0, 15)}...` : text}
           </span>
            ),
        },
        {title: "制度类型", dataIndex: "type"},
        {
            title: "时效性",
            dataIndex: "status",
            render: (status: number) => {
                switch (status) {
                    case 1:
                        return "未施行";
                    case 3:
                        return "现行有效";
                    case 4:
                        return "已废止";
                    default:
                        return "-";
                }
            },
        },

        {
            title: "发布日期",
            dataIndex: "pushTime",
            render: (timestamp: number | null | undefined) =>
                timestamp ? moment(timestamp).format("YYYY-MM-DD") : "-",
        },
        {
            title: "施行日期",
            dataIndex: "implTime",
            render: (timestamp: number | null | undefined) =>
                timestamp ? moment(timestamp).format("YYYY-MM-DD") : "-",
        },
        {title: "来源", width: 250, dataIndex: "source"},
    ] as any[];


    return (
        <>
            <PageContainer>
                <ProTable
                    scroll={{y: 800}}
                    actionRef={actionRef}
                    toolBarRender={() => [
                        <Button
                            key="reflash"
                            type="primary"
                            onClick={handleRefresh}
                            disabled={state.isRefreshing}
                        >
                            {state.isRefreshing ? '刷新中...' : '手动刷新'}
                        </Button>
                        ,
                        <React.Fragment key="search-options">
                            <Input
                                key="fileName"
                                placeholder="请输入关键词"
                                value={state.searchTitleName}
                                onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                                style={{width: 200, marginRight: 8}}
                            />
                        </React.Fragment>,

                        <Button key="search" type="primary" onClick={handleSearch}>
                            搜索
                        </Button>,
                        <Button key="reset" onClick={handleReset}>
                            重置
                        </Button>,
                    ]}
                    columns={columns}
                    search={false}
                    request={async (params, sort, filter) => {
                        return presenter?.searchOuterInstitutionTable(params, sort, filter);
                    }}
                />
            </PageContainer>


        </>
    );
};


export default CollectPage;