import {OuterInstitutionApi, Dispatch, OuterInstitutionState} from "./types";

export class OuterInstitutionPresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<OuterInstitutionState>;
    // 传入的Model对象
    private readonly api: OuterInstitutionApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: OuterInstitutionState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: OuterInstitutionState, dispatch: Dispatch<OuterInstitutionState>, api: OuterInstitutionApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('OuterInstitutionPresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: OuterInstitutionState) => {
        this.state = newState;
    }
    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: "",
            }
        })
    }
    public setSearchFileName = (title: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: title
            }
        })
    }
    public searchOuterInstitutionTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params,
            title: this.state.searchTitleName,
        }

        return this.api.loadOuterInstitutionTable(searchParams, sort, filter);
    }

    public searchRefreshStatus = () => {
        return this.api.outerInstitutionStatus();
    }

    public refreshOuterInstitution = () => {
        return this.api.refreshOuterInstitution();
    }

    public setIsRefreshing = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                isRefreshing: v
            }
        })
    }

}
