import React from "react";
import {OuterInstitutionState} from "@/pages/collect/outer/types";
import {OuterInstitutionPresenter} from "@/pages/collect/outer/presenter";
import {OuterInstitutionApiImpl} from "@/pages/collect/outer/model";

// state状态的初始值
const initialState: OuterInstitutionState = {
    searchTitleName: "",
    isRefreshing: false
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useOuterInstitutionPresenter() {
    const [state, dispatch] = React.useState<OuterInstitutionState>(initialState);

    const presenterRef = React.useRef<OuterInstitutionPresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new OuterInstitutionPresenter(state, dispatch, new OuterInstitutionApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };

}
