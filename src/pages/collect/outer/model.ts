import {OuterInstitutionApi} from "./types";
import {
    outerInstitutionStatus,
    outerList,
    refreshOuterInstitution,
} from "@/api/collect/institution";

/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class OuterInstitutionApiImpl implements OuterInstitutionApi {

    loadOuterInstitutionTable(params: any, sort: any, filter: any): Promise<any> {
        return outerList(params, sort, filter, []);
    }
    refreshOuterInstitution(): Promise<any> {
        return refreshOuterInstitution();
    }

    outerInstitutionStatus(): Promise<any> {
        return outerInstitutionStatus();
    }


}