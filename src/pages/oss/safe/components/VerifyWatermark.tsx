import React, {FC} from "react";
import {useSafeFiletPresenter} from "@/pages/oss/safe/hooks/useSafeFilePresenter";
import {Modal,Input, Message} from "@itm/components-pc";

interface VerifyWatermarkModalProps {
    verifyVisible: boolean;
    verifyResult?: (visible: boolean) => void;
}

const VerifyWatermarkModal: FC<VerifyWatermarkModalProps> = ({verifyVisible, verifyResult}) => {

    const {state, presenter} = useSafeFiletPresenter();
    const handleOk = async () => {
        if (!state.qrCodeValue) {
            return;
        }

        try {
            const res = await presenter?.verifyWatermark({qrContent: state.qrCodeValue});
            if ((res as any)?.success) {
                verifyResult?.(true);
                Message.success("验证通过");
            }
        } finally {
            presenter?.verifyVisible(false);
        }
    };

    const handleCancel = () => {
        presenter?.verifyVisible(false);
        presenter?.setQrCodeValue("");
        verifyResult?.(false);
    };

    return (
        <Modal
            title="防伪验证"
            open={verifyVisible}
            onOk={handleOk}
            onCancel={handleCancel}
        >
            <Input
                placeholder="请输入防伪二维码内容"
                value={state.qrCodeValue}
                onChange={(e) => presenter?.setQrCodeValue(e.target.value)}
            />
        </Modal>
    );
};

export default VerifyWatermarkModal;
