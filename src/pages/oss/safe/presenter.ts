import {FileApi, FileState, Dispatch} from "./types";

export class FilePresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<FileState>;
    // 传入的Model对象
    private readonly api: FileApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: FileState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: FileState, dispatch: Dispatch<FileState>, api: FileApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('SafeFilePresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: FileState) => {
        this.state = newState;
    }

    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchFileName: "",
                startTime: "",
                endTime: ""
            }
        })
    }

    public setPreviewData = (url: string, type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewUrl: url,
                previewType: type,
                previewVisible: true
            }
        })
    }

    public verifyVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                verifyVisible: v
            }
        })
    }

    public setSearchTime = (startTime: string, endTime: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                startTime: startTime,
                endTime: endTime
            }
        })
    }

    public setSearchFileName = (fileName: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchFileName: fileName
            }
        })
    }

    public setQrCodeValue = (qrCodeValue: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                qrCodeValue: qrCodeValue
            }
        })
    }

    public setPreviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                previewVisible: v
            }
        })
    }

    public searchSafeFileTable = (params: any, sort: any, filter: any) => {
        // 添加当前标签数据到查询参数中
        const searchParams = {
            ...params,
            fileName: this.state.searchFileName,
            startTime: this.state.startTime,
            endTime: this.state.endTime,
            bucket: "safe",
        }

        return this.api.loadSafeFileTable(searchParams, sort, filter);
    }

    public verifyWatermark = (body: any) => {
        return this.api.verifyWatermark(body);
    }
}
