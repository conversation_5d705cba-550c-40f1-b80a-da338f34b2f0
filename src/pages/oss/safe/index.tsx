import React from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import dayjs from "dayjs";
import {useSafeFiletPresenter} from "@/pages/oss/safe/hooks/useSafeFilePresenter";
import VerifyWatermark from "@/pages/oss/safe/components/VerifyWatermark";
import {Button, Modal, Input, Message} from "@itm/components-pc";
import {DatePicker} from "antd";

const SafeFileManagePage = () => {

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useSafeFiletPresenter();

    React.useEffect(() => {
        actionRef.current?.reload();
    }, []);

    const handleSearch = () => {
        actionRef.current?.reload();
    };

    const handleReset = () => {
        presenter?.resetSearch()
        actionRef.current?.reload();
    };


    const handlePreviewFile = (record: any) => {
        if (!record.fileUrl) {
            Message.error("文件地址不存在");
            return;
        }

        const fileExtension = record.fileType?.toLowerCase() || "";

        presenter?.setPreviewData(record.fileUrl, fileExtension);
    };

    const columns = [
        {title: "文件标识", dataIndex: "fileKey"},
        {title: "文件名", dataIndex: "fileName"},
        {title: "文件大小", dataIndex: "fileSize"},
        {title: "文件类型", dataIndex: "fileType"},
        {title: "创建时间", dataIndex: "createTime"},
        {
            title: "操作",
            valueType: "option",
            render: (text: any, record: any) => {
                const actions = [
                    <a key="download" onClick={() => handleDownload(record)}>
                        下载
                    </a>,
                ];

                const supportedPreviewTypes = ["jpg", "jpeg", "png", "gif"];
                const fileExtension = record.fileType?.toLowerCase() || "";

                if (supportedPreviewTypes.includes(fileExtension)) {
                    actions.push(
                        <a key="preview" onClick={() => handlePreviewFile(record)}>
                            预览
                        </a>
                    );
                }

                return actions;
            },
        },
    ] as any[];


    return (
        <>
            <PageContainer>
                <ProTable
                    actionRef={actionRef}
                    headerTitle={<h3 style={{margin: 0, fontWeight: "bold"}}>文件列表</h3>}
                    toolBarRender={() => [
                        <DatePicker.RangePicker
                            key="date"
                            style={{width: 400, marginRight: 8}}
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            value={
                                state.startTime && state.endTime
                                    ? [
                                        dayjs(parseInt(state.startTime)),
                                        dayjs(parseInt(state.endTime))
                                    ]
                                    : undefined
                            }
                            onChange={(dates) => {
                                if (dates && dates.length === 2) {
                                    presenter?.setSearchTime(dates[0]?.valueOf().toString() || "", dates[1]?.valueOf().toString() || "")
                                } else {
                                    presenter?.setSearchTime("", "")
                                }
                            }}
                        />
                        ,
                        <Input
                            key="fileName"
                            placeholder="请输入文件名"
                            value={state.searchFileName}
                            onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                            style={{width: 200, marginRight: 8}}
                        />,
                        <Button key="search" type="primary" onClick={handleSearch}>
                            搜索
                        </Button>,
                        <Button key="reset" onClick={handleReset}>
                            重置
                        </Button>,
                        <Button key="verify" type="primary" onClick={() => {
                            presenter?.setQrCodeValue(""); // 👈 清空上次输入内容
                            presenter?.verifyVisible(true);
                        }
                        }>
                            防伪验证
                        </Button>,
                    ]}
                    columns={columns}
                    search={false}
                    request={async (params, sort, filter) => {
                        return presenter?.searchSafeFileTable(params, sort, filter);
                    }}
                />
            </PageContainer>

            <VerifyWatermark
                verifyVisible={state.verifyVisible}
                verifyResult={(result) => {
                    presenter?.verifyVisible(false);
                }}
            />

            <Modal
                title="文件预览"
                open={state.previewVisible}
                onCancel={() => presenter?.setPreviewVisible(false)}
                footer={null}
                width="90%"
            >
                {state.previewUrl && state.previewType && (
                    <>
                        {["jpg", "jpeg", "png", "gif"].includes(state.previewType) && (
                            <img src={state.previewUrl} alt="预览" style={{width: "100%"}}/>
                        )}
                    </>
                )}
            </Modal>

        </>
    );
};


const handleDownload = (fileRecord: any) => {
    const {fileName, fileUrl} = fileRecord;
    if (!fileUrl) {
        Message.error("文件地址不存在");
        return;
    }

    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

export default SafeFileManagePage;