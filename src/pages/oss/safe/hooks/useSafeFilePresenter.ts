import React from "react";
import {FileState} from "@/pages/oss/safe/types";
import {FilePresenter} from "@/pages/oss/safe/presenter";
import {FileApiImpl} from "@/pages/oss/safe/model";

// state状态的初始值
const initialState: FileState = {
    searchFileName: "",
    startTime: "",
    endTime: "",
    verifyVisible: false,
    qrCodeValue: "",
    previewVisible: false,
    previewUrl: null,
    previewType: null,
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useSafeFiletPresenter() {
    const [state, dispatch] = React.useState<FileState>(initialState);

    const presenterRef = React.useRef<FilePresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new FilePresenter(state, dispatch, new FileApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
