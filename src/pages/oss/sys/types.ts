/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

interface uploadedFileProps {
    filename: string;
    base64: string;
}

/**
 * 数据状态类型的定义
 */
export interface FileState {
    searchFileName: string;
    startTime: string;
    endTime: string;
    uploadVisible: boolean;
    qrCodeValue: string;
    previewVisible: boolean;
    previewUrl: string | null;
    previewType: string | null;
    tableKey: string;
    uploadedFile: uploadedFileProps;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface FileApi {
    loadSysFileTable: (params: any, sort: any, filter: any) => Promise<any>;
    uploadSysFile: (formData: FormData) => Promise<any>;
    loadSysFiles: (ids: string) => Promise<any>;
}
