import React, {<PERSON>} from "react";
import {ValidateUtils} from "@codingapi/ui-framework";
import {useSysFiletPresenter} from "@/pages/oss/sys/hooks/useSysFilePresenter";
import {Modal,Message} from "@itm/components-pc";
import {Form, FormUploader} from "@/components/form-pc";
import {base64ToBlob, base64ToBlob2} from "@/utils/base64";
interface FileUploaderProps {
    bucket: string;
    uploadVisible: boolean;
    uploadResult?: (visible: boolean) => void;
}

const FileUploader: FC<FileUploaderProps> = ({bucket, uploadVisible, uploadResult}) => {

    const {state, presenter} = useSysFiletPresenter();

    const form = Form.useForm();
    const handleUploadFinish = async () => {
        if (!state.uploadedFile) {
            Message.error("请先选择并上传文件");
            return;
        }

        const {filename, base64} = state.uploadedFile;
        const blob = base64ToBlob2(base64);
        const file = new File([blob], filename, {type: blob.type});

        const formData = new FormData();
        formData.append("file", file);
        formData.append("bucket", bucket);

        try {
            const res = await presenter?.uploadSysFile(formData);
            if ((res as any)?.success) {
                Message.success("上传成功");
                presenter?.uploadVisible(false);
                uploadResult?.(true);
                form.reset()
                return;
            } else {
                Message.error("上传失败");
                form.reset()
                return;
            }
        } catch (error: any) {
            Message.error(error.Message || "上传过程中发生错误");
        }
    };

    return (
        <Modal
            open={uploadVisible}
            title="上传文件"
            onCancel={() => {
                presenter?.uploadVisible(false);
                uploadResult?.(false);
                form.reset()
            }}
            onOk={async () => {
                await form.submit();
                form.reset()
            }}
        >
            <Form form={form} onFinish={handleUploadFinish}>
                <FormUploader
                    name="avatar"
                    label="上传文件"
                    uploaderAccept="*"
                    validateFunction={ValidateUtils.validateNotEmpty}
                    onUploaderUpload={(filename, base64) => {
                        presenter?.setUploadFile(filename, base64);
                        return Promise.resolve({
                            id: "temp-id",
                            name: filename,
                            url: base64,
                        });
                    }}
                />
            </Form>
        </Modal>
    );
};


export default FileUploader;
