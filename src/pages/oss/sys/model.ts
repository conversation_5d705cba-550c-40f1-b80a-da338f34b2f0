import {FileApi} from "./types";
import {list, loadFiles, upload} from "@/api/oss/oss";


/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class FileApiImpl implements FileApi {

    loadSysFileTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    uploadSysFile(formData: FormData): Promise<any> {
        return upload(formData);
    }
    loadSysFiles(ids: string): Promise<any> {
        return loadFiles(ids);
    }
}