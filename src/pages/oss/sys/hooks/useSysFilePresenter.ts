import React from "react";
import {FileState} from "@/pages/oss/sys/types";
import {FilePresenter} from "@/pages/oss/sys/presenter";
import {FileApiImpl} from "@/pages/oss/sys/model";

// state状态的初始值
const initialState: FileState = {
    searchFileName: "",
    startTime: "",
    endTime: "",
    uploadVisible: false,
    qrCodeValue: "",
    previewVisible: false,
    previewUrl: null,
    previewType: null,
    tableKey: 'default',
    uploadedFile: {
        filename: "",
        base64: "",
    },
};

/**
 *  useDictPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useSysFiletPresenter() {
    const [state, dispatch] = React.useState<FileState>(initialState);

    const presenterRef = React.useRef<FilePresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new FilePresenter(state, dispatch, new FileApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
