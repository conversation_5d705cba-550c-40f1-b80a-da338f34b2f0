import React, {useCallback, memo} from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import dayjs from "dayjs";
import {useSysFiletPresenter} from "@/pages/oss/sys/hooks/useSysFilePresenter";
import FileUploader from "./components/FileUploader";
import {Button, Modal,Input, Message, Tabs} from "@itm/components-pc";
import {DatePicker} from "antd";
const {TabPane} = Tabs;

interface FileTableProps {
    tableType: string;
}

const SysFileManagePage: React.FC = () => {
    return (
        <PageContainer>
            <Tabs defaultActiveKey="document" style={{marginTop: 16}}>
                <TabPane tab="文档" key="document">
                    <FileTable tableType={"document"}/>
                </TabPane>
                <TabPane tab="媒体文件" key="media">
                    <FileTable tableType={"media"}/>
                </TabPane>
                <TabPane tab="临时文件" key="temp">
                    <FileTable tableType={"temp"}/>
                </TabPane>
            </Tabs>
        </PageContainer>
    );
};

const FileTable: React.FC<FileTableProps> = memo(({tableType}) => {

    const actionRef = React.useRef<ActionType>();

    const {state, presenter} = useSysFiletPresenter();

    const handleSearch = useCallback(() => {
        presenter?.refreshTable();
        actionRef.current?.reload();
    }, []);

    const handleReset = useCallback(() => {
        presenter?.resetSearch()
        actionRef.current?.reload();
    }, []);
    const handlePreviewFile = (record: any) => {
        if (!record.fileUrl) {
            Message.error("文件地址不存在");
            return;
        }
        const fileExtension = record.fileType?.toLowerCase() || "";
        presenter?.setPreviewData(record.fileUrl, fileExtension)
    };

    const columns = [
        {title: "文件标识", dataIndex: "fileKey"},
        {title: "文件名", dataIndex: "fileName"},
        {title: "文件大小", dataIndex: "fileSize"},
        {title: "文件类型", dataIndex: "fileType"},
        {title: "创建时间", dataIndex: "createTime"},
        {
            title: "操作",
            valueType: "option",
            render: (text: any, record: any) => {
                const actions = [
                    <a key="download" onClick={() => window.open(record.fileUrl)}>
                        下载
                    </a>,
                ];

                const supportedPreviewTypes = ["jpg", "jpeg", "png", "gif"];
                const fileExtension = record.fileType?.toLowerCase() || "";

                if (supportedPreviewTypes.includes(fileExtension)) {
                    actions.push(
                        <a key="preview" onClick={() => handlePreviewFile(record)}>
                            预览
                        </a>
                    );
                }

                return actions;
            },
        },
    ] as any[];

    return (
        <>
            <ProTable
                key={state.tableKey}
                actionRef={actionRef}
                headerTitle={<h3 style={{margin: 0, fontWeight: "bold"}}>文件列表</h3>}
                toolBarRender={() => [
                    <DatePicker.RangePicker
                        key="date"
                        style={{width: 400, marginRight: 8}}
                        showTime
                        format="YYYY-MM-DD HH:mm:ss"
                        value={
                            state.startTime && state.endTime
                                ? [
                                    dayjs(parseInt(state.startTime)),
                                    dayjs(parseInt(state.endTime))
                                ]
                                : undefined
                        }
                        onChange={(dates) => {
                            if (dates && dates.length === 2) {
                                presenter?.setSearchTime(dates[0]?.valueOf().toString() || "", dates[1]?.valueOf().toString() || "")
                            } else {
                                presenter?.setSearchTime("", "")
                            }
                        }}
                    />,
                    <Input
                        key="fileName"
                        placeholder="请输入文件名"
                        value={state.searchFileName}
                        onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                        style={{width: 200, marginRight: 8}}
                    />,
                    <Button key="search" type="primary" onClick={handleSearch}>
                        搜索
                    </Button>,
                    <Button key="reset" onClick={handleReset}>
                        重置
                    </Button>,
                    <Button
                        key="upload"
                        type="primary"
                        onClick={() => {
                            presenter?.uploadVisible(true);
                        }}
                    >
                        上传文件
                    </Button>,
                ]}
                columns={columns}
                search={false}
                request={async (params, sort, filter) => {
                    return presenter?.searchSafeFileTable(params, sort, filter, tableType);
                }}
            />

            <FileUploader
                bucket={tableType}
                uploadVisible={state.uploadVisible}
                uploadResult={(result) => {
                    presenter?.uploadVisible(false);
                    if (result) {
                        presenter?.refreshTable();
                    }
                }}
            />

            <Modal
                title="文件预览"
                open={state.previewVisible}
                onCancel={() => presenter?.previewVisible(false)}
                footer={null}
                width="90%"
            >
                {state.previewUrl && state.previewType && (
                    <>
                        {["jpg", "jpeg", "png", "gif"].includes(state.previewType) && (
                            <img src={state.previewUrl} alt="预览" style={{width: "100%"}}/>
                        )}
                    </>
                )}
            </Modal>
        </>
    );
});


export default SysFileManagePage;
