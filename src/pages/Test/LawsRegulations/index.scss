.general-manage-container,
.general-manage-container-filter {
  width: 100%;
  height: 100%;
  &::after {
    content: "";
    position: fixed;
    display: block;
    top: -19px;
    left: 0;
    width: 100%;
    height: 102%;
    background-image: url("@/assets/Images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    z-index: 0;
    opacity: 1;
  }

  .menu-item-1,
  .menu-item-2,
  .menu-item-3,
  .menu-item-4,
  .menu-item-5,
  .menu-item-6 {
    position: absolute;
    z-index: 9;
    background-color: transparent;
    height: 120px;
    width: 30px;
    cursor: pointer;
  }

  .menu-item-1 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(-90deg) translate(-130px, -37px);
  }

  .menu-item-2 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(-70deg) translate(-40px, -60px);
  }

  .menu-item-3 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(-20deg) translate(50px, -30px);
  }

  .menu-item-4 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(20deg) translate(100px, -90px);
  }

  .menu-item-5 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(70deg) translate(110px, -200px);
  }

  .menu-item-6 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(90deg) translate(130px, -200px);
  }
}

.general-manage-container-filter::after {
  // filter: blur(2px);
  // backdrop-filter: blur(2px);
  // -webkit-backdrop-filter: blur(2px);
  // /* 性能优化 */
  // will-change: transform;
  background-image: url("@/assets/Images/Home/background-empty.jpg");
}
