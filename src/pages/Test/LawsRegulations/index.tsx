/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-23 09:42:21
 * @Description:
 * @LastEditTime: 2025-07-25 08:33:06
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { useNavigate } from "react-router";
import "./index.scss";
export default function GeneralManage() {
  const navigate = useNavigate();

  const handlerNavigate = (key: number) => {
    const hash: Record<number, string> = {
      1: "/archive/institution/search/list",
      2: "/archive/institution/search/list",
      3: "/archive/institution/search/list",
      4: "/archive/institution/search/list",
      5: "/archive/institution/search/list",
      6: "/archive/institution/search/list",
    };
    navigate(hash[key]);
  };
  return (
    <div className="general-manage-container" id="general-manage-container">
      <div className="home-menus">
        {["1", "2", "3", "4", "5", "6"].map((menu) => (
          <div
            key={menu}
            className={`menu-item-${menu}`}
            onClick={() => handlerNavigate(Number(menu))}
          />
        ))}
      </div>
    </div>
  );
}
