.rules-databases-container,
.rules-databases-container-filter {
  width: 100%;
  height: 100%;
  &::after {
    content: "";
    position: fixed;
    display: block;
    top: -19px;
    left: 0;
    width: 100%;
    height: 102%;
    background-image: url("@/assets/Images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    z-index: 0;
    opacity: 1;
  }

  .menu-item-1,
  .menu-item-2,
  .menu-item-3,
  .menu-item-4,
  .menu-item-5,
  .menu-item-6 {
    position: absolute;
    z-index: 9;
    background-color: transparent;
    height: 120px;
    width: 30px;
    cursor: pointer;
  }

  .menu-item-1 {
    left: calc(100vw / 2 - 20px);
    top: calc(100vh / 2 + 10px);
    transform: rotate(-60deg) translate(-1px, -27px);
  }

  .menu-item-2 {
    left: calc(100vw / 2 + 50px);
    top: calc(100vh / 2 - 40px);
    transform: rotate(-10deg) translate(-5px, -10px);
  }

  .menu-item-3 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(20deg) translate(100px, -80px);
  }

  .menu-item-4 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(-65deg) translate(-115px, -50px);
  }

  .menu-item-5 {
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(60deg) translate(153px, -156px);
  }

  .menu-item-6 {
    height: 150px;
    left: calc(100vw / 2);
    top: calc(100vh / 2);
    transform: rotate(90deg) translate(106px, -70px);
  }
}

.rules-databases-container-filter::after {
  // filter: blur(2px);
  // backdrop-filter: blur(2px);
  // -webkit-backdrop-filter: blur(2px);
  // /* 性能优化 */
  // will-change: transform;
  background-image: url("@/assets/Images/Home/background-empty.jpg");
}
