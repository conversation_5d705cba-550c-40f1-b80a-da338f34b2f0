/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-23 09:42:21
 * @Description: 法规制度库
 * @LastEditTime: 2025-07-23 16:04:44
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { useNavigate } from "react-router";
import "./index.scss";
export default function RegulationsRulesDatabase() {
  const navigate = useNavigate();

  const handlerNavigate = (key: number) => {
    const hash: Record<number, string> = {
      1: "",
      2: "",
      3: "",
      4: "",
      5: "/general-manage",
      6: "",
    };
    navigate(hash[key]);
  };
  return (
    <div className="rules-databases-container" id="rules-databases-container">
      <h1>12321</h1>
      <div className="home-menus">
        {["1", "2", "3", "4", "5", "6"].map((menu) => (
          <div
            key={menu}
            className={`menu-item-${menu}`}
            onClick={() => handlerNavigate(Number(menu))}
          />
        ))}
      </div>
    </div>
  );
}
