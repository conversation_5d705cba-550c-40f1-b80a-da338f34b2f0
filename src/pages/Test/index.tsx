/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-14 21:55:00
 * @Description: 
 * @LastEditTime: 2025-07-15 11:53:36
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import {Form, FormInput} from "@/components/form-pc";
import { ValidateUtils } from "@codingapi/ui-framework";
import { Button } from "@itm/components-pc";

export default function Test() {
    
    const form = Form.useForm();

    const submit = async () => {
        const result = await form.validate()
        console.log(result);
        
    }
    return (
        <Form form={form} onFinish={submit}>
            <FormInput
                label='输入'
                name='input'
                required
                validateFunction={ValidateUtils.validateNotEmpty}
            />
            <Button htmlType="submit">提交</Button>
        </Form>
    )
}