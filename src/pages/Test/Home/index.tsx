/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-23 09:42:21
 * @Description: 首页
 * @LastEditTime: 2025-07-23 16:08:40
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import { useNavigate } from "react-router";
import "./index.scss";
export default function Home() {
  const navigate = useNavigate();

  const handlerNavigate = (key: number) => {
    const hash: Record<number, string> = {
      1: "",
      2: "/legal/affairs",
      3: "",
      4: "",
      5: "/team/building",
      6: "/regulations-rules-database",
    };
    navigate(hash[key]);
  };
  return (
    <div className="home-container" id="home-container">
      <div className="home-menus">
        {["1", "2", "3", "4", "5", "6"].map((menu) => (
          <div
            key={menu}
            className={`menu-item-${menu}`}
            onClick={() => handlerNavigate(Number(menu))}
          />
        ))}
      </div>
    </div>
  );
}
