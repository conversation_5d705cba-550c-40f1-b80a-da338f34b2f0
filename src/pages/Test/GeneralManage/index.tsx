/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-23 09:42:21
 * @Description: 综合管理
 * @LastEditTime: 2025-07-23 10:28:41
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import { useNavigate } from "react-router";
import "./index.scss";
export default function LawsRegulations() {
  const navigate = useNavigate();

  const handlerNavigate = (key: number) => {
    const hash: Record<number, string> = {
      1: "/laws-regulations",
    };
    navigate(hash[key]);
  };
  return (
    <div className="laws-regulations-container" id="laws-regulations-container">
      <div className="home-menus">
        {["1"].map((menu) => (
          <div
            key={menu}
            className={`menu-item-${menu}`}
            onClick={() => handlerNavigate(Number(menu))}
          />
        ))}
      </div>
    </div>
  );
}
