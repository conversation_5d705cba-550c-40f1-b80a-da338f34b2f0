.laws-regulations-container,
.laws-regulations-container-filter {
  width: 100%;
  height: 100%;
  &::after {
    content: "";
    position: fixed;
    display: block;
    top: -19px;
    left: 0;
    width: 100%;
    height: 102%;
    background-image: url("@/assets/Images/home/<USER>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    z-index: 0;
    opacity: 1;
  }

  .menu-item-1,
  .menu-item-2,
  .menu-item-3,
  .menu-item-4,
  .menu-item-5,
  .menu-item-6 {
    position: absolute;
    z-index: 9;
    background-color: transparent;
    height: 60px;
    width: 30px;
    cursor: pointer;
  }

  .menu-item-1 {
    left: calc(100vw / 2 - 20px);
    top: calc(100vh / 2 + 10px);
    transform: rotate(-40deg) translate(-12px, -27px);
  }
}

.laws-regulations-container-filter::after {
  background-image: url("@/assets/Images/Home/background-empty.jpg");
  // filter: blur(2px);
  // backdrop-filter: blur(2px);
  // -webkit-backdrop-filter: blur(2px);
  // /* 性能优化 */
  // will-change: transform;
}
