import {<PERSON><PERSON>pi, InstitutionState, Dispatch} from "@/pages/compliance/review/types";
import {SelectableUser} from "@/components/user/choice/type";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class InstitutionPresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<InstitutionState>;

    private readonly api: InstitutionApi;

    private state: InstitutionState;

    public constructor(dispatch: Dispatch<InstitutionState>, api: InstitutionApi, state: InstitutionState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: InstitutionState) => {
        this.state = newStatus;
    }


    public setSelectedInstitution = (id: number) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitution: {id: id}
            }
        })
    }

    public getInstitutionDetail = async (id: number) => {
        const params = {
            id: id
        }
        const res = await this.api.getInstitutionDetail(params);
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitutionDetail: {
                    ...res.data
                },
            }
        })
    }

    public getFlowRecordDetail = async (id: number) => {
        const res = await this.api.getFlowRecordDetail(id);
        console.log( res)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: res.data?.flowRecord.done
            }
        })
    }


    public loadComplianceDetail = async (id: number) => {

        const param = {
            institutionId: id
        };
        const res = await this.api.complianceDetail(param)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rows: (res.data.list as any[]).map((row, index) => ({
                    ...row,
                    key: (index + 1).toString()
                })),
            };
        });
    }

    public setComplianceReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: v
            }
        })
    }

    public setComplianceReviewFinishVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                reviewFinishVisible: v
            }
        })
    }

    public setRejectedReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rejectedReviewVisible: v
            }
        })
    }

    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }

    public submitFlow = async (records: SelectableUser[], recordId: number, success: boolean, currentNodeCode: string) => {
        const body = {
            recordId: recordId,
            reviewerId: records[0]?.id,
            institutionId: this.state.selectedInstitution.id,
            institutionName: this.state.selectedInstitutionDetail.title,
            success: success,
            currentNodeCode: currentNodeCode
        };
        await this.api.submitFlow(body)
    }

    public setViewType = (type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                viewType: type
            }
        })
    }

    public setAlreadySubmit = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: true
            }
        })
    }

    public getReviewButtonName = (currentNodeCode: string): string => {
        switch (currentNodeCode) {
            case 'review1':
                return "送呈法规部门管理员审核";
            case 'review2':
                return "送呈法规部门负责人审核";
            case 'review3':
                return "通过";
            default:
                return "";
        }
    };

}