import React, {useEffect} from "react";
import {Button, Input, Select, Space, Table, Tabs} from "antd";
import "./App.css";
import {usePresenter} from "@/pages/compliance/review/hooks/usePresenter";
import {Message, Modal} from "@itm/components-pc";
import {SelectableUser} from "@/components/user/choice/type";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {NoticeFormProps} from "@/components/user/notice/NoticeModalView";
import CustomSplitter from "@/components/CustomSplitter";
import {FlowImage} from "@/components/flowImage";

const {Option} = Select;

const ComplianceReview: React.FC<NoticeFormProps> = (props) => {
    const {state, presenter} = usePresenter();

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            institutionId: 0,
            currentNodeCode: "",
        };

    const recordId = customParams.recordId;
    const institutionId = customParams.institutionId;
    const currentNodeCode = customParams.currentNodeCode;

    useEffect(() => {
        console.log("customParams: ", customParams);
        if (institutionId) {
            presenter?.setSelectedInstitution(Number(institutionId));
            presenter?.getInstitutionDetail(Number(institutionId));
            presenter?.getFlowRecordDetail(Number(recordId));
            presenter?.loadComplianceDetail(Number(institutionId));
        }
    }, []);

    const columns: any[] = [
        {
            title: "序号",
            dataIndex: "key",
            key: "key",
            render: (text: string, record: any, index: number) => index + 1,
        },
        {
            title: "合规义务",
            align: "center",
            dataIndex: "institutionContent",
            key: "institutionContent",
            width: "30%", // 设置具体的宽度
            render: (text: string, record: any) => (
                <>
                    <Input.TextArea
                        rows={5}
                        value={record.institutionContent} // 显示选中的内容
                        readOnly={true}
                    />
                </>
            ),
        },
        {
            title: "合规风险",
            align: "center",
            dataIndex: "complianceRisk",
            key: "complianceRisk",
            width: "20%",
            render: (text: string, record: any) => (
                <Input.TextArea
                    rows={5}
                    value={record.complianceRisk}
                    readOnly={true}
                />
            ),
        },
        {
            title: "管控措施",
            align: "center",
            dataIndex: "controlMeasures",
            key: "controlMeasures",
            width: "20%",
            render: (text: string, record: any) => (
                <Input.TextArea
                    rows={5}
                    value={record.controlMeasures}
                    readOnly={true}
                />
            ),
        },
        {
            title: "落实措施途径",
            align: "center",
            dataIndex: "implMeasures",
            key: "implMeasures",
            width: "20%",
            render: (text: string, record: any) => (
                <Input.TextArea rows={5} value={record.implMeasures} readOnly={true}/>
            ),
        },
    ];

    return (
        <>
            <div className="compliance-review-container">
                <div className="twoPopul_header_box">
                    <Tabs
                        activeKey={state.viewType}
                        className="item_tabs"
                        onChange={(key) =>
                            presenter?.setViewType(key as "html" | "flowchart")
                        }
                        items={[
                            {
                                key: "html",
                                label: "正文",
                            },
                            {
                                key: "flowchart",
                                label: "流程",
                            },
                        ]}
                    />
                    <Space>
                        <Button
                            style={{
                                backgroundColor: "#c71114",
                                borderColor: "#c71114",
                                color: "#ffffff",
                            }}
                            type="default"
                            disabled={state.alreadySubmit}
                            onClick={() => {
                                presenter?.setRejectedReviewVisible(true);
                            }}
                        >
                            驳回
                        </Button>
                        <Button
                            type="primary"
                            disabled={state.alreadySubmit}
                            onClick={() => {
                                if (currentNodeCode === "review3") {
                                    presenter?.setComplianceReviewFinishVisible(true);
                                } else {
                                    presenter?.setComplianceReviewVisible(true);
                                }
                            }}
                        >
                            {presenter?.getReviewButtonName(currentNodeCode)}
                        </Button>
                    </Space>
                </div>
                {/* 左侧显示后端接口返回的 HTML 内容 */}
                {state.viewType === "html" ? (
                    <CustomSplitter
                        leftDefaultSize={825}
                        left={
                            <div
                                style={{
                                    transform: `scale(1.0)`,
                                    transformOrigin: "top left", // 设置缩放原点为左上角
                                    width: "789px",
                                    height: " calc(-110px + 100vh)",
                                }}
                                dangerouslySetInnerHTML={{
                                    __html: state.selectedInstitutionDetail?.htmlContent || "",
                                }}
                            />
                        }
                        right={
                            <div>
                                <header className="App-header">
                                    <div
                                        style={{
                                            display: "flex",
                                            flexDirection: "column",
                                            alignItems: "center",
                                            width: "100%",
                                        }}
                                    >
                                        <h1>{state.selectedInstitutionDetail?.title}</h1>
                                        <div
                                            style={{
                                                display: "flex",
                                                justifyContent: "space-between",
                                                alignItems: "center",
                                                width: "50%",
                                                fontSize: "14px",
                                            }}
                                        >
                                            {state.selectedInstitutionDetail?.drawUnit ||
                                            state.selectedInstitutionDetail?.drafter ? (
                                                <p>
                                                    {state.selectedInstitutionDetail?.drawUnit}/
                                                    {state.selectedInstitutionDetail?.drafter}
                                                </p>
                                            ) : null}

                                            {state.selectedInstitutionDetail?.drawTime && (
                                                <p>
                                                    起草时间：{state.selectedInstitutionDetail?.drawTime}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    、
                                </header>

                                <Table
                                    style={{marginLeft: 10}}
                                    dataSource={state.rows}
                                    columns={columns}
                                    pagination={false}
                                    bordered
                                />
                            </div>
                        }
                    />
                ) : (
                    <FlowImage recordId={recordId}/>
                )}
            </div>
            ,
            <Modal
                open={state.rejectedReviewVisible}
                title="确认驳回"
                onCancel={() => presenter?.setRejectedReviewVisible(false)}
                onOk={() => {
                    presenter?.submitFlow([], recordId, false, currentNodeCode);
                    presenter?.setRejectedReviewVisible(false);
                    presenter?.setAlreadySubmit();
                    Message.success("驳回成功");
                }}
            ></Modal>
            <UserChoiceModal
                visible={state.selectUserVisible}
                hide={presenter?.hideSelectUserModal ?? (() => {
                })}
                onFinish={(records: SelectableUser[]) => {
                    presenter?.submitFlow(records, recordId, true, currentNodeCode);
                    presenter?.setComplianceReviewVisible(false);
                    presenter?.setAlreadySubmit();
                    Message.success("提交成功");
                }}
                selectedUsers={state.currentSelectedUser}
            />
            <Modal
                open={state.reviewFinishVisible}
                title="确认通过"
                onCancel={() => presenter?.setRejectedReviewVisible(false)}
                onOk={() => {
                    presenter?.submitFlow([], recordId, true, currentNodeCode);
                    presenter?.setComplianceReviewFinishVisible(false);
                    presenter?.setAlreadySubmit();
                    Message.success("提交成功");
                }}
            ></Modal>
        </>
    );
};

export default ComplianceReview;
