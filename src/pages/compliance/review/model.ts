import {InstitutionApi} from "./types";
import {
    addOrUpdateCompliance,
    complianceDetail,
    submitFlow,
    getInstitutionDetail
} from "@/api/compliance/compliance";
import {detail} from "@/api/user/flow";


export class InstitutionApiImpl implements InstitutionApi {
    getInstitutionDetail(params: any): Promise<any> {
        return getInstitutionDetail(params);
    }

    getFlowRecordDetail(id?: any, workCode?: any): Promise<any> {
        return detail(id, workCode);
    }

    addOrUpdateCompliance(body: any): Promise<any> {
        return addOrUpdateCompliance(body);
    }

    complianceDetail(params: any): Promise<any> {
        return complianceDetail(params);
    }
    submitFlow(body: any): Promise<any> {
        return submitFlow(body);
    }

}