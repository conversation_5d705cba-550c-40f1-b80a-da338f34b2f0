.App {
    height: 100vh; /* 占满整个视口高度 */
    overflow: hidden; /* 防止自身滚动 */
}

.App-container {
    display: flex;
    height: 100%; /* 容器高度占满 App */
    overflow: hidden; /* 防止内部元素影响全局滚动 */
}

.App-left {
    width: 50%;
    padding: 16px;
    border-right: 1px solid #ddd;
    max-height: 950px; /* 设置最大高度 */
    overflow-y: auto; /* 启用垂直滚动条 */
}

.App-right {
    width: 50%;
    padding: 16px;
    max-height: 950px; /* 设置最大高度 */
    overflow-y: auto; /* 启用垂直滚动条 */
}

.App-header {
    text-align: center;
    margin-bottom: 20px;
}

.App-footer-right {
    display: flex;
    justify-content: flex-end; /* 将内容对齐到右侧 */
    gap: 8px; /* 按钮之间的间距 */
    padding: 16px; /* 内边距，可根据需要调整 */
}

.App-footer-left {
    display: flex;
    justify-content: flex-start; /* 将内容对齐到左侧 */
    gap: 8px; /* 按钮之间的间距 */
    padding: 16px; /* 内边距，可根据需要调整 */
}


