export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */


export interface InstitutionState {
    alreadySubmit: boolean,
    tableRefreshVersion: number,
    institutionVisible: boolean,
    rejectedReviewVisible: boolean,
    reviewFinishVisible: boolean,
    selectedInstitution: any,
    selectedInstitutionDetail: {
        title?: string;
        drawUnit?: string;
        drafter?: string;
        drawTime?: string;
        htmlContent?: string;
        contentList?: {id: number, content: string}[];
    },
    rows: {
        key?: string;
        institutionContentId?: number;
        institutionContent?: string;
        complianceRisk?: string;
        controlMeasures?: string;
        implMeasures?: string;
    }[]
    selectUserVisible: boolean;
    currentRoleId: any;
    currentSelectedUser: any[];
    viewType: string,
}


export interface InstitutionApi {

    getInstitutionDetail: (params: any) => Promise<any>,

    getFlowRecordDetail: (id?: any, workCode?: any) => Promise<any>,

    addOrUpdateCompliance: (body: any) => Promise<any>,

    complianceDetail: (params: any) => Promise<any>;

    submitFlow: (body: any) => Promise<any>;
}