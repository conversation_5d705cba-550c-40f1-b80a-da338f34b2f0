import React from "react";
import {InstitutionState} from "@/pages/compliance/review/types";
import {InstitutionPresenter} from "@/pages/compliance/review/presenter";
import {InstitutionApiImpl} from "@/pages/compliance/review/model";

const initialState: InstitutionState = {
    alreadySubmit: false,
    tableRefreshVersion: 0,
    institutionVisible: false,
    rejectedReviewVisible: false,
    reviewFinishVisible: false,
    selectedInstitution: null,
    selectedInstitutionDetail: {},
    rows: [],
    selectUserVisible: false,
    currentRoleId: null,
    currentSelectedUser: [],
    viewType: 'html',
}

export function usePresenter() {
    const [state, dispatch] = React.useState<InstitutionState>(initialState);

    const presenterRef = React.useRef<InstitutionPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InstitutionPresenter(dispatch, new InstitutionApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }

}