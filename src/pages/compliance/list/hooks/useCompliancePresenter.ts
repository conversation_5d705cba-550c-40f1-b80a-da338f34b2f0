import React from "react";
import {ComplianceState} from "@/pages/compliance/list/types";
import {CompliancePresenter} from "@/pages/compliance/list/presenter";
import {ComplianceApiImpl} from "@/pages/compliance/list/model";

// state状态的初始值
const initialState: ComplianceState = {
    searchTitleName: "",
    tableVersion: "",
    uploadedFile: {},
    uploadVisible: false,
    institutionVisible: false,
    selectedInstitution: {},
};

/**
 *  useCompliancePresenter hook
 *  创建 Presenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useCompliancePresenter() {
    const [state, dispatch] = React.useState<ComplianceState>(initialState);

    const presenterRef = React.useRef<CompliancePresenter>();

    // 如果 presenterRef.current 为空，则创建一个新的 Presenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new CompliancePresenter(state, dispatch, new ComplianceApiImpl());
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])


    return {
        state,
        presenter: presenterRef.current,
    };
}
