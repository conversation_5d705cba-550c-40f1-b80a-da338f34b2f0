import React, {useCallback, memo} from "react";
import {ActionType, PageContainer, ProTable,} from "@ant-design/pro-components";
import {useCompliancePresenter} from "@/pages/compliance/list/hooks/useCompliancePresenter";
import {Button, Input, Message, Modal, Tabs} from "@itm/components-pc";
import {Form, FormUploader} from "@/components/form-pc";
import InstitutionSearchPage from "@/pages/archive/institution/search/list";
import {useNavigate} from "react-router-dom";

const {TabPane} = Tabs;

interface FileTableProps {
    tableType: string;
}

const ComplianceManagePage: React.FC = () => {
    return (
        <PageContainer>
            <Tabs defaultActiveKey="inner" style={{marginTop: 16}}>
                <TabPane tab="行业内" key="inner">
                    <ComplianceTable tableType={"inner"}/>
                </TabPane>
                <TabPane tab="行业外" key="outer">
                    <ComplianceTable tableType={"outer"}/>
                </TabPane>
            </Tabs>
        </PageContainer>
    );
};
const ComplianceTable: React.FC<FileTableProps> = memo(({tableType}) => {

        const actionRef = React.useRef<ActionType>();
        const {state, presenter} = useCompliancePresenter();
        const form = Form.useForm();
        const navigate = useNavigate();

        const handleSearch = useCallback(() => {
            presenter?.refreshTable();
            actionRef.current?.reload();
        }, []);

        const handleReset = useCallback(() => {
            presenter?.resetSearch()
            actionRef.current?.reload();
        }, []);


        const columns = [
            {title: "制度id", dataIndex: "institutionId", hidden: true},
            {
                title: "制度名称", dataIndex: "title",
                render: (text: string) => (
                    <span title={text} style={{cursor: 'pointer'}}>
                    {text?.length > 50 ? `${text.slice(0, 50)}...` : text}
                </span>),
            },
            {
                title: "操作",
                valueType: "option",
                render: (text: any, record: any) => {
                    return [
                        <a key="tag" onClick={() => {
                            window.open(`/#/compliance/detail?id=${record.institutionId}&type=${tableType}`, '_blank');
                        }
                        }>
                            合规清单
                        </a>,
                    ];
                },
            },
        ] as any[];

        return (
            <>

                <ProTable
                    key={state.tableVersion}
                    actionRef={actionRef}
                    headerTitle={<h3 style={{margin: 0, fontWeight: "bold"}}>合规清单列表</h3>}
                    toolBarRender={() => [
                        <Button key="search" type="primary" onClick={() => {
                            form.reset()
                            presenter?.resetUploadFile()
                            presenter?.uploadVisible(true);
                        }
                        }>
                            导入合规清单
                        </Button>,
                        <Button key="search" type="primary" onClick={() => {
                            presenter?.setInstitutionVisible(true)
                        }
                        }>
                            新增合规清单
                        </Button>,
                        <Input
                            key="title"
                            placeholder="请输入制度名称"
                            value={state.searchTitleName}
                            onChange={(e) => presenter?.setSearchFileName(e.target.value)}
                            style={{width: 200, marginRight: 8}}
                        />,
                        <Button key="search" type="primary" onClick={handleSearch}>
                            搜索
                        </Button>,
                        <Button key="reset" onClick={handleReset}>
                            重置
                        </Button>,
                    ]}
                    columns={columns}
                    search={false}
                    request={async (params, sort, filter) => {
                        return presenter?.searchComplianceTable(params, sort, filter, tableType);
                    }}
                />

                <Modal
                    open={state.uploadVisible}
                    title="导入合规清单"
                    onCancel={() => {
                        presenter?.uploadVisible(false);
                    }}
                    onOk={() => {
                        presenter?.importCompliance();
                        presenter?.uploadVisible(false);
                    }}
                >
                    <Form form={form}>
                        <FormUploader
                            name="avatar"
                            label="上传文件"
                            uploaderAccept=".xlsx"
                            uploaderMaxCount={1}
                            onUploaderUpload={(filename, base64) => {
                                presenter?.setUploadFile(filename, base64);
                                return Promise.resolve({
                                    id: "temp-id",
                                    name: filename,
                                    url: base64,
                                });
                            }}
                        />
                        <div style={{ marginTop: 16 }}>
                            <Button
                                type="link"
                                onClick={() => {
                                    // 下载模板文件
                                    const templateUrl = "/compliance/合规清单导入模板.xlsx";
                                    const link = document.createElement("a");
                                    link.href = templateUrl;
                                    link.download = "合规清单模板.xlsx";
                                    link.click();
                                }}
                            >
                                下载模板文件
                            </Button>
                        </div>
                    </Form>
                </Modal>
                <Modal
                    open={state.institutionVisible}
                    title="选择制度"
                    width="100vw" // 宽度占满屏幕
                    style={{
                        top: 0,
                        left: 0,
                        height: "100vh",
                        maxWidth: "100%",
                        margin: 0,
                        padding: 0,
                        borderRadius: 0,
                    }}
                    bodyStyle={{
                        height: "calc(100vh - 120px)",
                        overflowY: "auto",
                    }}
                    onOk={() => {
                        if (state.selectedInstitution.id == 0 || state.selectedInstitution.id == null){
                            Message.error("请选择制度");
                        }else {
                            navigate(`/compliance/detail?id=${state.selectedInstitution.id}&type=${state.selectedInstitution.type}`);
                            presenter?.setInstitutionVisible(false);
                        }
                    }}
                    onCancel={() => presenter?.setInstitutionVisible(false)}
                >
                    <ErrorBoundary>
                        <InstitutionSearchPage
                            isComponent={true}
                            onSelectedChange={(id, type) => {
                                presenter?.setSelectedInstitution(id, type)
                            }}
                        />
                    </ErrorBoundary>
                </Modal>


            </>
        );
    }
);

class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean }
> {
    constructor(props: any) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError() {
        return { hasError: true };
    }

    render() {
        if (this.state.hasError) {
            return <div>组件加载出错</div>;
        }

        return this.props.children;
    }
}
export default ComplianceManagePage;
