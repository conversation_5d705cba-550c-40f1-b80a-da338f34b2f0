import {ComplianceApi} from "./types";
import {complianceList, importCompliance} from "@/api/compliance/compliance";


/**
 * Model层接口实现
 * ApiImpl 实现了Api接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class ComplianceApiImpl implements ComplianceApi {

    complianceList(params: any, sort: any, filter: any): Promise<any> {
        return complianceList(params, sort, filter);
    }


    importCompliance(formData: FormData): Promise<any> {
        return importCompliance(formData);
    }

}