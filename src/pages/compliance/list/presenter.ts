import {ComplianceApi, ComplianceState, Dispatch} from "./types";

export class CompliancePresenter {

    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<ComplianceState>;
    // 传入的Model对象
    private readonly api: ComplianceApi;
    // 当前的数据状态，由于Presenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: ComplianceState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(state: ComplianceState, dispatch: Dispatch<ComplianceState>, api: ComplianceApi) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
        console.log('CompliancePresenter initialized');
    }

    // 更新状态数据
    public syncState = (newState: ComplianceState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableVersion: `table-${Date.now()}`
            }
        })
    }

    public resetSearch = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: "",
            }
        })
    }


    public setSearchFileName = (title: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                searchTitleName: title
            }
        })
    }


    public searchComplianceTable = (params: any, sort: any, filter: any, searchType: string) => {
        const searchParams = {
            ...params,
            title: this.state.searchTitleName,
            searchType: searchType,
        };

        return this.api.complianceList(searchParams, sort, filter).then(res => {
            if (res.success) {
                return {
                    data: res.data?.list || [],
                    total: res.data?.total || 0,
                    success: true
                };
            } else {
                return {
                    data: [],
                    total: 0,
                    success: false
                };
            }
        });
    }

    public setInstitutionVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionVisible: v
            }
        })
    }

    public setSelectedInstitution = (id: number, type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitution: {
                    id: id,
                    type: type,
                }
            }
        })
    }


    public uploadVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadVisible: v
            }
        })
    }
    public setUploadFile = (filename: string, base64: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadedFile: {
                    filename: filename,
                    base64: base64,
                }
            }
        })
    }

    public resetUploadFile = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                uploadedFile: {}
            }
        })
    }

    public importCompliance = () => {
        const {filename, base64} = this.state.uploadedFile;
        const blob = this.dataURLtoBlob(base64);
        const file = new File([blob], filename, {type: blob.type});
        const formData = new FormData()
        formData.append('file', file);
        this.api.importCompliance(formData);
    };

    public dataURLtoBlob(dataUrl: string): Blob {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "";
        const bStr = atob(arr[1]);
        let n = bStr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bStr.charCodeAt(n);
        }
        return new Blob([u8arr], {type: mime});
    }
}
