
/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;


/**
 * 数据状态类型的定义
 */
export interface ComplianceState {
    searchTitleName: string;
    tableVersion: string;
    uploadedFile: any;
    uploadVisible: boolean;
    institutionVisible: boolean,
    selectedInstitution: any,
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface ComplianceApi {

    complianceList: (params: any, sort: any, filter: any) => Promise<any>;

    importCompliance: (formData: FormData) => Promise<any>;

}
