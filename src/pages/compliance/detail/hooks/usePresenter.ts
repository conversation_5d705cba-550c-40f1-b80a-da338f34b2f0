import React from "react";
import {InstitutionState} from "@/pages/compliance/detail/types";
import {InstitutionPresenter} from "@/pages/compliance/detail/presenter";
import {InstitutionApiImpl} from "@/pages/compliance/detail/model";

const initialState: InstitutionState = {
    loading: false,
    refreshVersion: 0,
    alreadySubmit: false,
    tableRefreshVersion: 0,
    recallReviewVisible: false,
    selectedInstitution: null,
    selectedInstitutionDetail: {},
    rows: [],
    selectUserVisible: false,
    currentRoleId: null,
    currentSelectedUser: [],
    viewType: 'html',
    recordId: 0 ,
    processId: '',
}

export function usePresenter() {
    const [state, dispatch] = React.useState<InstitutionState>(initialState);

    const presenterRef = React.useRef<InstitutionPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InstitutionPresenter(dispatch, new InstitutionApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }

}