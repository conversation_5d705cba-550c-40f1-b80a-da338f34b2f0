import {InstitutionApi} from "./types";
import {
    addOrUpdateCompliance,
    complianceDetail,
    startFlow,
    submitFlow,
    getInstitutionDetail,
    recallFlow
} from "@/api/compliance/compliance";
import {processDetail} from "@/api/user/flow";


export class InstitutionApiImpl implements InstitutionApi {
    getInstitutionDetail(params: any): Promise<any> {
        return getInstitutionDetail(params);
    }

    addOrUpdateCompliance(body: any): Promise<any> {
        return addOrUpdateCompliance(body);
    }

    complianceDetail(params: any): Promise<any> {
        return complianceDetail(params);
    }

    startFlow(body: any): Promise<any> {
        return startFlow(body);
    }
    recallFlow(body: any): Promise<any> {
        return recallFlow(body);
    }

    processDetail(processId: any): Promise<any> {
        return processDetail(processId);
    }

}