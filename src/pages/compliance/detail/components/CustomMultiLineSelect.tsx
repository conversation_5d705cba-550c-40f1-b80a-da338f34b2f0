import React from 'react';
import Select, { components } from 'react-select';

interface CustomMultiLineSelectOption {
    label: string;
    value: string | number;
}

interface CustomMultiLineSelectProps {
    options: CustomMultiLineSelectOption[];
    value?: CustomMultiLineSelectOption | null;
    onChange?: (value: CustomMultiLineSelectOption | null) => void;
    placeholder?: string;
    style?: React.CSSProperties;
}

const OptionComponent = (props: any) => {
    const labelText = props.data.label;

    // 截取前50个字符，超长则加省略号
    const displayText = labelText.length > 49 ? `${labelText.slice(0, 49)}...` : labelText;

    return (
        <components.Option {...props}>
            <div
                title={labelText} // 悬浮显示完整内容
                style={{
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all',
                    textAlign: 'left',
                    padding: 0,
                }}
            >
                {displayText}
            </div>
        </components.Option>
    );
};

// 选中值展示完整内容 + 支持滚动
const SingleValueComponent = (props: any) => {
    return (
        <components.SingleValue {...props}>
            <div
                style={{
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all',
                    maxHeight: '115px',
                    overflowY: 'auto',
                    textAlign: 'left',
                    padding: 0,
                }}
            >
                {props.children}
            </div>
        </components.SingleValue>
    );
};

// 自定义 Control 组件
const CustomControl = (props: any) => {
    return (
        <components.Control {...props} style={{ ...props.style, height: '115px', minHeight: '115px' }}>
            {/* 移除 DropdownIndicator 的占位空间 */}
            <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                {props.children}
            </div>
        </components.Control>
    );
};

const CustomNoOptionsMessage = (props: any) => {
    return (
        <components.NoOptionsMessage {...props}>
            暂无数据
        </components.NoOptionsMessage>
    );
};


// 自定义 DropdownIndicator 组件（隐藏箭头）
const CustomDropdownIndicator = () => null;

const CustomMultiLineSelect: React.FC<CustomMultiLineSelectProps> = ({
                                                                         options,
                                                                         value,
                                                                         onChange,
                                                                         placeholder = '请选择',
                                                                         style,
                                                                     }) => {
    const handleChange = (newValue: CustomMultiLineSelectOption | null) => {
        if (onChange) {
            onChange(newValue);
        }
    };

    return (
        <Select
            components={{
                Option: OptionComponent,
                SingleValue: SingleValueComponent,
                DropdownIndicator: CustomDropdownIndicator,
                Control: CustomControl, // 使用自定义的 Control
                NoOptionsMessage: CustomNoOptionsMessage, // 使用自定义的 NoOptionsMessage
            }}
            options={options}
            value={value}
            onChange={handleChange}
            placeholder={placeholder}
            styles={{
                menu: (provided) => ({
                    ...provided,
                    maxHeight: 300,
                }),
                control: (provided, state) => ({
                    ...provided,
                    height: '115px', // 设置固定高度
                    minHeight: '115px', // 确保最小高度
                }),
                // 移除 DropdownIndicator 的默认样式
                dropdownIndicator: (provided, state) => ({
                    ...provided,
                    display: 'none', // 完全隐藏 DropdownIndicator
                }),
            }}
        />
    );
};

export default CustomMultiLineSelect;
