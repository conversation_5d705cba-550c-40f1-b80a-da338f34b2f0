import {<PERSON><PERSON>pi, InstitutionState, Dispatch} from "@/pages/compliance/detail/types";
import {SelectableUser} from "@/components/user/choice/type";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class InstitutionPresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<InstitutionState>;

    private readonly api: InstitutionApi;

    private state: InstitutionState;

    public constructor(dispatch: Dispatch<InstitutionState>, api: InstitutionApi, state: InstitutionState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: InstitutionState) => {
        this.state = newStatus;
    }

    public refresh = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                refreshVersion: this.state.refreshVersion + 1,
                alreadySubmit: false,
            }
        })
    };


    public setSelectedInstitution = (id: number, type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitution: {id: id, type: type}
            }
        })
    }

    public getInstitutionDetail = async (id: number) => {
        const params = {
            id: id,
        }
        const detail = await this.api.getInstitutionDetail(params);
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitutionDetail: {
                    ...detail.data
                },
                alreadySubmit: detail.data?.currentNodeCode != null && detail.data?.currentNodeCode != 'start',
                loading: true,
            }
        })
        if (detail.data.complianceProcessId != null){
            await this.refreshProcessInfo(detail.data.complianceProcessId)
        }
    }

    public refreshProcessInfo = async (complianceProcessId: string) => {
        const processDetail = await this.api.processDetail(complianceProcessId)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recordId: processDetail.data.list[0].id,
            }
        });
    }

    public addRow = () => {
        this.dispatch((prevState) => {
            const newRows = [
                ...prevState.rows,
                {
                    key: `${prevState.rows.length + 1}`,
                }
            ];
            return {
                ...prevState,
                rows: newRows
            };
        });
    };


    public removeRow = (key: string) => {
        this.dispatch((prevState) => {
            const filteredRows = prevState.rows.filter(row => row.key !== key);
            return {
                ...prevState,
                rows: filteredRows
            };
        });
    };

    public removeAllRow = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rows: []
            };
        });
    };

    public handleChange = (key: string, field: keyof InstitutionState['rows'][0], value: any) => {
        this.dispatch((prevState) => {
            const updatedRows = prevState.rows.map(row =>
                row.key === key ? {...row, [field]: value} : row
            );
            return {
                ...prevState,
                rows: updatedRows
            };
        });
    };

    public addOrUpdateCompliance = async () => {
        const body = {
            institutionId: this.state.selectedInstitution.id,
            institutionType: this.state.selectedInstitution.type,
            dtoList: this.state.rows.map(row => ({
                institutionContentId: row.institutionContentId,
                institutionContent: row.institutionContent,
                complianceRisk: row.complianceRisk,
                controlMeasures: row.controlMeasures,
                implMeasures: row.implMeasures
            }))
        };
        return await this.api.addOrUpdateCompliance(body);
    };

    public loadComplianceDetail = async (id: number, type: string) => {

        const param = {
            institutionId: id,
            institutionType: type,
        };
        const res = await this.api.complianceDetail(param)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rows: (res.data.list as any[]).map((row, index) => ({
                    ...row,
                    key: (index + 1).toString()
                })),
            };
        });
    }

    public setComplianceReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: v
            }
        })
    }

    public setRecallReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recallReviewVisible: v
            }
        })
    }

    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }

    public startFlow = async (records: SelectableUser[]) => {
        const body = {
            reviewerId: records[0].id,
            institutionId: this.state.selectedInstitution.id,
            institutionName: this.state.selectedInstitutionDetail.title,
        };
        await this.api.startFlow(body)
    }

    public recallFlow = async () => {
        const param = {
            id: this.state.selectedInstitution.id,
        }
        const detail = await this.api.getInstitutionDetail(param);
        const res = await this.api.processDetail(detail.data.complianceProcessId)
        console.log(res);
        const body = {
            recordId: res.data.list[0].id,
            institutionId: this.state.selectedInstitution.id,
            institutionType: this.state.selectedInstitution.type,
        };
        return await this.api.recallFlow(body)
    }

    public setViewType = (type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                viewType: type
            }
        })
    }

    public setAlreadySubmit = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                alreadySubmit: true
            }
        })
    }



}