export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */


export interface InstitutionState {
    loading: boolean;
    refreshVersion: number,
    alreadySubmit: boolean,
    tableRefreshVersion: number,
    recallReviewVisible: boolean,
    selectedInstitution: any,
    selectedInstitutionDetail: {
        currentNodeCode?: string;
        complianceProcessId?: string;
        complianceProcessStatus?: string;
        title?: string;
        drawUnit?: string;
        drafter?: string;
        drawTime?: string;
        htmlContent?: string;
        contentList?: {id: number, content: string}[];
    },
    rows: {
        key?: string;
        institutionContentId?: number;
        institutionContent?: string;
        complianceRisk?: string;
        controlMeasures?: string;
        implMeasures?: string;
    }[]
    selectUserVisible: boolean;
    currentRoleId: any;
    currentSelectedUser: any[];
    viewType: string;
    recordId: number;
    processId: string;
}


export interface InstitutionApi {

    getInstitutionDetail: (params: any) => Promise<any>,

    addOrUpdateCompliance: (body: any) => Promise<any>,

    complianceDetail: (params: any) => Promise<any>;

    startFlow: (body: any) => Promise<any>;

    recallFlow: (body: any) => Promise<any>;

    processDetail: (processId: any) => Promise<any>;
}