import React, { useEffect } from "react";
import { Table, Button, Input, Select, message, Tabs, Space } from "antd";
import "@/pages/compliance/detail/index.scss";
import { usePresenter } from "@/pages/compliance/detail/hooks/usePresenter";
import {Message, Modal, Popconfirm} from "@itm/components-pc";
import { useLocation } from "react-router";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import { SelectableUser } from "@/components/user/choice/type";
import CustomSplitter from "@/components/CustomSplitter";
import CustomMultiLineSelect from "@/pages/compliance/detail/components/CustomMultiLineSelect";
import { FlowImage } from "@/components/flowImage";
import { useNavigate } from "react-router-dom";

const { Option } = Select;

const ComplianceDetail = () => {
  const { state, presenter } = usePresenter();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const id = searchParams.get("id");
  const type = searchParams.get("type");

  useEffect(() => {
    if (id && type) {
      presenter?.setSelectedInstitution(Number(id), String(type));
      presenter?.getInstitutionDetail(Number(id));
      presenter?.loadComplianceDetail(Number(id), String(type));
    }
  }, [id, state.refreshVersion]);

  const columns = React.useMemo(() => [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 30,
      align: "center",
      render: (text: string, record: any, index: number) => index + 1,
    },
    {
      title: "合规义务",
      dataIndex: "institutionContent",
      key: "institutionContent",
      width: "30%",
      align: "center",
      render: (text: string, record: any) => {
        const options = (
            state.selectedInstitutionDetail?.contentList || []
        ).map((content) => ({
          label: content.content,
          value: content.id,
        }));

        // 如果已提交，则展示只读的 TextArea
        if (state.alreadySubmit) {
          return (
              <Input.TextArea
                  rows={5}
                  value={record.institutionContent}
                  readOnly
              />
          );
        }

        // 否则展示可编辑的下拉选择组件
        return (
            <CustomMultiLineSelect
                options={options}
                value={
                    options.find(
                        (opt) => opt.value === record.institutionContentId
                    ) || null
                }
                onChange={(selectedOption) => {
                  if (selectedOption) {
                    presenter?.handleChange(
                        record.key,
                        "institutionContentId",
                        selectedOption.value
                    );
                    presenter?.handleChange(
                        record.key,
                        "institutionContent",
                        selectedOption.label
                    );
                  }
                }}
            />
        );
      }
    },
    {
      title: "合规风险",
      align: "center",
      dataIndex: "complianceRisk",
      key: "complianceRisk",
      width: "20%",
      render: (text: string, record: any) => (
        <Input.TextArea
          rows={5}
          readOnly={state.alreadySubmit}
          value={record.complianceRisk}
          onChange={(e) =>
            presenter?.handleChange(
              record.key,
              "complianceRisk",
              e.target.value
            )
          }
        />
      ),
    },
    {
      title: "管控措施",
      align: "center",
      dataIndex: "controlMeasures",
      key: "controlMeasures",
      width: "20%",
      render: (text: string, record: any) => (
        <Input.TextArea
          rows={5}
          readOnly={state.alreadySubmit}
          value={record.controlMeasures}
          onChange={(e) =>
            presenter?.handleChange(
              record.key,
              "controlMeasures",
              e.target.value
            )
          }
        />
      ),
    },
    {
      title: "落实措施途径",
      align: "center",
      dataIndex: "implMeasures",
      key: "implMeasures",
      width: "20%",
      render: (text: string, record: any) => (
        <Input.TextArea
          rows={5}
          readOnly={state.alreadySubmit}
          value={record.implMeasures}
          onChange={(e) =>
            presenter?.handleChange(record.key, "implMeasures", e.target.value)
          }
        />
      ),
    },
    {
      title: "操作",
      align: "center",
      key: "action",
      width: 50,
      hidden: state.alreadySubmit,
      render: (text: string, record: any) => (
          <Popconfirm
              title="确认删除该行？"
              onConfirm={() => presenter?.removeRow(record.key)}
              okText="确认"
              cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
      ),
    },
  ], [state.alreadySubmit, state.selectedInstitutionDetail, presenter]);

  return (
    <div className="write-container">
      <div className="write_header_box">
        <div className="tab_item">
          <Tabs
            activeKey={state.viewType}
            className="item_tabs"
            onChange={(key) =>
              presenter?.setViewType(key as "html" | "flowchart")
            }
            items={[
              {
                key: "html",
                label: "正文",
              },
              {
                key: "flowchart",
                label: "流程",
              },
            ]}
          />
        </div>
        <div style={{ marginRight: "30px" }}>
          <Space>
            {state.loading && !state.alreadySubmit && (
              <Button
                type="primary"
                onClick={() => {
                  try {
                    presenter?.addOrUpdateCompliance();
                    message.success("保存成功");
                  } catch (e) {
                    message.error("保存失败");
                  }
                }}
              >
                保存
              </Button>
            )}
            {!state.alreadySubmit ||
              (state.selectedInstitutionDetail.complianceProcessStatus !=
                "finished" && (
                <Button
                  type="default"
                  onClick={() => {
                    presenter?.setRecallReviewVisible(true);
                  }}
                >
                  撤回
                </Button>
              ))}
            {state.loading && !state.alreadySubmit && (
              <Button
                type="primary"
                onClick={() => {
                  presenter?.setComplianceReviewVisible(true);
                }}
              >
                送呈部门负责人
              </Button>
            )}
          </Space>
        </div>
      </div>
      {state.viewType == "html" && (
        <CustomSplitter
          leftDefaultSize={825}
          left={
            <div
              style={{
                transform: `scale(1.0)`,
                transformOrigin: "top left", // 设置缩放原点为左上角
                width: "790px",
                overflowX: "hidden",
              }}
              dangerouslySetInnerHTML={{
                __html: state.selectedInstitutionDetail?.htmlContent || "",
              }}
            />
          }
          right={
            <>
              <header className="App-header">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <h1>{state.selectedInstitutionDetail?.title}</h1>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      width: "50%",
                      fontSize: "14px",
                    }}
                  >
                    {state.selectedInstitutionDetail?.drawUnit ||
                    state.selectedInstitutionDetail?.drafter ? (
                      <p>
                        {state.selectedInstitutionDetail?.drawUnit}/
                        {state.selectedInstitutionDetail?.drafter}
                      </p>
                    ) : null}

                    {state.selectedInstitutionDetail?.drawTime && (
                      <p>
                        起草时间：{state.selectedInstitutionDetail?.drawTime}
                      </p>
                    )}
                  </div>
                </div>
              </header>

              <Table
                style={{ marginLeft: 10 }}
                dataSource={state.rows}
                columns={columns}
                pagination={false}
                bordered
              />
              {state.loading && !state.alreadySubmit && (
                <Button
                  type="primary"
                  onClick={presenter?.addRow}
                  style={{ marginTop: 16, marginLeft: 10 }}
                >
                  添加行
                </Button>
              )}
            </>
          }
        />
      )}

      {state.viewType == "flowchart" && (
        <FlowImage recordId={state.recordId} workCode="compliance_review" />
      )}
      <Modal
        open={state.recallReviewVisible}
        title="确认撤回吗"
        onCancel={() => presenter?.setRecallReviewVisible(false)}
        onOk={async () => {
          const res = await presenter?.recallFlow();
          if (res.success) {
            presenter?.setRecallReviewVisible(false);
            Message.success("撤回成功");
            presenter?.refresh();
          }
        }}
      ></Modal>
      <UserChoiceModal
        visible={state.selectUserVisible}
        hide={presenter?.hideSelectUserModal ?? (() => {})}
        onFinish={(records: SelectableUser[]) => {
          if (state.selectedInstitutionDetail == null) {
            Message.error("请选择制度");
          } else {
            presenter?.addOrUpdateCompliance();
            presenter?.startFlow(records);
            presenter?.setComplianceReviewVisible(false);
            presenter?.setAlreadySubmit();
            Message.success("提交成功");
          }
        }}
        selectedUsers={state.currentSelectedUser}
      />
    </div>
  );
};

class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <div>组件加载出错</div>;
    }

    return this.props.children;
  }
}

export default ComplianceDetail;
