import { S } from "mockjs";

export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AbolishWriteState {
    // 表格数据
    listData: any;
    editAble: boolean;
    // 呈送按钮
    passVisible: boolean;
    //提交流程表格
    flowData: AbolishFlowForm;
    // 选择用户模态框
    selectUserVisible: boolean;
    recallReviewVisible:boolean;
}

export interface AbolishApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAbolishInfoByInstitution: (params: any) => Promise<any>;
    saveAbolish: (params: any) => Promise<{ success: boolean; data?: any }>;
    submitFlow: (params: any) => Promise<any>;
    recallAbolishReview: (body: any) => Promise<any>;
}

// 废止表单数据类型
export interface AbolishFormData {
    id?: number;
    institutionId?: number;
    abolishDate?: number;
    operatorDept?: string | null;
    operatorName?: string | null;
    operatorDate?: number;
    abolishCause?: string;
    recordId?: any;
    processId?: string;
    reviewerId?: number;
}
export interface AbolishFlowForm {
    recordId?: number;
    currentReviewerId?: number;
    nextReviewerId?: number;
    abolishId?: number;
    institutionId?: number;
    institutionName?: String;
    success?: boolean;
}