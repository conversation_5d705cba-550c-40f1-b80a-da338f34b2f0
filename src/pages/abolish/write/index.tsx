import { Button, Space, Tabs } from "@itm/components-pc";
import React, { useState, useEffect, useRef } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { Message, Modal } from "@itm/components-pc";
import { useLocation } from "react-router-dom";
import { useWritePresenter } from "./hooks/usePresenter";
import { AbolishFormData } from "./types";
import GenericFormTable from "@/components/GenericFormTable";
import { Form, Input } from "antd";
import ComplianceDetailModal from "@/components/institution/OnlyComplanceDetail/index";
import CustomSplitter from "@/components/CustomSplitter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { FlowImage } from "@/components/flowImage";

const messPending = () => {
  // 页面初始化参数
  const location = useLocation();
  const { state, presenter } = useWritePresenter();
  const searchParams = new URLSearchParams(location.search);
  const instututionId = searchParams.get("id");

  // 废止表单数据
  const defaultForm: AbolishFormData = {
    institutionId: undefined,
    abolishDate: new Date().getTime(),
    operatorDept: localStorage.getItem("userdeptname"),
    operatorName: localStorage.getItem("usernickname"),
    operatorDate: new Date().getTime(),
    abolishCause: "",
  };
  const [form, setForm] = useState<AbolishFormData>(defaultForm);
  const formRef = useRef(form);

  // 页面需要变量
  const [activeKey, setActiveKey] = useState<string>("1");
  const [detailModalVisible, setDetailModalVisible] = React.useState(false);
  const [selectedInstitutionId, setSelectedInstitutionId] = React.useState<
    number | undefined
  >();
  const [userIds, setUserIds] = useState<string | null>("");

  useEffect(() => {
    if (instututionId) {
      presenter
        .getAbolishInfoDetailById(Number(instututionId))
        .then((res) => {
          if (res && Object.keys(res).length > 0) {
            setForm(res);
            presenter.updateFlowData(res);
          } else {
            setForm(defaultForm);
          }
        })
        .catch(() => {
          setForm(defaultForm);
        });
      presenter.getInstitutionDetailById(Number(instututionId));
    }
  }, []);

  useEffect(() => {
    setForm((prev) => ({
      ...prev,
      institutionId: state.listData?.id,
    }));
  }, [state.listData?.id]);

  useEffect(() => {
    formRef.current = form;
  }, [form]);

  const onChange = (key: string) => {
    setActiveKey(key);
  };
  const handleDetailClick = (id: number | undefined) => {
    setSelectedInstitutionId(id);
    setDetailModalVisible(true);
  };
  // 暂存废止记录
  const handleSaveAsDraft = async () => {
    try {
      const res = await presenter.createAbolishAsDraft(formRef.current);
      if (res.id > 0) {
        setForm(res);
        Message.info("暂存成功");
      } else {
        Message.error("暂存失败，请检查提交内容后重试");
      }
    } catch (error) {
      console.error("暂存失败:", error);
    }
  };
  // 保存废止记录
  const handleSave = async (userIds: any) => {
    try {
      const res = await presenter.createAbolish(formRef.current);
      if (res.id > 0) {
        presenter?.submitAbolishFlow(userIds, res.id);
        presenter?.changeSelectUserVisible(false);
        Message.info("提交成功");
      } else {
        Message.error("提交失败，请假查提交内容后重试");
      }
    } catch (error) {
      console.error("保存失败:", error);
    }
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "正文",
    },
    {
      key: "2",
      label: "流程",
    },
  ];

  const tableHeader = [
    {
      label: "经办人",
      value: form.operatorDept + "/" + form.operatorName,
    },
    {
      label: "经办时间",
      value: presenter.formatDate(form.operatorDate),
    },
  ];

  const tableRows = [
    {
      label: "制度名称",
      value: state.listData.title,
      isLabelRed: true,
    },
    {
      label: "制度标签",
      value: (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "2px",
            padding: "2px 0",
          }}
        >
          <span>
            制度层级：{" "}
            {state.listData.tags?.Tef && state.listData.tags.Tef.length > 0
              ? state.listData.tags.Tef[0].codeName
              : ""}
          </span>
          <span>
            制度类别：{" "}
            {state.listData.tags?.TeL && state.listData.tags.TeL.length > 0
              ? state.listData.tags.TeL[0].codeName
              : ""}
          </span>
        </div>
      ),
      isLabelRed: true,
    },
    {
      label: "合规风险清单",
      value: (
        <div
          style={{
            display: "flex",
            alignItems: "center", // 垂直居中
            justifyContent: "flex-end", // 水平靠右
          }}
        >
          <Button
            style={{ marginRight: "10px" }}
            onClick={() => handleDetailClick(form.institutionId)}
          >
            查看清单
          </Button>
        </div>
      ),
      isLabelRed: true,
    },
    {
      label: "废止理由",
      value: (
        <Form.Item style={{ marginBottom: 0 }}>
          <Input.TextArea
            readOnly={state.editAble}
            rows={3}
            placeholder="输入废止理由"
            value={form.abolishCause || ""}
            onChange={(e) =>
              setForm((prev) => ({ ...prev, abolishCause: e.target.value }))
            }
          />
        </Form.Item>
      ),
      isLabelRed: true,
    },
  ];

  return (
    <div className="abolist-write-container">
      <div className="twoPopul_header_box">
        <div className="tab_item">
          <Tabs
            className="item_tabs"
            defaultActiveKey={activeKey}
            items={items}
            onChange={onChange}
          />
        </div>
        <div style={{ marginRight: "30px" }}>
          <Space>
            <Button
              type="primary"
              disabled={state.passVisible}
              onClick={() => {
                handleSaveAsDraft();
              }}
            >
              保存
            </Button>
            {state.passVisible == true && (
              <Button
                type="primary"
                danger
                onClick={() => {
                  presenter?.setRecallReviewVisible(true);
                }}
              >
                撤回
              </Button>
            )}

            <Button
              type="primary"
              disabled={state.passVisible}
              onClick={() => {
                if (formRef.current.abolishCause?.length == 0) {
                  Message.error("请填写废止理由");
                } else {
                  presenter?.changeSelectUserVisible(true);
                }
              }}
            >
              送呈部门负责人
            </Button>
          </Space>
        </div>
      </div>
      <div>
        {activeKey == "1" && (
          <CustomSplitter
            leftDefaultSize={825}
            left={
              <div
                style={{
                  transform: `scale(1.0)`,
                  transformOrigin: "top left", // 设置缩放原点为左上角
                  width: "789px",
                  overflow: "auto",
                }}
                dangerouslySetInnerHTML={{
                  __html: state.listData?.htmlContent,
                }}
              />
            }
            right={
              <div className={`compliance_right_box`}>
                <GenericFormTable header={tableHeader} rows={tableRows} />
              </div>
            }
          />
        )}
      </div>
      {activeKey == "2" && (
        <div className="process">
          <FlowImage workCode={"innerInstitution_abolish"} />
        </div>
      )}
      <Modal
        title="选择审批人员"
        width={800}
        centered
        open={state.selectUserVisible}
        cancelText="取消"
        okText="确定"
        onCancel={() => {
          presenter?.changeSelectUserVisible(false);
        }}
        onOk={() => handleSave(userIds)}
        onClose={() => {
          presenter?.changeSelectUserVisible(false);
        }}
      >
        <UserChoiceSelect
          multiple
          onChange={setUserIds}
          value={userIds || ""}
        />
      </Modal>

      <Modal
        open={state.recallReviewVisible}
        title="确认撤回吗"
        onCancel={() => presenter?.setRecallReviewVisible(false)}
        onOk={() => {
          presenter?.recallAbolishReview();
          presenter?.setRecallReviewVisible(false);
          Message.success("提交成功");
        }}
      ></Modal>
      <ComplianceDetailModal
        visible={detailModalVisible}
        onClose={() => setDetailModalVisible(false)}
        institutionId={selectedInstitutionId || 0}
      />
    </div>
  );
};

export default messPending;
