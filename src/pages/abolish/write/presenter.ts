import { AbolishApi, AbolishWriteState, Dispatch, AbolishFormData } from "@/pages/abolish/write/types";

export class AbolishPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AbolishWriteState>;
    public readonly api: AbolishApi;
    private state: AbolishWriteState;

    public constructor(dispatch: Dispatch<AbolishWriteState>, api: AbolishApi, state: AbolishWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AbolishWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };
    // 更新修改状态
    public onEditAbleChange = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editAble: false,
            }
        });
    };
    // 暂存废止记录
    public createAbolishAsDraft = async (record: AbolishFormData) => {
        try {
            const res = await this.api.saveAbolish(record);
            return res.data.data;
        } catch (e) {
            console.error("创建失败", e);
        }
    }
    // 创建废止记录
    public createAbolish = async (record: AbolishFormData) => {
        try {
            const res = await this.api.saveAbolish(record);
            if (res.success) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        editAble: true,
                        passVisible:true,
                    }
                });
            }
            return res.data.data;
        } catch (e) {
            console.error("创建失败", e);
        }
        return {id:1};
    }

    // 获取废止记录详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
            return res;
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }

    // 获取废止记录详情
    public getAbolishInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAbolishInfoByInstitution(id);
            if (res && Object.keys(res).length > 0) {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        editAble: (res.recordId !=null && res.recordId !=undefined) ? true:false,
                        passVisible:(res.recordId !=null && res.recordId !=undefined) ? true:false,
                    }
                });
                return res;
            } else {
                return null;
            }

        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 提交流程
    public submitAbolishFlow = async (record: any, abolishId: number) => {
        if (record.length > 0) {
            const newFlowData = {
            ...this.state.flowData,
            nextReviewerId:  record.split(",")[0],
            abolishId: abolishId
        };
        this.dispatch((prevState) => {
            return {
                ...prevState,
                flowData: newFlowData,
            }
        });
        await this.api.submitFlow(newFlowData);
        }
    }
    // 撤销流程
    public recallAbolishReview = async () => {
        await this.api.recallAbolishReview(this.state.flowData)
    }

    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                listData: data,
                flowData: {
                    ...prevState.flowData,
                    institutionName: data.title,
                    institutionId: data.id
                }
            }
        });
    }
    //更新流程提交数据
    public updateFlowData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                flowData: {
                    ...prevState.flowData,
                    abolishId: data.id
                }
            }
        });
    }

    // 变更用户选择框状态
    public changeSelectUserVisible = (visible: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: visible,
            }
        });
    };
    // 更新撤销弹框控制键
    public setRecallReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recallReviewVisible: v
            }
        })
    }
} 