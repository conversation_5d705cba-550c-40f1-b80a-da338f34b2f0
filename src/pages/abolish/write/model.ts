import { AbolishApi } from "./types";
import { getInstitutionDetail,save ,getAbolishInfoDetail,submitFlow,recallFlow} from "@/api/abolish/abolish";

/**
 * Model层接口实现
 * AbolishApiImpl 实现了AbolishApi接口，提供了数据的增删改查操作
 */
export class AbolishApiImpl implements AbolishApi {

    async getInstitutionDetailById(params: any): Promise<any> {
        try {
            const res = await getInstitutionDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }

    async saveAbolish(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            const res = await save(params);
            return { success: true, data: res };
        } catch (err) {
            return { success: false };
        }
    }
    async getAbolishInfoByInstitution(params: any): Promise<any> {
        try {
            const res = await getAbolishInfoDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async submitFlow(params: any): Promise<any> {
        try {
            await submitFlow(params);
        } catch (err) {
            throw err;
        }
    }
    
    async recallAbolishReview(params: any): Promise<any> {
        try {
            await recallFlow(params);
        } catch (err) {
            throw err;
        }
    }
}