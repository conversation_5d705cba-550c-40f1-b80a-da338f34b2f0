import { Button, Space, Tabs, Row } from "@itm/components-pc";
import React, { useState, useEffect, useRef } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { Message, Modal } from "@itm/components-pc";
import { useWritePresenter } from "@/pages/abolish/examine/hooks/usePresenter";
import { ExamineFormData } from "@/pages/abolish/examine/types";
import { NoticeFormProps } from "@/components/user/notice/NoticeModalView";
import GenericFormTable from "@/components/GenericFormTable";
import { Form, Input } from "antd";
import ComplianceDetailModal from "@/components/institution/OnlyComplanceDetail/index";
import CustomSplitter from "@/components/CustomSplitter";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import { FlowImage } from "@/components/flowImage";

const AbolishReview: React.FC<NoticeFormProps> = (props) => {
    const { state, presenter } = useWritePresenter();

    const customParams = props.data?.customParams
        ? JSON.parse(props.data.customParams)
        : {
            recordId: 0,
            reviewerId: 0,
            abolishId: 0,
            institutionId: 0,
            nodeCode: "",
        };

    // 废止表单数据
    const defaultForm: ExamineFormData = {
        abolishId: undefined,
        operatorDept: localStorage.getItem("userdeptname"),
        operatorName: localStorage.getItem("usernickname"),
        operatorDate: new Date().getTime(),
        examineSuggest: "",
    };
    const [form, setForm] = useState<ExamineFormData>(defaultForm);
    const formRef = useRef(form);

    const [activeKey, setActiveKey] = useState<string>("1");

    const [detailModalVisible, setDetailModalVisible] = React.useState(false);
    const [selectedInstitutionId, setSelectedInstitutionId] = React.useState<number | undefined>();
    const [userIds, setUserIds] = useState<string | null>("");

    useEffect(() => {
        presenter.initFlowForm(
            customParams.recordId,
            customParams.reviewerId,
            customParams.nodeCode
        );
        // 节点相关信息
        presenter.getRecordIdStatusSum(customParams.recordId);
        presenter.getExamineInfoByRecordId(customParams.recordId, customParams.abolishId)
            .then((res) => {
                if (res != null) {
                    setForm({
                        id: res.id,
                        abolishId: res.abolishId,
                        operatorDept: res.operatorDept,
                        operatorName: res.operatorName,
                        operatorDate: res.operatorDate,
                        examineSuggest: res.examineSuggest,
                        recordId: res.recordId,
                    });
                } else {
                    setForm({
                        ...form,
                        recordId: customParams.recordId,
                    });
                }
            });
        // 初始化展示数据
        presenter.getAbolishInfoDetailById(customParams.abolishId);
        presenter.getInstitutionDetailById(customParams.institutionId);
        presenter.getExamineInfoDetail(customParams.abolishId);
    }, []);

    useEffect(() => {
        if (state.abolishData?.id) {
            setForm((prev) => ({
                ...prev,
                abolishId: state.abolishData.id,
            }));
        }
    }, [state.abolishData?.id]);

    useEffect(() => {
        formRef.current = form;
    }, [form]);

    const onChange = (key: string) => {
        setActiveKey(key);
    };
    const handleDetailClick = (id: number | undefined) => {
        setSelectedInstitutionId(id);
        setDetailModalVisible(true);
    };
    // 保存废止记录
    const handleSave = async (condition: boolean, userIds: any | null) => {
        try {
            await presenter.createAbolishExamine(formRef.current);
            if (condition == true && userIds.length > 0) {
                presenter?.submitAbolishFlow(userIds);
                Message.info("提交成功");
                presenter?.onEditAbleChange();
                presenter?.changeSelectUserVisible(false, customParams.nodeCode);
                props.setVisible(false);
            }
        } catch (error) {
            console.error("保存失败:", error);
        }
    };

    const items: TabsProps["items"] = [
        {
            key: "1",
            label: "正文",
        },
        {
            key: "2",
            label: "流程",
        },
    ];
    const tableHeader = [
        {
            label: "经办人",
            value:
                state.abolishData.operatorDept + "/" + state.abolishData.operatorName,
        },
        {
            label: "经办时间",
            value: presenter.formatDate(state.abolishData.operatorDate),
        },
    ];

    const tableRows = [
        {
            label: "制度名称",
            value: state.institutionData.title,
            isLabelRed: true,
        },
        {
            label: "制度标签",
            value: (
                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "2px",
                        padding: "2px 0",
                    }}
                >
                    <span>
                        制度层级：{" "}
                        {state.institutionData.tags?.Tef &&
                            state.institutionData.tags.Tef.length > 0
                            ? state.institutionData.tags.Tef[0].codeName
                            : ""}
                    </span>
                    <span>
                        制度类别：{" "}
                        {state.institutionData.tags?.TeL &&
                            state.institutionData.tags.TeL.length > 0
                            ? state.institutionData.tags.TeL[0].codeName
                            : ""}
                    </span>
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "合规风险清单",
            value: (
                <div
                    style={{
                        display: "flex",
                        alignItems: "center", // 垂直居中
                        justifyContent: "flex-end", // 水平靠右
                    }}
                >
                    <Button
                        style={{ marginRight: "10px" }}
                        onClick={() => handleDetailClick(state.abolishData.institutionId)}
                    >
                        查看清单
                    </Button>
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "废止理由",
            value: state.abolishData.abolishCause || "",
            isLabelRed: true,
        },
        {
            label: "已办意见",
            value: (
                <div>
                    {state.examineListData && state.examineListData.length > 0 ? (
                        state.examineListData.map((opinion: any, index: number) => (
                            <div
                                key={opinion.id || index}
                                style={{
                                    padding: "4px 0",
                                    color: "#222",
                                    fontSize: "15px",
                                    lineHeight: "1.8",
                                    wordBreak: "break-all",
                                }}
                            >
                                {opinion.operatorDept}：
                                <b style={{ fontWeight: 600 }}>[{opinion.operatorName}]</b>
                                {opinion.examineSuggest}
                                <span style={{ color: "#888", marginLeft: 8 }}>
                                    （{presenter.formatDate(opinion.operatorDate)}）
                                </span>
                            </div>
                        ))
                    ) : (
                        <div
                            style={{
                                padding: "10px",
                                textAlign: "center",
                                color: "#999",
                                fontStyle: "italic",
                            }}
                        >
                            暂无已办意见
                        </div>
                    )}
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "拟办意见",
            value: (
                <Form.Item style={{ marginBottom: 0 }}>
                    <Input.TextArea
                        readOnly={state.editAble}
                        rows={3}
                        placeholder="输入拟办意见"
                        value={form.examineSuggest || ""}
                        onChange={(e) =>
                            setForm((prev) => ({ ...prev, examineSuggest: e.target.value }))
                        }
                    />
                </Form.Item>
            ),
            isLabelRed: true,
        },
    ];
    return (
        <>
            <div className="twoPopul_header_box">
                <div className="tab_item">
                    <Tabs
                        className="item_tabs"
                        defaultActiveKey={activeKey}
                        items={items}
                        onChange={onChange}
                    />
                </div>
                <div style={{ marginRight: '30px' }}>
                    <Space>
                        <Button
                            disabled={state.editAble}
                            type="primary"
                            danger
                            onClick={() => {
                                if (formRef.current.examineSuggest?.length == 0) {
                                    Message.error("请填写拟办意见");
                                } else {
                                    presenter?.setRejectedReviewVisible(true);
                                }
                            }}
                        >
                            驳回
                        </Button>
                        <Button
                            type="primary"
                            disabled={state.editAble}
                            onClick={() => {
                                if (formRef.current.examineSuggest?.length == 0) {
                                    Message.error("请填写拟办意见");
                                } else {
                                    presenter?.changeSelectUserVisible(true, customParams.nodeCode);
                                }
                            }}
                        >
                            {presenter?.getReviewButtonName(customParams.nodeCode)}
                        </Button>
                    </Space>
                </div>
            </div>
            <div className="twoPopul_body_box">
                {activeKey == "1" && (
                    <CustomSplitter
                        leftDefaultSize={825}
                        left={
                            <div
                                style={{
                                    transform: `scale(1.0)`,
                                    transformOrigin: "top left", // 设置缩放原点为左上角
                                    width: "789px",
                                    overflow: "auto",
                                }}
                                dangerouslySetInnerHTML={{
                                    __html: state.institutionData?.htmlContent,
                                }}
                            />
                        }
                        right={
                            <div className={`compliance_right_box`}>
                                <GenericFormTable header={tableHeader} rows={tableRows} />
                            </div>
                        }
                    />
                )}
                {activeKey == "2" && <div className="process">
                    <FlowImage recordId={customParams.recordId} />
                </div>}
            </div>

            <Modal
                title="选择审批人员"
                width={800}
                centered
                open={state.selectUserVisible}
                cancelText="取消"
                okText="确定"
                onCancel={() => {
                    presenter?.changeSelectUserVisible(false, customParams.nodeCode);
                }}
                onOk={() => handleSave(true, userIds)}
                onClose={() => {
                    presenter?.changeSelectUserVisible(false, customParams.nodeCode);
                }}
            >
                <UserChoiceSelect
                    multiple
                    onChange={setUserIds}
                    value={userIds || ""}
                />
            </Modal>
            <Modal
                open={state.rejectedReviewVisible}
                title="确认驳回"
                onCancel={() => presenter?.setRejectedReviewVisible(false)}
                onOk={() => {
                    handleSave(false, userIds);
                    presenter?.rejectAbolishFlow();
                    presenter?.setRejectedReviewVisible(false);
                    Message.success("提交成功");
                    presenter?.onEditAbleChange();
                    props.setVisible(false);
                }}
            />
            <Modal
                open={state.abolishPassVisible}
                title="审核通过"
                onCancel={() =>
                    presenter?.changeSelectUserVisible(false, customParams.nodeCode)
                }
                onOk={() => {
                    handleSave(false, userIds);
                    presenter?.abolishPassFlow();
                    presenter?.changeSelectUserVisible(false, customParams.nodeCode);
                    Message.success("提交成功");
                    presenter?.onEditAbleChange();
                    props.setVisible(false);
                }}
            />
            <ComplianceDetailModal
                visible={detailModalVisible}
                onClose={() => setDetailModalVisible(false)}
                institutionId={selectedInstitutionId || 0}
            />
        </>
    );
};

export default AbolishReview;
