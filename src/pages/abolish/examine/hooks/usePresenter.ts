import React from "react";
import { AbolishWriteState } from "@/pages/abolish/examine/types";
import { AbolishPresenter } from "@/pages/abolish/examine/presenter";
import { AbolishApiImpl } from "@/pages/abolish/examine/model";

const initialState: AbolishWriteState = {
    // 表格数据
    institutionData: {},
    abolishData: {},
    examineListData: [],
    editAble: false,
    rejectedReviewVisible: false,
    abolishPassVisible:false,
    flowData: {},

    selectUserVisible: false,
}

export function useWritePresenter() {
    const [state, dispatch] = React.useState<AbolishWriteState>(initialState);

    const presenterRef = React.useRef<AbolishPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 AbolishPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new AbolishPresenter(dispatch, new AbolishApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
} 