import { S } from "mockjs";

export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AbolishWriteState {
    // 表格数据
    institutionData: any;
    abolishData: any;
    examineListData: any[];
    editAble: boolean;
    // 驳回弹窗
    rejectedReviewVisible: boolean;
    abolishPassVisible: boolean;
    //提交流程表格
    flowData: AbolishFlowForm;
    // 选择用户模态框
    selectUserVisible: boolean;
}

export interface AbolishApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAbolishInfoByInstitution: (params: any) => Promise<any>;
    getExamineInfosByAbolishId: (params: any) => Promise<any>;
    saveAbolishExamine: (params: any) => Promise<{ success: boolean; data?: any }>;
    submitFlow: (params: any) => Promise<any>;
    getRecordIdStatusSum: (params: any) => Promise<any>;
    getExamineInfoByRecordId: (params1: any,params2: any) => Promise<any>;
}

// 废止表单数据类型
export interface ExamineFormData {
    id?: number;
    abolishId?: number;
    operatorDept?: string | null;
    operatorName?: string | null;
    operatorDate?: number;
    examineSuggest?: string;
    recordId?: number;
}
export interface AbolishFlowForm {
    recordId?: number;
    currentReviewerId?: number;
    nextReviewerId?: number;
    abolishId?: number;
    institutionId?: number;
    institutionName?: String;
    success?: boolean;
    nodeCode?: String;
}