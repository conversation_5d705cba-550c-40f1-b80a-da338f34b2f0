.twoPopul_header_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  margin-bottom: 15px;

  .item_tabs {
    .ant-tabs-tab-btn {
      padding: 5px 20px;
    }

    .ant-tabs-nav {
      margin: 0;
    }
  }

}

.twoPopul_body_box {
  width: 100%;
  position: relative;

  .process {
    height: calc(100vh - 240px);
    background-color: white;
  }

  .compliance_icon {
    width: 40px;
    height: 80px;
    position: absolute;
    z-index: 999;
    border-radius: 10px 0 0 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #007356;
    background-color: white;
    cursor: pointer;
    border: 1px solid #007356;
    border-right: none;
  }

  .compliance_icon_open {
    left: 50%;
    transform: translateX(-70%)
  }

  .compliance_icon_close {
    right: 0;
  }

  .compliance_left_box {
    width: 100%;
    height: 100%;
    background-color: white;
    height: calc(100vh - 240px);
    overflow-y: auto;
    transform: scale(1.0),
  }

  .compliance_right_box {
    transition: display 0.3s ease;
    padding: 10px;
    position: relative;
    background-color: white;
  }

  .inputStyle {
    border: none;
    width: 100%;
    height: 100%;
  }

  .textareaStyle {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    resize: none;
    padding: 0 11px;
    border: none;
    outline: none;

    &:focus {
      box-shadow: none
    }
  }
}