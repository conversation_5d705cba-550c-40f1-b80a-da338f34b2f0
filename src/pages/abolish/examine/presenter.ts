import { AbolishApi, AbolishWriteState, Dispatch, ExamineFormData } from "@/pages/abolish/examine/types";
import { message } from "antd";
import { SelectableUser } from "@/components/user/choice/type";

export class AbolishPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AbolishWriteState>;
    public readonly api: AbolishApi;
    private state: AbolishWriteState;

    public constructor(dispatch: Dispatch<AbolishWriteState>, api: AbolishApi, state: AbolishWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AbolishWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };
    // 更新修改状态
    public onEditAbleChange = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                editAble: true,
            }
        });
    };
    // 创建废止审核记录
    public createAbolishExamine = async (record: ExamineFormData) => {
        try {
            await this.api.saveAbolishExamine(record);
        } catch (e) {
            console.error("创建失败", e);
        }
    }

    // 获取流程节点的状态
    public getRecordIdStatusSum = async (recordId: number) => {
        try {
            const res = await this.api.getRecordIdStatusSum(recordId);
            if (res > 0) {
                this.dispatch((prevState) => ({
                    ...prevState,
                    editAble: true
                }));
            }
            return res;
        } catch (e) {
            console.error("获取失败", e);
        }
    }

    // 获取当前节点的审核信息
    public getExamineInfoByRecordId = async (recordId: number, abolishId: number) => {
        try {
            const res = await this.api.getExamineInfoByRecordId(recordId, abolishId);
            return res;
        } catch (e) {
            console.error("获取失败", e);
            return null;
        }
    }
    // 获取制度详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }

    // 获取废止记录详情
    public getAbolishInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAbolishInfoByInstitution(id);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    abolishData: res,
                    flowData: {
                        ...prevState.flowData,
                        abolishId: res.id
                    }
                }
            });
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 获取废止审核记录详情
    public getExamineInfoDetail = async (id: number) => {
        try {
            const res = await this.api.getExamineInfosByAbolishId(id);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    examineListData: res?.list,
                }
            });
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }
    // 提交流程
    public submitAbolishFlow = async (record: any) => {
        if (record.length>0) {
            const newFlowData = {
            ...this.state.flowData,
            nextReviewerId: record.split(",")[0],
            success: true,
        };
        await this.api.submitFlow(newFlowData);
        }
    }

    // 驳回流程
    public rejectAbolishFlow = async () => {
        const newFlowData = {
            ...this.state.flowData,
            success: false,
        };
        await this.api.submitFlow(newFlowData);
    }
    // 审核通过
    public abolishPassFlow = async () => {
        const newFlowData = {
            ...this.state.flowData,
            success: true,
        };
        await this.api.submitFlow(newFlowData);
    }
    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionData: data,
                flowData: {
                    ...prevState.flowData,
                    institutionName: data.title,
                    institutionId: data.id
                }
            }
        });
    }

    // 变更用户选择框状态
    public changeSelectUserVisible = (visible: boolean, nodeNoCode: string) => {
        if (nodeNoCode === "supervisor") {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    abolishPassVisible: visible,
                }
            });
        } else {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    selectUserVisible: visible,
                }
            });
        }
    };

    // 初始化时更新flowForm
    public initFlowForm = (recordId: number, currentReviewerId: number, nodeNoCode: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                flowData: {
                    ...prevState.flowData,
                    recordId: recordId,
                    currentReviewerId: currentReviewerId,
                    nodeCode: nodeNoCode
                }
            }
        });
    }

    // 驳回弹窗修改
    public setRejectedReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rejectedReviewVisible: v,
            }
        });
    };
    // 审核提交按钮展示
    public getReviewButtonName = (nodeNoCode: String) => {
        if (!nodeNoCode) {
            return "";
        } else if (nodeNoCode === "deptExamine") {
            return "送呈法规部门管理员审核";
        } else if (nodeNoCode === "lawDeptAdmin") {
            return "送呈法规部门负责人审核";
        } else if (nodeNoCode === "lawDeptManager") {
            return "送呈分管领导审核";
        } else if (nodeNoCode === "supervisor") {
            return "审核通过";
        }
    };
}