import { Abolish<PERSON>pi } from "@/pages/abolish/examine/types";
import {
    getInstitutionDetail,
    saveExamineInfo,
    getAbolishInfoDetailById,
    submitFlow,
    getAbolishExamineInfos,
    getAbolishExamineInfoByRecordId,
    countRecordIdStatus
} from "@/api/abolish/abolish";

/**
 * Model层接口实现
 * AbolishApiImpl 实现了AbolishApi接口，提供了数据的增删改查操作
 */
export class AbolishApiImpl implements AbolishApi {

    async getInstitutionDetailById(params: any): Promise<any> {
        try {
            const res = await getInstitutionDetail(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async getExamineInfosByAbolishId(params: any): Promise<any> {
        try {
            const res = await getAbolishExamineInfos(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }

    async saveAbolishExamine(params: any): Promise<{ success: boolean; data?: any }> {
        try {
            await saveExamineInfo(params);
            return { success: true };
        } catch (err) {
            return { success: false };
        }
    }
    async getAbolishInfoByInstitution(params: any): Promise<any> {
        try {
            const res = await getAbolishInfoDetailById(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
    async submitFlow(params: any): Promise<any> {
        try {
            await submitFlow(params);
        } catch (err) {
            throw err;
        }
    }
   
    async getRecordIdStatusSum(params: any): Promise<any> {
        try {
            const res = await countRecordIdStatus(params);
            return res.data;
        } catch (err) {
            throw err;
        }
    }


     async getExamineInfoByRecordId(params1: any,params2: any): Promise<any> {
        try {
            const res = await getAbolishExamineInfoByRecordId(params1,params2);
            return res.data;
        } catch (err) {
            throw err;
        }
    }
}