import React from "react";
import { AbolishWriteState } from "@/pages/abolish/detail/types";
import { AbolishPresenter } from "@/pages/abolish/detail/presenter";
import { AbolishApiImpl } from "@/pages/abolish/detail/model";

const initialState: AbolishWriteState = {
    // 表格数据
    institutionData: {},
    abolishData: {},
    examineListData: [],
}

export function useWritePresenter() {
    const [state, dispatch] = React.useState<AbolishWriteState>(initialState);

    const presenterRef = React.useRef<AbolishPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 AbolishPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new AbolishPresenter(dispatch, new AbolishApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
} 