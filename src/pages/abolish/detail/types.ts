import { S } from "mockjs";

export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AbolishWriteState {
    // 表格数据
    institutionData: any;
    abolishData: any;
    examineListData: any[];
}

export interface AbolishApi {
    getInstitutionDetailById: (params: any) => Promise<any>;
    getAbolishInfoByInstitution: (params: any) => Promise<any>;
    getExamineInfosByAbolishId: (params: any) => Promise<any>;
}
