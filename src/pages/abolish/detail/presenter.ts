import { AbolishApi, AbolishWriteState, Dispatch } from "@/pages/abolish/detail/types";
import { message } from "antd";
import { SelectableUser } from "@/components/user/choice/type";

export class AbolishPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AbolishWriteState>;
    public readonly api: AbolishApi;
    private state: AbolishWriteState;

    public constructor(dispatch: Dispatch<AbolishWriteState>, api: AbolishApi, state: AbolishWriteState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: AbolishWriteState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const h = String(date.getHours()).padStart(2, '0');    // 小时
        const min = String(date.getMinutes()).padStart(2, '0'); // 分钟
        const s = String(date.getSeconds()).padStart(2, '0');   // 秒

        return `${y}-${m}-${d} ${h}:${min}:${s}`;
    };

    // 获取制度详情
    public getInstitutionDetailById = async (id: number) => {
        try {
            const res = await this.api.getInstitutionDetailById(id);
            this.updateTableData(res);
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }

    // 获取废止记录详情
    public getAbolishInfoDetailById = async (id: number) => {
        try {
            const res = await this.api.getAbolishInfoByInstitution(id);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    abolishData: res
                }
            });
        } catch (e) {
            console.error("获取详情失败", e);
            return null;
        }
    }
    // 获取废止审核记录详情
    public getExamineInfoDetail = async (id: number) => {
        try {
            const res = await this.api.getExamineInfosByAbolishId(id);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    examineListData: res?.list,
                }
            });
        } catch (e) {
            console.error("获取详情失败", e);
        }
    }
    

    // 更新表格数据
    public updateTableData = (data: any) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                institutionData: data,
            }
        });
    }
}