import { But<PERSON>, Tabs, } from "@itm/components-pc";
import React, { useState, useEffect } from "react";
import "./index.scss";
import type { TabsProps } from "@itm/components-pc";
import { useWritePresenter } from "@/pages/abolish/detail/hooks/usePresenter";
import GenericFormTable from "@/components/GenericFormTable";
import ComplianceDetailModal from "@/components/institution/OnlyComplanceDetail/index";
import CustomSplitter from "@/components/CustomSplitter";
import { FlowImage } from "@/components/flowImage";
import { useSearchParams } from "react-router-dom";

const AbolishDetail: React.FC = () => {
    const [searchParams] = useSearchParams();
    const abolishId = searchParams.get("abolishId");
    const institutionId = searchParams.get("institutionId");

    const { state, presenter } = useWritePresenter();

    const [activeKey, setActiveKey] = useState<string>("1");

    const [detailModalVisible, setDetailModalVisible] = React.useState(false);
    const [selectedInstitutionId, setSelectedInstitutionId] = React.useState<number | undefined>();

    useEffect(() => {
        // 初始化展示数据
        presenter.getAbolishInfoDetailById(Number(abolishId));
        presenter.getInstitutionDetailById(Number(institutionId));
        presenter.getExamineInfoDetail(Number(abolishId));
    }, []);

    const onChange = (key: string) => {
        setActiveKey(key);
    };
    const handleDetailClick = (id: number | undefined) => {
        setSelectedInstitutionId(id);
        setDetailModalVisible(true);
    };


    const items: TabsProps["items"] = [
        {
            key: "1",
            label: "正文",
        },
        {
            key: "2",
            label: "流程",
        },
    ];
    const tableHeader = [
        {
            label: "经办人",
            value:
                state.abolishData.operatorDept + "/" + state.abolishData.operatorName,
        },
        {
            label: "经办时间",
            value: presenter.formatDate(state.abolishData.operatorDate),
        },
    ];

    const tableRows = [
        {
            label: "制度名称",
            value: state.institutionData.title,
            isLabelRed: true,
        },
        {
            label: "制度标签",
            value: (
                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "2px",
                        padding: "2px 0",
                    }}
                >
                    <span>
                        制度层级：{" "}
                        {state.institutionData.tags?.Tef &&
                            state.institutionData.tags.Tef.length > 0
                            ? state.institutionData.tags.Tef[0].codeName
                            : ""}
                    </span>
                    <span>
                        制度类别：{" "}
                        {state.institutionData.tags?.TeL &&
                            state.institutionData.tags.TeL.length > 0
                            ? state.institutionData.tags.TeL[0].codeName
                            : ""}
                    </span>
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "合规风险清单",
            value: (
                <div
                    style={{
                        display: "flex",
                        alignItems: "center", // 垂直居中
                        justifyContent: "flex-end", // 水平靠右
                    }}
                >
                    <Button
                        style={{ marginRight: "10px" }}
                        onClick={() => handleDetailClick(state.abolishData.institutionId)}
                    >
                        查看清单
                    </Button>
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "废止理由",
            value: state.abolishData.abolishCause || "",
            isLabelRed: true,
        },
        {
            label: "已办意见",
            value: (
                <div>
                    {state.examineListData && state.examineListData.length > 0 ? (
                        state.examineListData.map((opinion: any, index: number) => (
                            <div
                                key={opinion.id || index}
                                style={{
                                    padding: "4px 0",
                                    color: "#222",
                                    fontSize: "15px",
                                    lineHeight: "1.8",
                                    wordBreak: "break-all",
                                }}
                            >
                                {opinion.operatorDept}：
                                <b style={{ fontWeight: 600 }}>[{opinion.operatorName}]</b>
                                {opinion.examineSuggest}
                                <span style={{ color: "#888", marginLeft: 8 }}>
                                    （{presenter.formatDate(opinion.operatorDate)}）
                                </span>
                            </div>
                        ))
                    ) : (
                        <div
                            style={{
                                padding: "10px",
                                textAlign: "center",
                                color: "#999",
                                fontStyle: "italic",
                            }}
                        >
                            暂无已办意见
                        </div>
                    )}
                </div>
            ),
            isLabelRed: true,
        }
    ];
    return (
        <>
            <div className="twoPopul_header_box">
                <div className="tab_item">
                    <Tabs
                        className="item_tabs"
                        defaultActiveKey={activeKey}
                        items={items}
                        onChange={onChange}
                    />
                </div>
                <div style={{ marginRight: '30px' }}>
                </div>
            </div>
            <div className="twoPopul_body_box">
                {activeKey == "1" && (
                    <CustomSplitter
                        leftDefaultSize={825}
                        style={{ padding: 24 }}
                        left={
                            <div
                                style={{
                                    transform: `scale(1.0)`,
                                    transformOrigin: "top left", // 设置缩放原点为左上角
                                    width: "789px",
                                    overflow: "auto",
                                }}
                                dangerouslySetInnerHTML={{
                                    __html: state.institutionData?.htmlContent,
                                }}
                            />
                        }
                        right={
                            <div className={`compliance_right_box`}>
                                <GenericFormTable header={tableHeader} rows={tableRows} />
                            </div>
                        }
                    />
                )}
                {activeKey == "2" && <div className="process">
                    <FlowImage recordId={state.abolishData.recordId} />
                </div>}
            </div>

            <ComplianceDetailModal
                visible={detailModalVisible}
                onClose={() => setDetailModalVisible(false)}
                institutionId={selectedInstitutionId || 0}
            />
        </>
    );
};

export default AbolishDetail;
