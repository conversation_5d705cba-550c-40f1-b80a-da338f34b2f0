import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Tabs, Card, Flex, Typography, Space, Tag, Table, Modal, Input, Empty, Tooltip } from 'antd';
import './index.scss';
import { StopOutlined, CalendarOutlined, BookOutlined, InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { useAbolishPresenter } from './hooks/usePresenter';
import dayjs from "dayjs";

const { Title, Text } = Typography;

const Institutional = () => {
    const { state, presenter } = useAbolishPresenter();
    const [activeKey, setActiveKey] = React.useState<string>("1");
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedId, setSelectedId] = useState<number | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [tableParams, setTableParams] = useState<any>({
        pagination: {
            current: 1,
            pageSize: 10,
        },
    });
    const [filteredAbolishableInstitutions, setFilteredAbolishableInstitutions] = useState<any[]>([]);

    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        setTableParams({
            pagination,
            filters,
            ...sorter,
        });
    };

    const item_tabs = [
        { label: '我发起的废止流程', key: '1' },
        { label: '已废止的制度', key: '2' },
    ];

    const abolishedColumns = [
        {
            title: '制度名称',
            dataIndex: 'title',
            key: 'title',
            width: "22%",
            align: 'left' as const,
            ellipsis: true,
            render: (text: string) => (
                <Tooltip placement="topLeft" title={text}>
                    <span
                        style={{
                            maxWidth: 330,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'normal',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            display: '-webkit-box',
                            verticalAlign: 'middle',
                        }}
                    >
                        {text}
                    </span>
                </Tooltip>
            ),
        },
        {
            title: '发文部门',
            dataIndex: 'pushOrgName',
            key: 'pushOrgName',
            width: "16%",
            align: 'left' as const,
            ellipsis: true,
            render: (text: string) => (
                <Tooltip placement="topLeft" title={text}>
                    <span style={{ display: 'inline-block', maxWidth: 220, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', verticalAlign: 'middle' }}>{text}</span>
                </Tooltip>
            ),
        },
        {
            title: '发布时间',
            dataIndex: 'pushTime',
            key: 'pushTime',
            width: '12%',
            align: 'left' as const,
            render: (timestamp: number) => {
                return timestamp !== null ? dayjs(timestamp).format('YYYY-MM-DD') : '';
            },
        },
        {
            title: '废止时间',
            dataIndex: 'abolishDate',
            key: 'abolishDate',
            width: '12%',
            align: 'left' as const,
            render: (timestamp: number) => {
                return timestamp !== null ? dayjs(timestamp).format('YYYY-MM-DD') : '';
            },
        },
        {
            title: '废止原因',
            dataIndex: 'abolishCause',
            key: 'abolishCause',
            width: '30%',
            align: 'left' as const,
            ellipsis: true,
            render: (text: string) => (
                <Tooltip placement="topLeft" title={text}>
                    <span
                        style={{
                            maxWidth: 400,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'normal',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            display: '-webkit-box',
                            verticalAlign: 'middle',
                        }}
                    >
                        {text}
                    </span>
                </Tooltip>
            ),
        },
        {
            title: '操作',
            key: 'action',
            width: '8%',
            align: 'left' as const,
            render: (_: any, record: any) => (
                <Button type="link" onClick={() => handleDetailClick(record.abolishId, record.id)}>查看详情</Button>
            ),
        },
    ];

    // 查看详情
    const handleDetailClick = (abolishId: number, institutionId: number) => {
        window.open(`/#/abolish/detail?abolishId=${abolishId}&institutionId=${institutionId}`, '_blank');
    };
    //继续处理
    const handleToDoClick = (id: number) => {
        window.open(`/#/abolish/write?id=${id}`, '_blank');
    };
    const handleOk = () => {
        if (selectedId !== null) {
            window.open(`/#/abolish/write?id=${selectedId}`, '_blank');
        }
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
        setSelectedId(null)
    };
    //废止弹窗展开
    const handleOpenAbolish = () => {
        setIsModalOpen(true);
        setSelectedId(null)
        presenter.getCanAbolishList();
    };

    // 获取可废止的制度列表
    useEffect(() => {
        setFilteredAbolishableInstitutions(state.canAbolishInstitutionList)
    }, [state.canAbolishInstitutionList]);

    // 筛选可废止的制度列表
    useEffect(() => {
        setFilteredAbolishableInstitutions(state.canAbolishInstitutionList.filter(item =>
            item.title.toLowerCase().includes(searchTerm.toLowerCase())
        ))
    }, [searchTerm]);

    //初始化加载
    useEffect(() => {
        presenter.getMyAbolishList()
    }, []);

    // 切换标签页加载
    useEffect(() => {
        if (activeKey === '1') {
            presenter.getMyAbolishList()
        }
        if (activeKey === '2') {
            presenter.getAlreadyAbolishList(tableParams.pagination.current, tableParams.pagination.pageSize)
        }
    }, [activeKey, tableParams]);

    return (
        <div className="abolish-page-container">
            <Card style={{ marginBottom: 24 }}>
                <Flex justify="space-between" align="center">
                    <div>
                        <Title level={4} style={{ margin: 0, fontSize: '20px', fontWeight: 'bold' }}>去废止</Title>
                        <Text type="secondary" style={{ fontSize: '14px' }}>发起制度废止流程或查看废止记录</Text>
                    </div>
                    <Button type="primary" danger icon={<StopOutlined />} onClick={handleOpenAbolish}>发起废止流程</Button>
                </Flex>
            </Card>

            <Card style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Tabs activeKey={activeKey} items={item_tabs} onChange={setActiveKey} />
                <div className="scrollable-content" style={{ flex: 1, minHeight: 0 }}>
                    {activeKey === '1' && (
                        <div style={{ height: 600, marginTop: '24px', overflowY: 'auto' }}>
                            {state.myAbolishInstitutionList.length === 0 ? (
                                <div style={{ textAlign: 'center', color: '#999', marginTop: 100, fontSize: 16 }}>暂无数据</div>
                            ) : (
                                state.myAbolishInstitutionList.map(item => (
                                    <Card key={item.id} style={{ marginBottom: 16, borderColor: '#e8e8e8' }}>
                                        <Flex vertical>
                                            <Flex justify="space-between" align="center">
                                                <Title
                                                    level={5}
                                                    style={{ fontWeight: 'bold', margin: 0, cursor: 'pointer', color: '#1677ff'}}
                                                    onClick={() => handleDetailClick(item.id, item.institutionId)}
                                                >
                                                    {item.title}
                                                </Title>
                                                <div>
                                                    {item.endDate === null && <Tag color="yellow">进行中</Tag>}
                                                    {item.endDate !== null && <Tag color="green">已完成</Tag>}
                                                </div>
                                            </Flex>

                                            <Space size={40} style={{ marginTop: 24, marginBottom: 16 }}>
                                                <Text type="secondary">
                                                    <CalendarOutlined style={{ marginRight: 8 }} />
                                                    发起时间: {dayjs(item.operatorDate).format('YYYY-MM-DD : HH:mm:ss')}
                                                </Text>
                                                {item.endDate === null ? (
                                                    <Text type="secondary">
                                                        <BookOutlined style={{ marginRight: 8 }} />
                                                        当前环节: {item.recordTitle != null ? item.recordTitle : '待提交'}
                                                    </Text>
                                                ) : (
                                                    <Text type="secondary">
                                                        <BookOutlined style={{ marginRight: 8 }} />
                                                        当前环节: {'流程已结束'}
                                                    </Text>
                                                )}

                                            </Space>

                                            <Flex justify="end">
                                                <Space>
                                                    {item.recordId !== null && <Button type="link" onClick={() => handleDetailClick(item.id, item.institutionId)}>查看详情</Button>}
                                                    {item.recordId === null && <Button type="primary" onClick={() => handleToDoClick(item.institutionId)}>继续处理</Button>}
                                                </Space>
                                            </Flex>
                                        </Flex>
                                    </Card>
                                ))
                            )}
                        </div>
                    )}

                    {activeKey === '2' && (
                        state.alreadyAbolishInstitutionList.length === 0 ? (
                            <div style={{ textAlign: 'center', color: '#999', marginTop: 100, fontSize: 16 }}>暂无数据</div>
                        ) : (
                            <Table
                                columns={abolishedColumns}
                                dataSource={state.alreadyAbolishInstitutionList}
                                pagination={{ ...tableParams.pagination, total: state.alreadyAbolishInstitutionTotal }}
                                rowKey="id"
                                scroll={{ y: 500 }}
                                style={{ marginTop: '24px' }}
                                onChange={handleTableChange}
                            />
                        )
                    )}
                </div>
            </Card>

            <Modal
                title="选择要废止的制度"
                open={isModalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                width={600}
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        取消
                    </Button>,
                    <Button key="ok" type="primary" danger disabled={!selectedId} onClick={handleOk}>
                        确定
                    </Button>,
                ]}
            >
                <Input
                    placeholder="搜索制度名称"
                    prefix={<SearchOutlined />}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ marginBottom: '16px' }}
                />
                <div style={{ maxHeight: '50vh', overflowY: 'auto' }}>
                    {state.canAbolishInstitutionList.length === 0 ? (
                        <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ marginTop: 60, marginBottom: 60 }} />
                    ) : (
                        filteredAbolishableInstitutions.map((item) => (
                            <div
                                key={item.id}
                                onClick={() => setSelectedId(item.id)}
                                style={{
                                    padding: '16px',
                                    border: selectedId === item.id ? '1.5px solid #ff4d4f' : '1px solid #f0f0f0',
                                    borderRadius: '8px',
                                    marginBottom: '16px',
                                    cursor: 'pointer',
                                    background: selectedId === item.id ? '#fff1f0' : '#ffffff',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    transition: 'border-color 0.3s, background-color 0.3s',
                                }}
                            >
                                <div>
                                    <Text style={{ fontWeight: 600, fontSize: 16 }}>{item.title}</Text>
                                    <br />
                                    <Text type="secondary">
                                        文号: {item.docNumber} 发布时间: {dayjs(item.pushTime).format("YYYY-MM-DD")}
                                    </Text>
                                </div>
                                <Tag color="green">现行有效</Tag>
                            </div>
                        ))
                    )}
                </div>
                <div style={{ marginTop: '16px' }}>
                    <Text style={{ color: '#1677ff' }}>
                        <InfoCircleOutlined style={{ marginRight: '8px' }} />
                        提示: 废止制度需提供充分理由并经过审批
                    </Text>
                </div>
            </Modal>
        </div>
    );
};

export default Institutional;