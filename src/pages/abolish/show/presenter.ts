import { AbolishApi, AbolishState, Dispatch } from "@/pages/abolish/show/types";

export class AbolishPresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<AbolishState>;
    private readonly api: AbolishApi;
    private state: AbolishState;

    public constructor(dispatch: Dispatch<AbolishState>, api: AbolishApi, state: AbolishState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }
    public syncState = (newStatus: AbolishState) => {
        this.state = newStatus;
    }

    // 时间格式化工具
    public formatDate = (timestamp: number | null | undefined) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        return `${y}-${m}-${d}`;
    };

    // 获取我的abolish列表
    public getMyAbolishList = async () => {
        const res = await this.api.getMyAbolishList();
        console.log("获取我的 abolish 列表成功", res);
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    myAbolishInstitutionList: res.data?.list,
                }
            });
        }
    };
    // 获取已 abolish列表
    public getAlreadyAbolishList = async (current: any, pageSize: any) => {
        const res = await this.api.getAlreadyAbolishList(current, pageSize);
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    alreadyAbolishInstitutionList: res.data?.list,
                    alreadyAbolishInstitutionTotal: res.data?.total,
                }
            });
        }
    }
    // 获取可abolish列表
    public getCanAbolishList = async () => {
        const res = await this.api.getcanAbolishList();
        if (res.success) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    canAbolishInstitutionList: res.data?.list,
                }
            });
        }
    };
} 