import { AbolishApi } from "./types";
import { getMyAbolishInstitutionList, getAlreadyAbolishInstitutionList, getCanAbolishInstitutionList } from "@/api/abolish/abolish";

/**
 * Model层接口实现
 * AbolishApiImpl 实现了AbolishApi接口，提供了数据的增删改查操作
 */
export class AbolishApiImpl implements AbolishApi {
    async getMyAbolishList(): Promise<any> {
        try {
            const res = await getMyAbolishInstitutionList();
            return res;
        } catch (err) {
            throw err;
        }
    };
    async getAlreadyAbolishList(current: any, pageSize: any): Promise<any> {
        try {
            const res = await getAlreadyAbolishInstitutionList(current, pageSize);
            return res;
        } catch (err) {
            throw err;
        }
    };
    async getcanAbolishList(): Promise<any> {
        try {
            const params = {
                title: null
            };
            const res = await getCanAbolishInstitutionList(params);
            return res;
        } catch (err) {
            throw err;
        }
    };
}