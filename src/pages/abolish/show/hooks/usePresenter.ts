import React from "react";
import { AbolishState } from "@/pages/abolish/show/types";
import { AbolishPresenter } from "@/pages/abolish/show/presenter";
import { AbolishApiImpl } from "@/pages/abolish/show/model";

const initialState: AbolishState = {
    myAbolishInstitutionList: [],
    alreadyAbolishInstitutionList: [],
    alreadyAbolishInstitutionTotal: 0,
    canAbolishInstitutionList: [],
}

export function useAbolishPresenter() {
    const [state, dispatch] = React.useState<AbolishState>(initialState);

    const presenterRef = React.useRef<AbolishPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 AbolishPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new AbolishPresenter(dispatch, new AbolishApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
} 