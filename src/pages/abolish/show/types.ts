export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */
export interface AbolishState {
    myAbolishInstitutionList: any[];
    alreadyAbolishInstitutionList: any[];
    alreadyAbolishInstitutionTotal: number;
    canAbolishInstitutionList: any[];
}

export interface AbolishApi {
    getMyAbolishList: () => Promise<any>;
    getAlreadyAbolishList: (current: any, pageSize: any) => Promise<any>;
    getcanAbolishList: () => Promise<any>;
}
