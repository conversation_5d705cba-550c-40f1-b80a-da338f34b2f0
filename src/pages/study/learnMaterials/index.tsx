import React from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {Col, message, Popconfirm, Row, Switch, Card, Menu, Divider} from "antd";
import {useValuePresenter} from "@/pages/study/learnMaterials/hooks/usePresenter";
import {Button, Modal, ProTable} from "@/components-pc";
import {Form, FormInput, FormRadio, FormSelect, FormTextArea} from "@/form-pc";
import {useNavigate} from "react-router-dom";
import ArchiveFileUploader from "@/components/archive/archiveFileUpload";
import {FolderOutlined, UnorderedListOutlined} from "@ant-design/icons";

interface LearnMaterialsManagePageProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, name: string) => void;
}

const LearnMaterialsManagePage: React.FC<LearnMaterialsManagePageProps> = (props) => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();
    const {state, presenter} = useValuePresenter();
    const [selectedCategory, setSelectedCategory] = React.useState<number | null>(null);

    const navigate = useNavigate();

    React.useEffect(() => {
        presenter?.getCategoryList();
    }, [props.isComponent, presenter]);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion, selectedCategory]);

    const handleCategorySelect = (categoryId: number | null) => {
        setSelectedCategory(categoryId);
        // 重置表格到第一页
        actionRef.current?.setPageInfo?.({
            ...actionRef.current?.pageInfo,
            current: 1
        });
    };

    const columns: ProColumns<any>[] = [
        {
            title: "序号",
            search: false,
            width: 100,
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "资料名称",
            dataIndex: "fileName",
            search: true,
            hidden: props.isComponent,
        },
        {
            title: "类型",
            dataIndex: "type",
            search: false,
            hidden: props.isComponent,
            render: (text) => {
                const statusMap = {
                    "1": "文档",
                    "2": "视频",
                };
                return statusMap[text] || '-';
            },
        },
        {
            title: "状态",
            valueType: "option",
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        try {
                            if (record.sysEnabled) {
                                await presenter?.disable(record.id);
                                message.success("禁用成功");
                            } else {
                                await presenter?.enable(record.id);
                                message.success("启用成功");
                            }
                        } catch (e: any) {
                            message.error(e.message || "操作失败");
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" checked={record.sysEnabled} />
                </Popconfirm>
            ],
        },
        {
            title: "排序号",
            dataIndex: "sysIndex",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "创建时间",
            dataIndex: "sysCreateTime",
            search: false,
            hidden: props.isComponent,
            render: (timestamp: number | string) : string => {
                if (!timestamp) return "-";
                const d = new Date(Number(timestamp));
                return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')} ${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`;
            }
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            hidden: props.isComponent,
            render: (_, record) => [
                <a
                    key="edit"
                    onClick={async () => {
                        try {
                            const detail = await presenter?.getInfo(record.id);
                            if (detail) {
                                form.reset();
                                form.setFieldsValue({learnMaterials: detail});
                                presenter?.resetFileKey()
                                presenter?.setFileKey((detail as any)?.fileKey);
                                presenter?.setAddVisible(true);
                            }
                        } catch (e: any) {
                            message.error("加载详情失败: " + e.message);
                        }
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title="提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={() => {
                        presenter?.delete(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    // 处理表单提交
    const handleFormSubmit = async () => {
        try {
            // 获取表单值并移除嵌套的category属性
            const formValues = form.getFieldsValue();
            const submitData = formValues.learnMaterials || {};

            // 提交数据
            await presenter?.add(submitData);

            // 关闭弹窗并重置表单
            presenter?.setAddVisible(false);
            form.reset();
        } catch (e: any) {
            message.error("操作失败: " + e.message);
        }
    };

    return (
        <PageContainer>
            <Row gutter={[16, 16]}>
                {/* 左侧分类导航 */}
                {!props.isComponent && (
                    <Col xs={24} sm={24} md={6} lg={5} xl={4}>
                        <Menu
                            mode="inline"
                            selectedKeys={selectedCategory?.toString() || ['all']}
                            style={{height: '100%', borderRight: 0}}
                        >

                            {state.categoryList && state.categoryList.length > 0 ? (
                                state.categoryList.map(item => (
                                    <Menu.Item
                                        key={item.id}
                                        icon={<FolderOutlined/>}
                                        onClick={() => handleCategorySelect(item.id)}
                                    >
                                        {item.name}
                                    </Menu.Item>
                                ))
                            ) : (
                                <div style={{textAlign: 'center', padding: '16px', color: '#999'}}>
                                    {state.categoryList === undefined ? '加载中...' : '暂无分类数据'}
                                </div>
                            )}
                        </Menu>
                    </Col>
                )}

                {/* 右侧资料表格 */}
                <Col xs={24} sm={24} md={!props.isComponent ? 18 : 24} lg={!props.isComponent ? 19 : 24} xl={!props.isComponent ? 20 : 24}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="学习资料列表"
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                !props.isComponent && (
                                    <Button
                                        key="upload"
                                        type="primary"
                                        onClick={() => {
                                            form.reset();
                                            presenter?.resetUploadFile()
                                            presenter?.resetFileKey()
                                            presenter?.setAddVisible(true)
                                        }}
                                    >
                                        新增
                                    </Button>
                                ),
                            ].filter(Boolean)
                        }}
                        request={async (params, sort, filter) => {
                            // 添加分类筛选条件
                            const queryParams = selectedCategory ?
                                {...params, categoryId: selectedCategory} :
                                params;
                            return presenter?.searchTable(queryParams, sort, filter);
                        }}
                    />
                </Col>
            </Row>

            <Modal
                width={1000}
                centered={true}
                open={state.addVisible}
                title={form.getFieldValue(['learnMaterials', 'id']) ? "编辑" : "新增"}
                onCancel={() => {
                    presenter?.setAddVisible(false);
                    presenter?.resetUploadFile();
                }}
                onOk={async () => {
                    if (await form.validate()) {
                        await handleFormSubmit();
                    }
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                >
                    <FormInput
                        name={["learnMaterials", "id"]}
                        hidden={true}
                    />
                    <FormInput
                        required={true}
                        name={["learnMaterials", "fileName"]}
                        label={"资料名称"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value && value.trim()) {
                                return [];
                            }
                            return ['学习资料名称不能为空'];
                        }}
                    />
                    <FormSelect
                        required={true}
                        name={["learnMaterials", "categoryId"]}
                        label={"资料分类"}
                        options={(state.categoryList || []).map(item => ({
                            label: item.name,
                            value: item.id
                        }))}
                        placeholder={(state.categoryList || []).length === 0 ? "加载中..." : "请选择资料分类"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value !== undefined && value !== null && value !== "") {
                                return [];
                            }
                            return ['资料分类不能为空'];
                        }}
                    />
                    <FormRadio
                        required={true}
                        name={["learnMaterials", "type"]}
                        label={"类型"}
                        options={[
                            {
                                label: "文档",
                                value: "1"
                            },
                            {
                                label: "视频",
                                value: "2"
                            }
                        ]}
                    />

                    <FormInput
                        required={true}
                        name={["learnMaterials", "sysIndex"]}
                        label={"排序号"}
                        type="number"
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value !== undefined && value !== null && !isNaN(Number(value))) {
                                return [];
                            }
                            return ['排序号必须为有效数字'];
                        }}
                    />
                    <FormInput
                        name={["learnMaterials", "sysEnabled"]}
                        hidden={true}
                    />
                    <FormInput
                        name={["learnMaterials", "fileKey"]}
                        hidden={true}
                        initialValue={state.fileKey}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value && value.trim()) {
                                return [];
                            }
                            return ['请上传资料文件'];
                        }}
                    />
                    <ArchiveFileUploader
                        label="资料上传"
                        required={true}
                        uploaderMaxCount={1}
                        uploaderAccept="*"
                        archiveFileKey={state.fileKey}
                        hideUploadButton={!!state.fileKey}
                        onKeyChange={(key) => {
                            presenter?.setFileKey(key);
                            form.setFieldsValue({
                                learnMaterials: { ...form.getFieldValue("learnMaterials"), fileKey: key }
                            });
                        }}
                    />
                    <FormTextArea
                        required={false}
                        name={["learnMaterials", "remark"]}
                        label={"备注"}
                    />
                </Form>
            </Modal>
        </PageContainer>
    );
};

export default LearnMaterialsManagePage;
