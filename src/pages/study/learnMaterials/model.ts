import {LearnMaterialsApi} from "./types";
import {
    list,
    del,
    addOrUpdate,
    getInfo,
    disable,
    enable,
    getCategoryList
} from "@/api/study/learnMaterials";

export class LearnMaterialsApiImpl implements LearnMaterialsApi {

    loadTable(params: any, sort: any, filter: any): Promise<any> {
        return list(params, sort, filter, []);
    }

    getInfo(params: any): Promise<any> {
        return getInfo(params);
    }

    addOrUpdate(formData: any): Promise<any> {
        return addOrUpdate(formData);
    }

    async delete(params: any): Promise<{ success: boolean; message?: string }> {
        try {
            const res = await del(params);
            return {success: true};
        } catch (err: any) {
            return {success: false, message: err.message};
        }
    }

    disable(id: any): any {
        return disable(id);
    }

    enable(id: any): any {
        return enable(id);
    }

    getCategoryList(): Promise<any> {
        return getCategoryList();
    }
}
