export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface LearnMaterialsState {
    tableRefreshVersion: number; // 表格刷新版本号，用于表格刷新
    searchParams: any;
    addVisible: boolean;
    categoryList: any;
    fileKey: string;//附件回显
}


export interface LearnMaterialsApi {

    loadTable: (params: any, sort: any, filter: any) => Promise<any>;

    getInfo: (params: any) => Promise<any>;

    delete: (params: any) => Promise<{ success: boolean }>;

    addOrUpdate: (formData: FormData) => Promise<any>;

    enable: (id: any) => Promise<any>;

    disable: (id: any) => Promise<any>;

    getCategoryList: () => Promise<any>;

}
