import React from "react";
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import {Col, message, Popconfirm, Row, Switch} from "antd";
import {useValuePresenter} from "@/pages/study/category/hooks/usePresenter";
import {<PERSON><PERSON>, Modal, ProTable} from "@/components-pc";
import {Form, FormInput, FormTextArea} from "@/form-pc";
import {useNavigate} from "react-router-dom";

interface CategoryManagePageProps {
    isComponent?: boolean;
    onSelectedChange?: (id: number, name: string) => void;
}

const CategoryManagePage: React.FC<CategoryManagePageProps> = (props) => {

    const actionRef = React.useRef<ActionType>();
    const form = Form.useForm();
    const {state, presenter} = useValuePresenter();

    const navigate = useNavigate();

    React.useEffect(() => {
        // 可以在这里添加组件初始化逻辑
    }, [props.isComponent]);

    React.useEffect(() => {
        actionRef.current?.reload();
    }, [state.tableRefreshVersion]);

    const columns: ProColumns<any>[] = [
        {
            title: "序号",
            search: false,
            width: 100,
            dataIndex: "no",
            render: (_: any, record: any, index: number) => {
                const currentPage = actionRef.current?.pageInfo?.current || 1;
                const pageSize = actionRef.current?.pageInfo?.pageSize || 20;
                return (currentPage - 1) * pageSize + index + 1;
            },
        },
        {
            title: "分类名称",
            dataIndex: "name",
            search: true,
            hidden: props.isComponent,
        },
        {
            title: "分类编码",
            dataIndex: "code",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "状态",
            valueType: "option",
            render: (_: any, record: any) => [
                <Popconfirm
                    key="enableConfirm"
                    title={"确认" + (record.sysEnabled ? "禁用" : "启用") + "?"}
                    onConfirm={async () => {
                        try {
                            if (record.sysEnabled) {
                                await presenter?.disable(record.id);
                                message.success("禁用成功");
                            } else {
                                await presenter?.enable(record.id);
                                message.success("启用成功");
                            }
                        } catch (e: any) {
                            message.error(e.message || "操作失败");
                        }
                    }}
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" checked={record.sysEnabled} />
                </Popconfirm>
            ],
        },
        {
            title: "排序号",
            dataIndex: "sysIndex",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            hidden: props.isComponent,
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            hidden: props.isComponent,
            render: (_, record) => [
                <a
                    key="edit"
                    onClick={async () => {
                        try {
                            const detail = await presenter?.getInfo(record.id);
                            if (detail) {
                                form.reset();
                                form.setFieldsValue({category: detail});
                                presenter?.setAddVisible(true);
                            }
                        } catch (e: any) {
                            message.error("加载详情失败: " + e.message);
                        }
                    }}
                >
                    编辑
                </a>,
                <Popconfirm
                    key="delete"
                    title="提示"
                    description="确认要删除这条数据吗?"
                    onConfirm={() => {
                        presenter?.delete(record);
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <a
                        key="delete"
                        hidden={(record.children?.length ?? 0) !== 0}
                    >
                        删除
                    </a>
                </Popconfirm>,
            ],
        },
    ];

    // 处理表单提交
    const handleFormSubmit = async () => {
        try {
            // 获取表单值并移除嵌套的category属性
            const formValues = form.getFieldsValue();
            const submitData = formValues.category || {};

            // 提交数据
            await presenter?.add(submitData);

            // 关闭弹窗并重置表单
            presenter?.setAddVisible(false);
            form.reset();
        } catch (e: any) {
            message.error("操作失败: " + e.message);
        }
    };

    return (
        <PageContainer>
            <Row>
                <Col span={24}>
                    <ProTable
                        actionRef={actionRef}
                        rowKey="id"
                        headerTitle="分类列表"
                        columns={columns}
                        toolBarRender={() => {
                            return [
                                !props.isComponent && (
                                    <Button
                                        key="upload"
                                        type="primary"
                                        onClick={() => {
                                            form.reset();
                                            presenter?.setAddVisible(true)
                                        }}
                                    >
                                        新增
                                    </Button>
                                ),
                            ].filter(Boolean)
                        }}
                        request={async (params, sort, filter) => {
                            return presenter?.searchTable(params, sort, filter);
                        }}
                    />
                </Col>
            </Row>

            <Modal
                width={1000}
                centered={true}
                open={state.addVisible}
                title={form.getFieldValue(['category', 'id']) ? "编辑" : "新增"}
                onCancel={() => {
                    presenter?.setAddVisible(false);
                }}
                onOk={async () => {
                    if (await form.validate()) {
                        await handleFormSubmit();
                    }
                }}
            >
                <Form
                    form={form}
                    layout={"vertical"}
                >
                    <FormInput
                        name={["category", "id"]}
                        hidden={true}
                    />
                    <FormInput
                        required={true}
                        name={["category", "name"]}
                        label={"分类名称"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value && value.trim()) {
                                return [];
                            }
                            return ['分类名称不能为空'];
                        }}
                    />
                    <FormInput
                        name={["category", "sysEnabled"]}
                        hidden={true}
                    />
                    <FormInput
                        required={true}
                        name={["category", "code"]}
                        label={"分类编码"}
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value && value.trim()) {
                                return [];
                            }
                            return ['分类编码不能为空'];
                        }}
                    />
                    <FormInput
                        required={true}
                        name={["category", "sysIndex"]}
                        label={"排序号"}
                        type="number"
                        validateFunction={async (content) => {
                            const value = content.value;
                            if (value !== undefined && value !== null && !isNaN(Number(value))) {
                                return [];
                            }
                            return ['排序号必须为有效数字'];
                        }}
                    />
                    <FormTextArea
                        required={false}
                        name={["category", "remark"]}
                        label={"备注"}
                    />
                </Form>
            </Modal>
        </PageContainer>
    );
};

export default CategoryManagePage;
