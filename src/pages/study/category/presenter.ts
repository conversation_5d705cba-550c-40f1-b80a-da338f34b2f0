import {CategoryApi, CategoryState, Dispatch} from "@/pages/study/category/types";

export class ValuePresenter {
    // 更新数据状态
    private readonly dispatch: Dispatch<CategoryState>;
    private readonly api: CategoryApi;
    private state: CategoryState;

    public constructor(dispatch: Dispatch<CategoryState>, api: CategoryApi, state: CategoryState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: CategoryState) => {
        this.state = newStatus;
    }

    // 刷新表格数据
    public refreshTable = () => {
        this.dispatch((prevState) => ({
            ...prevState,
            tableRefreshVersion: prevState.tableRefreshVersion + 1,
            searchParams: null,
        }));
    }

    // 删除数据
    public delete = async (record: any) => {
        try {
            const res = await this.api.delete(record);
            if (res.success) {
                this.refreshTable();
            }
        } catch (e) {
            console.error("删除失败", e);
        }
    }

    // 标签表格数据加载函数
    public searchTable = (params: any, sort: any, filter: any) => {
        const {current, pageSize, ...restParams} = params;

        this.dispatch((prevState) => ({
            ...prevState,
            searchParams: restParams,
        }));
        return this.api.loadTable(params, sort, filter);
    }

    public getInfo = async (id: number) => {
        const params = {id};
        const res = await this.api.getInfo(params);
        if (res.success) {
            return res.data;
        }
        throw new Error(res.message || "获取详情失败");
    }

    public setAddVisible = (v: boolean) => {
        this.dispatch((prevState) => ({
            ...prevState,
            addVisible: v
        }));
    }

    public add = async (formData: any) => {
        await this.api.addOrUpdate(formData);
        this.refreshTable();
    };

    public enable = async (id: any) => {
        const res = await this.api.enable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message || "启用失败");
        }
    }

    public disable = async (id: any) => {
        const res = await this.api.disable(id);
        if (res.success) {
            this.refreshTable();
        } else {
            throw new Error(res.message || "禁用失败");
        }
    }
}
