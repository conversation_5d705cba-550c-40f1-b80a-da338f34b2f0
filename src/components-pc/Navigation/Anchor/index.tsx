import React from 'react';
import {Anchor as AntdAnchor, AnchorProps as AntdAnchorProps} from 'antd';

const {Link} = AntdAnchor;

/**
 * ITMAnchor 组件属性接口
 * @extends AnchorProps - 继承 antd Anchor 的所有属性
 */
export interface AnchorProps extends AntdAnchorProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    anchorProps?: AntdAnchorProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMAnchor 组件类型定义
 */
interface AnchorComponent extends React.FC<AnchorProps> {
    Link: typeof Link;
}

/**
 * ITMAnchor 组件
 * @description 基于 antd Anchor 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Anchor: AnchorComponent = (props) => {
    const restProps = {...props, ...props?.anchorProps};
    return <AntdAnchor {...restProps} />;
};

// 导出 Anchor 的子组件
Anchor.Link = Link; 