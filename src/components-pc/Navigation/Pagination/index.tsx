import React from 'react';
import {Pagination as AntdPagination, PaginationProps as AntdPaginationProps} from 'antd';

/**
 * ITMPagination 组件属性接口
 * @extends PaginationProps - 继承 antd Pagination 的所有属性
 */
export interface PaginationProps extends AntdPaginationProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    paginationProps?: AntdPaginationProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMPagination 组件
 * @description 基于 antd Pagination 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Pagination: React.FC<PaginationProps> = (props) => {
    const restProps = {...props, ...props?.paginationProps};

    return <AntdPagination {...restProps} />;
}; 