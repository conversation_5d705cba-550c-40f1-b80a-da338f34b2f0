import React from 'react';
import {Dropdown as AntdDropdown, DropdownProps as AntdDropdownProps} from 'antd';

// 扩展 DropdownProps 类型，添加自定义属性
export interface DropdownProps extends AntdDropdownProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 下拉菜单的触发方式
     */
    trigger?: ('click' | 'hover' | 'contextMenu')[];
    /**
     * 下拉菜单的位置
     */
    placement?: 'bottomLeft' | 'bottomCenter' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight';
    /**
     * 组件原始属性
     */
    dropdownProps?: AntdDropdownProps;
}

export const Dropdown: React.FC<DropdownProps> = (props) => {
    const restProps = {...props, ...props?.dropdownProps};

    return (
        <AntdDropdown
            className={restProps?.className}
            trigger={restProps?.trigger}
            placement={restProps?.placement}
            {...restProps}
        />
    );
}; 