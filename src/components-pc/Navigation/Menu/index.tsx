import React from 'react';
import {Menu as AntdMenu, MenuProps as AntdMenuProps} from 'antd';

const {Item, SubMenu, ItemGroup, Divider} = AntdMenu;

/**
 * ITMMenu 组件属性接口
 * @extends MenuProps - 继承 antd Menu 的所有属性
 */
export interface MenuProps extends AntdMenuProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    menuProps?: AntdMenuProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMMenu 组件类型定义
 */
interface MenuComponent extends React.FC<MenuProps> {
    Item: typeof Item;
    SubMenu: typeof SubMenu;
    ItemGroup: typeof ItemGroup;
    Divider: typeof Divider;
}

/**
 * ITMMenu 组件
 * @description 基于 antd Menu 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Menu: MenuComponent = (props) => {
    const restProps = {...props, ...props?.menuProps};
    return <AntdMenu {...restProps} />;
};

// 导出 Menu 的子组件
Menu.Item = Item;
Menu.SubMenu = SubMenu;
Menu.ItemGroup = ItemGroup;
Menu.Divider = Divider; 