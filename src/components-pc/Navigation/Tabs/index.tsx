import React from 'react';
import {Tabs as AntdTabs, TabsProps as AntdTabsProps} from 'antd';

const {TabPane} = AntdTabs;

/**
 * ITMTabs 组件属性接口
 * @extends TabsProps - 继承 antd Tabs 的所有属性
 */
export interface TabsProps extends AntdTabsProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    tabsProps?: AntdTabsProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMTabs 组件类型定义
 */
interface TabsComponent extends React.FC<TabsProps> {
    TabPane: typeof TabPane;
}

/**
 * ITMTabs 组件
 * @description 基于 antd Tabs 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Tabs: TabsComponent = (props) => {
    const restProps = {...props, ...props?.tabsProps};

    return <AntdTabs {...restProps} />;
};

// 导出 Tabs 的子组件
Tabs.TabPane = TabPane; 