import React from 'react';
import {Steps as AntdSteps, StepsProps as AntdStepsProps} from 'antd';

const {Step} = AntdSteps;

/**
 * ITMSteps 组件属性接口
 * @extends StepsProps - 继承 antd Steps 的所有属性
 */
export interface StepsProps extends AntdStepsProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    stepsProps?: AntdStepsProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMSteps 组件类型定义
 */
interface StepsComponent extends React.FC<StepsProps> {
    Step: typeof Step;
}

/**
 * ITMSteps 组件
 * @description 基于 antd Steps 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Steps: StepsComponent = (props) => {
    const restProps = {...props, ...props?.stepsProps};

    return <AntdSteps {...restProps} />;
};

// 导出 Steps 的子组件
Steps.Step = Step; 