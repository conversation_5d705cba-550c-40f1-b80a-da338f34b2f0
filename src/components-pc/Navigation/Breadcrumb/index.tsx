import React from 'react';
import {Breadcrumb as AntdBreadcrumb, BreadcrumbProps as AntdBreadcrumbProps} from 'antd';

const {Item} = AntdBreadcrumb;

/**
 * ITMBreadcrumb 组件属性接口
 * @extends BreadcrumbProps - 继承 antd Breadcrumb 的所有属性
 */
export interface BreadcrumbProps extends AntdBreadcrumbProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    breadcrumbProps?: AntdBreadcrumbProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMBreadcrumb 组件类型定义
 */
interface BreadcrumbComponent extends React.FC<BreadcrumbProps> {
    Item: typeof Item;
}

/**
 * ITMBreadcrumb 组件
 * @description 基于 antd Breadcrumb 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Breadcrumb: BreadcrumbComponent = (props) => {
    const restProps = {...props, ...props?.breadcrumbProps};
    return <AntdBreadcrumb {...restProps} />;
};

// 导出 Breadcrumb 的子组件
Breadcrumb.Item = Item; 