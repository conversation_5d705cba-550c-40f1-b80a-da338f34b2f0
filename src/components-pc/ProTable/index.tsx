import {ProTable as AntdProTable} from '@ant-design/pro-components';
import type {ProTableProps as AntdProTableProps} from '@ant-design/pro-components';
import './style.css';

// 扩展 ProTableProps 类型，添加自定义属性
export interface ProTableProps<T, U extends Record<string, any>> extends AntdProTableProps<T, U> {
    /**
     * 是否启用自定义动画
     */
    customAnimation?: boolean;
    /**
     * 是否显示表格边框
     */
    bordered?: boolean;
    /**
     * 是否显示表格头部
     */
    showHeader?: boolean;
    /**
     * 组件原始属性
     */
    proTableProps?: AntdProTableProps<T, U>;
}

export const ProTable = <T extends Record<string, any>, U extends Record<string, any>>(props: ProTableProps<T, U>) => {
    const restProps = {...props, ...props?.proTableProps};
    return (
        <AntdProTable<T, U>
            bordered={props?.bordered}
            showHeader={props?.showHeader}
            {...restProps}
        />
    );
};

