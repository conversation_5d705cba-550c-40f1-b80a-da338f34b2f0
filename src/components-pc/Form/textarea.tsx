import React, {useEffect} from "react";
import {FormItemProps} from "@/ui-framework";
import {Form} from "antd";
import {Input} from "../DataEntry/Input";
import formFieldInit from "./common";
import "./index.scss";

export const FormTextArea: React.FC<FormItemProps> = (props) => {

    const {formContext} = formFieldInit(props);

    useEffect(() => {
        formContext?.addFormField(
            {
                type: 'textarea',
                props: props
            }
        );
    }, []);

    return (
        <Form.Item
            name={props.name}
            label={props.label}
            required={props.required}
            hidden={props.hidden}
            help={props.help}
            tooltip={props.tooltip}
        >
            <Input.TextArea
                disabled={props.disabled}
                value={props.value}
                showCount={true}
                placeholder={props.placeholder}
                maxLength={props.textAreaMaxLength}
                rows={props.textAreaRows}
                onChange={(value) => {
                    const currentValue = value.target.value;
                    props.name && formContext?.setFieldValue(props.name, currentValue);
                    props.onChange && props.onChange(currentValue, formContext);
                }}
                {...props.itemProps}
            />
        </Form.Item>
    )
}

