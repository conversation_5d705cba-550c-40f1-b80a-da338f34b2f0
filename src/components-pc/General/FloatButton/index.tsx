import React from "react";
import {FloatButton as AntdFloatButton} from "antd";
import type {FloatButtonProps as AntdFloatButtonProps} from "antd";

/**
 * ITMFloatButton 组件属性接口
 * @extends FloatButtonProps - 继承 antd FloatButton 的所有属性
 */
export interface FloatButtonProps extends AntdFloatButtonProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    floatButtonProps?: AntdFloatButtonProps

}

/**
 * ITMFloatButton 组件类型定义
 */
interface ITMFloatButtonComponent extends React.FC<FloatButtonProps> {
    Group: typeof AntdFloatButton.Group;
}

/**
 * ITMFloatButton 组件
 * @description 基于 antd FloatButton 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const FloatButton: ITMFloatButtonComponent = (props) => {
    const restProps = {...props, ...props?.floatButtonProps};
    return <AntdFloatButton {...restProps} />;
};

/**
 * ITMFloatButton.Group 组件
 * @description 基于 antd FloatButton.Group 的二次封装组件
 */
FloatButton.Group = AntdFloatButton.Group;
