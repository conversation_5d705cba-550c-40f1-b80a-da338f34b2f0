import React from 'react';
import {Button as AntdButton} from 'antd';
import type {ButtonProps as AntdButtonProps} from 'antd';
import './style.css';

// 扩展 ButtonProps 类型，添加自定义属性
export interface ButtonProps extends AntdButtonProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 是否显示加载状态
     */
    loading?: boolean;
    /**
     * 按钮大小
     */
    size?: 'small' | 'middle' | 'large';
    /**
     * 组件原始属性
     */
    buttonProps?: AntdButtonProps

}


export const Button: React.FC<ButtonProps> = (props) => {
    const restProps = {...props, ...props?.buttonProps}
    return (
        <AntdButton
            className={props?.className}
            loading={props?.loading}
            size={props?.size}
            {...restProps}
        />
    );
};