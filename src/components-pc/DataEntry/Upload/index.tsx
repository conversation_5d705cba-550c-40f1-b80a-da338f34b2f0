import React from 'react';
import {Upload as AntdUpload} from 'antd';
import type {UploadProps as AntdUploadProps} from 'antd';
import {InboxOutlined} from '@ant-design/icons';

const {<PERSON><PERSON>} = AntdUpload;

/**
 * ITMUpload 组件属性接口
 * @extends UploadProps - 继承 antd Upload 的所有属性
 */
export interface UploadProps extends AntdUploadProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    uploadProps?: AntdUploadProps;
}

/**
 * ITMUpload 组件
 * @description 基于 antd Upload 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
const Upload: React.FC<UploadProps> & {
    Dragger: React.FC<UploadProps>;
} = (props) => {
    const restProps = {...props, ...props?.uploadProps};

    return <AntdUpload {...restProps} />;
};

/**
 * ITMUpload.Dragger 组件
 * @description 基于 antd Upload.Dragger 的二次封装组件
 */
Upload.Dragger = (props: UploadProps) => {
    const restProps = {...props, ...props?.uploadProps};

    return <AntdUpload.Dragger {...restProps} />;
};

export {Upload};