import React from 'react';
import {Transfer as AntdTransfer} from 'antd';
import type {TransferProps as AntdTransferProps} from 'antd';

/**
 * ITMTransfer 组件属性接口
 * @extends TransferProps - 继承 antd Transfer 的所有属性
 */
export interface TransferProps extends AntdTransferProps<any> {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    transferProps?: AntdTransferProps<any>;
}

/**
 * ITMTransfer 组件
 * @description 基于 antd Transfer 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Transfer: React.FC<TransferProps> = (props) => {
    const restProps = {...props, ...props?.transferProps};

    return <AntdTransfer {...restProps} />;
}; 