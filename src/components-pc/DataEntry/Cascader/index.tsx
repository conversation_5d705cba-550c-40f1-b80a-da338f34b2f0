import React from 'react';
import {Cascader as AntdCascader} from 'antd';
import type {CascaderProps as AntdCascaderProps} from 'antd';
import type {DefaultOptionType} from 'antd/es/cascader';

/**
 * ITMCascader 组件属性接口
 * @extends CascaderProps - 继承 antd Cascader 的所有属性
 */
export interface CascaderProps extends Omit<AntdCascaderProps<DefaultOptionType>, 'multiple'> {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    cascaderProps?: Omit<AntdCascaderProps<DefaultOptionType>, 'multiple'>;
}

/**
 * ITMCascader 组件
 * @description 基于 antd Cascader 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Cascader: React.FC<CascaderProps> = (props) => {
    const restProps = {...props, ...props?.cascaderProps};

    return <AntdCascader<DefaultOptionType> {...restProps} />;
}; 