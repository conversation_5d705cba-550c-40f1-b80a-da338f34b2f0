import React from 'react';
import {Checkbox as AntdCheckbox, Row, Col} from 'antd';
import type {CheckboxProps as AntdCheckboxProps} from 'antd';
import type {CheckboxGroupProps as AntdCheckboxGroupProps} from 'antd/es/checkbox';

/**
 * ITMCheckbox 组件属性接口
 * @extends CheckboxProps - 继承 antd Checkbox 的所有属性
 */
export interface CheckboxProps extends AntdCheckboxProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    checkboxProps?: AntdCheckboxProps;
}

/**
 * ITMCheckboxGroup 组件属性接口
 * @extends CheckboxGroupProps - 继承 antd CheckboxGroup 的所有属性
 */
export interface CheckboxGroupProps extends AntdCheckboxGroupProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    checkboxGroupProps?: AntdCheckboxGroupProps;
    /**
     * 是否使用栅格布局
     */
    useGrid?: boolean;
    /**
     * 栅格列数
     */
    colSpan?: number;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMCheckbox 组件
 * @description 基于 antd Checkbox 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Checkbox: React.FC<CheckboxProps> = (props) => {
    const restProps = {...props, ...props?.checkboxProps};

    return <AntdCheckbox {...restProps} />;
};

/**
 * ITMCheckboxGroup 组件
 * @description 基于 antd Checkbox.Group 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const CheckboxGroup: React.FC<CheckboxGroupProps> = (props) => {
    const {checkboxGroupProps, useGrid, colSpan = 8, children, ...restProps} = props;
    const finalProps = {...restProps, ...checkboxGroupProps};

    console.log('ITMCheckboxGroup props:', finalProps);

    if (useGrid) {
        return (
            <AntdCheckbox.Group {...finalProps}>
                <Row>
                    {React.Children.map(children, (child) => {
                        if (React.isValidElement(child)) {
                            return (
                                <Col span={colSpan}>
                                    {React.cloneElement(child, {
                                        ...child.props,
                                    })}
                                </Col>
                            );
                        }
                        return null;
                    })}
                </Row>
            </AntdCheckbox.Group>
        );
    }

    return <AntdCheckbox.Group {...finalProps}>{children}</AntdCheckbox.Group>;
}; 