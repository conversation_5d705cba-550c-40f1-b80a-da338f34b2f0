import React from 'react';
import {Switch as AntdSwitch} from 'antd';
import type {SwitchProps as AntdSwitchProps} from 'antd';

/**
 * ITMSwitch 组件属性接口
 * @extends SwitchProps - 继承 antd Switch 的所有属性
 */
export interface SwitchProps extends AntdSwitchProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    switchProps?: AntdSwitchProps;
}

/**
 * ITMSwitch 组件
 * @description 基于 antd Switch 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Switch: React.FC<SwitchProps> = (props) => {
    const restProps = {...props, ...props?.switchProps};

    return <AntdSwitch {...restProps} />;
}; 