import React from 'react';
import {Rate as AntdRate} from 'antd';
import type {RateProps as AntdRateProps} from 'antd';

/**
 * ITMRate 组件属性接口
 * @extends RateProps - 继承 antd Rate 的所有属性
 */
export interface RateProps extends AntdRateProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    rateProps?: AntdRateProps;
}

/**
 * ITMRate 组件
 * @description 基于 antd Rate 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Rate: React.FC<RateProps> = (props) => {
    const restProps = {...props, ...props?.rateProps};

    return <AntdRate {...restProps} />;
}; 