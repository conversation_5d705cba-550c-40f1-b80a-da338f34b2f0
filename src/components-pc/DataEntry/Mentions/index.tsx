import React from 'react';
import {Mentions as AntdMentions} from 'antd';
import type {MentionsProps as AntdMentionsProps} from 'antd';

/**
 * ITMMentions 组件属性接口
 * @extends MentionsProps - 继承 antd Mentions 的所有属性
 */
export interface MentionsProps extends AntdMentionsProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    mentionsProps?: AntdMentionsProps;
}

/**
 * ITMMentions 组件
 * @description 基于 antd Mentions 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Mentions: React.FC<MentionsProps> = (props) => {
    const restProps = {...props, ...props?.mentionsProps};

    return <AntdMentions {...restProps} />;
}; 