import React from 'react';
import {InputNumber as AntdInputNumber} from 'antd';
import type {InputNumberProps as AntdInputNumberProps} from 'antd';

/**
 * ITMInputNumber 组件属性接口
 * @extends InputNumberProps - 继承 antd InputNumber 的所有属性
 */
export interface InputNumberProps extends AntdInputNumberProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    inputNumberProps?: AntdInputNumberProps;
}

/**
 * ITMInputNumber 组件
 * @description 基于 antd InputNumber 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const InputNumber: React.FC<InputNumberProps> = (props) => {
    const restProps = {...props, ...props?.inputNumberProps};

    return <AntdInputNumber {...restProps} />;
}; 