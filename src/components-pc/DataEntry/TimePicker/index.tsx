import React from 'react';
import {TimePicker as AntdTimePicker} from 'antd';
import type {TimePickerProps as AntdTimePickerProps} from 'antd';

/**
 * ITMTimePicker 组件属性接口
 * @extends TimePickerProps - 继承 antd TimePicker 的所有属性
 */
export interface TimePickerProps extends AntdTimePickerProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    timePickerProps?: AntdTimePickerProps;
}

/**
 * ITMTimePicker 组件
 * @description 基于 antd TimePicker 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const TimePicker: React.FC<TimePickerProps> = (props) => {
    const restProps = {...props, ...props?.timePickerProps};

    return <AntdTimePicker {...restProps} />;
}; 