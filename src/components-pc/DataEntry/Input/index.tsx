import React from 'react';
import {Input as AntdInput} from 'antd';
import type {InputProps as AntdInputProps} from 'antd';
import type {TextAreaProps as AntdTextAreaProps} from 'antd/es/input';
import type {PasswordProps as AntdPasswordProps} from 'antd/es/input/Password';
import type {SearchProps as AntdSearchProps} from 'antd/es/input/Search';
import type {OTPProps as AntdOTPProps} from 'antd/es/input/OTP';

/**
 * ITMInput 组件属性接口
 * @extends InputProps - 继承 antd Input 的所有属性
 */
export interface InputProps extends AntdInputProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    inputProps?: AntdInputProps;
}

/**
 * ITMInput 组件
 * @description 基于 antd Input 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Input: React.FC<InputProps> & {
    TextArea: React.FC<AntdTextAreaProps>;
    Password: React.FC<AntdPasswordProps>;
    Search: React.FC<AntdSearchProps>;
    OTP: React.FC<AntdOTPProps>;
} = (props) => {
    const restProps = {...props, ...props?.inputProps};

    return <AntdInput {...restProps} />;
};

// 添加 TextArea 组件
Input.TextArea = AntdInput.TextArea;

// 添加 Password 组件
Input.Password = AntdInput.Password;

// 添加 Search 组件
Input.Search = AntdInput.Search;

// 添加 OTP 组件
Input.OTP = AntdInput.OTP; 