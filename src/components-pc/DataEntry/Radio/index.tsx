import React from 'react';
import {Radio as AntdRadio} from 'antd';
import type {RadioProps as AntdRadioProps} from 'antd';

/**
 * Radio 选项接口
 */
export interface RadioOption {
    /**
     * 选项值
     */
    value: string | number;
    /**
     * 选项标签
     */
    label: React.ReactNode;
    /**
     * 是否禁用
     */
    disabled?: boolean;
}

/**
 * ITMRadio 组件属性接口
 * @extends RadioProps - 继承 antd Radio 的所有属性
 */
export interface RadioProps extends AntdRadioProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    radioProps?: AntdRadioProps;
    /**
     * 选项列表
     */
    options?: RadioOption[];
}

/**
 * ITMRadio 组件
 * @description 基于 antd Radio 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Radio: React.FC<RadioProps> = (props) => {
    const restProps = {...props, ...props?.radioProps};

    return <AntdRadio {...restProps} />;
};

// 导出 Radio 的其他组件
export const RadioGroup = AntdRadio.Group;
export const RadioButton = AntdRadio.Button; 