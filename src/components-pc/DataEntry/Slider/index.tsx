import React from 'react';
import {Slider as AntdSlider} from 'antd';
import type {SliderSingleProps as AntdSliderSingleProps} from 'antd';

/**
 * ITMSlider 组件属性接口
 * @extends SliderSingleProps - 继承 antd Slider 的所有属性
 */
export interface SliderProps extends AntdSliderSingleProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    sliderProps?: AntdSliderSingleProps;
}

/**
 * ITMSlider 组件
 * @description 基于 antd Slider 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Slider: React.FC<SliderProps> = (props) => {
    const restProps = {...props, ...props?.sliderProps};

    return <AntdSlider {...restProps} />;
}; 