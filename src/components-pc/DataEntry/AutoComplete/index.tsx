import React from 'react';
import {AutoComplete as AntdAutoComplete} from 'antd';
import type {AutoCompleteProps as AntdAutoCompleteProps} from 'antd';

const {Option} = AntdAutoComplete;

/**
 * ITMAutoComplete 组件属性接口
 * @extends AutoCompleteProps - 继承 antd AutoComplete 的所有属性
 */
export interface AutoCompleteProps extends AntdAutoCompleteProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    autoCompleteProps?: AutoCompleteProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMAutoComplete 组件类型定义
 */
interface AutoCompleteComponent extends React.FC<AutoCompleteProps> {
    Option: typeof Option;
}

/**
 * ITMAutoComplete 组件
 * @description 基于 antd AutoComplete 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const AutoComplete: AutoCompleteComponent = (props) => {
    const restProps = {...props, ...props?.autoCompleteProps};

    return <AntdAutoComplete {...restProps} />;
};

// 导出 AutoComplete 的子组件
AutoComplete.Option = Option; 