import React from 'react';
import {Select as AntdSelect} from 'antd';
import type {SelectProps as AntdSelectProps} from 'antd';

/**
 * ITMSelect 组件属性接口
 * @extends SelectProps - 继承 antd Select 的所有属性
 */
export interface SelectProps extends AntdSelectProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    selectProps?: AntdSelectProps;
}

/**
 * ITMSelect 组件
 * @description 基于 antd Select 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Select: React.FC<SelectProps> = (props) => {
    const restProps = {...props, ...props?.selectProps};

    return <AntdSelect {...restProps} />;
}; 