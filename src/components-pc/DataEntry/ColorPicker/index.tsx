import React from 'react';
import {ColorPicker as AntdColorPicker} from 'antd';
import type {ColorPickerProps as AntdColorPickerProps} from 'antd';

/**
 * ITMColorPicker 组件属性接口
 * @extends ColorPickerProps - 继承 antd ColorPicker 的所有属性
 */
export interface ColorPickerProps extends AntdColorPickerProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    colorPickerProps?: AntdColorPickerProps;
}

/**
 * ITMColorPicker 组件
 * @description 基于 antd ColorPicker 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const ColorPicker: React.FC<ColorPickerProps> = (props) => {
    const restProps = {...props, ...props?.colorPickerProps};

    return <AntdColorPicker {...restProps} />;
}; 