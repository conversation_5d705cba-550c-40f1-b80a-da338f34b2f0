import React from 'react';
import {DatePicker as AntdDatePicker} from 'antd';
import type {DatePickerProps as AntdDatePickerProps} from 'antd';
import type {Dayjs} from 'dayjs';

/**
 * ITMDatePicker 组件属性接口
 * @extends DatePickerProps - 继承 antd DatePicker 的所有属性
 */
export interface DatePickerProps extends AntdDatePickerProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    datePickerProps?: AntdDatePickerProps;
}

/**
 * ITMDatePicker 组件
 * @description 基于 antd DatePicker 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const DatePicker: React.FC<DatePickerProps> = (props) => {
    const restProps = {...props, ...props?.datePickerProps};

    return <AntdDatePicker {...restProps} />;
}; 