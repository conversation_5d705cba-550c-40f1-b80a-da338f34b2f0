import React from 'react';
import {Divider as AntdDivider} from 'antd';
import type {DividerProps as AntdDividerProps} from 'antd';

/**
 * ITMDivider 组件属性接口
 * @extends DividerProps - 继承 antd Divider 的所有属性
 */
export interface DividerProps extends AntdDividerProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    dividerProps?: AntdDividerProps;
}

/**
 * ITMDivider 组件
 * @description 基于 antd Divider 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Divider: React.FC<DividerProps> = (props) => {
    const restProps = {...props, ...props?.dividerProps};
    return <AntdDivider {...restProps} />;
}; 