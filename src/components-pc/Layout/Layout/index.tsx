import React from 'react';
import {Layout as AntdLayout, LayoutProps as AntdLayoutProps} from 'antd';

const {Header, Footer, Sider, Content} = AntdLayout;

/**
 * ITMLayout 组件属性接口
 * @extends LayoutProps - 继承 antd Layout 的所有属性
 */
export interface LayoutProps extends AntdLayoutProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    layoutProps?: AntdLayoutProps;
}

/**
 * ITMLayout 组件类型定义
 */
interface LayoutComponent extends React.FC<LayoutProps> {
    Header: typeof Header;
    Footer: typeof Footer;
    Sider: typeof Sider;
    Content: typeof Content;
}

/**
 * ITMLayout 组件
 * @description 基于 antd Layout 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Layout: LayoutComponent = (props) => {
    const restProps = {...props, ...props?.layoutProps};
    return <AntdLayout {...restProps} />;
};

// 导出 Layout 的子组件
Layout.Header = Header;
Layout.Footer = Footer;
Layout.Sider = Sider;
Layout.Content = Content; 