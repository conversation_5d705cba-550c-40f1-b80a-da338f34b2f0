import React from 'react';
import {Flex as AntdFlex} from 'antd';
import type {FlexProps as AntdFlexProps} from 'antd';

/**
 * ITMFlex 组件属性接口
 * @extends FlexProps - 继承 antd Flex 的所有属性
 */
export interface FlexProps extends AntdFlexProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    flexProps?: AntdFlexProps;
}

/**
 * ITMFlex 组件
 * @description 基于 antd Flex 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Flex: React.FC<FlexProps> = (props) => {
    const restProps = {...props, ...props?.flexProps};
    return <AntdFlex {...restProps} />;
}; 