import React from 'react';
import {Splitter as AntdSplitter, SplitterProps as AntdSplitterProps} from 'antd';

const {Panel} = AntdSplitter;

/**
 * ITMSplitter 组件属性接口
 * @extends SplitterProps - 继承 antd Splitter 的所有属性
 */
export interface SplitterProps extends AntdSplitterProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    splitterProps?: AntdSplitterProps;
    /**
     * 子元素
     */
    children?: React.ReactNode;
}

/**
 * ITMSplitter 组件类型定义
 */
interface SplitterComponent extends React.FC<SplitterProps> {
    Panel: typeof Panel;
}

/**
 * ITMSplitter 组件
 * @description 基于 antd Splitter 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Splitter: SplitterComponent = (props) => {
    const restProps = {...props, ...props?.splitterProps};
    return <AntdSplitter {...restProps} />;
};

// 导出 Splitter 的子组件
Splitter.Panel = Panel;