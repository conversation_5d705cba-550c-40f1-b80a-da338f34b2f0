import React from 'react';
import {Space as AntdSpace, SpaceProps as AntdSpaceProps} from 'antd';

const {Compact} = AntdSpace;

// 扩展 SpaceProps 类型，添加自定义属性
export interface SpaceProps extends AntdSpaceProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    spaceProps?: AntdSpaceProps;
}

interface SpaceComponent extends React.FC<SpaceProps> {
    Compact: typeof Compact;
}

export const Space: SpaceComponent = (props) => {
    const restProps = {...props, ...props?.spaceProps};

    return <AntdSpace {...restProps} />;
};

Space.Compact = Compact;
