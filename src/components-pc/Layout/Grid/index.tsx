import React from 'react';
import {Row as AntdRow, Col as AntdCol, RowProps as AntdRowProps, ColProps as AntdColProps} from 'antd';

/**
 * ITMRow 组件属性接口
 * @extends RowProps - 继承 antd Row 的所有属性
 */
export interface RowProps extends AntdRowProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    rowProps?: AntdRowProps;
}

/**
 * ITMCol 组件属性接口
 * @extends ColProps - 继承 antd Col 的所有属性
 */
export interface ColProps extends AntdColProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    colProps?: AntdColProps;
}

/**
 * ITMRow 组件类型定义
 */
interface RowComponent extends React.FC<RowProps> {
    Col: React.FC<ColProps>;
}

/**
 * ITMRow 组件
 * @description 基于 antd Row 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Row: RowComponent = (props) => {
    const restProps = {...props, ...props?.rowProps};
    return <AntdRow {...restProps} />;
};

/**
 * ITMCol 组件
 * @description 基于 antd Col 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
Row.Col = (props) => {
    const restProps = {...props, ...props?.colProps};
    return <AntdCol {...restProps} />;
};

export const Col = Row.Col;