import React from 'react';
import {Tree as AntdTree} from 'antd';
import type {TreeProps as AntdTreeProps} from 'antd';
import './style.css';

// 扩展 TreeProps 类型，添加自定义属性
export interface TreeProps extends AntdTreeProps {
    /**
     * 组件原始属性
     */
    treeProps?: AntdTreeProps;
}

export const Tree: React.FC<TreeProps> = (props) => {
    const restProps = {...props, ...props?.treeProps};
    return (
        <AntdTree
            {...restProps}
        />
    );
};
