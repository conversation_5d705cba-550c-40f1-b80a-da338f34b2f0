import React from 'react';
import {Statistic as AntdStatistic} from 'antd';
import type {StatisticProps as AntdStatisticProps} from 'antd';

export interface StatisticProps extends AntdStatisticProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    statisticProps?: AntdStatisticProps;
}

export const Statistic: React.FC<StatisticProps> = (props) => {
    const restProps = {...props, ...props?.statisticProps};

    return <AntdStatistic {...restProps} />;
};
