import React from 'react';
import {Avatar as AntdAvatar} from 'antd';
import type {AvatarProps as AntdAvatarProps} from 'antd';

/**
 * ITMAvatar 组件属性接口
 * @extends AvatarProps - 继承 antd Avatar 的所有属性
 */
export interface AvatarProps extends AntdAvatarProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    avatarProps?: AntdAvatarProps;
}

/**
 * ITMAvatar 组件
 * @description 基于 antd Avatar 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Avatar: React.FC<AvatarProps> = (props) => {
    const restProps = {...props, ...props?.avatarProps};

    return <AntdAvatar {...restProps} />;
}; 