import React from 'react';
import {Tag as AntdTag} from 'antd';
import type {TagProps as AntdTagProps} from 'antd';

export interface TagProps extends AntdTagProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    tagProps?: TagProps;
}

export const Tag: React.FC<TagProps> = (props) => {
    const restProps = {...props, ...props?.tagProps};

    return <AntdTag {...restProps} />;
};
