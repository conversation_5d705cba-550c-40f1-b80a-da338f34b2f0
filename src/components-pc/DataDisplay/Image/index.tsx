import React from 'react';
import {Image as AntdImage} from 'antd';
import type {ImageProps as AntdImageProps} from 'antd';

/**
 * Image 组件属性接口
 * @extends ImageProps - 继承 antd Image 的所有属性
 */
export interface ImageProps extends AntdImageProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    imageProps?: AntdImageProps;
}

/**
 * Image 组件
 * @description 基于 antd Image 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Image: React.FC<ImageProps> = (props) => {
    const restProps = {...props, ...props?.imageProps};
    return <AntdImage {...restProps} />;
}; 