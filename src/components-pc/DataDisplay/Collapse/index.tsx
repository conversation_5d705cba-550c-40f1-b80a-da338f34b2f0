import React from 'react';
import {Collapse as AntdCollapse} from 'antd';
import type {CollapseProps as AntdCollapseProps} from 'antd';

/**
 * Collapse 组件属性接口
 * @extends CollapseProps - 继承 antd Collapse 的所有属性
 */
export interface CollapseProps extends AntdCollapseProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    collapseProps?: AntdCollapseProps;
}

/**
 * Collapse 组件
 * @description 基于 antd Collapse 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Collapse: React.FC<CollapseProps> = (props) => {
    const restProps = {...props, ...props?.collapseProps};
    return <AntdCollapse {...restProps} />;
}; 