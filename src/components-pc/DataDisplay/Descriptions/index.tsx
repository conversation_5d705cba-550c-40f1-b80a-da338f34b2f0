import React from 'react';
import {Descriptions as AntdDescriptions} from 'antd';
import type {DescriptionsProps as AntdDescriptionsProps} from 'antd';

/**
 * Descriptions 组件属性接口
 * @extends DescriptionsProps - 继承 antd Descriptions 的所有属性
 */
export interface DescriptionsProps extends AntdDescriptionsProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    descriptionsProps?: AntdDescriptionsProps;
}

/**
 * Descriptions 组件
 * @description 基于 antd Descriptions 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Descriptions: React.FC<DescriptionsProps> = (props) => {
    const restProps = {...props, ...props?.descriptionsProps};
    return <AntdDescriptions {...restProps} />;
}; 