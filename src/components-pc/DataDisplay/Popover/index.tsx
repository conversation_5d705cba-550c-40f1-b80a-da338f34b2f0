import React from 'react';
import {Popover as AntdPopover} from 'antd';
import type {PopoverProps as AntdPopoverProps} from 'antd';

/**
 * Popover 组件属性接口
 * @extends PopoverProps - 继承 antd Popover 的所有属性
 */
export interface PopoverProps extends AntdPopoverProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    popoverProps?: AntdPopoverProps;
}

/**
 * Popover 组件
 * @description 基于 antd Popover 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Popover: React.FC<PopoverProps> = (props) => {
    const restProps = {...props, ...props?.popoverProps};

    return <AntdPopover {...restProps} />;
}; 