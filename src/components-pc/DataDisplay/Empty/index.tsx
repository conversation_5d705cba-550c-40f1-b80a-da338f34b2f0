import React from 'react';
import {Empty as AntdEmpty} from 'antd';
import type {EmptyProps as AntdEmptyProps} from 'antd';

/**
 * Empty 组件属性接口
 * @extends EmptyProps - 继承 antd Empty 的所有属性
 */
export interface EmptyProps extends AntdEmptyProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    emptyProps?: AntdEmptyProps;
}

/**
 * Empty 组件
 * @description 基于 antd Empty 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Empty: React.FC<EmptyProps> = (props) => {
    const restProps = {...props, ...props?.emptyProps};
    return <AntdEmpty {...restProps} />;
}; 