import React from 'react';
import {Tour as AntdTour} from 'antd';
import type {TourProps as AntdTourProps} from 'antd';
import './style.css';

// 扩展 TourProps 类型，添加自定义属性
export interface TourProps extends AntdTourProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 组件原始属性
     */
    tourProps?: AntdTourProps;
}

export const Tour: React.FC<TourProps> = (props) => {
    const restProps = {...props, ...props?.tourProps};

    return <AntdTour {...restProps} />;
}; 