import React from 'react';
import {List as AntdList} from 'antd';
import type {ListProps as AntdListProps} from 'antd';

/**
 * List 组件属性接口
 * @extends ListProps - 继承 antd List 的所有属性
 */
export interface ListProps extends AntdListProps<any> {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    listProps?: AntdListProps<any>;
}

/**
 * List 组件
 * @description 基于 antd List 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const List: React.FC<ListProps> = (props) => {
    const restProps = {...props, ...props?.listProps};

    return <AntdList {...restProps} />;
}; 