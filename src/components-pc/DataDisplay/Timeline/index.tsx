import React from 'react';
import {Timeline as AntdTimeline} from 'antd';
import type {TimelineProps as AntdTimelineProps} from 'antd';

export interface TimelineProps extends AntdTimelineProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    timelineProps?: TimelineProps;
}

export const Timeline: React.FC<TimelineProps> = (props) => {
    const restProps = {...props, ...props?.timelineProps};

    return <AntdTimeline {...restProps} />;
};
