import React from 'react';
import {Card as AntdCard} from 'antd';
import type {CardProps as AntdCardProps} from 'antd';

/**
 * Card 组件属性接口
 * @extends CardProps - 继承 antd Card 的所有属性
 */
export interface CardProps extends AntdCardProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    cardProps?: AntdCardProps;
}

/**
 * Card 组件
 * @description 基于 antd Card 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Card: React.FC<CardProps> = (props) => {
    const restProps = {...props, ...props?.cardProps};
    return <AntdCard {...restProps} />;
}; 