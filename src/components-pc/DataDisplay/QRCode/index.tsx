import React from "react";
import {QRCode as AntdQRCode} from "antd";
import type {QRCodeProps as AntdQRCodeProps} from "antd";

export interface QRCodeProps extends AntdQRCodeProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    qrCodeProps?: AntdQRCodeProps;
}

export const QRCode: React.FC<QRCodeProps> = (props) => {
    const restProps = {...props, ...props?.qrCodeProps};

    return <AntdQRCode {...restProps} />;
};

