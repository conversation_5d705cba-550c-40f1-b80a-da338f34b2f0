import React from 'react';
import {Badge as AntdBadge} from 'antd';
import type {BadgeProps as AntdBadgeProps} from 'antd';

/**
 * Badge 组件属性接口
 * @extends BadgeProps - 继承 antd Badge 的所有属性
 */
export interface BadgeProps extends AntdBadgeProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    badgeProps?: AntdBadgeProps;
}

/**
 * Badge 组件
 * @description 基于 antd Badge 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Badge: React.FC<BadgeProps> = (props) => {
    const restProps = {...props, ...props?.badgeProps};
    return <AntdBadge {...restProps} />;
}; 