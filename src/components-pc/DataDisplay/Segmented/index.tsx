import React from 'react';
import {Segmented as AntdSegmented} from 'antd';
import type {SegmentedProps as AntdSegmentedProps} from 'antd';

export interface SegmentedProps extends AntdSegmentedProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    segmentedProps?: AntdSegmentedProps;
}

export const Segmented: React.FC<SegmentedProps> = (props) => {
    const restProps = {...props, ...props?.segmentedProps};

    return <AntdSegmented {...restProps} />;
};
