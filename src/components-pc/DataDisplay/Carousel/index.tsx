import React from 'react';
import {Carousel as AntdCarousel} from 'antd';
import type {CarouselProps as AntdCarouselProps} from 'antd';

/**
 * Carousel 组件属性接口
 * @extends CarouselProps - 继承 antd Carousel 的所有属性
 */
export interface CarouselProps extends AntdCarouselProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    carouselProps?: AntdCarouselProps;
}

/**
 * Carousel 组件
 * @description 基于 antd Carousel 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Carousel: React.FC<CarouselProps> = (props) => {
    const restProps = {...props, ...props?.carouselProps};
    return <AntdCarousel {...restProps} />;
}; 