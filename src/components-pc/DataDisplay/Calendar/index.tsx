import React from 'react';
import {Calendar as AntdCalendar} from 'antd';
import type {CalendarProps as AntdCalendarProps} from 'antd';
import type {Dayjs} from 'dayjs';

/**
 * Calendar 组件属性接口
 * @extends CalendarProps - 继承 antd Calendar 的所有属性
 */
export interface CalendarProps extends AntdCalendarProps<Dayjs> {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    calendarProps?: AntdCalendarProps<Dayjs>;
}

/**
 * Calendar 组件
 * @description 基于 antd Calendar 的二次封装组件
 * @param props - 组件属性
 * @returns React.FC
 */
export const Calendar: React.FC<CalendarProps> = (props) => {
    const restProps = {...props, ...props?.calendarProps};
    return <AntdCalendar {...restProps} />;
}; 