import React, {ReactNode} from "react";
import {Tooltip as AntdTooltip} from "antd";
import type {TooltipProps as AntdTooltipProps} from "antd";
import "./style.css";

// 扩展 TooltipProps 类型，添加自定义属性
export interface TooltipProps extends Omit<AntdTooltipProps, "children"> {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 组件原始属性
     */
    tooltipProps?: TooltipProps;
    /**
     * 子元素
     */
    children: ReactNode;
}

export const Tooltip: React.FC<TooltipProps> = (props) => {
    const restProps = {...props, ...props?.tooltipProps};

    return <AntdTooltip {...restProps}>{restProps?.children}</AntdTooltip>;
};
