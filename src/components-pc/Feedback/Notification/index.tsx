import React from 'react';
import {notification as AntdNotification} from 'antd';
import type {
    ArgsProps as AntdArgsProps,
    NotificationConfig as AntdNotificationConfig
} from 'antd/es/notification/interface';

// 扩展 NotificationConfig 类型，添加自定义属性
export interface NotificationConfig extends AntdNotificationConfig {
    /**
     * 自定义类名
     */
    customClassName?: string;
}

// 扩展 ArgsProps 类型，添加自定义属性
export interface NotificationProps extends AntdArgsProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
}

// 创建 ITMNotification 类型
export interface Notification {
    success: (config: NotificationProps) => void;
    error: (config: NotificationProps) => void;
    info: (config: NotificationProps) => void;
    warning: (config: NotificationProps) => void;
    open: (config: NotificationProps) => void;
    destroy: () => void;
    config: (options: NotificationConfig) => void;
}

// 创建 ITMNotification 实例
export const Notification: Notification = {
    success: (config) => {
        AntdNotification.success(config);
    },
    error: (config) => {
        AntdNotification.error(config);
    },
    info: (config) => {
        AntdNotification.info(config);
    },
    warning: (config) => {
        AntdNotification.warning(config);
    },
    open: (config) => {
        AntdNotification.open(config);
    },
    destroy: () => {
        AntdNotification.destroy();
    },
    config: (options) => {
        AntdNotification.config(options);
    }
}; 