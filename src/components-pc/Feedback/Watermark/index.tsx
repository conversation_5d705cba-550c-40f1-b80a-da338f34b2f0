import React from 'react';
import {Watermark as AntdWatermark} from 'antd';
import type {WatermarkProps as AntdWatermarkProps} from 'antd';

export interface WatermarkProps extends AntdWatermarkProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    watermarkProps?: WatermarkProps;
}

export const Watermark: React.FC<WatermarkProps> = (props) => {
    const restProps = {...props, ...props?.watermarkProps};

    return <AntdWatermark {...restProps} />;
};
