import React from 'react';
import {Spin as AntdSpin} from 'antd';
import type {SpinProps as AntdSpinProps} from 'antd';

export interface SpinProps extends AntdSpinProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    spinProps?: SpinProps;
}

export const Spin: React.FC<SpinProps> = (props) => {
    const restProps = {...props, ...props?.spinProps};
    return <AntdSpin {...restProps} />;
};
