import React from 'react';
import {Result as AntdResult} from 'antd';
import type {ResultProps as AntdResultProps} from 'antd';
import classNames from 'classnames';

export interface ResultProps extends AntdResultProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 组件原始属性
     */
    resultProps?: ResultProps;
}

export const Result: React.FC<ResultProps> = (props) => {
    const restProps = {...props, ...props?.resultProps};
    return <AntdResult {...restProps} />;
};
