import React from "react";
import {Progress as AntdProgress} from "antd";
import type {ProgressProps as AntdProgressProps} from "antd";

export interface ProgressProps extends AntdProgressProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 组件原始属性
     */
    progressProps?: AntdProgressProps;
}

export const Progress: React.FC<ProgressProps> = (props) => {
    const restProps = {...props, ...props?.progressProps};
    return <AntdProgress {...restProps} />;
};
