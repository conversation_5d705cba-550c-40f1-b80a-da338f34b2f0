import React from "react";
import {Popconfirm as AntdPopconfirm} from "antd";
import type {PopconfirmProps as AntdPopconfirmProps} from "antd";

/**
 * ITMPopconfirm 组件属性接口
 * @extends PopconfirmProps - 继承 antd Popconfirm 的所有属性
 */
export interface PopconfirmProps extends AntdPopconfirmProps {
    /**
     * 自定义类名
     */
    customClassName?: string;
    /**
     * 组件原始属性
     */
    popconfirmProps?: AntdPopconfirmProps;
}

export const Popconfirm: React.FC<PopconfirmProps> = (props) => {
    const restProps = {...props, ...props?.popconfirmProps};
    return <AntdPopconfirm {...restProps}>{restProps?.children}</AntdPopconfirm>;
};
