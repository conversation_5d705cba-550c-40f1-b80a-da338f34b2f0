import React from 'react';
import {Alert as AntdAlert} from 'antd';
import type {AlertProps as AntdAlertProps} from 'antd';
import './style.css';

// 扩展 AlertProps 类型，添加自定义属性
export interface AlertProps extends AntdAlertProps {
    /**
     * 自定义类名
     */
    className?: string;
    /**
     * 组件原始属性
     */
    alertProps?: AntdAlertProps;
}

export const Alert: React.FC<AlertProps> = (props) => {
    ;
    const restProps = {...props, ...props?.alertProps};

    return (
        <AntdAlert
            className={props?.className}
            {...restProps}
        />
    );
}; 