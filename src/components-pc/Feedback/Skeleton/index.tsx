import React from 'react';
import {Skeleton as AntdSkeleton} from 'antd';
import type {SkeletonProps as AntdSkeletonProps} from 'antd';

export interface SkeletonProps extends AntdSkeletonProps {
    /**
     * 自定义类名
     */
    className?: string
    /**
     * 组件原始属性
     */
    skeletonProps?: SkeletonProps;
}

export const Skeleton: React.FC<SkeletonProps> & {
    Button: typeof AntdSkeleton.Button;
    Avatar: typeof AntdSkeleton.Avatar;
    Input: typeof AntdSkeleton.Input;
    Image: typeof AntdSkeleton.Image;
    Node: typeof AntdSkeleton.Node;
} = (props) => {
    const restProps = {...props, ...props?.skeletonProps};
    return <AntdSkeleton {...restProps} />;
};

// 添加所有子组件
Skeleton.Button = AntdSkeleton.Button;
Skeleton.Avatar = AntdSkeleton.Avatar;
Skeleton.Input = AntdSkeleton.Input;
Skeleton.Image = AntdSkeleton.Image;
Skeleton.Node = AntdSkeleton.Node;