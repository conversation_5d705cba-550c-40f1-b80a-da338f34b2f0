import React from 'react';
import {message as AntdMessage} from 'antd';

// 参考 antd message.config 支持的参数
export interface ITMMessageConfig extends Partial<{
    top: number;
    duration: number;
    maxCount: number;
    rtl: boolean;
    prefixCls: string;
    getContainer: () => HTMLElement;
    transitionName: string;
}> {
    /**
     * 自定义类名
     */
    customClassName?: string;
}

// 创建 ITMMessage 类型
export interface ITMMessage {
    success: (content: React.ReactNode, duration?: number, onClose?: () => void) => void;
    error: (content: React.ReactNode, duration?: number, onClose?: () => void) => void;
    info: (content: React.ReactNode, duration?: number, onClose?: () => void) => void;
    warning: (content: React.ReactNode, duration?: number, onClose?: () => void) => void;
    loading: (content: React.ReactNode, duration?: number, onClose?: () => void) => void;
    config: (options: ITMMessageConfig) => void;
    destroy: () => void;
}

// 创建 ITMMessage 实例
export const Message: ITMMessage = {
    success: (content, duration, onClose) => {
        AntdMessage.success(content, duration, onClose);
    },
    error: (content, duration, onClose) => {
        AntdMessage.error(content, duration, onClose);
    },
    info: (content, duration, onClose) => {
        AntdMessage.info(content, duration, onClose);
    },
    warning: (content, duration, onClose) => {
        AntdMessage.warning(content, duration, onClose);
    },
    loading: (content, duration, onClose) => {
        AntdMessage.loading(content, duration, onClose);
    },
    config: (options) => {
        AntdMessage.config(options);
    },
    destroy: () => {
        AntdMessage.destroy();
    }
}; 