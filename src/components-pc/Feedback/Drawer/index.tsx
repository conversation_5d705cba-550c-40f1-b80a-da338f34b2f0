import React, {ReactNode} from "react";
import {Drawer as AntdDrawer} from "antd";
import type {DrawerProps as AntdDrawerProps} from "antd";
import "./style.css";

/***
 * 扩展 DrawerProps 类型，添加自定义属性
 * */
export interface DrawerProps extends AntdDrawerProps {
    /**
     * 抽屉的内容
     */
    children?: ReactNode;
    /**
     * 是否显示抽屉的底部
     * @default false
     */
    showFooter?: boolean;
    /**
     * 组件原始属性
     */
    drawerProps?: AntdDrawerProps;
}

export const Drawer: React.FC<DrawerProps> = (props) => {
    const restProps = {...props, ...props?.drawerProps};
    return (
        <AntdDrawer {...restProps} footer={props?.showFooter ? props?.footer : null}>
            {props?.children}
        </AntdDrawer>
    );
};
