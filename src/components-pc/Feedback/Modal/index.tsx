import React, {ReactNode} from "react";
import {Modal as AntdModal} from "antd";
import type {ModalProps as AntdModalProps} from "antd";
import "./style.css";

// 扩展 ModalProps 类型，添加自定义属性
export interface ModalProps extends AntdModalProps {
    /**
     * 对话框的内容
     */
    children?: ReactNode;
    /**
     * 关闭对话框
     */
    onClose?: () => void;
    /**
     * 组件原始属性
     */
    modalProps?: AntdModalProps;
}

export const Modal: React.FC<ModalProps> = (props) => {
    const restProps = {...props, ...props?.modalProps};
    return (
        <AntdModal onCancel={props?.onClose} {...restProps}>
            {props?.children}
        </AntdModal>
    );
};
