interface RoleKey {
    key: string;
    children?: <PERSON><PERSON><PERSON>[];
}

class Role<PERSON>eyManager {
    private roles: <PERSON><PERSON><PERSON>[] = [];

    private static manager = new RoleKeyManager();

    static getInstances(): RoleKeyManager {
        return RoleKeyManager.manager;
    }

    addRole(role: <PERSON><PERSON><PERSON>): void {
        this.roles.push(role);
    }

    public hasRole(code: string) {
        return this.roles.some((role: <PERSON><PERSON><PERSON>) => {
            return role.key === code;
        })
    }
}

export default RoleKeyManager;