// src/store/test.ts
import { create } from 'zustand';

interface StoreState {
    count: number;
    increment: () => void;
    decrement: () => void;
    reset: () => void;
}

export const useStore = create<StoreState>((set) => ({
    count: 0,
    increment: () => set((state) => ({ count: state.count + 1 })),
    decrement: () => set((state) => ({ count: state.count - 1 })),
    reset: () => set({ count: 0 }),
}));