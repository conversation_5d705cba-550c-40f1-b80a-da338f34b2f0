/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 15:12:17
 * @Description:
 * @LastEditTime: 2025-06-27 15:04:05
 * @LastEditors: wangjiantao
 */
import { IArr, IArrObj, IItem, IMessageObj } from "@/layout/types";
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface StoreState {
  menus: any[];
  arrRightList: IArrObj[];
  messageList: IMessageObj[];
  leftCheckArr: IArr;
  topCheckArr: IArr;
  arrList: IArrObj[];
  topList: IArrObj[];
  topCopyList: IArrObj[];
  checkArr: IItem[]; // 用于存储右侧菜单的选中项

  setArrRightList: (_arrRightList: IArrObj[]) => void;
  setMessageList: (_messageList: IMessageObj[]) => void;
  setLeftCheckArr: (_leftCheckArr: IArr) => void;
  setTopCheckArr: (_topCheckArr: IArr) => void;
  setArrList: (_arrList: IArrObj[]) => void;
  setTopList: (_topList: IArrObj[]) => void;
  setTopCopyList: (_topList: IArrObj[]) => void;
  setCheckArr: (_topList: IItem[]) => void;
  setMenus: (menus: any[]) => void;
  clear: () => void;
}

export const useMenuStore = create<StoreState>()(
  persist(
    (set) => ({
      arrRightList: [],
      messageList: [],
      leftCheckArr: [],
      topCheckArr: [],
      arrList: [],
      topList: [],
      topCopyList: [],
      checkArr: [],
      menus: [],

setArrRightList: (_arrRightList) =>
  set({ arrRightList: [...(_arrRightList || [])] }),
      setMessageList: (_messageList) => set({ messageList: [..._messageList] }),
      setLeftCheckArr: (_leftCheckArr) =>
        set({ leftCheckArr: [..._leftCheckArr] }),
      setTopCheckArr: (_topCheckArr) => set({ topCheckArr: [..._topCheckArr] }),
      setArrList: (_arrList) => set({ arrList: [..._arrList] }),
      setTopList: (_topList) => set({ topList: [..._topList] }),
      setTopCopyList: (_topList) => set({ topCopyList: [..._topList] }),
      setCheckArr: (_checkArr) => set({ checkArr: [..._checkArr] }),
      setMenus: (menus) => set({ menus: [...menus] }),
      clear: () =>
        set({
          arrRightList: [],
          messageList: [],
          leftCheckArr: [],
          topCheckArr: [],
          arrList: [],
          topList: [],
          topCopyList: [],
          checkArr: [],
          menus: [],
        }),
    }),
    {
      name: "homeMenu", // 存储到localStorage的key名
      storage: createJSONStorage(() => localStorage), // 使用localStorage作为存储介质
      // 可选：只持久化部分状态
      // partialize: (state) => ({
      //     arrRightList: state.arrRightList,
      //     messageList: state.messageList,
      //     // 选择需要持久化的字段
      // }),
    }
  )
);
