import { TreeDataNode } from "antd";
import { LogoutOutlined, SettingOutlined } from "@ant-design/icons";
import React from "react";
import { IMessageObj } from "@/layout/types";

const labels: TreeDataNode[] = [];

const values: any = [];

export const data = {
  labels,
  values,
};
// 菜单选择
export const menuItems: any = [
  {
    key: "changePwd",
    icon: <SettingOutlined />,
    label: "修改密码",
  },
  {
    key: "logout",
    icon: <LogoutOutlined />,
    label: "退出登录",
  },
];
// 消息信息
export const messageList: IMessageObj[] = [
  {
    name: "todo",
    id: "pending",
  },
  {
    name: "notice",
    id: "notice",
  },
  {
    name: "early-warning​",
    id: "early-warning​",
  },
];
