/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 15:12:17
 * @Description: 
 * @LastEditTime: 2025-06-26 21:57:37
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import {Button, Result} from "antd";
import {useNavigate} from "react-router";
import {config} from "@/config/theme";

const Index = () => {
    const navigate = useNavigate();

    const goHome = () => {
        navigate(config.welcomePath, {replace: true});
    }

    return (
        <Result
            status="404"
            title="404"
            subTitle="抱歉，您访问的页面不存在."
            extra={(
                <Button
                    type="primary"
                    onClick={goHome}
                >
                    回到首页
                </Button>
            )}
        />
    )
}

export default Index;
