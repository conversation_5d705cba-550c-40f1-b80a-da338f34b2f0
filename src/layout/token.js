export const getTokenFromWindowParams = () => {
    if (typeof pageofficectrl !== 'undefined' && pageofficectrl !== null) {
        const windowParams = pageofficectrl.WindowParams;
        const params = typeof windowParams === 'string' ? JSON.parse(windowParams) : windowParams;
        return params?.token || null;
    }
    return null;
};

export const getPathFromWindowParams = () => {
    if (typeof pageofficectrl !== 'undefined' && pageofficectrl !== null) {
        const windowParams = pageofficectrl.WindowParams;
        const params = typeof windowParams === 'string' ? JSON.parse(windowParams) : windowParams;
        return params?.path || null;
    }
    return null;
};

export const getContractIdFromWindowParams = () => {
    if (typeof pageofficectrl !== 'undefined' && pageofficectrl !== null) {
        const windowParams = pageofficectrl.WindowParams;
        const params = typeof windowParams === 'string' ? JSON.parse(windowParams) : windowParams;
        return params?.contractId || null;
    }
    return null;
};