/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 15:12:17
 * @Description:
 * @LastEditTime: 2025-07-24 08:40:44
 * @LastEditors: wangjiantao
 */
import { ActionType, ProLayout } from "@ant-design/pro-components";
import { useEffect, useRef } from "react";
import "./index.scss";
import Header from "@/layout/components/Header/index";
import Search from "@/layout/components/search";
import RightBar from "@/layout/components/rightBar";
import LeftSidebar from "@/layout/components/LeftSidebar";
import RightSidebar from "@/layout/components/RightSidebar";
import { config } from "@/config/theme";
import { loadHeaderAction } from "@/layout/components/HeaderAction";
import { Outlet, useNavigate } from "react-router";
import { getWindowParams } from "@/utils/pageofficeParams";
import { Modal } from "antd";

const Layout = () => {
  const actionRef = useRef<ActionType>();
  const loginPath = config.loginPath;
  const navigate = useNavigate();

  useEffect(() => {
    actionRef.current?.reload();
  }, []);

  return (
    <>
      <ProLayout
        id="layout"
        defaultCollapsed={true}
        className="layoutWithBackground"
        fixedHeader
        siderWidth={0}
        layout="top"
        collapsed={true}
        splitMenus={false}
        menuRender={false}
        title={false}
        logo={false}
        onPageChange={(location: any) => {
          const token = localStorage.getItem("token");
          if (!token) {
            const windowParams = getWindowParams();
            const tokenFromWindowParams = windowParams?.token;
            if (tokenFromWindowParams) {
              //用于pageOffice跳转
              localStorage.setItem("token", tokenFromWindowParams);
              navigate(windowParams?.path);
            } else {
              navigate(loginPath, { replace: true });
            }
          } else {
            navigate(location.pathname);
          }
        }}
        actionRef={actionRef}
        // waterMarkProps={{
        //     content: username || config.waterMark,
        // }}
        breadcrumbProps={{
          itemRender: (route: any, params, routes, paths) => {
            return (
              <label
                className={"breadcrumb-item"}
                onClick={() => {
                  return;
                }}
              >
                {route.breadcrumbName}
              </label>
            );
          },
        }}
        headerContentRender={() => <Header />}
        actionsRender={(props) => {
          return loadHeaderAction(props);
        }}
      >
       
      </ProLayout>
      <Search />
      <RightBar />
      <LeftSidebar />
      {/* <RightSidebar /> */}
      <Outlet />
    </>
  );
};

export default Layout;
