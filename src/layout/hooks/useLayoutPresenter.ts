/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-27 16:12:37
 * @Description: 
 * @LastEditTime: 2025-07-25 11:08:27
 * @LastEditors: wangjiantao
 */
import React from "react";
import {DictPresenter} from "@/layout/presenter";
import {DictApiImpl} from "@/layout/model";
import {IArrObj, LayoutState} from "@/layout/types";
import {getLocalStorageItem} from "@/utils/storage";
import {useMenuStore} from "@/store";

// state状态的初始值
const initialState: LayoutState = {
    //左侧定义数据
    arrList: [],
    Obj: {},
    openLeftState: "",
    openLeftCheckState: false,
    openLeftLookState: false,
    openLeftStudyState: false,
    openLeftDoState: false,
    leftCheckArr: [],
    leftNum: 8,
    leftPopWidth: 443,

    // 右侧代码
    rightObj: {},
    arrRightList: [],
    openState: "",
    openSymaState: false,
    openRuofState: false,
    openPopuState: false,
    openDispState: false,
    openPrevState: false,
    openTeamState: false,
    openTestState: false,
    checkArr: [],
    num: 8,
    popWidth: 443,

    topList: [],
    topCopyList: [],
    topItemObj: {},
    openCommonState: false,
    openTopState: "",
    topCheckArr: [],
    visible: false,
    noticeNumber: 0,
    todoNumber: 0,
    warnNumber: 0,
};

/**
 *  useLayoutPresenter hook
 *  创建 DictPresenter 实例，并缓存到React.useRef对象，返回数据状态state和 presenter
 */
export function useLayoutPresenter() {
    const [state, dispatch] = React.useState<LayoutState>(initialState);

    const {
        setArrList,
        arrList,
        topList,
        topCopyList,
        setTopCopyList,
        setTopList,
        arrRightList,
        setArrRightList,
        setCheckArr,
        checkArr,
        menus,
    } = useMenuStore();

    const presenterRef = React.useRef<DictPresenter>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        // @ts-ignore
        presenterRef.current = new DictPresenter(
            state,
            dispatch,
            new DictApiImpl()
        );
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state]);

    const getResult = () => {
        const result = getLocalStorageItem("homeMenu", []);
        // const resultArr = result[0]?.children || [];
        const resultArr = result?.state?.menus[0]?.children;

        const leftObj = resultArr?.find((item: any) => item.code == "left_area");
        const rightObj = resultArr?.find((item: any) => item.code == "right_area");
        const topObj = resultArr?.find((item: any) => item.code == "COMMON");
        return {
            leftObj,
            rightObj,
            topObj,
        };
    };

    const getLeftArrList = () => {
        const {leftObj} = getResult();
        dispatch((prevState) => {
            return {
                ...prevState,
                arrList: leftObj?.children || [],
            };
        });
        // 更新全局状态
        setArrList(leftObj?.children || []);
    };

    /**
     * @description 获取顶部路由
     * */
    const getTopArrList = () => {
        const {topObj} = getResult();
        setTopList(topObj?.children || []);
        setTopCopyList(topObj?.children || []);
    };

    const searchApp = () => {
        setTopList(topCopyList);
    };
    const searchCopy = (val: string) => {
        const result = presenterRef.current!.fuzzySearchNestedTree(topList, val);
        setTopList(result);
    };

    const getRightArrList = () => {
        const {rightObj} = getResult();
        setArrRightList(rightObj?.children || []);
    };

    const handlerOpenPopover = (item: IArrObj) => {
        setCheckArr(item.children || []);
        presenterRef.current!.handleOpenChange(item);
    };

    return {
        state,
        presenter: presenterRef.current,
        getLeftArrList,
        arrList,
        getTopArrList,
        searchApp,
        searchCopy,
        topList,
        arrRightList,
        getRightArrList,
        handlerOpenPopover,
        checkArr,
    };
}
