/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description: 
 * @LastEditTime: 2025-07-25 09:46:03
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import {DictApi} from "@/layout/types";
import {data} from "@/layout/api/dict";
import {httpClient} from "@/api/index";
import {getSelfNotDoneTodoCount, getSelfNotReadNoticeCount, getSelfNotReadWarnCount,} from "@/api/user/self";

/**
 * Model层接口实现
 * DictApiImpl 实现了DictApi接口，提供了数据的增删改查操作
 * 这里使用了模拟数据，实际应用中可以使用axios等库来请求后端接口
 */
export class DictApiImpl implements DictApi {
    getHomeBar() {
        return httpClient.get("/api/homeBar");
    }

    // 左侧数据
    getLeftBar() {
        return httpClient.get("/api/leftBar");
    }

    // 左侧数据
    getRightBar() {
        return httpClient.get("/api/rightBar");
    }

    getLeftContentBar() {
        return httpClient.get("/api/leftContentBar");
    }

    getRightContentBar() {
        return httpClient.get("/api/rightContentBar");
    }

    // 退出登录
    loginOut(): Promise<any> {
        return Promise.resolve(data.labels);
    }

    // 修改密码
    changePwd(): Promise<any> {
        return Promise.resolve(data.labels);
    }

    // 搜索框函数
    getInputText(value: any): Promise<any> {
        return Promise.resolve(data.labels);
    }

    getSelfNotDoneTodoCount(): Promise<any> {
        return getSelfNotDoneTodoCount();
    }

    getSelfNotReadNoticeCount(): Promise<any> {
        return getSelfNotReadNoticeCount();
    }

    getSelfNotReadWarnCount(): Promise<any> {
        return getSelfNotReadWarnCount();
    }
}
