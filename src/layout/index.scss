.layoutWithBackground {
  width: 100vw;
  height: 100vh;

  .ant-layout-header {
    height: 71px !important;
  }

  .ant-pro-top-nav-header-main {
    padding: 0;
  }

  .ant-pro-top-nav-header-logo {
    width: 0;
  }

  .ant-pro-top-nav-header-menu {
    padding: 0;
  }

  .ant-layout-content {
    position: relative;
    z-index: 1; // 确保内容在背景上方
    // 为内容区域添加内边距
    height: 100vh;
    padding: 0 !important;
  }

  // 内容区域下方的背景


  // /* 新增背景（上层） */
  // .ant-layout-content::before {
  //   content: '';
  //   position: fixed;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 100%;
  //   background-image: url('@/assets/Images/cartoon_tree.png'); /* 叠加的图案 */
  //   background-repeat: no-repeat; /* 示例：重复图案 */
  //   background-position: center;
  //   background-size: auto; /* 或根据需要调整 */
  //   z-index: -2; /* 位于 -2 和内容之间 */
  //   pointer-events: none; /* 防止干扰交互 */
  // }
}

.layout {
  width: calc(100vw);
  height: calc(100vh - 18px);
  background: url("@/assets/form.png") !important;
  background-size: cover;
  position: relative;
}

.layout_box {
  height: calc(100vh - 173px) !important;
  text-align: center;
  overflow: hidden;

  img {
    width: 85vw;
    height: 90vh;
  }
}
