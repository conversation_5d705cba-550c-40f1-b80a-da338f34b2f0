/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description:
 * @LastEditTime: 2025-07-25 09:51:37
 * @LastEditors: wangjiantao
 */
import React from "react";

export interface IArrObj {
    id: number;
    nodeId: string;
    parentId: number;
    parentNodeId: string;
    title: string;
    code: string;
    type: string;
    display: null;
    image: string;
    url: "";
    sysEnabled: number;
    sysIndex: number;
    children: IItem[];
}

export interface IItem {
    id: number;
    nodeId: string;
    parentId: number;
    parentNodeId: string;
    title: string;
    code: string;
    type: string;
    display: null;
    image: string;
    url: string;
    sysEnabled: number;
    sysIndex: number;
    children: any;
}

export type IArr = IItem[];

export interface IObj {
    [any: string]: IArr;
}

// 定义菜单项类型
export interface MenuItem {
    key: React.Key;
    label: React.ReactNode;
    icon?: React.ReactNode | React.ComponentType<any>;
    children?: MenuItem[];
}

/**
 * Dispatch对象，对应React的useState的dispatch函数能力
 */
export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface LayoutState {
    //左侧定义数据
    arrList: IArrObj[];
    Obj: IObj;
    openLeftState: string;
    openLeftCheckState: boolean;
    openLeftLookState: boolean;
    openLeftStudyState: boolean;
    openLeftDoState: boolean;
    leftCheckArr: IArr;
    leftNum: number;
    leftPopWidth: number;

    // 右侧数据
    rightObj: IObj;
    arrRightList: IArrObj[];
    openState: string;
    openSymaState: boolean;
    openRuofState: boolean;
    openPopuState: boolean;
    openDispState: boolean;
    openPrevState: boolean;
    openTeamState: boolean;
    openTestState: boolean;
    checkArr: IArr;
    num: number;
    popWidth: number;

    //顶部定义数据
    topList: IArrObj[];
    topCopyList: IArrObj[];
    topItemObj: IObj;
    openCommonState: boolean;
    openTopState: string;
    topCheckArr: IArr;
    visible: boolean;

    noticeNumber: number;
    todoNumber: number;
    warnNumber: number;
}

/**
 * Model层接口定义，防止接口层逻辑污染Presenter的逻辑
 */
export interface DictApi {
    // loadLabelTree: () => Promise<any>;
    //
    // loadValueTable: (params: any, sort: any, filter: any) => Promise<any>;
    //
    // updateLabel: (params: any) => void;
    //
    // deleteLabel: (params: any) => void;
    //
    // updateValue: (params: any) => void;
    //
    // handleLeftCloseChange: (params: any) => void;

    getHomeBar: (params?: any) => Promise<any>;
    getLeftBar: (params?: any) => Promise<any>;
    getRightBar: (params?: any) => Promise<any>;
    getLeftContentBar: (params?: any) => Promise<any>;
    getRightContentBar: (params?: any) => Promise<any>;
    // 左侧数据

    loginOut: (params?: any) => void;

    changePwd: (params?: any) => void;

    getInputText: (value: any) => void;

    getSelfNotDoneTodoCount: (value?: any) => Promise<any>;
    getSelfNotReadNoticeCount: (value?: any) => Promise<any>;
    getSelfNotReadWarnCount: (value?: any) => Promise<any>;
}

//右侧点击栏
export interface IMessageObj {
    name: string;
    id: string;
}
