import {DictApi, Dispatch, IArrObj, LayoutState} from "@/layout/types";
import {clearUser} from "@/api/user/account";
import {getLocalStorageItem} from "@/utils/storage";
import {getSelfDetail} from "@/api/user/self";

/**
 *  DictPresenter MVVM对象中ViewModel对象
 *  Presenter: 将UI控制对象交给View，Presenter 只做状态和业务逻辑的处理。设计到UI的界面数据刷新也通过数据状态来控制
 */
export class DictPresenter {
    // 更新数据状态的dispatch函数
    private readonly dispatch: Dispatch<LayoutState>;
    // 传入的Model对象
    private readonly api: DictApi;
    // 当前的数据状态，由于DictPresenter是通过React.useRef对象来保存的，为了保持数据状态的同步，需要通过updateState函数来更新。
    private state: LayoutState;

    // 通过构造函数传入数据状态和Model对象
    public constructor(
        state: LayoutState,
        dispatch: Dispatch<LayoutState>,
        api: DictApi
    ) {
        this.state = state;
        this.dispatch = dispatch;
        this.api = api;
    }

    // 更新状态数据
    public syncState = (newState: LayoutState) => {
        this.state = newState;
    };
    // 退出登录
    public loginOut = (params?: any) => {
        localStorage.removeItem("homeMenu");
        clearUser();
    };
    // 更新密码
    public changePwd = (params?: any) => {
        console.log("我被触发了更新密码");
    };
    //     搜索框触发函数
    public getInputText = (e?: any) => {
        console.log("我是搜索框", e.target.value);
        const value = (e.target as HTMLInputElement).value;

        this.api.getInputText(value);
    };

    public getResult = () => {
        const result = getLocalStorageItem("homeMenu", []);
        const resultArr = result[0]?.children || [];
        const leftObj = resultArr?.find((item: any) => item.code == "left_area");
        const rightObj = resultArr?.find((item: any) => item.code == "right_area");
        const topObj = resultArr?.find((item: any) => item.code == "COMMON");
        return {
            leftObj,
            rightObj,
            topObj,
        };
    };
    //     左侧导航leftSiderBar
    //     // 统一全都关闭
    public getLeftArrList = () => {
        // const {leftObj} = this.getResult()
        // this.dispatch((prevState) => {
        //     return {
        //         ...prevState,
        //         arrList: leftObj?.children
        //     }
        // })
    };
    public handleLeftCloseChange = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                openLeftCheckState: false,
                openLeftLookState: false,
                openLeftStudyState: false,
                openLeftDoState: false,
            };
        });
    };
    public handleLeftOpenChange = (item: IArrObj) => {
        this.handleLeftCloseChange();
        if (item.code === this.state.openLeftState || item.children?.length == 0) {
            this.handleLeftCloseChange();
            return;
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                openLeftCheckState:
                    item.code === "check" ? true : prevState.openLeftCheckState,
                openLeftLookState:
                    item.code === "look" ? true : prevState.openLeftLookState,
                openLeftStudyState:
                    item.code === "study" ? true : prevState.openLeftStudyState,
                openLeftDoState: item.code === "do" ? true : prevState.openLeftDoState,
            };
        });
    };

    public messLeftClick = (item: IArrObj) => {
        if (item.code && item.code == this.state.openLeftState) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    openLeftState: "",
                };
            });
            return;
        }
        if (item.code) {
            const objList: any = this.state.arrList?.find((i) => i.code == item.code);
            const children = objList?.children || [];
            this.dispatch((prevState: any) => {
                return {
                    ...prevState,
                    openLeftState: item.code,
                    leftCheckArr: children,
                    leftNum: item.code == "do" ? 4 : 8,
                    leftPopWidth: item.code == "do" ? 730 : 443,
                };
            });
        }
    };

    //     右侧导航rightSiderBar

    //     统一关闭
    public handleCloseChange = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                openSymaState: false,
                openRuofState: false,
                openPopuState: false,
                openDispState: false,
                openPrevState: false,
                openTeamState: false,
                openTestState: false,
            };
        });
    };

    public handleOpenChange = (item: IArrObj) => {
        this.handleCloseChange();
        if (item.code === this.state.openState || item.children?.length == 0) {
            this.handleLeftCloseChange();
            return;
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                openSymaState: item.code === "syma" ? true : prevState.openSymaState,
                openRuofState: item.code === "ruof" ? true : prevState.openRuofState,
                openPopuState: item.code === "popu" ? true : prevState.openPopuState,
                openDispState: item.code === "disp" ? true : prevState.openDispState,
                openPrevState: item.code === "prev" ? true : prevState.openPrevState,
                openTeamState: item.code === "team" ? true : prevState.openTeamState,
                openTestState: item.code === "test" ? true : prevState.openTestState,
            };
        });
    };

    public messClick = (item: IArrObj) => {
        if (item.code && item.code == this.state.openState) {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    openState: "",
                };
            });
            return;
        }
        if (item.code) {
            const objList: any = this.state.arrRightList.find(
                (i) => i.code == item.code
            );
            const children = objList?.children || [];
            this.dispatch((prevState: any) => {
                return {
                    ...prevState,
                    openState: item.code,
                    num: item.code == "syma" || item.code == "team" ? 12 : 8,
                    popWidth: item.code == "syma" || item.code == "team" ? 315 : 443,
                };
            });
        }
    };

    // top部bar
    public fuzzySearchNestedTree(tree: any, keyword: string, options: any = {}) {
        const {searchProp = "title", caseSensitive = false} = options;

        const keywordLower = caseSensitive ? keyword : keyword.toLowerCase();

        function traverse(node: any) {
            if (!node) return null;

            // 检查当前节点是否匹配
            const value = caseSensitive
                ? node[searchProp]
                : (node[searchProp] || "").toLowerCase();

            const isMatch = value.includes(keywordLower);
            let hasMatchingChildren = false;

            // 递归处理子节点
            let newChildren = null;
            if (node.children && Array.isArray(node.children)) {
                newChildren = node.children
                    .map((child: any) => traverse(child))
                    .filter((child: any) => child !== null);

                hasMatchingChildren = newChildren.length > 0;
            }

            // 如果当前节点或其子节点匹配，返回该节点及其匹配的子树
            if (isMatch || hasMatchingChildren) {
                const newNode = {...node};
                if (newChildren) newNode.children = newChildren;
                return newNode;
            }

            return null;
        }

        // 处理根节点
        if (Array.isArray(tree)) {
            return tree.map((node) => traverse(node)).filter((node) => node !== null);
        }

        return traverse(tree) || null;
    }

    public handleTopOpenModel = () => {
        this.dispatch((prevState: any) => {
            return {
                ...prevState,
                openCommonState: !prevState.openCommonState,
            };
        });
    };

    public validateAuth = async () => {
        await getSelfDetail();
    };

    public changeVisible = (value: boolean) => {
        this.dispatch((prevState: any) => {
            return {
                ...prevState,
                visible: value,
            };
        });
        this.changeContainerFilter(value);
    };

    public changeContainerFilter = (visible: boolean) => {
        const classNames = [
            "home-container",
            "rules-databases-container",
            "laws-regulations-container",
            "general-manage-container",
        ];
        classNames.forEach((item) => {
            console.log(`${item}-filter`);

            const element: HTMLElement | null = document.getElementById(item);
            if (visible) {
                element?.classList.add(`${item}-filter`);
            } else {
                element?.classList.remove(`${item}-filter`);
            }
        });
    };

    public loadTodoList = async () => {
        const {success, data} = await this.api.getSelfNotDoneTodoCount();
        if (!success) return;
        this.dispatch(prev => ({
            ...prev,
            todoNumber: data
        }))
    }


    public loadNoticeList = async () => {
        const {success, data} = await this.api.getSelfNotReadNoticeCount();
        if (!success) return;
        this.dispatch(prev => ({
            ...prev,
            noticeNumber: data
        }))
    }

    public loadNoticeCount = async () => {
        const [notDoneTodoCount, notReadNoticeCount, notReadWarnCount] = await Promise.all([
            this.api.getSelfNotDoneTodoCount(),
            this.api.getSelfNotReadNoticeCount(),
            this.api.getSelfNotReadWarnCount()
        ]);

        this.dispatch((prevState) => {
            return {
                ...prevState,
                todoNumber: notDoneTodoCount.data,
                noticeNumber: notReadNoticeCount.data,
                warnNumber: notReadWarnCount.data
            }
        });
    }
}
