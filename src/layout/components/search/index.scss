@mixin placeholder {
  &::placeholder {
    @content;
  }

  &::-webkit-input-placeholder {
    @content;
  }

  &:-moz-placeholder {
    @content;
  }

  &::-moz-placeholder {
    @content;
  }

  &:-ms-input-placeholder {
    @content;
  }

  &::-ms-input-placeholder {
    @content;
  }
}

// 定义变量
$primary-color: #59847b;
$secondary-color: linear-gradient(0deg, #ECFFF6 0%, #FFFFFF 100%);;
$border-radius: 27px;
$text-color:#246d5c;
.container-box {
  //width: 100%;
  position: fixed;
  top: 121px;
  left: 35px;
  z-index: 2;
  .search-box {
    position: relative;
    width: 360px;
    height: 50px;
    input {
      width: 360px;
      height: 50px;
      padding: 0 40px 0 15px;
      border-radius: $border-radius;
      background: $secondary-color !important;
      outline: none;
      font-size: 21px;
      color: #333;
      box-shadow: 7px 11px 18px 0px rgba(0,115,86,0.21);
      border: 2px solid #007356;
      opacity: 0.58;
      @include placeholder {
        //color: $primary-color;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 21px;
        color: #007356;
        opacity: 0.49;
      }
    }

    /* 聚焦时的样式 */
    input:focus::placeholder {
      opacity: 0.3;
    }

    .search-icon {
      position: absolute;
      right: -40px;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      font-size: 40px;
      color: $primary-color;
      fill: $primary-color;
    }
  }
}