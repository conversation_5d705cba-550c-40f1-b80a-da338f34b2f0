/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description: 
 * @LastEditTime: 2025-07-24 21:25:07
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import "./index.scss";
import {useLayoutPresenter} from "@/layout/hooks/useLayoutPresenter";

const nav = () => {
    const {state, presenter} = useLayoutPresenter();
    return (
        <>
            <div className='container-box'>
                <div className="search-box">
                    <input type="text" placeholder="全库搜索" onChange={presenter.getInputText}/>
                    <img src={require('@/assets/Images/search.png')} className="search-icon" alt=""/>
                </div>
            </div>

        </>
    );
};

export default nav;
