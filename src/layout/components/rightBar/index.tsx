/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description:
 * @LastEditTime: 2025-07-25 15:19:58
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description:
 * @LastEditTime: 2025-07-25 09:52:12
 * @LastEditors: wangjiantao
 */
import {Badge} from "antd";
import "./index.scss";
import {useLayoutPresenter} from "@/layout/hooks/useLayoutPresenter";
import {useEffect} from "react";

const nav = () => {
    const {state, presenter} = useLayoutPresenter();

    useEffect(() => {
        // 打开之后先调一遍
        presenter.loadNoticeCount();
        setInterval(() => {
            if (!localStorage.getItem("token")) return;
            presenter.loadNoticeCount();
        }, 10000);
    }, []);

    return (
        <>
            <div className="container_box">
                <div className="messageItem">
                    <div style={{position: "relative"}} onClick={() => {
                        window.open(`#/user/notice/record?type=todo`, "_blank");
                    }}>
                        <Badge count={state.todoNumber}/>
                        <img src={require(`@/assets/Images/home/<USER>""/>
                    </div>

                    <div style={{position: "relative"}} onClick={() => {
                        window.open(`#/user/notice/record?type=notice`, "_blank");
                    }}>
                        <Badge count={state.noticeNumber}/>
                        <img src={require(`@/assets/Images/home/<USER>""/>
                    </div>
                    <div style={{position: "relative"}} onClick={() => {
                        window.open(`#/user/notice/record?type=warn`, "_blank");
                    }}>
                        <Badge count={state.warnNumber}/>
                        <img
                            src={require(`@/assets/Images/home/<USER>
                            alt=""
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default nav;
