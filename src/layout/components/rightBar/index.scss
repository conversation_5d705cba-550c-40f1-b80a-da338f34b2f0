// 定义变量
$primary-color: #59847b;
$secondary-color: rgba(215, 247, 227, 0.5);
$border-radius: 20px;
$text-color: #246d5c;
.container_box {
  display: flex;
  justify-content: space-between;
  position: fixed;
  top: 105px;
  right: 44px;
  z-index: 2;

  .custom-badge .ant-badge-count {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 50%;
    border: 3px solid #f2fffc;
    //box-shadow: 4px 7px 13px 0px rgba(0,115,86,0.21);
  }

  .messageItem {
    //background-color: $secondary-color;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;

    img {
      width: 125px;
      height: auto;
      display: inline-block;
      margin: 0 12px;
      cursor: pointer;
    }

    div {
      display: inline-block;
      font-weight: 800;
      color: $text-color;
      font-style: italic;
    }
    .ant-badge {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}

@media (max-width: 1680px) {
  .container_box {
    .messageItem {
      .img {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
  }
}

// 响应式：中等屏幕
@media (max-width: 1200px) {
  .container_box {
    .messageItem {
      .img {
        width: 50px;
        height: 50px;
      }
    }
  }
}

// 响应式：小屏幕
@media (max-width: 768px) {
  .container_box {
    .messageItem {
      .img {
        width: 70px;
        height: 70px;
      }
    }
  }
}
