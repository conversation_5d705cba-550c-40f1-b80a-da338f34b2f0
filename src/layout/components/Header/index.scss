.breadcrumb-item {
  color: #b88888;
}

.navLayout {
  width: 100vw;
  height: 71px;
  background-color: #0194FDFF;

  display: flex;
  justify-content: space-between;

  .navContainerimg {
    height: 100%;
    display: flex;
    align-items: center;

    .navImg {
      width: auto;
      height: 54px;
      margin: 15px 0 15px 34px;
    }

    span {
      color: white;
      font-size: 30px;
      font-weight: 600;
      display: inline-block;
      margin-left: 20px;
    }
  }

  .navContainer_btn {
    height: 71px;
    line-height: 71px;
  }

  .navContainer {
    height: 71px;
    display: flex;
    align-content: center;
    justify-content: center;
    margin-right: 30px;

    .nav_caption {
      //font-size: 20.71px !important;
      margin-right: 50px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 20px;
      color: #fff;
      display: inline-block;

      &:nth-child(1) {
        margin-right: 70px;
      }
    }

    .navItem {
      height: 71px;
      display: flex;
      justify-content: center;
      align-items: center;

      .nav_user {
        cursor: pointer;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 21px;
        color: #fff;
      }
    }
  }
}

.messageItem {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;

  img {
    width: 62px;
    height: 60px;
  }

  div {
    display: inline-block;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 21px;
    color: #2d2d2d;
  }
}
