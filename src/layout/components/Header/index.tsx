import React, { useState, useCallback, useEffect } from "react";
import { Avatar, Button, Col, Row, Space } from "antd";
import "./index.scss";
import HeaderDropdown from "@/layout/components/HeaderDropdown";
import { useLayoutPresenter } from "@/layout/hooks/useLayoutPresenter";
import { menuItems } from "../../api/dict";
import { useNavigate } from "react-router";
import CommonModel from "@/layout/components/commonModel";
import { useMenuStore } from "@/store";

const Header = () => {
  const navigate = useNavigate();
  const { state, presenter } = useLayoutPresenter();
  const username = localStorage.getItem("username");
  useEffect(() => {
    // presenter.getTopArrList()
  }, []);

  const clear = useMenuStore((state) => state.clear);
  const onMenuClick = useCallback((event: any) => {
    const { key } = event;
    if (key === "logout") {
      // 1. 先清除本地状态
      clear();

      // 2. 执行登出API调用
      presenter.loginOut();

      // 或者使用更温和的方式：
      navigate("/login", {
        replace: true,
        state: { from: null },
      });

      // 3. 强制导航到登录页
      window.location.href = "#/login"; // 硬刷新确保完全重置
    }
    if (key === "changePwd") {
      presenter.changePwd();
    }
  }, []);

  const ContentFun = (
    <>
      <Row gutter={45}>
        {state.topCheckArr.length > 0 &&
          state.topCheckArr.map((item, index) => (
            <Col span={state.leftNum} key={index}>
              <div
                className="imgContent"
                key={index}
                onClick={() => {
                  presenter.handleLeftCloseChange();
                  window.open("#/404", "_blank");
                }}
              >
                <div className="img_nav">
                  <img src={require(`@/assets/Images/${item.image}`)} alt="" />
                </div>
                <div className="img_title">{item.title}</div>
              </div>
            </Col>
          ))}
      </Row>
    </>
  );
  return (
    <>
      <div className="navLayout">
        <div className="navContainerimg">
          <img
            src={require("@/assets/Images/home/<USER>")}
            className="navImg"
          />
          <span>贵州烟草商业政策法规制度数字化平台</span>
        </div>

        <div className="navContainer_btn">
          <CommonModel />
        </div>

        <div className="navContainer">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              width: 400,
            }}
          >
            <div style={{ lineHeight: 1.5, width: "auto" }}>
              <span className="nav_caption">依法治国是坚持和发展中国特色</span>
            </div>
            <div style={{ lineHeight: 1.5, width: "auto", marginLeft: 40 }}>
              <span className="nav_caption">社会主义的本质要求和重要保障</span>
            </div>
          </div>

          <HeaderDropdown
            menu={{
              selectedKeys: [],
              onClick: onMenuClick,
              items: menuItems,
            }}
          >
            <div className="navItem">
              <Avatar
                src={require("@/assets/Images/avatar.png")}
                style={{
                  marginRight: 10,
                }}
              />
              <div className="nav_user">{username}</div>
            </div>
          </HeaderDropdown>
        </div>
      </div>
    </>
  );
};

export default Header;
