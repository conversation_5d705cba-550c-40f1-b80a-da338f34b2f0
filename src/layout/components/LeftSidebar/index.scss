$background-color: rgba(255, 255, 255, 0.5);

.sidebar_layout {
  position: fixed;
  top: 50%;
  transform: translateY(-40%);
  left: 19px;
  padding: 40px 20px;
  background-image: url("@/assets/Images/leftbg.png");
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;

  .messageItem {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 40px;
      height: 40px;
    }

    div {
      display: inline-block;
      margin-top: 5px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 21px;
      color: #2d2d2d;
    }
  }
}

.custom-popover {
  .ant-popover-inner {
    width: 721px;
    height: 348px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: -4px 7px 13px 0px rgba(0, 115, 86, 0.17);
    border-radius: 12px;
    border: 1px solid #007356;
  }

  .ant-popover-arrow {
    border-color: #1890ff; /* 改变箭头颜色 */
    width: 30px;
    height: 30px;
  }
}

.container_box_img {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.imgContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;

  .img_nav {
    width: 63px;
    height: 63px;
    cursor: pointer;

    img {
      width: 63px;
      height: 63px;
    }
  }

  .img_title {
    text-align: center;
    width: 120px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
  }
}

@media (max-width: 1680px) {
  .sidebar_layout_right {
    .messageItem {
      display: flex;
      flex-wrap: wrap;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}

// 响应式：中等屏幕
@media (max-width: 1200px) {
  .sidebar_layout_right {
    .messageItem {
      img {
        width: 30px;
        height: 30px;
      }
      div {
        font-size: 16px;
      }
    }
  }
}

// 响应式：小屏幕
@media (max-width: 768px) {
  .sidebar_layout_right {
    padding: 30px 10px;
    right: 5px;
    .messageItem {
      img {
        width: 28px;
        height: 28px;
      }
      div {
        font-size: 12px;
      }
    }
  }
}
