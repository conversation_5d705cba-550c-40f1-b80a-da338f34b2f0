/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 10:10:35
 * @Description:
 * @LastEditTime: 2025-07-28 16:03:56
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React, { useEffect } from "react";
import { Space, Row, Col, Modal } from "antd";
import "./index.scss";
import { useNavigate } from "react-router-dom";
import { IArrObj } from "@/layout/types";

import { useLayoutPresenter } from "@/layout/hooks/useLayoutPresenter";

const sidebar = () => {
  const navigate = useNavigate();
  const { state, presenter, getLeftArrList, arrList } = useLayoutPresenter();
  const stateMap: any = {
    check: state.openLeftCheckState,
    look: state.openLeftLookState,
    study: state.openLeftStudyState,
    do: state.openLeftDoState,
  };
  useEffect(() => {
    getLeftArrList();
  }, []);

  return (
    <>
      <div className="sidebar_layout">
        <Space size={"large"} direction="vertical">
          {arrList?.length > 0 &&
            arrList.map((item: IArrObj, index: number) => {
              const imgSrc = (() => {
                try {
                  return require(`@/assets/Images/${item.image}`);
                } catch (e) {
                  return ""; // 或返回一个占位图片
                }
              })();
              return (
                <div
                  key={index}
                  className="messageItem"
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止事件冒泡   presenter.handleLeftCloseChange();
                    presenter.messLeftClick(item);
                    presenter.changeVisible(true);
                  }}
                >
                  {imgSrc && <img src={imgSrc} alt="" />}
                  <div>{item.title}</div>
                </div>
              );
            })}
        </Space>
      </div>

      <Modal
        onCancel={() => presenter.changeVisible(false)}
        open={state.visible}
        footer={null}
        mask={false}
        styles={{
          content: {
            maxWidth: "40vw",
            minWidth: "40vw",
            backgroundColor: "rgba(255, 255, 255, 0.5)",
            transform: `translate(-40%, ${window.innerHeight / 2 - 200}px)`,
            left: "50%",
          },
          mask: {
            backgroundColor: "rgba(0, 0, 0, 0)",
          },
        }}
      >
        <Row gutter={45}>
          {state.leftCheckArr?.length > 0 &&
            state.leftCheckArr.map((item, index) => {
              const imgSrc = (() => {
                try {
                  return require(`@/assets/Images/${item.image}`);
                } catch (e) {
                  console.warn(`Image not found: ${item.image}`);
                  return ""; // 或返回一个占位图片
                }
              })();

              return (
                <Col span={6} key={index}>
                  <div
                    className="imgContent"
                    onClick={(e) => {
                      e.stopPropagation();
                      presenter.handleLeftCloseChange();
                      navigate(item.url);
                    }}
                  >
                    {imgSrc && (
                      <div className="img_nav">
                        <img
                          src={imgSrc}
                          alt=""
                          onError={(e) => {
                            e.currentTarget.style.display = "none";
                            e.stopPropagation();
                          }}
                        />
                      </div>
                    )}
                    <div className="img_title">{item.title}</div>
                  </div>
                </Col>
              );
            })}
        </Row>
      </Modal>
    </>
  );
};

export default sidebar;
