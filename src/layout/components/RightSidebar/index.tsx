import React, { useEffect } from "react";
import "./index.scss";
import { useNavigate } from "react-router-dom";
import { IArrObj } from "@/layout/types";
import { useLayoutPresenter } from "@/layout/hooks/useLayoutPresenter";
import { Button, Popover, Space, Row } from "@itm/components-pc";

const sidebar = () => {
  const navigate = useNavigate();

  const {
    state,
    presenter,
    arrRightList,
    getRightArrList,
    checkArr,

    handlerOpenPopover,
  } = useLayoutPresenter();
  const stateMap: any = {
    syma: state.openSymaState,
    ruof: state.openRuofState,
    popu: state.openPopuState,
    disp: state.openDispState,
    prev: state.openPrevState,
    team: state.openTeamState,
    test: state.openTestState,
  };
  useEffect(() => getRightArrList(), []);

  const ContentFun = (
    <>
      <Row gutter={45}>
        {checkArr?.length > 0 &&
          checkArr?.map((item, index) => (
            <Row.Col span={state.num} key={index}>
              <div
                className="imgsContent"
                key={index}
                onClick={() => {
                  presenter.handleCloseChange();
                  window.open("#/404", "_blank");
                }}
              >
                {item.image && (
                  <div className="img_nav">
                    <img
                      src={require(`@/assets/Images/${item.image}`)}
                      alt=""
                    />
                  </div>
                )}

                <div className="img_title">{item.title}</div>
              </div>
            </Row.Col>
          ))}
      </Row>
    </>
  );

  return (
    <>
      <div className="sidebar_layout_right">
        {/* <button onClick={() => {
                    console.log(state.checkArr, 'state.checkArr');

                }}>点击</button> */}
        <Space size={"large"} direction="vertical">
          {arrRightList?.length > 0 &&
            arrRightList.map((item: IArrObj, index: number) => (
              <Popover
                content={ContentFun}
                trigger="click"
                placement="right"
                key={index}
                overlayInnerStyle={{
                  width: state.popWidth + "px",
                  padding: "52px 44px",
                  background: "rgba(255,255,255,0.5)",
                  boxShadow: "-4px 7px 13px 0px rgba(0,115,86,0.17)",
                  borderRadius: "12px",
                  border: "1px solid #007356",
                }}
                open={stateMap[item.code]}
                onOpenChange={() => handlerOpenPopover(item)}
              >
                <div
                  className="messageItem"
                  onClick={() => presenter.messClick(item)}
                >
                  {item.image && (
                    <img
                      src={require(`@/assets/Images/${item.image}`)}
                      alt=""
                    />
                  )}

                  <div>{item.title}</div>
                </div>
              </Popover>
            ))}
        </Space>
      </div>
    </>
  );
};

export default sidebar;
