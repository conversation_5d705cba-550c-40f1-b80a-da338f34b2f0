$background-color: rgba(255, 255, 255, .5);

.sidebar_layout_right {
  position: fixed;
  top: 50%;
  transform: translateY(-40%);
  right: 19px;
  padding: 40px 20px;
  background-image: url("@/assets/Images/rightbg.png");
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;

  .messageItem {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;

    img {
      width: 40px;
      height: 40px;
    }

    div {
      display: inline-block;
      margin-top: 10px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 21px;
      color: #2D2D2D;
    }
  }
}

.imgsContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 10px;


  .img_nav {
    width: 83px;
    height: 83px;

  }

  .img_title {
    text-align: center;
    width: 130px;
    font-family: Source <PERSON>;
    font-weight: 500;
    font-size: 21px;
    color: #333333;
  }
}

@media (max-width: 1680px) {
  .sidebar_layout_right {
    .messageItem {
      img {
        width: 40px;
        height: 40px;
      }
    }

  }
}

// 响应式：中等屏幕
@media (max-width: 1200px) {
  .sidebar_layout_right {
    .messageItem {
      img {
        width: 30px;
        height: 30px;
      }
      div {
        font-size: 16px;
      }
    }
  }
}

// 响应式：小屏幕
@media (max-width: 768px) {
  .sidebar_layout_right {
    padding: 30px 10px;
    right: 5px;
    .messageItem {
      img {
        width: 28px;
        height: 28px;
      }
      div {
        font-size: 12px;
      }
    }
  }
}