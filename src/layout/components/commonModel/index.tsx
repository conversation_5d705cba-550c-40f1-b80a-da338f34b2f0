/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description:
 * @LastEditTime: 2025-07-17 16:35:52
 * @LastEditors: wangjiantao
 */
import React, { useState, useCallback, useEffect } from "react";
import "./index.scss";
import { Button, Modal, Row } from "@itm/components-pc";
import { useLayoutPresenter } from "@/layout/hooks/useLayoutPresenter";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { IArrObj, IItem } from "@/layout/types";
import { useNavigate } from "react-router";
import { POBrowser } from "js-pageoffice";

const CommonModel = () => {
  const { state, presenter, getTopArrList, searchApp, searchCopy, topList } =
    useLayoutPresenter();
  useEffect(() => getTopArrList(), []);
  const [value, setValue] = useState("");

  const goJump = (info: IItem) => {
    if (info.code === "contract_draft") {
      POBrowser.setProxyBaseAPI("/itm-legal-review-contract");
      POBrowser.setHeader("token", localStorage.getItem("token"));
      const params = {
        token: localStorage.getItem("token"),
        username: localStorage.getItem("username"),
      }
      return POBrowser.openWindowModeless(
        `/contact-draft-page-office`,
        `width=${screen.width}px;height=${screen.height}px;`,
        JSON.stringify(params)
      );
    }

    window.open(`#${info.url}`, "_blank");
  };

  useEffect(() => {
    presenter.validateAuth()
  }, [])

  return (
    <>
      <Button onClick={presenter.handleTopOpenModel}>通用管理</Button>
      <Modal
        title=""
        centered={false}
        closable={false}
        open={state.openCommonState}
        footer={null}
        width={1330}
      >
        <Row>
          <Row.Col span={9} />
          <Row.Col span={3}>
            <div className="search_app">搜索应用</div>
          </Row.Col>
          <Row.Col span={10}>
            <Input
              placeholder="请输入图标名称"
              onPressEnter={(e: any) => {
                setValue(e.target.value);
              }}
              prefix={<SearchOutlined />}
            />
          </Row.Col>
          <Row.Col span={2}>
            <Button onClick={presenter.handleTopOpenModel}>关闭</Button>
          </Row.Col>
        </Row>
        {topList?.length > 0 &&
          topList.map((item: IArrObj, index) => (
            <div className="modal_box" key={index}>
              <div className="modal_nav">
                <div className="model_icon"></div>
                <div>{item.title}</div>
              </div>
              <Row gutter={30}>
                {item.children.length > 0 &&
                  item.children.map((info: IItem, i: number) => (
                    <Row.Col span={3} key={i}>
                      <div
                        className="imgContent_box"
                        onClick={() => goJump(info)}
                      >
                        <div className="img_nav">
                          <img src={require(`@/assets/Images/1.png`)} alt="" />
                        </div>
                        <div className="img_title">{info.title}</div>
                      </div>
                    </Row.Col>
                  ))}
              </Row>
            </div>
          ))}
      </Modal>
    </>
  );
};

export default CommonModel;
