
import type { DropDownProps } from 'antd/es/dropdown';
import React from 'react';
import {Dropdown} from "@itm/components-pc";

export type HeaderDropdownProps = {
  overlayClassName?: string;
  placement?: 'bottomLeft' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight' | 'bottomCenter';
} & Omit<DropDownProps, 'overlay'>;

const HeaderDropdown: React.FC<HeaderDropdownProps> = ({ overlayClassName: cls, ...restProps }) => {

  return <Dropdown  {...restProps} />;
};

export default HeaderDropdown;
