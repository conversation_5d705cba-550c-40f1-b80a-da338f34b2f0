import {get, page, post} from "@/api";

export async function getInstitutionDetail(id:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/institution/detail?id=${id}`);
}
export async function save(data: any) {
    return post('/itm-abolish/api/cmd/institutionAbolish/save', data);
}

export async function getAbolishInfoDetail(id:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/abolish/byInstitutionId?institutionId=${id}`);
}

export async function getAbolishInfoDetailById(id:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/abolish/byId?id=${id}`);
}

export async function submitFlow(data: any) {
    return post('/itm-abolish/api/cmd/institutionAbolish/abolishFlow', data);
}
export async function recallFlow(data: any) {
    return post('/itm-abolish/api/cmd/institutionAbolish/recallAbolishFlow', data);
}

export async function getAbolishExamineInfos(id:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/abolish/examine/infos?abolishId=${id}`);
}
export async function saveExamineInfo(data: any) {
    return post('/itm-abolish/api/cmd/institutionAbolish/saveExamine', data);
}

export async function countRecordIdStatus(recordId:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/flow/recordId/status?recordId=${recordId}`);
}
export async function getAbolishExamineInfoByRecordId(recordId:any,id:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/abolish/examineInfo?recordId=${recordId}&abolishId=${id}`);
}
// ........................
export async function getMyAbolishInstitutionList() {
    return get('/itm-abolish/api/query/institutionAbolish/myAbolish');
}

export async function getAlreadyAbolishInstitutionList(current:any,pageSize:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/alreadyAbolish?current=${current}&pageSize=${pageSize}`);
}

export async function getCanAbolishInstitutionList(params:any) {
    return get('/itm-abolish/api/query/institutionAbolish/canAbolish',params);
}
// 查看合规清单使用
export async function getInstitutionCDetail(params: any) {
    return get('/itm-archive-institution/api/query/institution/institutionDetailFromCompliance', params);
}
export async function complianceDetail(params: any) {
    return get('/itm-compliance/api/query/compliance/detail', params);
}