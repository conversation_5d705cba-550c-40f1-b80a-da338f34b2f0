import {httpClient} from "@/api/index";


export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-supervise/api/query/number/maintenance/list', params, sort, filter, match);
}

export async function getInfo(params: any) {
    return httpClient.get("/itm-supervise/api/query/number/maintenance/getNumberMaintenance", params);
}
export async function addOrUpdate(formData: FormData) {
    return httpClient.post("/itm-supervise/api/cmd/number/maintenance/addOrUpdate", formData);
}

export async function del(body: { id: number, }) {
    return httpClient.post('/itm-supervise/api/cmd/number/maintenance/delete', body);
}

export async function init(body: { id: number, }) {
    return httpClient.post('/itm-supervise/api/cmd/number/maintenance/init', body);
}
