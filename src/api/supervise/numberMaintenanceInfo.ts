import {httpClient} from "@/api/index";
import {download} from "@/utils/download";


export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-supervise/api/query/number/maintenance/info/list', params, sort, filter, match);
}

export async function exportList(params: any) {
    return download("/itm-supervise/api/query/number/maintenance/info/exportNumberMaintenanceInfoList", params, "号段列表详情.xlsx");
}
