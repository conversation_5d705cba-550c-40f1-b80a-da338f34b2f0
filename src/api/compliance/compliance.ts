import {httpClient} from "@/api/index";

export async function getInstitutionDetail(params: any) {
    return httpClient.get('/itm-archive-institution/api/query/institution/institutionDetailFromCompliance', params);
}

export async function addOrUpdateCompliance(body: any) {
    return httpClient.post('/itm-compliance/api/cmd/compliance/addOrUpdate', body);
}
export async function importCompliance(formData: FormData) {
    return httpClient.post('/itm-compliance/api/cmd/compliance/importCompliance', formData);
}
export async function complianceList(params: any, sort: any, filter: any) {
    return httpClient.get('/itm-compliance/api/query/compliance/list', params);
}

export async function complianceDetail(params: any) {
    return httpClient.get('/itm-compliance/api/query/compliance/detail', params);
}

export async function startFlow(body: any) {
    return httpClient.post('/itm-compliance/api/cmd/compliance/startFlow', body);
}

export async function submitFlow(body: any) {
    return httpClient.post('/itm-compliance/api/cmd/compliance/submitFlow', body);
}

export async function recallFlow(body: any) {
    return httpClient.post('/itm-compliance/api/cmd/compliance/recallFlow', body);
}
