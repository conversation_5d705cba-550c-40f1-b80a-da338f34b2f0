import {httpClient} from "@/api/index";

export async function getNumberPage(params: any, sort: any, filter: any, match: {key: string, type: string}[]){
    return httpClient.page('itm-supervise/api/query/enforcement-number/page', params, sort, filter, match);
}

export async function getNumberDetail(params: any){
    return httpClient.get('itm-supervise/api/query/enforcement-number/detail', params);
}

export async function saveNumber(params: any){
    return httpClient.post('itm-supervise/api/cmd/enforcement-number/save', params);
}

export async function deleteNumber(params: any){
    return httpClient.post('itm-supervise/api/cmd/enforcement-number/delete', params);
}