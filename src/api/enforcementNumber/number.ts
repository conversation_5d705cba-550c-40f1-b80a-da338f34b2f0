import {httpClient} from "@/api/index";

export async function getNumberPage(params: any, sort: any, filter: any, match: {key: string, type: string}[]){
    return httpClient.page('itm-supervise/api/query/enforcement-number/page', params, sort, filter, match);
}

export async function getNumberDetail(params: any){
    return httpClient.get('itm-supervise/api/query/enforcement-number/detail', params);
}


export async function getStatusOptions(){
    return httpClient.get('itm-supervise/api/query/number-segment/status-options');
}