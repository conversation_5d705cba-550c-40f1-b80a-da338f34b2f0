import {httpClient} from "@/api";

export async function page(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-archive-tag/api/query/tag/page', params, sort, filter, match);
}

export async function tree() {
    return httpClient.get('/itm-archive-tag/api/query/tag/tree');
}

export async function save(body: any) {
    return httpClient.post('/itm-archive-tag/api/cmd/tag/save', body);
}

export async function del(body: {
    id: number,
}) {
    return httpClient.post('/itm-archive-tag/api/cmd/tag/delete', body);
}

export async function detail(code: any) {
    return httpClient.get('/itm-archive-tag/api/query/tag/detail', {code: code});
}

export async function enable(id: any) {
    return httpClient.post('/itm-archive-tag/api/meta/tag/enable', {id: id});
}

export async function disable(id: any) {
    return httpClient.post('/itm-archive-tag/api/meta/tag/disable', {id: id});
}


export async function findTagsByDataType(dataType: string) {
    return httpClient.get('/itm-archive-tag/api/query/tag/list', {dataType: dataType});
}

export async function saveRelationData(body: any) {
    return httpClient.post('/itm-archive-tag/api/cmd/tagRelationData/save', body);
}

export async function relationList(dataType: any, dataId: any) {
    return httpClient.get('/itm-archive-tag/api/query/tagRelationData/list', {dataType: dataType, dataId: dataId});
}