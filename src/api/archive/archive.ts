import {page, post, get, download} from "@/api/index";

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-archive-oss/api/query/file/list', params, sort, filter, match);
}

export async function upload(formData: FormData) {
    return post("/itm-archive-oss/api/cmd/file/upload", formData);
}

export async function updateArchiveFile(formData: FormData) {
    return post("/itm-archive-oss/rpc/file/upload", formData);
}

export async function generateFileAuthFromArchive(params: any) {
    return get("/itm-archive-oss/api/cmd/file/generateFileAuth", params);
}

export async function downloadFileFromArchive(code: string,fileName: string) {
    return download("/itm-archive-oss/open/query/file/download?code="+code,fileName);
}


