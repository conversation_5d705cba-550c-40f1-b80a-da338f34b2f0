/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 17:57:48
 * @Description:
 * @LastEditTime: 2025-07-21 17:45:09
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { get, page, post } from "@/api";

export async function innerInstitutionList(
  params: any,
  sort: any,
  filter: any,
  match: {
    key: string;
    type: string;
  }[]
) {
  return page(
    "/itm-archive-institution/api/query/institution/institutionList",
    params,
    sort,
    filter,
    match
  );
}

export async function institutionItem(params: any) {
  return get(
    "/itm-archive-institution/api/query/institution/institutionItem",
    params
  );
}

export async function updateInstitution(params: any) {
  return post(
    "/itm-archive-institution/api/cmd/institution/updateInstitution",
    params
  );
}

export async function institutionSearch(
  params: any,
  sort: any,
  filter: any,
  match: {
    key: string;
    type: string;
  }[]
) {
  return page(
    "/itm-archive-institution/api/query/institution/institutionSearch",
    params,
    sort,
    filter,
    match
  );
}

export async function institutionTagTree() {
  return get("itm-archive-tag/api/query/tag/tree");
}

export async function loadInstitutionCategoryNum() {
  return get("/itm-archive-institution/api/query/institution/institutionCategoryNum");
}

export async function institutionDetail(params: any) {
  return get(
    "/itm-archive-institution/api/query/institution/institutionDetail",
    params
  );
}

export async function addOrUpdateQuestion(body: any) {
  return post("/itm-archive-institution/api/cmd/comment/addOrUpdate", body);
}

export async function removeQuestion(id: number) {
  return post("/itm-archive-institution/api/cmd/comment/delete", { id });
}

/**
 * @description 查询提问及问答列表
 * */
export async function questionList(params: { institutionId: number }) {
  return get("/itm-archive-institution/api/query/comment/list", params);
}
