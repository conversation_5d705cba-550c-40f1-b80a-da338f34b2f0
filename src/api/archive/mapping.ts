import {httpClient} from "@/api";

export async function page(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-archive-tag/api/query/tagRelationMapping/page', params, sort, filter, match);
}

export async function create(body: any) {
    return httpClient.post('/itm-archive-tag/api/cmd/tagRelationMapping/save', body);
}

export async function update(body: any) {
    return httpClient.post('/itm-archive-tag/api/cmd/tagRelationMapping/save', body);
}

export async function del(body: {
    id: number,
}) {
    return httpClient.post('/itm-archive-tag/api/cmd/tagRelationMapping/delete', body);
}

export async function detail(id: any) {
    return httpClient.get('/itm-archive-tag/api/query/tagRelationMapping/detail', {id: id});
}