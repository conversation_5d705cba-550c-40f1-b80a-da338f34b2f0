import {post} from "@/api";

export interface OrgTreeQuery {
    // 父节点id，如果传入，则从这个节点开始加载
    parentNodeId?: string;
    // 是否需要部门数据
    needDept?: boolean;
    // 是否需要岗位数据
    needPost?: boolean;
}

export type TreeData = {
    id: number;
    nodeId: string;
    // post的parentId指的是所属部门的id
    parentId?: number;
    parentNodeId?: string;

    name: string;
    // 只有unit有
    fullName: string;
    // unit,dept有，post没有
    shortName: string;
    code?: string;
    type: string;
    unitId?: number;
    unitNodeId?: string;

    children?: TreeData[];
}

export async function getOrgTree(
    request: OrgTreeQuery
) {
    return post('/itm-user/api/query/org/tree', request);

}
