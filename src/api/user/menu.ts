import {get, page, post} from "@/api";

export type TreeData = {
    nodeId: string;
    parentNodeId?: string;
    id: number;
    parentId?: number;
    title: string;
    code: string;
    type: string;
    location?: string;
    image?: string;
    icon?: string;
    url?: string;
    sysEnabled: number;
    sysIndex: number;

    children?: TreeData[];
}

export async function getMenuTree() {

    return get('/itm-user/api/query/menu/tree', {});

}

export async function getUserMenus() {

    return get('/itm-user/api/query/menu/userMenus', {});

}

export async function queryMenuPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-user/api/query/menu/page', params, sort, filter, match);
}

export async function saveMenu(body: any) {

    console.log("/itm-user/api/cmd/menu/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/menu/save', body);
}

export async function deleteMenu(id: any) {

    return post('/itm-user/api/cmd/menu/delete', {id: id});
}

export async function enableMenu(id: any) {
    console.log("/itm-user/api/meta/menu/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/menu/enable', {id: id});
}

export async function disableMenu(id: any) {
    console.log("/itm-user/api/meta/menu/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/menu/disable', {id: id});
}