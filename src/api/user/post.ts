import {page, post} from "@/api";

export async function queryPostPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/itm-user/api/query/post/page", params);
    // return {
    //     data: [
    //         {
    //             id: 1,
    //             name: '测试部门1',
    //             code: 'test',
    //             type: "部门",
    //             unitId: "UNIT-2"
    //         },
    //         {
    //             id: 2,
    //             name: '测试部门2',
    //             code: 'test2',
    //             type: "部门",
    //             unitId: "UNIT-3"
    //         }
    //     ],
    //     success: true,
    //     total: 2
    // }

    return page('/itm-user/api/query/post/page', params, sort, filter, match);
}

export async function queryPostList(params: any,) {
    const response = await post('/itm-user/api/query/post/list', {deptNodeId: params});
    if (response.success) {
        const list = response.data.total > 0 ? response.data.list : [];
        return {
            data: list,
            success: response.success,
            message: response.errMessage
        };
    } else {
        return {
            data: [],
            success: response.success,
            message: response.errMessage
        }
    }
}

export async function savePost(body: any) {

    console.log("/itm-user/api/cmd/post/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/post/save', body);
}

export async function deletePost(id: any) {
    console.log("/itm-user/api/cmd/post/delete", {id: id});
    return {
        success: true,
    }

    // return post('/api/cmd/unit/save', {id: id});
}

export async function enablePost(id: any) {
    console.log("/itm-user/api/meta/post/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/post/enable', {id: id});
}

export async function disablePost(id: any) {
    console.log("/itm-user/api/meta/post/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/post/disable', {id: id});
}
