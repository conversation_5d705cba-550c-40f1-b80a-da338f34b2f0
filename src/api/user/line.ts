import {get, page, post} from "@/api";


export async function queryLinePage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/itm-user/api/query/line/page", params);
    return page('/itm-user/api/query/line/page', params, sort, filter, match);
}

export async function queryLineList() {
    const response = await get('/itm-user/api/query/line/list');
    if (response.success) {
        const list = response.data.total > 0 ? response.data.list : [];
        return {
            data: list,
            success: response.success,
            message: response.errMessage
        };
    } else {
        return {
            data: [],
            success: response.success,
            message: response.errMessage
        }
    }
}

export async function saveLine(body: any) {

    console.log("/itm-user/api/cmd/line/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/line/save', body);
}

export async function deleteLine(id: any) {
    console.log("/itm-user/api/cmd/line/delete", {id: id});
    return {
        success: true,
    }

    // return line('/api/cmd/unit/save', {id: id});
}

export async function enableLine(id: any) {
    console.log("/itm-user/api/meta/line/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/line/enable', {id: id});
}

export async function disableLine(id: any) {
    console.log("/itm-user/api/meta/line/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/line/disable', {id: id});
}