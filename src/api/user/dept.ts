import {page, post} from "@/api";

export async function queryDeptPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/itm-user/api/query/dept/page", params);
    // return {
    //     data: [
    //         {
    //             id: 1,
    //             name: '测试部门1',
    //             code: 'test',
    //             type: "部门",
    //             unitId: "UNIT-2"
    //         },
    //         {
    //             id: 2,
    //             name: '测试部门2',
    //             code: 'test2',
    //             type: "部门",
    //             unitId: "UNIT-3"
    //         }
    //     ],
    //     success: true,
    //     total: 2
    // }

    return page('/itm-user/api/query/dept/page', params, sort, filter, match);
}

export async function saveDept(body: any) {

    console.log("/itm-user/api/cmd/dept/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/dept/save', body);
}

export async function deleteDept(id: any) {
    console.log("/itm-user/api/cmd/dept/delete", {id: id});
    return {
        success: true,
    }

    // return post('/api/cmd/unit/save', {id: id});
}

export async function enableDept(id: any) {
    console.log("/itm-user/api/meta/dept/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/dept/enable', {id: id});
}

export async function disableDept(id: any) {
    console.log("/itm-user/api/meta/dept/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/dept/disable', {id: id});
}
