/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 17:29:45
 * @Description:
 * @LastEditTime: 2025-06-27 17:24:56
 * @LastEditors: wangjiant<PERSON>
 */
import { httpClient } from "@/api";
import { getHomeBar } from "../home";

export async function login(body: Account.LoginRequest) {
  return httpClient.post("/itm-user/user/login", body);
}

export async function captcha() {
  return httpClient.get("/open/captcha");
}

export function clearUser() {
  localStorage.removeItem("username");
  localStorage.removeItem("token");
  localStorage.removeItem("authorities");
  localStorage.removeItem("avatar");
}

export async function initUser(user: {
  username: string;
  token: string;
  authorities: string[];
  avatar: string;
}) {
  const { username, token, authorities, avatar } = user;
  localStorage.setItem("username", username);
  localStorage.setItem("token", token);
  if (authorities) {
    localStorage.setItem("authorities", JSON.stringify(authorities));
  }
  localStorage.setItem("avatar", avatar || "/logo.png");

  const { data } = await getHomeBar();
  localStorage.setItem("homeMenu", JSON.stringify({ state: { menus: data } }));

  return data;
}
