import {page, post} from "@/api";

export async function queryUnitPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {

    console.log("/itm-user/api/cmd/unit/page", params);
    // return {
    //     data: [
    //         {
    //             id: 1,
    //             name: '测试公司',
    //             code: 'test',
    //             type: "公司"
    //         },
    //         {
    //             id: 2,
    //             name: '测试公司2',
    //             code: 'test2',
    //             type: "公司"
    //         }
    //     ],
    //     success: true,
    //     total: 2
    // }

    return page('/itm-user/api/query/unit/page', params, sort, filter, match);

}

export async function saveUnit(body: any) {

    console.log("/itm-user/api/cmd/unit/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/unit/save', body);
}

export async function deleteUnit(id: any) {
    console.log("/itm-user/api/cmd/unit/delete", {id: id});

    return post('/itm-user/api/cmd/unit/delete', {id: id});
}

export async function enableUnit(id: any) {
    console.log("/itm-user/api/meta/unit/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/unit/enable', {id: id});
}

export async function disableUnit(id: any) {
    console.log("/itm-user/api/meta/unit/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/unit/disable', {id: id});
}
