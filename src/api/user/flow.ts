import {get, page, post} from "@/api";

// 流程设计

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page("/itm-user/api/query/flowWork/page", params, sort, filter, match);
}

export async function save(body: any) {
    return post("/itm-user/api/cmd/flowWork/save", body);
}

export async function copy(id: any) {
    return post("/itm-user/api/cmd/flowWork/copy", {id});
}

export async function remove(id: any) {
    return post("/itm-user/api/cmd/flowWork/delete", {id});
}

export async function changeState(id: any) {
    return post("/itm-user/api/cmd/flowWork/changeState", {id});
}

export async function schema(body: any) {
    return post("/itm-user/api/cmd/flowWork/schema", body);
}

// 流程控制

export async function startFlow(body: any) {
    return post("/itm-user/api/cmd/flowRecord/startFlow", body);
}

export async function getFlowStep(body: any) {
    return post("/itm-user/api/cmd/flowRecord/getFlowStep", body);
}

export async function removeFlow(body: any) {
    return post("/itm-user/api/cmd/flowRecord/remove", body);
}

export async function detail(id?: any, workCode?: any) {
    return get("/itm-user/api/query/flowRecord/detail", {id, workCode});
}

export async function flowWorkDetailWithUnitCustom(workCode: any) {
    return get("/itm-user/api/query/flowRecord/detailWithUnitCustom", {workCode});
}

export async function processDetail(processId: any) {
    return get("/itm-user/api/query/flowRecord/processDetail", {processId});
}

export async function recordDetail(id: any) {
    return get("/itm-user/api/query/flowRecord/recordDetail", {id});
}

export async function processImgProperties(workCode: any, recordId?: any, processId?: any) {
    return get("/itm-user/api/query/flowRecord/processImgProperties", {workCode, recordId, processId});
}

export async function flowStepOperatorsView(workCode: any, recordId?: any) {
    return get("/itm-user/api/query/flowRecord/flowStepOperatorsView", {workCode, recordId});
}

export async function stepNodeOperators(recordId?: any, processId?: any) {
    return get("/itm-user/api/query/flowRecord/stepNodeOperators", {recordId, processId});
}

export async function saveFlow(body: any) {
    return post("/itm-user/api/cmd/flowRecord/save", body);
}

export async function submitFlow(body: any) {
    return post("/itm-user/api/cmd/flowRecord/submitFlow", body);
}

export async function trySubmitFlow(body: any) {
    return post("/itm-user/api/cmd/flowRecord/trySubmitFlow", body);
}

export async function custom(body: any) {
    return post("/itm-user/api/cmd/flowRecord/custom", body);
}

export async function recall(body: any) {
    return post("/itm-user/api/cmd/flowRecord/recall", body);
}

export async function postponed(body: any) {
    return post("/itm-user/api/cmd/flowRecord/postponed", body);
}

export async function transfer(body: any) {
    return post("/itm-user/api/cmd/flowRecord/transfer", body);
}

export async function urge(body: any) {
    return post("/itm-user/api/cmd/flowRecord/urge", body);
}

export async function submitReview(contractId: number, userIds: number[]) {
    return post("/itm-user/api/cmd/flowRecord/transfer", {contractId, userIds});
}

export async function submitDep(contractId: number, userId: number) {
    return post("/itm-user/api/cmd/flowRecord/urge", {contractId, userId});
}

// 待办中心控制

export async function flowRecordList(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/page",
        params,
        sort,
        filter,
        match
    );
}

export async function findAllByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findAllByOperatorId",
        params,
        sort,
        filter,
        match
    );
}

export async function findTodoByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findTodoByOperatorId",
        params,
        sort,
        filter,
        match
    );
}

export async function findDoneByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findDoneByOperatorId",
        params,
        sort,
        filter,
        match
    );
}

export async function findInitiatedByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findInitiatedByOperatorId",
        params,
        sort,
        filter,
        match
    );
}

export async function findTimeoutTodoByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findTimeoutTodoByOperatorId",
        params,
        sort,
        filter,
        match
    );
}

export async function findPostponedTodoByOperatorId(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return page(
        "/itm-user/api/query/flowRecord/findPostponedTodoByOperatorId",
        params,
        sort,
        filter,
        match
    );
}
