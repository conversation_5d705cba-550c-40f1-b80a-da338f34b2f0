import {get, page, post} from "@/api";


export async function queryNoticeTemplatePage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/itm-user/api/query/notice/template/page", params);
    return page('/itm-user/api/query/notice/template/page', params, sort, filter, match);
}

export async function queryNoticeTemplateList() {
    const response = await get('/itm-user/api/query/notice/template/list');
    if (response.success) {
        const list = response.data.total > 0 ? response.data.list : [];
        return {
            data: list,
            success: response.success,
            message: response.errMessage
        };
    } else {
        return {
            data: [],
            success: response.success,
            message: response.errMessage
        }
    }
}

export async function saveNoticeTemplate(body: any) {

    console.log("/itm-user/api/cmd/notice/template/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/notice/template/save', body);
}

export async function deleteNoticeTemplate(id: any) {

    console.log("/itm-user/api/cmd/notice/template/delete", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/notice/template/delete', {id: id});
}

export async function getNoticeRecordById(id: any) {
    return post('/itm-user/api/query/notice/record/detail', {id: id});
}

export async function testSendTodo() {
    return get('/itm-user/api/cmd/notice/record/testSendTodo');
}

export async function testSendNotice() {
    return get('/itm-user/api/cmd/notice/record/testSendNotice');
}