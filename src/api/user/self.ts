import {get, page} from "@/api";


export async function getSelfDetail() {
    return get('/itm-user/api/query/user/self/detail');
}

export async function getSelfRoles() {
    return get('/itm-user/api/query/user/self/roles');
}

export async function getSelfRoleCodes() {
    return get('/itm-user/api/query/user/self/roleCodes');
}

export async function getSelfMenus() {
    return get('/itm-user/api/query/user/self/menus');
}

export async function getSelfPermissions() {
    return get('/itm-user/api/query/user/self/permissions');
}

export async function getSelfTodoPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-user/api/query/user/self/todoPage', params, sort, filter, match);
}

export async function getSelfNoticePage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-user/api/query/user/self/noticePage', params, sort, filter, match);
}

export async function getSelfWarnPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-user/api/query/user/self/warnPage', params, sort, filter, match);
}

export async function getSelfNotDoneTodoCount() {
    return get('/itm-user/api/query/user/self/notDoneTodoCount');
}

export async function getSelfNotReadNoticeCount() {
    return get('/itm-user/api/query/user/self/notReadNoticeCount');
}

export async function getSelfNotReadWarnCount() {
    return get('/itm-user/api/query/user/self/notReadWarnCount');
}