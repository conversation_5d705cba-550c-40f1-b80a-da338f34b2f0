export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/api/query/sync/list", params);
    // return {
    //     data: [
    //         {
    //             id: 1,
    //             name: '测试部门1',
    //             code: 'test',
    //             type: "部门",
    //             unitId: "UNIT-2"
    //         },
    //         {
    //             id: 2,
    //             name: '测试部门2',
    //             code: 'test2',
    //             type: "部门",
    //             unitId: "UNIT-3"
    //         }
    //     ],
    //     success: true,
    //     total: 2
    // }

    return {
        data: [
            {
                id: "2",
                "syncTime": "2025-5-12 10:32:44",
                "syncResult": "成功",
                "syncDetail": {
                    "user": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "unit": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "dept": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "post": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "line": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                }
            },
            {
                id: "1",
                "syncTime": "2025-5-12 10:29:51",
                "syncResult": "成功",
                "syncDetail": {
                    "user": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "unit": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "dept": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "post": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                    "line": {
                        "add": 0,
                        "update": 5,
                        "delete": 0,
                    },
                }
            },
        ],
        success: true,
        total: 2
    };
}