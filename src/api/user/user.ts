import {get, page, post} from "@/api";

export async function users() {
    return get('/itm-user/api/query/user/list', {current: 1, pageSize: 999999});
}

export async function entrust(body: any) {
    return post('/itm-user/api/cmd/user/entrust', body);
}

export async function removeEntrust(id: any) {
    return post('/itm-user/api/cmd/user/removeEntrust', {id});
}


export async function changeManager(id: any) {
    return post('/itm-user/api/cmd/user/changeManager', {id});
}


export async function deleteUser(id: any) {
    return post('/itm-user/api/cmd/user/delete', {id});
}

export async function queryUserPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/api/cmd/user/page", params);
    // return {
    //     data: [
    //         {
    //             id: 1,
    //             name: '张三',
    //             username: '<PERSON><PERSON><PERSON>',
    //             userNo: "0001",
    //             unitId: "UNIT-2",
    //             deptId: "DEPT-1",
    //         },
    //         {
    //             id: 2,
    //             name: '李四',
    //             username: 'lisi',
    //             userNo: "0002",
    //             unitId: "UNIT-3",
    //             deptId: "DEPT-2",
    //
    //         }
    //     ],
    //     success: true,
    //     total: 2
    // }
    return page('/itm-user/api/query/user/page', params, sort, filter, match);
}

export async function saveUser(body: any) {

    console.log("/itm-user/api/cmd/user/save", body);
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/cmd/user/save', body);
}

export async function resetUserPassword(id: any) {
    console.log("/api/cmd/user/delete", {id: id});


    return post('/itm-user/api/cmd/user/resetPwd', {id: id});
}

export async function enableUser(id: any) {
    console.log("/api/cmd/user/enable", {id: id});


    return post('/itm-user/api/meta/user/enable', {id: id});
}

export async function disableUser(id: any) {
    console.log("/api/cmd/user/disable", {id: id});


    return post('/itm-user/api/meta/user/disable', {id: id});
}

export async function getUserTree() {
    return get('/itm-user/api/query/user/tree', {});
}

export async function findRoleIdsByUserId(id: any) {
    return get('/itm-user/api/query/user/roleIds', {id: id})
}

export async function saveUserRoleRelations(userId: any, roleIds: any[]) {
    return post('/itm-user/api/cmd/user/saveUserRoles', {userId: userId, roleIds: roleIds});
}

export async function getUserDetailsByIds(userIds: any[]) {
    return post('/itm-user/api/query/user/detailByIds', {ids: userIds});
}
