import {get, post} from "@/api";

export interface UserChoicePageQuery {
    current: number;
    pageSize: number;
    keyword?: any;
    orgNodeId?: any;
    customAttribute?: any;
}

export async function userChoiceQueryPage(
    param: UserChoicePageQuery,
    type: string = "common") {
    const response = await post('/itm-user/api/query/user/choice/' + type + "/page", param);
    if (response.success) {
        const list = response.data.total > 0 ? response.data.list : [];
        return {
            data: list,
            success: response.success,
            total: response.data.total
        };
    } else {
        return {
            data: [],
            success: response.success,
            total: 0
        }
    }
}

export async function userChoiceKeywordSearch(
    keyword?: string,
    type: string = "common") {
    const response = await get('/itm-user/api/query/user/choice/' + type + "/search", {keyword: keyword});
    if (response.success) {
        const list = response.data.total > 0 ? response.data.list : [];
        return {
            data: list,
            success: response.success,
            total: response.data.total
        };
    } else {
        return {
            data: [],
            success: response.success,
            total: 0
        }
    }
}


export async function userChoiceGetOrgTree(
    type?: string,
    orgType?: string
) {
    if (!type) {
        type = "common"
    }
    if (!orgType) {
        orgType = "dept"
    }
    return get('/itm-user/api/query/user/choice/' + type + "/" + orgType + "/tree");
}