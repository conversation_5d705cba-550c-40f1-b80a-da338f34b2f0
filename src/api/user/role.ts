import {get, page, post} from "@/api";

export type RoleGroupTreeData = {
    nodeId: string;
    nodeParentId?: string;
    name: string;
    children?: RoleGroupTreeData[]
}

export async function queryRolePage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    console.log("/itm-user/api/query/role/page", params);

    return page('/itm-user/api/query/role/page', params, sort, filter, match);
}

export async function queryRoleList() {
    return get('/itm-user/api/query/role/list');
}

export async function saveRole(body: any) {

    console.log("/itm-user/api/cmd/role/save", body);

    return post('/itm-user/api/cmd/role/save', body);
}

export async function deleteRole(id: any) {
    console.log("/itm-user/api/cmd/role/delete", {id: id});


    return post('/itm-user/api/cmd/role/delete', {id: id});
}

export async function enableRole(id: any) {
    console.log("/itm-user/api/meta/role/enable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/role/enable', {id: id});
}

export async function disableRole(id: any) {
    console.log("/itm-user/api/meta/role/disable", {id: id});
    // return {
    //     success: true,
    // }

    return post('/itm-user/api/meta/role/disable', {id: id});
}

export async function saveRoleUserRelations(roleId: any, userIds: any[]) {
    console.log("/itm-user/api/cmd/role/saveRoleUsers", {roleId: roleId, userIds: userIds});
    return post('/itm-user/api/cmd/role/saveRoleUsers', {roleId: roleId, userIds: userIds});
}

export async function saveRoleMenuRelations(roleId: any, menuIds: any[]) {
    console.log("/itm-user/api/cmd/role/saveRoleMenus", {roleId: roleId, menuIds: menuIds});
    return post('/itm-user/api/cmd/role/saveRoleMenus', {roleId: roleId, menuIds: menuIds});
}

export async function findMenuIdsByRoleId(id: any) {
    console.log("/itm-user/api/query/role/menuIds", {id: id});

    return get('/itm-user/api/query/role/menuIds', {id: id})
}

export async function findUsersByRoleId(id: any) {
    console.log("/itm-user/api/query/role/users", {id: id});

    return get('/itm-user/api/query/role/users', {id: id})
}