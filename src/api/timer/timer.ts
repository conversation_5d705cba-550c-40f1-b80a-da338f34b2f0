import {get, page, post} from "@/api";


export async function queryTimerConfigPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-timer/api/query/config/page', params, sort, filter, match);
}

export async function saveTimerConfig(body: any) {
    return post('/itm-timer/api/cmd/config/save', body);
}

export async function deleteTimerConfig(id: any) {
    return post('/itm-timer/api/cmd/config/delete', {id: id});
}

export async function enableTimerConfig(id: any) {
    return post('/itm-timer/api/cmd/config/enable', {id: id});
}

export async function disableTimerConfig(id: any) {
    return post('/itm-timer/api/cmd/config/disable', {id: id});
}

export async function queryTimerTaskRecordPage(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-timer/api/query/record/page', params, sort, filter, match);
}

export async function queryTimerTaskLogsByRecordId(recordId: any) {
    return get('/itm-timer/api/query/record/queryLogsByRecordId', {recordId: recordId});
}