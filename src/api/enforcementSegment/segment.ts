import {httpClient} from "@/api/index";

export async function getSegmentPage(params: any,sort: any,filter: any,match: {key: string,type: string}[]){
    return httpClient.page('itm-supervise/api/query/number-segment/page',params,sort,filter,match)
}
export async function getSegmentDetail(id: number){
    return httpClient.get('itm-supervise/api/query/number-segment/detail',id)
}

export async function del(params:any){
    return httpClient.post('itm-supervise/api/cmd/number-segment/delete',params)
}
export async function init(ids:number[]){
    return httpClient.post('itm-supervise/api/cmd/number-segment/delete',ids)
}
export async function save(params:any){
    return httpClient.post('itm-supervise/api/cmd/number-segment/save',params)

}
