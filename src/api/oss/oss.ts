import {page, post,get,download} from "@/api/index";

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-oss/api/query/file/list', params, sort, filter, match);
}

export async function verifyWatermark(body: any) {
    return post('/itm-oss/api/cmd/file/verifyWatermark', body);
}

export async function upload(formData: FormData) {
    return post("/itm-oss/api/cmd/file/upload", formData);
}

export async function loadFiles(ids: string) {
    return get('/itm-oss/api/query/file/load', {ids: ids});
}

export async function updateOssFile(formData: FormData) {
    return post("/itm-oss/api/cmd/file/upload", formData);
}

export async function generateFileAuth(params: any) {
    return get("/itm-oss/api/cmd/file/generateFileAuth", params);
}

export async function downloadFile(code: string,fileName: string) {
    return download("/itm-oss/open/file/download?code="+code,fileName);
}
