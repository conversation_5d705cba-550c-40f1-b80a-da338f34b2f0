import {httpClient} from "@/api";
import {Attachment} from "@/pages/contract/draft/types";

export async function show() {
    return httpClient.get("/itm-legal-review-contract/simpleWord/read");
}

export async function page(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string;
        type: string;
    }[]
) {
    return httpClient.page(
        "/itm-legal-review-contract/api/query/contract/page",
        params,
        sort,
        filter,
        match
    );
}

export async function saveContractData(params: any) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contract/save",
        params
    );
}

export async function queryContractById(id: number) {
    return httpClient.get(
        "/itm-legal-review-contract/api/query/contract/detail",
        {id}
    );
}

export async function getSerialNumber(id: any) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contract/createSerialNumber",
        {id: id}
    );
}

/**
 * @description 合同审批提交到领导
 */
export async function submitLeader(params: {
    contractId: number;
    recordId: number,
    userId: number;
    advice?: string;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/submitToLeader",
        params
    );
}

/**
 * @description 合同审批提交到领法规部门
 */
export async function submitGeneralLegal(params: {
    contractId: number;
    recordId: number,
    userId: number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/submitToLegalDept",
        params
    );
}

/**
 * @description 审批通过
 */
export async function approval(params: {
    contractId: number;
    recordId: number,
    userId: number;
    advice?: string;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/approved",
        params
    );
}

/**
 * @description 发放授权书
 * */

export async function submitAuthorize(params: {
    contractId: number;
    recordId: number,
    serialNumber: number;
    authorizedId: number;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/authorize",
        params
    );
}

/**
 * @description 合同签订
 * */

export async function submitSign(params: {
    contractId: string;
    recordId: number,
    startTime: string;
    endTime: string;
    attachments: Attachment[];
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/sign",
        params
    );
}

/**
 * @description 发起会审
 * */

export async function jointReview(params: {
    contractId: number;
    recordId?: number,
    userIds: number[] | string[];
    flag?: number
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/jointReview",
        params
    );
}

/**
 * @description 发往法律顾问
 * */

export async function expertReview(params: {
    contractId: number;
    recordId: number;
    processId: number;
    userId: string | number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/expertReview",
        params
    );
}

/**
 * @description 送呈部门负责人
 * */

export async function contractProcess(params: {
    contractId: number;
    recordId: number;
    processId: number;
    userId: string | number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/submitToDeptManagerWithJointReview",
        params
    );
}

/**
 * @description 会审审核通过
 * */

export async function approvedWithJointReview(params: {
    contractId: number;
    recordId: number;
    processId: number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/approvedWithJointReview",
        params
    );
}

/**
 * @description 送法规部
 * */

export async function regulatoryWithJointReview(params: {
    contractId: number;
    recordId: number;
    processId: number;
    userId: string | number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/submitToRegulatoryWithJointReview",
        params
    );
}

/**
 * @description 退回修改（主流程）
 * */

export async function sendBack(params: {
    contractId: number;
    recordId: number,
    processId: number;
    advice?: string;
    flag?: number;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/sendBack",
        params
    );
}

/**
 * @description 退回修改（子流程）
 * */

export async function sendBackWithJointReview(params: {
    contractId: number;
    advice?: string;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/sendBackWithJointReview",
        params
    );
}

/**
 * @description 撤回（通用）
 * */

export async function recall(params: {
    contractId: number;
    recordId: number,
    processId: number,
    advice?: string;
}) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractProcess/recall",
        params
    );
}

/*
* @description 保存履行计划
* */
export async function savePerformancePlan(params: any) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contractPerformancePlan/save",
        params
    )
}

/**
 * @description 保存合同到草稿
 * */
export async function saveToDraft(params: any) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contract/temporarySave",
        params
    )
}

/**
 * @description 当前有无起草阶段合同
 * */
export async function getCurrentContract() {
    return httpClient.get(
        "/itm-legal-review-contract/api/query/contract/current");
}

export async function save(params: any) {
    return httpClient.post(
        "/itm-legal-review-contract/api/cmd/contract/save",
        params
    );
}

export async function queryProcessById(id: number) {
    return httpClient.get(
        "/itm-legal-review-contract//api/query/contractProcess/detail",
        {id}
    );
}

