import {httpClient} from "@/api";

export async function page(
    current: number,
    pageSize: number,
    deptId: number,
    search: any,
) {
    return httpClient.get('/itm-legal-review-contract/api/query/powerOfAttorney/page',
        {current: current, pageSize: pageSize, deptId: deptId, search: search});
}

export async function save(body: any) {
    return httpClient.post('/itm-legal-review-contract/api/cmd/powerOfAttorney/save', body);
}

export async function del(body: any) {
    return httpClient.post('/itm-legal-review-contract/api/cmd/powerOfAttorney/delete', body);
}
