import {httpClient} from "@/api";

export async function saveContractAttachment(params: any) {
    return httpClient.post('/itm-legal-review-contract/api/cmd/contractAttachment/save', params);
}

export async function getContractContentHtml(id: number) {
    return httpClient.get('/itm-legal-review-contract/api/query/contractAttachment/showHTML', {contractId: id});
}

export async function transToHtml(id: number) {
    return httpClient.post('/itm-legal-review-contract/api/cmd/contractAttachment/transToHTML', {contractId:id});
}
