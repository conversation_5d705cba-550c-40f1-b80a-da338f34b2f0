/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 08:47:35
 * @Description:
 * @LastEditTime: 2025-07-21 09:20:23
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { httpClient } from "@/api";

export async function loadPerformancePlanList(contractId: number) {
  return httpClient.get(
    "/itm-legal-review-contract/api/query/contractPerformancePlan/list",
    { contractId: contractId }
  );
}

export async function savePerformancePlan(params: any) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractPerformancePlan/save",
    params
  );
}

export async function saveOnePlan(params: any) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractPerformancePlan/save",
    params
  );
}

export async function delPlan(id: number) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractPerformancePlan/del",
    { id: id }
  );
}

export async function saveRecords(body: any) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractPerformanceRecord/save",
    body
  );
}

export async function loadPerformanceRecordList(contractId: number) {
  return httpClient.get(
    "/itm-legal-review-contract/api/query/contractPerformanceRecord/list",
    { contractId: contractId }
  );
}

export async function loadContractAcceptanceList(contractId: number) {
  return httpClient.get(
    "/itm-legal-review-contract/api/query/contractAcceptance/list",
    { contractId: contractId }
  );
}

export async function saveContractAcceptance(body: any) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractAcceptance/save",
    body
  );
}
