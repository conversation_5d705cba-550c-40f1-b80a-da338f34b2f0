/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-10 15:11:33
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { get, post, httpClient } from "@/api";

export async function submitFlow(params: any) {
  return httpClient.post(
    "/itm-legal-review-contract/api/cmd/contractProcess/submitFlow",
    params
  );
}

export async function detail(params: { workCode?: string; id?: number }) {
  return get("/itm-user/api/query/flowRecord/detail", params);
}

/**
 * @description 送呈部门负责人接口
 * */
export async function submitDepGeneralTerm(params: {
  recordId?: number;
  contractId?: number;
  userId?: number;
  flag?: number;
}) {
  return post(
    "/itm-legal-review-contract/api/cmd/contractProcess/submitToDeptManagerFromDraft",
    params
  );
}

/**
 * @description 合同正文转html
 * */

export const contractToHtml = async (contractId: number) => {
  return post(
    "itm-legal-review-contract/api/cmd/contractAttachment/transToHTML",
    { contractId }
  );
};
