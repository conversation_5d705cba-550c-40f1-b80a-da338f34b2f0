import {httpClient} from "@/api";

export async function authorizeList() {
    return httpClient.get('/itm-legal-review-contract/api/query/powerOfAttorney/list');
}

export async function contractOpinions(params: any) {
    return httpClient.get('/itm-legal-review-contract/api/cmd/contract/save', params);
}



export async function authorizeDetail(params: any) {
    return httpClient.get('/itm-legal-review-contract/api/query/powerOfAttorney/detail', params);
}

export async function saveContract(params: any) {
    return httpClient.post('/itm-legal-review-contract/api/cmd/contract/save', params);
}