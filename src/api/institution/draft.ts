import {post, get} from "@/api";

export async function addOrUpdateInstitutionDraft(body: any) {
    return post("/itm-institution-draft/api/cmd/draft/addOrUpdate", body);
}

export async function queryDraftInstitutionDetail(params: any) {
    return get("/itm-institution-draft/api/query/draft/detail", params);
}

export async function showHtml(params: any) {
    return get("/itm-institution-draft/api/query/draft/showHtml", params);
}

export async function opinionStartFlow(body: any) {
    return post("/itm-institution-draft/api/cmd/opinion/startFlow", body);
}

export async function opinionSubmitFlow(body: any) {
    return post("/itm-institution-draft/api/cmd/opinion/submitFlow", body);
}

export async function opinionRecallFlow(body: any) {
    return post("/itm-institution-draft/api/cmd/opinion/recallFlow", body);
}

export async function addOrUpdateItem(body: any) {
    return post("/itm-institution-draft/api/cmd/opinion/addOrUpdateItem", body);
}

export async function queryOpinionItemList(params: any) {
    return get("/itm-institution-draft/api/query/opinion/opinionItemList", params);
}

export async function addOpinionAttachment(body: any) {
    return post("/itm-institution-draft/api/cmd/opinion/addAttachment", body);
}
