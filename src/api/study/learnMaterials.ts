import {httpClient, post} from "@/api/index";


export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-study/api/query/study/learnMaterials/list', params, sort, filter, match);
}

export async function getInfo(params: any) {
    return httpClient.get("/itm-study/api/query/study/learnMaterials/getCategoryInfo", params);
}
export async function addOrUpdate(formData: FormData) {
    return httpClient.post("/itm-study/api/cmd/study/learnMaterials/addOrUpdate", formData);
}

export async function del(body: { id: number, }) {
    return httpClient.post('/itm-study/api/cmd/study/learnMaterials/delete', body);
}

export async function enable(id: any) {
    return post('/itm-study/api/meta/learnMaterials/enable', {id: id});
}

export async function disable(id: any) {
    return post('/itm-study/api/meta/learnMaterials/disable', {id: id});
}

export async function getCategoryList() {
    return httpClient.get("/itm-study/api/query/study/category/getCategoryList");
}

