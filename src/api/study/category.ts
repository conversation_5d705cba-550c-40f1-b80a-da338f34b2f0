import {httpClient, post} from "@/api/index";


export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-study/api/query/study/category/list', params, sort, filter, match);
}

export async function getInfo(params: any) {
    return httpClient.get("/itm-study/api/query/study/category/getCategoryInfo", params);
}
export async function addOrUpdate(formData: FormData) {
    return httpClient.post("/itm-study/api/cmd/study/category/addOrUpdate", formData);
}

export async function del(body: { id: number, }) {
    return httpClient.post('/itm-study/api/cmd/study/category/delete', body);
}

export async function enable(id: any) {
    return post('/itm-study/api/meta/category/enable', {id: id});
}

export async function disable(id: any) {
    return post('/itm-study/api/meta/category/disable', {id: id});
}

