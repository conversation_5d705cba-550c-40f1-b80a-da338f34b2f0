import {httpClient} from "@/api";

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-dict/api/query/dictValue/page', params, sort, filter, match);
}

export async function valueTree() {
    return httpClient.get('/itm-dict/api/query/dictValue/tree');
}

export async function valueTreeByLabelCode(labelCode: string) {
    return httpClient.get('/itm-dict/api/query/dictValue/treeByLabel', {labelCode: labelCode});
}

export async function getDictValue(code: string) {
    return httpClient.get('/itm-dict/api/query/dictValue/detail', {code: code});
}

export async function create(body: any) {
    return httpClient.post('/itm-dict/api/cmd/dictValue/save', body);
}

export async function update(body: any) {
    return httpClient.post('/itm-dict/api/cmd/dictValue/save', body);
}

export async function del(body: {
    id: number,
}) {
    return httpClient.post('/itm-dict/api/cmd/dictValue/delete', body);
}