import {httpClient} from "@/api";

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-dict/api/query/dictLabel/page', params, sort, filter, match);
}

export async function labelTree() {
    return httpClient.get('/itm-dict/api/query/dictLabel/tree');
}

export async function getDictLabel(code: string) {
    return httpClient.get('/itm-dict/api/query/dictLabel/detail', {code: code});
}

export async function create(body: any) {
    return httpClient.post('/itm-dict/api/cmd/dictLabel/save', body);
}

export async function update(body: any) {
    return httpClient.post('/itm-dict/api/cmd/dictLabel/save', body);
}

export async function del(body: {
    id: number,
}) {
    return httpClient.post('/itm-dict/api/cmd/dictLabel/delete', body);
}