import {get, page, post} from "@/api";

export async function getInstitutionDetail(id:any) {
    return get(`/itm-abolish/api/query/institutionAssess/institution/detail?id=${id}`);
}

export async function save(data: any) {
    return post('/itm-abolish/api/cmd/institutionAssess/save', data);
}

export async function getAssessInfoDetail(id:any) {
    return get(`/itm-abolish/api/query/institutionAssess/assess/byInstitutionId?institutionId=${id}`);
}

export async function getAssessInfoDetailById(id:any) {
    return get(`/itm-abolish/api/query/institutionAssess/assess/byId?id=${id}`);
}

export async function submitFlow(data: any) {
    return post('/itm-abolish/api/cmd/institutionAssess/assessFlow', data);
}

export async function recallFlow(data: any) {
    return post('/itm-abolish/api/cmd/institutionAssess/recallAssessFlow', data);
}

export async function saveExamineInfo(data: any) {
    return post('/itm-abolish/api/cmd/institutionAssess/saveExamine', data);
}

export async function getAssessExamineInfoByRecordId(recordId:any,id:any) {
    return get(`/itm-abolish/api/query/institutionAssess/assess/examineInfo?recordId=${recordId}&assessId=${id}`);
}
export async function countRecordIdStatus(recordId:any) {
    return get(`/itm-abolish/api/query/institutionAbolish/flow/recordId/status?recordId=${recordId}`);
}
export async function getAssessExamineInfos(id:any) {
    return get(`/itm-abolish/api/query/institutionAssess/assess/examine/infos?assessId=${id}`);
}
//*********************************************************************/
export async function getMyAssessInstitutionList() {
    return get('/itm-abolish/api/query/institutionAssess/myAssess');
}

export async function getAlreadyAssessInstitutionList(current:any,pageSize:any) {
    return get(`/itm-abolish/api/query/institutionAssess/alreadyAssess?current=${current}&pageSize=${pageSize}`);
}

export async function getCanAssessInstitutionList(params:any) {
    return get('/itm-abolish/api/query/institutionAssess/canAssess',params);
}