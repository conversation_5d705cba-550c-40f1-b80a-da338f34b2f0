import {page, post, get} from "@/api/index";

export async function outerList(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-collect/api/query/collect/outer/list', params, sort, filter, match);
}

export async function interList(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return page('/itm-collect/api/query/collect/inter/list', params, sort, filter, match);
}

export async function uploadInterInstitutionFile(body: any) {
    return post("/itm-collect/api/cmd/collect/inter/upload", body);
}

export async function refreshOuterInstitution() {
    return get('/itm-collect/api/cmd/collect/outer/refresh');
}

export async function outerInstitutionStatus() {
    return get("/itm-collect/api/cmd/collect/outer/refreshStatus");
}

export async function savePublishFormData(body: any) {
    return post("/itm-collect/api/cmd/collect/inter/setInstitutionInfo", body);
}

export async function getInstitutionDetail(id: number) {
    return get('/itm-collect/api/query/collect/inter/getInstitutionDetail', {id: id});
}

export async function deleteInterInstitution(formData: { id: number; }) {
    return post('/itm-collect/api/cmd/collect/inter/delete', formData);
}

export async function institutionReview(body: any) {
    return post('/itm-collect/api/cmd/collect/inter/institutionReview', body);
}

export async function passInstitutionReview(body: any) {
    return post('/itm-collect/api/cmd/collect/inter/passInstitutionReview', body);
}

export async function rejectedInstitutionReview(body: any) {
    return post('/itm-collect/api/cmd/collect/inter/rejectedInstitutionReview', body);
}