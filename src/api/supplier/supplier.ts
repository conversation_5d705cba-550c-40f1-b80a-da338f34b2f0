import {httpClient} from "@/api/index";
import {download} from "@/utils/download";

export async function getBankDepositList() {
    return httpClient.get("/itm-supplier/api/query/supplier/getBankDepositList");
}

export async function getEnterpriseTypeList() {
    return httpClient.get("/itm-supplier/api/query/supplier/getEnterpriseTypeList");
}

export async function getTypeList() {
    return httpClient.get("/itm-supplier/api/query/supplier/getTypeList");
}

export async function getCategoryList() {
    return httpClient.get("/itm-supplier/api/query/supplier/getCategoryList");
}

export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-supplier/api/query/supplier/list', params, sort, filter, match);
}

export async function exportSupplierList(params: any) {
    return download("/itm-supplier/api/query/supplier/exportSupplierList", params, "供应商.xlsx");
}

export async function getSupplierInfo(params: any) {
    return httpClient.get("/itm-supplier/api/query/supplier/getSupplierInfo", params);
}


export async function generateFileAuth(params: any) {
    return httpClient.get("/itm-archive-oss/api/cmd/file/generateFileAuth", params);
}

export async function addOrUpdateSupplier(formData: FormData) {
    return httpClient.post("/itm-supplier/api/cmd/supplier/addOrUpdate", formData);
}

export async function del(body: { id: number, }) {
    return httpClient.post('/itm-supplier/api/cmd/supplier/delete', body);
}