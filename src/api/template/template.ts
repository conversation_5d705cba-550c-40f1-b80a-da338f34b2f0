import {httpClient} from "@/api/index";

export async function labelTree() {
    return httpClient.get('/itm-template/api/query/template/getCategoryTree?labelCode=tLe6Oat7zt20');
}
export async function list(
    params: any,
    sort: any,
    filter: any,
    match: {
        key: string,
        type: string
    }[]
) {
    return httpClient.page('/itm-template/api/query/template/list', params, sort, filter, match);
}

export async function uploadTemplateFile(formData: FormData) {
    return httpClient.post("/itm-template/api/cmd/template/upload", formData);
}

export async function publish(body: { id: number, }) {
    return httpClient.post('/itm-template/api/cmd/template/publish', body);
}
export async function del(body: { id: number, }) {
    return httpClient.post('/itm-template/api/cmd/template/delete', body);
}