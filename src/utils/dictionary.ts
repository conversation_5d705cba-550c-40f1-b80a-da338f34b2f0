
const dictionary: any = {
    '异常类型': [
        { value: '', label: '全部' },
        { value: '41', label: '黑名单' },
        { value: '1', label: 'TAC码校验不通过' },
        { value: '3', label: '时间出现问题' },
        { value: '21', label: '逾期提交拒付' },
        { value: '2', label: '收费金额超出限制' },
        { value: '4', label: '交易记录id不符合规范' },
        { value: '5', label: '找不到该公司下的停车场的通道' },
        { value: '6', label: '交易金额不正确' },
        { value: '7', label: '通道类型不正确' },
        { value: '8', label: '通道密钥不正确' },
        { value: '11', label: '车牌号与obu内车牌号不一致' },
        { value: '12', label: '车牌颜色与obu内车牌颜色不一致' },
        { value: '13', label: '车牌颜色不存在' },
        { value: '14', label: '卡已过期' },
        { value: '15', label: 'obu已过期' },
        { value: '16', label: 'obu发行方不符合规范' },
        { value: '17', label: '发行省份不符合规范' },
        { value: '18', label: '卡网络号不正确' },
        { value: '22', label: '主键已存在' },
        { value: '31', label: '参数校验失败' },
        { value: '32', label: '非法车型' },
        { value: '1001', label: '连接TAC服务器失败' },
        { value: '23', label: 'PSAM卡号错误' },
        { value: '24', label: '加密卡交易序号错误' },
        { value: '51', label: '未知发行方' },
        { value: '52', label: '发行方不匹配' },
        { value: '53', label: '优惠金额异常' },
        { value: '25', label: '路侧重复扣费' },
    ],
    categoryOptions: [
        { label: '普法先进', value: '普法先进' },
        { label: '其他类别', value: '其他类别' },
        { label: '先进个人', value: '先进个人' },
        { label: '其他分类', value: '其他分类' },
        { label: '草稿', value: '草稿' },
        { label: '已提交', value: '已提交' },
        { label: '审核中', value: '审核中' },
        { label: '已发布', value: '已发布' },
    ],
    carAbnormal:[
        { value: '11', label: '车牌号与obu内车牌号不一致' },
        { value: '12', label: '车牌颜色与obu内车牌颜色不一致' },
        { value: '13', label: '车牌颜色不存在' },
        { value: '14', label: '卡已过期' },
        { value: '15', label: 'obu已过期' },
        { value: '16', label: 'obu发行方不符合规范' },
        { value: '17', label: '发行省份不符合规范' },
        { value: '18', label: '卡网络号不正确' },
        { value: '22', label: '主键已存在' },
        { value: '31', label: '参数校验失败' },
        { value: '32', label: '非法车型' },
        { value: '1001', label: '连接TAC服务器失败' },
        { value: '23', label: 'PSAM卡号错误' },
        { value: '24', label: '加密卡交易序号错误' },
        { value: '51', label: '未知发行方' },
        { value: '52', label: '发行方不匹配' },
        { value: '53', label: '优惠金额异常' },
        { value: '25', label: '路侧重复扣费' },
    ]
}
// 返回对应的数组
export const getDictionary = (target: string) => {

    let data = dictionary[target] ? dictionary[target] : []
    return data
}



// 定义
export const getDictionaryTag = (target: string, value: any) => {

    typeof value === 'number' ? value = value.toString() : value
    let tag = value ? value : '-'
    let data = dictionary[target] ? dictionary[target] : []
    let filter = data?.filter((item: any) => (value && item.value == value))
    if (filter?.length > 0) {
        tag = filter[0].label
    }

    return tag
}
