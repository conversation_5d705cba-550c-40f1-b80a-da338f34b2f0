import {message} from "antd";
import {generateFileAuth, downloadFile} from '@/api/oss/oss';
import {generateFileAuthFromArchive, downloadFileFromArchive} from '@/api/archive/archive';

/**
 * 通用文件下载方法
 * @param url 下载地址
 * @param params GET 请求参数（可选）
 * @param filename 下载的文件名（可选，默认从 Content-Disposition 或 接口获取）
 */
export const download = async (
    url: string,
    params?: Record<string, any>,
    filename?: string
) => {
    try {
        // 处理参数拼接
        const queryString = params
            ? `?${new URLSearchParams(params).toString()}`
            : "";

        const fullUrl = `${url}${queryString}`;
        const token = localStorage.getItem("token");
        const headers: Record<string, string> = {};
        if (token) {
            headers["Authorization"] = `${token}`;
        }
        // 发起请求
        const response = await fetch(fullUrl, {
            method: "GET",
            headers: headers,
        });

        if (!response.ok) {
            throw new Error(`下载失败，状态码：${response.status}`);
        }

        // 获取 blob 数据和文件名
        const blob = await response.blob();

        // 从 Content-Disposition 提取文件名（如果有的话）
        const disposition = response.headers.get("Content-Disposition");
        if (disposition && !filename) {
            const utf8FilenameRegex =
                /filename\*=UTF-8''([\w%\-\.]+)/i;
            const asciiFilenameRegex =
                /filename="?([^"]+)"?/i;

            let utf8Matches = disposition.match(utf8FilenameRegex);
            if (utf8Matches && utf8Matches[1]) {
                filename = decodeURIComponent(utf8Matches[1]);
            } else {
                const asciiMatches = disposition.match(asciiFilenameRegex);
                if (asciiMatches && asciiMatches[1]) {
                    filename = asciiMatches[1];
                }
            }
        }

        // 创建下载链接并触发下载
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = downloadUrl;
        a.download = filename || "file.xlsx";
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(downloadUrl);

        message.success("下载成功");
    } catch (error: any) {
        console.error("文件下载异常", error);
        message.error(error.message || "文件下载失败");
    }
};

export const downloadOssFile = async (fileKey: string) => {
    try {
        const params = {
            fileKey: fileKey
        }
        const fileAuth = await generateFileAuth(params);
        const code = fileAuth.data?.code;
        const fileName = fileAuth.data?.name;

        await downloadFile(code, fileName);
    } catch (error) {
        console.error('下载失败:', error);
    }
};

export const downloadArchiveFile = async (fileKey: string) => {
    try {
        const params = {
            fileKey: fileKey
        }
        const fileAuth = await generateFileAuthFromArchive(params);
        const code = fileAuth.data?.code;
        const fileName = fileAuth.data?.name;

        await downloadFileFromArchive(code, fileName);
    } catch (error) {
        console.error('下载失败:', error);
    }
};
