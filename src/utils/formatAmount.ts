export const formatAmount = (value: number | string | null | undefined): string => {
    if (value === null || value === undefined || isNaN(Number(value))) {
        return "0.000000";
    }

    const numberValue = Number(value);

    // 使用 toFixed(6) 保证 6 位小数，再使用 toLocaleString 加上千分位
    const fixedValue = numberValue.toFixed(6);
    const [integerPart, decimalPart] = fixedValue.split(".");

    return `${Number(integerPart).toLocaleString()}.${decimalPart}`;
};