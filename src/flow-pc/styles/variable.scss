// 主题颜色
$theme-primary-color: var(--primary-color,#4a79d8);
// 背景颜色
$body-background-color: var(--body-background-color,#e6e7ea);

// 标题字体大小
$title-font-size: var(--content-font-size-middle,16px);
// 内容字体大小
$content-font-size:var(--content-font-size,12px);


/* 流程的背景色 */
$flow-background-color: var(--flow-background-color,white);
/* 流程的边框颜色 */
$flow-box-shadow-color: var(--flow-box-shadow-color,rgb(228, 224, 219));

/* 抄送节点的颜色 */
$flow-circulate-color: var(--flow-circulate-color,#faad14);
/* 流程节点的颜色 */
$flow-node-color: var(--flow-node-color,#1677ff);
/* 结束节点的颜色 */
$flow-over-color: var(--flow-over-color,#f5222d);
/* 开始节点的颜色 */
$flow-start-color: var(--flow-start-color,#52c41a);
