export class FlowFromTriggerContext{

    private readonly map:Map<String,()=>Promise<boolean>>;

    private constructor() {
        this.map = new Map<string,()=>Promise<boolean>>();
    }

    private static instance:FlowFromTriggerContext = new FlowFromTriggerContext();

    public static getInstance(){
        return FlowFromTriggerContext.instance;
    }

    public register(key:string,trigger:()=>Promise<boolean>){
        this.map.set(key,trigger);
    }

    public getTrigger(key:string){
        return this.map.get(key);
    }
}
