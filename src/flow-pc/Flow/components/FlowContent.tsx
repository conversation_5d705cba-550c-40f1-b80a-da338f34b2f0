/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-04 11:47:03
 * @Description:
 * @LastEditTime: 2025-07-04 15:27:36
 * @LastEditors: wangjiantao
 */
import React, { useContext, useEffect } from "react";
import { ComponentBus, FlowFormViewProps } from "@/ui-framework";
import { FlowViewReactContext } from "../view";
import { useSelector } from "react-redux";
import { FlowReduxState } from "../store";
import { Tabs, TabsProps } from "antd";
import {
  FlowViewChartPropsKey,
  FlowViewOpinionPropsKey,
  FlowViewRecordPropsKey,
} from "@/ui-framework";
import DefaultFlowViewOpinionView from "../plugins/DefaultFlowViewOpinionView";
import DefaultFlowViewRecordView from "../plugins/DefaultFlowViewRecordView";
import DefaultFlowViewChartView from "../plugins/DefaultFlowViewChartView";

export const FlowContent = () => {
  const flowViewReactContext = useContext(FlowViewReactContext);

  const flowRecordContext = flowViewReactContext?.flowRecordContext;
  const formInstance = flowViewReactContext?.formInstance;

  const FlowFormView =
    flowRecordContext?.getFlowFormView() as React.ComponentType<FlowFormViewProps>;

  const formParams = flowRecordContext?.getFlowFormParams();

  const opinionVisible = useSelector(
    (state: FlowReduxState) => state.flow.opinionVisible
  );
  const dataVersion = useSelector(
    (state: FlowReduxState) => state.flow.dataVersion
  );
  const contentHiddenVisible = useSelector(
    (state: FlowReduxState) => state.flow.contentHiddenVisible
  );
  const [currentTab, setCurrentTab] = React.useState("detail");

  const FlowViewOpinionView = ComponentBus.getInstance().getComponent(
    FlowViewOpinionPropsKey,
    DefaultFlowViewOpinionView
  );
  const FlowViewRecordView = ComponentBus.getInstance().getComponent(
    FlowViewRecordPropsKey,
    DefaultFlowViewRecordView
  );
  const FlowViewChartView = ComponentBus.getInstance().getComponent(
    FlowViewChartPropsKey,
    DefaultFlowViewChartView
  );

  useEffect(() => {
    if (!flowRecordContext?.isEditable()) {
      setTimeout(() => {
        formInstance?.disableAll();
      }, 100);
    }
  }, []);

  const style = contentHiddenVisible ? { display: "none" } : {};

  const items = [
    {
      label: "流程详情",
      key: "detail",
    },
    {
      label: "流程记录",
      key: "record",
    },
    {
      label: "流程图",
      key: "chart",
    },
  ] as TabsProps["items"];

  return (
    <div className={"flow-view-content"} style={style}>
      <Tabs
        items={items}
        activeKey={currentTab}
        onChange={(value) => {
          setCurrentTab(value);
        }}
      />

      {currentTab === "detail" && (
        <div style={{height: "calc(100vh - 130px)", overflow: "auto"}}>
          {formInstance && (
              <FlowFormView
                  data={formParams}
                  form={formInstance}
                  dataVersion={dataVersion}
              />
          )}

          {/* 合同起草没有审批意见 */}
          {opinionVisible &&
            FlowViewOpinionView &&
            flowViewReactContext?.flowRecordContext.getWorkCode() !==
              "contract_draft" && <FlowViewOpinionView />}
        </div>
      )}

      {currentTab === "record" && FlowViewRecordView && <FlowViewRecordView />}

      {currentTab === "chart" && FlowViewChartView && <FlowViewChartView />}
    </div>
  );
};
