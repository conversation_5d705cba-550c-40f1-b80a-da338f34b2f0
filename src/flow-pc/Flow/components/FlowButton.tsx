import React, {useContext} from "react";
import {FlowViewReactContext} from "../view";
import {useSelector} from "react-redux";
import {FlowReduxState} from "../store";
import {Button, Popconfirm} from "@/components-pc";
import { FlowFromTriggerContext } from "./FlowFromTriggerContext";

interface FlowHeaderProps{
    setVisible:(visible:boolean)=>void;
}

export const FlowButton:React.FC<FlowHeaderProps> = (props) => {
    const flowViewReactContext = useContext(FlowViewReactContext);

    const flowRecordContext = flowViewReactContext?.flowRecordContext;

    const flowButtonClickContext = flowViewReactContext?.flowButtonClickContext;

    const buttons = flowRecordContext?.getFlowButtons()||[];
    const requestLoading = useSelector((state: FlowReduxState) => state.flow.requestLoading);
    const contentHiddenVisible = useSelector((state: FlowReduxState) => state.flow.contentHiddenVisible);

    const style = contentHiddenVisible ? {"display":"none"} : {};

    if(flowRecordContext?.isWithdraw()){
        return (
            <div className={"flow-buttons-content"} style={style}>

                <Popconfirm
                    title={"确认要撤回流程吗？"}
                    onConfirm={()=>{
                        flowButtonClickContext?.handlerRecall();
                    }}
                >
                    <Button
                        color={"default"}
                        className={"flow-buttons-item"}
                    >
                        撤回
                    </Button>
                </Popconfirm>

                <Button
                    color={"default"}
                    className={"flow-buttons-item"}
                    onClick={() => {
                        props.setVisible(false);
                    }}
                >
                    关闭
                </Button>
            </div>
        )
    }

    if(flowRecordContext?.isEditable()){
        return (
            <div className={"flow-buttons-content"} style={style}>
                {buttons.map((item:any) => {
                    const style = item.style && JSON.parse(item.style) || {};
                    return (
                        <Button
                            loading={requestLoading}
                            key={item.id}
                            className={"flow-buttons-item"}
                            style={{
                                ...style
                            }}
                            onClick={async () => {
                                const trigger =  FlowFromTriggerContext.getInstance().getTrigger(flowRecordContext?.getNodeCode());
                                if(trigger){
                                    const state = await trigger();
                                    if(state){
                                        flowButtonClickContext?.handlerClick(item);
                                    }
                                    return;
                                }
                                flowButtonClickContext?.handlerClick(item);
                            }}
                        >{item.name}</Button>
                    )
                })}

                <Button
                    color={"default"}
                    className={"flow-buttons-item"}
                    onClick={() => {
                        props.setVisible(false);
                    }}
                >
                    关闭
                </Button>

            </div>
        )
    }else {
        return (
            <div className={"flow-buttons-content"} style={style}>
                <Button
                    loading={requestLoading}
                    style={{
                        marginLeft:'15%',
                        marginRight:'15%'
                    }}
                    className={"flow-buttons-item"}
                    onClick={() => {
                        props.setVisible(false);
                    }}
                >关闭</Button>
            </div>
        )
    }
}

