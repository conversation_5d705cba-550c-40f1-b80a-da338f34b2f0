import {HtmlNode, HtmlNodeModel} from '@logicflow/core';
import React from "react";
import ReactDOM from "react-dom/client";
import {OverSettingPanel} from "../panel";
import {NodeState} from "@/ui-framework";
import NodeCard from "@/flow-pc/Flow/nodes/NodeCard";

type OverProperties = {
    id: string;
    name: string;
    code: string;
    type: string;
    view: string;
    operatorMatcher: string;
    editable: boolean;
    titleGenerator: string;
    errTrigger: string;
    approvalType: string;
    timeout: number;
    settingVisible?: boolean;
    state?: NodeState;
    records?: any[];
}

interface OverProps {
    name: string;
    code?: string;
    update?: (values: any) => void;
    settingVisible?: boolean;
    properties?: OverProperties;
}

export const OverView: React.FC<OverProps> = (props) => {
    const [visible, setVisible] = React.useState(false);

    const state = props.properties?.state;

    return (
        <div>
            <NodeCard
                type={"over-node"}
                status={state ?? "wait"}
                title={props.name}
                editable={props.settingVisible}
                onClick={() => setVisible(true)}
            />
            <OverSettingPanel
                visible={visible}
                setVisible={setVisible}
                properties={props.properties}
                onSettingChange={(values: any) => {
                    props.update && props.update(values);
                }}
            />
        </div>
    );
}

class OverModel extends HtmlNodeModel {

    setAttributes() {
        const name = this.properties.name as string;
        this.width = 240;
        this.height = 80;
        this.text.editable = false;
        this.menu = [];

        this.sourceRules = [
            {
                message: `不允许输出`,
                validate: (sourceNode, targetNode: any, sourceAnchor, targetAnchor) => {
                    const edges = this.graphModel.getNodeIncomingEdge(targetNode.id);
                    if (edges.length >= 0) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
        ];

        this.anchorsOffset = [
            // [this.width / 2, 0],
            // [0, this.height / 2],
            // [-this.width / 2, 0],
            [0, -this.height / 2],
        ];
    }


}

class OverNode extends HtmlNode {
    setHtml(rootEl: SVGForeignObjectElement) {
        const {properties} = this.props.model as HtmlNodeModel<OverProperties>;
        const div = document.createElement('div');

        const settingVisible = properties.settingVisible !== false;

        ReactDOM.createRoot(div).render(
            <OverView
                name={properties.name}
                code={properties.code}
                properties={properties}
                settingVisible={settingVisible}
                update={async (values) => {
                    this.props.model.setProperties(values);
                }}/>,
        );
        //需要清空
        rootEl.innerHTML = '';
        rootEl.appendChild(div);
        super.setHtml(rootEl);
    }
}

export const OverNodeConfig = {
    type: 'over-node',
    view: OverNode,
    model: OverModel,
};

