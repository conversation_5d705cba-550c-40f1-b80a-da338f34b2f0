import {HtmlNode, HtmlNodeModel} from "@logicflow/core";
import React from "react";
import ReactDOM from "react-dom/client";
import {NodeSettingPanel} from "../panel";
import {NodeState} from "@/ui-framework";
import NodeCard from "@/flow-pc/Flow/nodes/NodeCard";
import {FlowOperator} from "@/flow-pc/Flow/nodes/type";

type NodeProperties = {
    id: string;
    name: string;
    code: string;
    type: string;
    view: string;
    operatorMatcher: string;
    editable: boolean;
    titleGenerator: string;
    errTrigger: string;
    approvalType: string;
    timeout: number;
    settingVisible?: boolean;
    state?: NodeState;
    records?: any[];
    operators?: FlowOperator[];
};

interface NodeProps {
    name: string;
    operators?: FlowOperator[];
    update?: (values: any) => void;
    settingVisible?: boolean;
    properties?: NodeProperties;
    state?: NodeState;
}

export const NodeView: React.FC<NodeProps> = (props) => {
    const [visible, setVisible] = React.useState(false);

    const state = props.properties?.state;

    return (
        <div>
            <NodeCard
                type={"node-node"}
                status={state ?? "wait"}
                title={props.name}
                operators={props.operators}
                editable={props.settingVisible}
                onClick={() => setVisible(true)}
            />

            <NodeSettingPanel
                visible={visible}
                setVisible={setVisible}
                properties={props.properties}
                onSettingChange={(values: any) => {
                    props.update && props.update(values);
                }}
            />
        </div>
    );
};

class NodeModel extends HtmlNodeModel {
    setAttributes() {
        const name = this.properties.name as string;
        let operatorCount = 0;
        if (this.properties.operaters) {
            operatorCount = (this.properties.operaters as FlowOperator[]).length;
        }

        this.width = 240;
        this.height = 100 + (name.length > 15 ? name.length / 15 * 24 : 0) + operatorCount * 20;
        this.text.editable = false;
        this.menu = [];

        this.anchorsOffset = [
            [this.width / 2, 0],
            [0, this.height / 2],
            [-this.width / 2, 0],
            [0, -this.height / 2],
        ];
    }
}

class NodeNode extends HtmlNode {
    setHtml(rootEl: SVGForeignObjectElement) {
        const {properties} = this.props.model as HtmlNodeModel<NodeProperties>;
        console.log(this.props)
        const div = document.createElement("div");

        const settingVisible = properties.settingVisible !== false;

        ReactDOM.createRoot(div).render(
            <NodeView
                name={properties.name}
                operators={properties.operators}
                properties={properties}
                settingVisible={settingVisible}
                update={async (values) => {
                    this.props.model.setProperties(values);
                }}
            />
        );
        //需要清空
        rootEl.innerHTML = "";
        rootEl.appendChild(div);
        super.setHtml(rootEl);
    }
}

export const NodeNodeConfig = {
    type: "node-node",
    view: NodeNode,
    model: NodeModel,
};
