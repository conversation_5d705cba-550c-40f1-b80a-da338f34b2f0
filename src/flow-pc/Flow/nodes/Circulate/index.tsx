import {HtmlNode, HtmlNodeModel} from '@logicflow/core';
import React from "react";
import ReactDOM from "react-dom/client";
import {CirculateSettingPanel} from "../panel";
import {NodeState} from "@/ui-framework";
import NodeCard from "@/flow-pc/Flow/nodes/NodeCard";
import {FlowOperator} from "@/flow-pc/Flow/nodes/type";

type CirculateProperties = {
    id: string;
    name: string;
    code: string;
    type: string;
    view: string;
    operatorMatcher: string;
    editable: boolean;
    titleGenerator: string;
    errTrigger: string;
    approvalType: string;
    timeout: number;
    settingVisible?: boolean;
    state?: NodeState;
    records?: any[];
    operators?: FlowOperator[];
}

interface CirculateProps {
    name: string;
    code?: string;
    update?: (values: any) => void;
    settingVisible?: boolean;
    properties?: CirculateProperties;
    operators?: FlowOperator[];
}

export const CirculateView: React.FC<CirculateProps> = (props) => {
    const [visible, setVisible] = React.useState(false);

    const state = props.properties?.state;

    return (
        <div>
            <NodeCard
                type={"circulate-node"}
                status={state ?? "wait"}
                title={props.name}
                operators={props.operators}
                editable={props.settingVisible}
                onClick={() => setVisible(true)}
            />

            <CirculateSettingPanel
                visible={visible}
                setVisible={setVisible}
                properties={props.properties}
                onSettingChange={(values: any) => {
                    props.update && props.update(values);
                }}
            />
        </div>
    );
}

class CirculateModel extends HtmlNodeModel {
    setAttributes() {
        const name = this.properties.name as string;
        this.width = 240;
        this.height = 100;
        this.text.editable = false;
        this.menu = [];

        this.sourceRules = [
            {
                message: `不允许输出`,
                validate: (sourceNode: any, targetNode: any, sourceAnchor, targetAnchor) => {
                    const edges = this.graphModel.getNodeOutgoingEdge(sourceNode.id);
                    if (edges.length >= 1) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
        ];

        this.anchorsOffset = [
            [this.width / 2, 0],
            [0, this.height / 2],
            [-this.width / 2, 0],
            [0, -this.height / 2],
        ];
    }


}

class CirculateNode extends HtmlNode {
    setHtml(rootEl: SVGForeignObjectElement) {
        const {properties} = this.props.model as HtmlNodeModel<CirculateProperties>;
        const div = document.createElement('div');
        const settingVisible = properties.settingVisible !== false;
        ReactDOM.createRoot(div).render(
            <CirculateView
                name={properties.name}
                code={properties.code}
                operators={properties.operators}
                properties={properties}
                settingVisible={settingVisible}
                update={async (values) => {
                    this.props.model.setProperties(values);
                }}/>,
        );
        //需要清空
        rootEl.innerHTML = '';
        rootEl.appendChild(div);
        super.setHtml(rootEl);
    }
}

export const CirculateNodeConfig = {
    type: 'circulate-node',
    view: CirculateNode,
    model: CirculateModel,
};

