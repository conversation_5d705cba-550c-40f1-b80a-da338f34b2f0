import {HtmlNode, HtmlNodeModel} from '@logicflow/core';
import React from "react";
import ReactDOM from "react-dom/client";
import {StartSettingPanel} from "../panel";
import {NodeState} from "@/ui-framework";
import NodeCard from "@/flow-pc/Flow/nodes/NodeCard";
import {FlowOperator} from "@/flow-pc/Flow/nodes/type";

type StartProperties = {
    id: string;
    name: string;
    code: string;
    type: string;
    view: string;
    operatorMatcher: string;
    editable: boolean;
    titleGenerator: string;
    errTrigger: string;
    approvalType: string;
    timeout: number;
    settingVisible?: boolean;
    state?: NodeState;
    records?: any[];
    startUser?: FlowOperator;
}

interface StartProps {
    name: string;
    settingVisible?: boolean;
    update?: (values: any) => void;
    properties?: StartProperties;
    startUser?: FlowOperator;
}

export const StartView: React.FC<StartProps> = (props) => {
    const [visible, setVisible] = React.useState(false);

    const state = props.properties?.state;

    return (
        <div>
            <NodeCard
                type={"start-node"}
                status={state ?? "wait"}
                title={props.name}
                operators={props.startUser? [props.startUser]:[]}
                editable={props.settingVisible}
                onClick={() => setVisible(true)}
            />
            <StartSettingPanel
                visible={visible}
                setVisible={setVisible}
                properties={props.properties}
                onSettingChange={(values) => {
                    props.update && props.update(values);
                }}
            />
        </div>
    );
}

class StartModel extends HtmlNodeModel {
    setAttributes() {
        const name = this.properties.name as string;
        this.width = 240;
        this.height = 100;
        this.text.editable = false;
        this.menu = [];

        this.anchorsOffset = [
            // [this.width / 2, 0],
            [0, this.height / 2],
            // [-this.width / 2, 0],
            // [0, -this.height / 2],
        ];
    }
}


class StartNode extends HtmlNode {

    setHtml(rootEl: SVGForeignObjectElement) {
        const {properties} = this.props.model as HtmlNodeModel<StartProperties>;
        const div = document.createElement('div');
        const settingVisible = properties.settingVisible !== false;


        ReactDOM.createRoot(div).render(
            <StartView
                name={properties.name}
                properties={properties}
                startUser={properties.startUser}
                settingVisible={settingVisible}
                update={async (values) => {
                    this.props.model.setProperties(values);
                }}/>
        );
        //需要清空
        rootEl.innerHTML = '';
        rootEl.appendChild(div);
        super.setHtml(rootEl);
    }
}

export const StartNodeConfig = {
    type: 'start-node',
    view: StartNode,
    model: StartModel,
};

