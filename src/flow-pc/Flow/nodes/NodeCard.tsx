import React from 'react';
import {Card, ConfigProvider, Typography} from 'antd';
import {
    BellOutlined,
    CheckSquareOutlined,
    PlayCircleOutlined,
    SettingFilled,
    StopOutlined,
    UserOutlined
} from '@ant-design/icons';
import './NodeCard.scss';
import {NodeType} from "@/ui-framework";
import {NodeState} from "@/ui-framework";
import {StateTag} from "@/flow-pc";
import {FlowOperator} from "@/flow-pc/Flow/nodes/type";

const {Text} = Typography;

export type ClickArea = "all" | "icon";

// 节点属性接口
interface NodeCardProps {
    type: NodeType;
    status: NodeState;
    operators?: FlowOperator[];
    approvalComment?: string;

    title?: string;
    editable?: boolean;
    onClick?: () => void;
    clickArea?: ClickArea;
}

// 获取节点类型图标
const getNodeTypeIcon = (type: NodeType) => {
    switch (type) {
        case 'start-node':
            return <PlayCircleOutlined className="start-icon node-type-icon"/>;
        case 'node-node':
            return <CheckSquareOutlined className="audit-icon node-type-icon"/>;
        case 'circulate-node':
            return <BellOutlined className="copy-icon node-type-icon"/>;
        case 'over-node':
            return <StopOutlined className="end-icon node-type-icon"/>;
    }
};


const NodeCard: React.FC<NodeCardProps> = ({
                                               type,
                                               status,
                                               operators = [],
                                               approvalComment,
                                               onClick,
                                               clickArea = "icon",
                                               title,
                                               editable = true
                                           }) => {

    // 构建卡片类名
    const cardClasses = `node-card ${type} status-${status} ${editable ? 'editable' : ''} ${clickArea === 'all' ?'cursor-pointer':''}`;

    // 定义卡片内容
    const cardContent = (
        <div className="node-card-content">
            {/* 顶部图标行 - 类型图标和编辑图标 */}
            <div className="node-card-icon-row">
                {getNodeTypeIcon(type)}
                {editable && (
                    <SettingFilled className="node-type-icon edit-icon" onClick={onClick}/>
                )}
            </div>

            {/* 标题和状态行 */}
            <div className="node-card-title-row">
                <Text strong className="node-card-title">
                    {title || '未命名节点'}
                </Text>

                <StateTag state={status}/>
            </div>

            {/* 办理人信息，结束节点没有办理人 */}
            {type !== 'over-node' && operators && operators.length > 0 && (
                <div className="node-operators">
                    <UserOutlined className="assignee-icon"/>
                    {operators.map((operator, index) => (
                        <span
                            key={index}
                            className={`assignee-item ${operator.completed ? 'completed' : 'pending'}`}
                        >
                 {operator.name}
                            {index < operators.length - 1 && ', '}
               </span>
                    ))}
                </div>
            )}
        </div>
    );

    // // 如果有审批意见且状态为已执行，则添加Popover
    // if (approvalComment && status === 'completed') {
    //     return (
    //         <Popover
    //             content={
    //                 <div className="p-2">
    //                     <Text strong>审批意见：</Text>
    //                     <Text>{approvalComment}</Text>
    //                 </div>
    //             }
    //             placement="top"
    //             trigger="hover"
    //         >
    //             <Card
    //                 className={cardClasses}
    //                 onClick={onClick}
    //             >
    //                 {cardContent}
    //             </Card>
    //         </Popover>
    //     );
    // }

    return (
        <ConfigProvider
            theme={{
                components: {
                    Card: {
                        bodyPadding: 8
                    },
                },
            }}>
            <Card
                className={cardClasses}
                onClick={() => {
                    clickArea === 'all' && onClick && onClick()
                }}
            >
                {cardContent}
            </Card>
        </ConfigProvider>
    );
};

export default NodeCard;
