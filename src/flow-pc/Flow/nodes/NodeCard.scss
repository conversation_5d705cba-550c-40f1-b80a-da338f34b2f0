@use "../../styles/variable" as *;
/* NodeCard.scss */
.node-card {
  border-radius: 8px;
  border-width: 2px;
  width: 240px;
  padding: 6px;

  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  /*&:hover {*/
  /*    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);*/
  /*}*/

  /* 节点类型边框颜色 */
  /* 办理类型 */
  .start-node {
    min-height: 100px;
    border-color: $flow-start-color; /* 绿色 - 发起 */
  }

  .node-node {
    min-height: 100px;
    border-color: $flow-node-color; /* 蓝色 - 审批 */
  }

  .circulate-node {
    min-height: 100px;
    border-color: $flow-circulate-color; /* 黄色 - 抄送 */
  }

  .over-node {
    min-height: 80px;
    border-color: $flow-over-color; /* 红色 - 结束 */
  }

  /* 节点状态背景色 */

  &.status-done {
    background-color: #f4ffb8;

    .node-card-title {
      color: #006400;
    }
  }

  &.status-current {
    background-color: #fff1b8;

    .node-card-title {
      color: #804000;
    }
  }

  &.status-wait {
    background-color: #f5f5f5;

    .node-card-title {
      color: #434343;
    }
  }

  /* 可编辑状态 */

  &.editable {

    &:hover {
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.5), 0 0 8px rgba(24, 144, 255, 0.3);
      transition: box-shadow 0.2s ease;
    }
  }

  &.cursor-pointer {
    cursor: pointer;
  }
}

/* 卡片内容容器 */
.node-card-content {
  width: 100%;
}

/* 顶部图标行 */
.node-card-icon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 类型图标 */
.node-type-icon {
  font-size: 12px;
}

.start-icon {
  color: $flow-start-color; /* 绿色 - 发起 */
}

.audit-icon {
  color: $flow-node-color; /* 蓝色 - 审批 */
}

.copy-icon {
  color: $flow-circulate-color; /* 黄色 - 抄送 */
}

.end-icon {
  color: $flow-over-color; /* 红色 - 结束 */
}

/* 编辑图标 */
.edit-icon {
  font-size: 12px;
  color: $flow-node-color;
}

/* 标题和状态行 */
.node-card-title-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-content: space-between;
  gap: 4px;
  margin-bottom: 4px;
}

/* 标题文本 */
.node-card-title {
  font-weight: bold;
  font-size: 14px;
  flex-grow: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 状态标签 */
.node-status-tag {
  padding: 0 4px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

/* 办理人信息 */
.node-operators {
  display: flex;
  align-items: flex-start;
  margin-top: 4px;
  flex-wrap: wrap;
  font-size: 12px;
}

/* 办理人图标 */
.assignee-icon {
  color: #8c8c8c;
  margin-right: 4px;
  font-size: 12px;
  margin-top: 2px;
}

/* 办理人列表容器 */
.assignee-list {
  font-size: 12px;
  max-width: 120px;
}

/* 单个办理人项 */
.assignee-item {
  display: inline;
}

/* 已办理状态 */
.assignee-item.completed {
  color: #52c41a; /* 绿色表示已办理 */
}

/* 未办理状态 */
.assignee-item.pending {
  color: #595959;
}

