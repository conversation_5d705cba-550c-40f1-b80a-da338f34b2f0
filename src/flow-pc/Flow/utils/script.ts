export class GroovyScript {

    public static defaultOutTrigger = "def run(content) {return true;}";
    public static defaultTitleGenerator = "def run(content){ return content.getCurrentOperator().getName() + '-' + content.getFlowWork().getTitle() + '-' + content.getFlowNode().getName();}";
    public static defaultErrTrigger = "";

    public static anyOperatorMatcher = "def run(content) {return [content.getCurrentOperator().getUserId()];}";
    public static creatorOperatorMatcher = "def run(content) {return [content.getCreateOperator().getUserId()];}";
    // 当操作人设置为自定义，用户打开选人弹窗选择人员后，使用这种方式记录选的人
    public static specifyOperatorMatcher = "def run(content) {\n" +
        "  return [%s];\n" +
        "}";
    // 当操作人设置为自定义时，默认使用节点选人方式
    public static byUserMatcher = "// by user [%s] 首行注释不要删\n" +
        "def run(content) {\n" +
        "  return content.getBindData().findNodeOperators(content.getFlowNode().getCode());\n" +
        "}";

    public static byRoleMatcher = "// by role [%s] 首行注释不要删\n" +
        "def run(content) {\n" +
        "  return content.getBindData().findNodeOperators(content.getFlowNode().getCode());\n" +
        "}";

    public static byDeptMatcher = "// by dept [%s] 首行注释不要删\n" +
        "def run(content) {\n" +
        "  return content.getBindData().findNodeOperators(content.getFlowNode().getCode());\n" +
        "}";

    public static byUnitMatcher = "// by unit [%s] 首行注释不要删\n" +
        "def run(content) {\n" +
        "  return content.getBindData().findNodeOperators(content.getFlowNode().getCode());\n" +
        "}";


    static operatorMatcherType(operatorMatcher: string) {
        if (operatorMatcher === GroovyScript.anyOperatorMatcher) {
            return "any";
        } else if (operatorMatcher === GroovyScript.creatorOperatorMatcher) {
            return "creator";
        } else if (operatorMatcher.startsWith("// by role")) {
            return "role";
        } else if (operatorMatcher.startsWith("// by dept")) {
            return "dept";
        } else if (operatorMatcher.startsWith("// by unit")) {
            return "unit";
        } else {
            return "custom";
        }
    }


    static getFormatNumValues(script: string) {
        if (script) {
            const match = script.match(/\[([\d,\s]+)\]/);
            if (match && match[1]) {
                return match[1]
                    .split(',')
                    .map(num => parseInt(num.trim(), 10));
            }
        }
        return [];
    }

    static getFormatStrValues(script: string) {
        if (script) {
            const match = script.match(/\[([^\]]+)\]/);
            if (match && match[1]) {
                return match[1].split(',');
            }
        }
        return [];
    }

    static errTriggerType(errTrigger: string) {
        if (errTrigger === GroovyScript.defaultErrTrigger) {
            return "default";
        } else {
            return "custom";
        }
    }

    static titleGeneratorType = (titleGenerator: string) => {
        if (titleGenerator === GroovyScript.defaultTitleGenerator) {
            return "default";
        } else {
            return "custom";
        }
    }

    static operatorMatcher(operatorMatcherType: string) {
        if (operatorMatcherType === "any") {
            return GroovyScript.anyOperatorMatcher;
        } else if (operatorMatcherType === "creator") {
            return GroovyScript.creatorOperatorMatcher;
        } else if (operatorMatcherType === "role") {
            return GroovyScript.byRoleMatcher;
        } else if (operatorMatcherType === "dept") {
            return GroovyScript.byDeptMatcher;
        } else if (operatorMatcherType === "unit") {
            return GroovyScript.byUnitMatcher;
        } else {
            return GroovyScript.byUserMatcher;
        }
    }
}

