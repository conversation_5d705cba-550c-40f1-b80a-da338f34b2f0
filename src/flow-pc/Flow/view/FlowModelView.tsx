import {FlowViewProps} from "@/ui-framework";
import React from "react";
import {Modal} from "@/components-pc";
import {FlowView} from "./index";
import "./FlowModelView.scss";

 export const FlowModelView:React.FC<FlowViewProps> = (props)=>{
    return (
        <Modal
            className={"flow-modal"}
            open={props.visible}
            onCancel={()=>{
                props.setVisible(false);
            }}
            onOk={()=>{
                props.setVisible(false);
            }}
            destroyOnClose={true}
            footer={false}
            closable={false}
        >
            <FlowView
                {...props}
            />
        </Modal>
    )
}

