@use "../../styles/variable" as *;


.flow-skeleton-header {
  width: 100%;
  height: 40px;
}

.flow-skeleton-body {
  height: 60vh;
  width: 100%;
}

.flow-view {
  background-color: white !important;
  width: 100%;
}


.flow-chart {
  transition: 0.3s;
  border-radius: 12px;
  text-align: center;

  .flow-chart-content {
    width: 100%;
    height: 75vh;
  }

  .flow-img {
    max-width: 90vw;
    max-height: 70vh;
    width: auto !important;

  }
}


.flow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .flow-header-left {
    font-size: $title-font-size;
  }

  .flow-header-right {

  }

}

.flow-buttons-content {
  display: flex;
  justify-content: center;

  .flow-buttons-item {
    font-size: 14px;
    margin: 5px 5px;
    padding-left: 20px;
    padding-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
