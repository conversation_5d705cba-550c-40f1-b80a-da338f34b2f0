/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-04 15:29:08
 * @Description: 
 * @LastEditTime: 2025-07-07 09:29:13
 * @LastEditors: wangjiantao
 */
import React, {useContext, useEffect} from "react";
import '@logicflow/core/es/index.css';
import '@logicflow/extension/lib/style/index.css';
import {LogicFlow, Options} from "@logicflow/core";
import {DndPanel, Menu, MiniMap, Snapshot} from "@logicflow/extension";
import {FlowViewReactContext} from "../../view";
import {StartNodeConfig} from "../../nodes";
import {OverNodeConfig} from "../../nodes";
import {CirculateNodeConfig} from "../../nodes";
import {NodeNodeConfig} from "../../nodes";
import "../../index.scss";

import EdgeType = Options.EdgeType;
import {FlowImage} from "@/components/flowImage";

interface FlowChartProps {
    edgeType?: EdgeType;
}

export const FlowChart: React.FC<FlowChartProps> = (props) => {

    const flowViewReactContext = useContext(FlowViewReactContext);
    const flowRecordContext = flowViewReactContext?.flowRecordContext;

    return (
        <FlowImage edgeType={props.edgeType}
                   workCode={flowRecordContext?.getWorkCode()}
                   recordId={flowRecordContext?.getCurrentFlowRecord()?.id} />
    )
}

