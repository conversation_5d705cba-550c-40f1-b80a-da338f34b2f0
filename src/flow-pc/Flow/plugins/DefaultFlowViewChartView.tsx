import React from "react";
import {FlowHistoryLine} from "./views/FlowHistoryLine";
import {FlowChart} from "./views/FlowChart";
import {Divider} from "@/components-pc";
import {FlowViewChartProps} from "@/ui-framework";

const DefaultFlowViewRecordView: React.FC<FlowViewChartProps> = () => {

    return (
        <div style={{overflowX: "auto", height: "calc(100vh - 130px)"}}>
            <FlowChart/>
            <Divider>流转历史</Divider>
            <FlowHistoryLine/>
        </div>
    )
}

export default DefaultFlowViewRecordView;
