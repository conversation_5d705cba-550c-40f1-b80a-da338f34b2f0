:root {
  --primary-color: #094edc;
  --body-background-color: #fdfdfd;

  --content-font-size-large: 24px;
  --content-font-size-middle: 14px;
  --content-font-size-small: 12px;

  --content-font-size: var(--content-font-size-middle);
  width: 100%;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--body-background-color);
  font-size: var(--content-font-size);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

#root {
  overflow: auto;
  height: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
  height: 8px; /* 水平滚动条高度 */
}

/* 滚动条轨道（背景） */
::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道背景色 */
  border-radius: 4px; /* 圆角 */
}

/* 滚动条滑块（可拖动部分） */
::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滑块颜色 */
  border-radius: 4px; /* 圆角 */
}

/* 滚动条滑块悬停效果 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 悬停时颜色 */
}

/* 滚动条两端按钮（可选） */
::-webkit-scrollbar-button {
  display: none; /* 隐藏滚动条两端的按钮 */
}
