export const exhibit: string = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>软件开发流程与详解</title>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <style>
    /* 基础样式 */
    body {
      font-family: 'Inter', system-ui, sans-serif;
      background-color: #f8fafc;
      color: #1e293b;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 3rem 1.5rem;
    }
    
    .text-center {
      text-align: center;
    }
    
    .mb-12 {
      margin-bottom: 3rem;
    }
    
    .text-3xl {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
    
    .text-4xl {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
    
    .font-bold {
      font-weight: 700;
    }
    
    .text-gray-600 {
      color: #64748b;
    }
    
    .max-w-2xl {
      max-width: 42rem;
    }
    
    .mx-auto {
      margin-left: auto;
      margin-right: auto;
    }
    
    /* 主要内容区样式 */
    .main-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    
    @media (min-width: 768px) {
      .main-content {
        flex-direction: row;
      }
    }
    
    /* 左侧tab栏样式 */
    .tab-container {
      width: 100%;
    }
    
    @media (min-width: 768px) {
      .tab-container {
        width: 30%;
      }
    }
    
    .tab {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      overflow: hidden;
    }
    
    .tab-item {
      padding: 1.25rem;
      cursor: pointer;
      border-bottom: 1px solid #e2e8f0;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
    }
    
    .tab-item:last-child {
      border-bottom: none;
    }
    
    .tab-item:hover {
      background-color: #f1f5f9;
    }
    
    .tab-item.active {
      background-color: #3b82f6;
      color: white;
    }
    
    .tab-icon {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
    }
    
    .tab-item.active .tab-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    /* 右侧内容区样式 */
    .content-container {
      width: 100%;
    }
    
    @media (min-width: 768px) {
      .content-container {
        width: 70%;
      }
    }
    
    .content-item {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      padding: 2rem;
      display: none;
      animation: fadeIn 0.5s ease;
    }
    
    .content-item.active {
      display: block;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .content-image {
      width: 100%;
      height: auto;
      border-radius: 0.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .content-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }
    
    .content-description {
      line-height: 1.6;
    }
    
    .content-list {
      margin-top: 1.5rem;
      list-style-type: none;
      padding-left: 0;
    }
    
    .content-list li {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
    }
    
    .content-list li i {
      margin-right: 0.75rem;
      color: #3b82f6;
    }
    
    /* 颜色定义 */
    .bg-primary { background-color: #3b82f6; }
    .bg-secondary { background-color: #10b981; }
    .bg-accent { background-color: #8b5cf6; }
    .bg-warning { background-color: #f59e0b; }
    .bg-danger { background-color: #ef4444; }
    .bg-gray { background-color: #94a3b8; }
    
    .text-primary { color: #3b82f6; }
    .text-secondary { color: #10b981; }
    .text-accent { color: #8b5cf6; }
    .text-warning { color: #f59e0b; }
    .text-danger { color: #ef4444; }
    .text-gray { color: #94a3b8; }
  </style>
</head>
<body>
  <div class="container">
    <div class="text-center mb-12">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">软件开发流程详解</h1>
      <p class="text-gray-600 max-w-2xl mx-auto">探索软件开发的各个阶段，从需求分析到维护，了解每个阶段的关键任务和交付成果</p>
    </div>
    
    <!-- 主要内容区 -->
    <div class="main-content">
      <!-- 左侧tab栏 -->
      <div class="tab-container">
        <div class="tab">
          <div class="tab-item active" data-tab="tab1">
            <div class="tab-icon bg-primary">
              <i class="fa fa-list-alt text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">需求分析</h3>
              <p class="text-sm text-gray-600">理解和定义项目需求</p>
            </div>
          </div>
          
          <div class="tab-item" data-tab="tab2">
            <div class="tab-icon bg-secondary">
              <i class="fa fa-paint-brush text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">设计阶段</h3>
              <p class="text-sm text-gray-600">架构和UI/UX设计</p>
            </div>
          </div>
          
          <div class="tab-item" data-tab="tab3">
            <div class="tab-icon bg-accent">
              <i class="fa fa-code text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">开发阶段</h3>
              <p class="text-sm text-gray-600">编码实现与单元测试</p>
            </div>
          </div>
          
          <div class="tab-item" data-tab="tab4">
            <div class="tab-icon bg-warning">
              <i class="fa fa-bug text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">测试阶段</h3>
              <p class="text-sm text-gray-600">质量保证与缺陷修复</p>
            </div>
          </div>
          
          <div class="tab-item" data-tab="tab5">
            <div class="tab-icon bg-primary">
              <i class="fa fa-rocket text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">部署阶段</h3>
              <p class="text-sm text-gray-600">系统上线与发布</p>
            </div>
          </div>
          
          <div class="tab-item" data-tab="tab6">
            <div class="tab-icon bg-secondary">
              <i class="fa fa-cogs text-white"></i>
            </div>
            <div>
              <h3 class="font-bold">维护阶段</h3>
              <p class="text-sm text-gray-600">持续支持与优化</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容区 -->
      <div class="content-container">
        <div id="tab1" class="content-item active">
          <img src="https://picsum.photos/id/180/800/450" alt="需求分析阶段" class="content-image">
          <h2 class="content-title">需求分析</h2>
          <p class="content-description">
            需求分析是软件开发的第一阶段，主要目标是理解客户需求并将其转化为详细的需求规格说明书。这个阶段是整个项目成功的基础，需要与客户进行充分沟通，明确项目目标、功能需求和非功能需求。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 与客户沟通，收集需求</li>
            <li><i class="fa fa-check-circle"></i> 分析和记录功能需求</li>
            <li><i class="fa fa-check-circle"></i> 定义项目范围和边界</li>
            <li><i class="fa fa-check-circle"></i> 进行可行性研究</li>
            <li><i class="fa fa-check-circle"></i> 创建需求规格说明书</li>
            <li><i class="fa fa-check-circle"></i> 与客户确认需求</li>
          </ul>
        </div>
        
        <div id="tab2" class="content-item">
          <img src="https://picsum.photos/id/119/800/450" alt="设计阶段" class="content-image">
          <h2 class="content-title">设计阶段</h2>
          <p class="content-description">
            设计阶段是将需求转化为具体实现方案的过程，包括系统架构设计、数据库设计和UI/UX设计。这个阶段的输出将作为开发阶段的蓝图，直接影响软件的质量和可维护性。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 系统架构设计</li>
            <li><i class="fa fa-check-circle"></i> 数据库设计与建模</li>
            <li><i class="fa fa-check-circle"></i> UI/UX设计与原型制作</li>
            <li><i class="fa fa-check-circle"></i> 技术选型与评估</li>
            <li><i class="fa fa-check-circle"></i> 制定开发标准和规范</li>
            <li><i class="fa fa-check-circle"></i> 创建详细设计文档</li>
          </ul>
        </div>
        
        <div id="tab3" class="content-item">
          <img src="https://picsum.photos/id/1/800/450" alt="开发阶段" class="content-image">
          <h2 class="content-title">开发阶段</h2>
          <p class="content-description">
            开发阶段是根据设计文档进行编码实现的过程，开发团队按照预定的技术栈和编码规范进行开发，同时进行单元测试以确保代码质量。这个阶段需要严格遵循项目计划和代码管理规范。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 前端界面开发</li>
            <li><i class="fa fa-check-circle"></i> 后端服务开发</li>
            <li><i class="fa fa-check-circle"></i> 数据库实现与优化</li>
            <li><i class="fa fa-check-circle"></i> API开发与集成</li>
            <li><i class="fa fa-check-circle"></i> 单元测试与代码审查</li>
            <li><i class="fa fa-check-circle"></i> 持续集成与部署</li>
          </ul>
        </div>
        
        <div id="tab4" class="content-item">
          <img src="https://picsum.photos/id/1005/800/450" alt="测试阶段" class="content-image">
          <h2 class="content-title">测试阶段</h2>
          <p class="content-description">
            测试阶段是确保软件质量的关键阶段，通过各种测试方法发现并修复软件中的缺陷。测试内容包括功能测试、性能测试、安全测试、兼容性测试等，确保软件满足需求并能够稳定运行。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 功能测试与验证</li>
            <li><i class="fa fa-check-circle"></i> 性能测试与优化</li>
            <li><i class="fa fa-check-circle"></i> 安全测试与漏洞修复</li>
            <li><i class="fa fa-check-circle"></i> 用户体验测试</li>
            <li><i class="fa fa-check-circle"></i> 兼容性测试</li>
            <li><i class="fa fa-check-circle"></i> 缺陷跟踪与修复</li>
          </ul>
        </div>
        
        <div id="tab5" class="content-item">
          <img src="https://picsum.photos/id/1015/800/450" alt="部署阶段" class="content-image">
          <h2 class="content-title">部署阶段</h2>
          <p class="content-description">
            部署阶段是将开发和测试完成的软件部署到生产环境的过程，包括环境配置、数据迁移、系统上线等工作。这个阶段需要精心计划和执行，确保系统平稳过渡到生产环境。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 生产环境配置</li>
            <li><i class="fa fa-check-circle"></i> 数据迁移与验证</li>
            <li><i class="fa fa-check-circle"></i> 系统部署与上线</li>
            <li><i class="fa fa-check-circle"></i> 用户培训与文档交付</li>
            <li><i class="fa fa-check-circle"></i> 监控系统设置</li>
            <li><i class="fa fa-check-circle"></i> 应急响应计划制定</li>
          </ul>
        </div>
        
        <div id="tab6" class="content-item">
          <img src="https://picsum.photos/id/1025/800/450" alt="维护阶段" class="content-image">
          <h2 class="content-title">维护阶段</h2>
          <p class="content-description">
            维护阶段是软件交付后的持续支持过程，包括监控系统运行状态、修复问题、进行功能更新和性能优化等。这个阶段是软件生命周期中最长的阶段，需要建立有效的维护流程和客户反馈机制。
          </p>
          <ul class="content-list">
            <li><i class="fa fa-check-circle"></i> 系统监控与性能优化</li>
            <li><i class="fa fa-check-circle"></i> 问题修复与紧急响应</li>
            <li><i class="fa fa-check-circle"></i> 功能更新与增强</li>
            <li><i class="fa fa-check-circle"></i> 安全补丁与合规性</li>
            <li><i class="fa fa-check-circle"></i> 用户反馈收集与分析</li>
            <li><i class="fa fa-check-circle"></i> 技术栈升级与重构</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Tab切换功能
    document.addEventListener('DOMContentLoaded', function() {
      const tabItems = document.querySelectorAll('.tab-item');
      
      tabItems.forEach(item => {
        item.addEventListener('click', function() {
          // 移除所有tab的active状态
          tabItems.forEach(tab => {
            tab.classList.remove('active');
          });
          
          // 添加当前tab的active状态
          this.classList.add('active');
          
          // 获取当前tab的数据属性
          const tabId = this.getAttribute('data-tab');
          
          // 隐藏所有内容
          const contentItems = document.querySelectorAll('.content-item');
          contentItems.forEach(content => {
            content.classList.remove('active');
          });
          
          // 显示当前内容
          const currentContent = document.getElementById(tabId);
          currentContent.classList.add('active');
        });
      });
    });
  </script>
</body>
</html>
    
`;

export const shoppDemo = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商后台管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 基础样式 */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F5F7FA;
            color: #606266;
            margin: 0;
            padding: 0;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        button {
            border: none;
            background: none;
            cursor: pointer;
        }
        
        /* 工具类 */
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .btn-primary {
            background-color: #165DFF;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #0040C9;
        }
        
        .btn-secondary {
            background-color: #FF7D00;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background-color: #E06D00;
        }
        
        .btn-outline {
            border: 1px solid #165DFF;
            color: #165DFF;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-outline:hover {
            background-color: #165DFF;
            color: white;
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
        }
        
        .data-table th,
        .data-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .data-table th {
            font-weight: 600;
            color: #333;
            background-color: #F9FAFB;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.05em;
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: #F9FAFB;
            transition: background-color 0.2s ease;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        .status-active {
            background-color: #DCFCE7;
            color: #15803D;
        }
        
        .status-pending {
            background-color: #FEF3C7;
            color: #92400E;
        }
        
        .status-canceled {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
        }
        
        .pagination .page-info {
            font-size: 0.875rem;
            color: #6B7280;
        }
        
        .pagination .page-controls {
            display: flex;
            gap: 0.25rem;
        }
        
        .pagination .page-btn {
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            border: 1px solid #E5E7EB;
            background-color: white;
            color: #6B7280;
            transition: all 0.2s ease;
        }
        
        .pagination .page-btn:hover {
            background-color: #F9FAFB;
        }
        
        .pagination .page-btn.active {
            background-color: #165DFF;
            color: white;
            border-color: #165DFF;
        }
        
        /* 卡片样式 */
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 1rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .card-body {
            padding: 1rem;
        }
        
        /* 搜索和筛选 */
        .search-filter {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .search-input {
            flex: 1;
            position: relative;
        }
        
        .search-input input {
            width: 100%;
            padding: 0.5rem 1rem;
            padding-left: 2.5rem;
            border-radius: 0.375rem;
            border: 1px solid #E5E7EB;
            outline: none;
            transition: border-color 0.2s ease;
        }
        
        .search-input input:focus {
            border-color: #165DFF;
        }
        
        .search-input i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
        }
        
        /* 导航栏 */
        .main-header {
            background-color: white;
            position: sticky;
            top: 0;
            z-index: 50;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 4rem;
            padding: 0 1rem;
        }
        
        .brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: #165DFF;
        }
        
        .nav-links {
            display: flex;
            gap: 1.5rem;
        }
        
        .nav-link {
            color: #6B7280;
            font-weight: 500;
            transition: color 0.2s ease;
        }
        
        .nav-link:hover {
            color: #165DFF;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 9999px;
            background-color: #165DFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background-color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .sidebar.open {
            transform: translateX(0);
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .sidebar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: #165DFF;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-item {
            padding: 0.75rem 1rem;
            color: #6B7280;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: background-color 0.2s ease;
        }
        
        .sidebar-item:hover {
            background-color: #F9FAFB;
        }
        
        .sidebar-item.active {
            background-color: #E8F0FF;
            color: #165DFF;
        }
        
        /* 移动端菜单按钮 */
        .menu-toggle {
            display: none;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: #F9FAFB;
            color: #6B7280;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .menu-toggle {
                display: block;
            }
            
            .sidebar {
                display: block;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        @media (min-width: 769px) {
            .sidebar {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 240px;
            }
            
            .menu-toggle {
                display: none;
            }
        }
        
        /* 内容区域 */
        .main-content {
            padding: 1.5rem;
        }
        
        .page-header {
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            font-size: 0.875rem;
            color: #6B7280;
        }
        
        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .stats-card {
            background-color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stats-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .stats-icon-primary {
            background-color: #165DFF;
        }
        
        .stats-icon-secondary {
            background-color: #FF7D00;
        }
        
        .stats-icon-success {
            background-color: #00B42A;
        }
        
        .stats-icon-warning {
            background-color: #FF7D00;
        }
        
        .stats-value {
            font-size: 1.75rem;
            font-weight: 600;
            color: #333;
        }
        
        .stats-label {
            font-size: 0.875rem;
            color: #6B7280;
        }
        
        .stats-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }
        
        .stats-change.positive {
            color: #00B42A;
        }
        
        .stats-change.negative {
            color: #F53F3F;
        }
        
        .stats-change i {
            margin-right: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- 主内容 -->
    <div class="main-content">
        <!-- 导航栏 -->
        <header class="main-header">
            <div class="header-content">
                <div class="flex items-center">
                    <button class="menu-toggle" id="menuToggle">
                        <i class="fa fa-bars"></i>
                    </button>
                    <div class="brand ml-4 hidden md:block">ShopEase</div>
                </div>
                <div class="user-menu">
                    <span class="hidden md:inline">管理员</span>
                    <div class="user-avatar">
                        <i class="fa fa-user"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容 -->
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">商品管理</h1>
                <p class="page-subtitle">查看和管理所有商品</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-header">
                        <div class="stats-icon stats-icon-primary">
                            <i class="fa fa-shopping-bag"></i>
                        </div>
                        <div class="stats-change positive">
                            <i class="fa fa-arrow-up"></i>
                            <span>12.5%</span>
                        </div>
                    </div>
                    <div class="stats-value">2,456</div>
                    <div class="stats-label">商品总数</div>
                </div>
                <div class="stats-card">
                    <div class="stats-header">
                        <div class="stats-icon stats-icon-secondary">
                            <i class="fa fa-shopping-cart"></i>
                        </div>
                        <div class="stats-change positive">
                            <i class="fa fa-arrow-up"></i>
                            <span>8.3%</span>
                        </div>
                    </div>
                    <div class="stats-value">1,842</div>
                    <div class="stats-label">今日订单</div>
                </div>
                <div class="stats-card">
                    <div class="stats-header">
                        <div class="stats-icon stats-icon-success">
                            <i class="fa fa-line-chart"></i>
                        </div>
                        <div class="stats-change positive">
                            <i class="fa fa-arrow-up"></i>
                            <span>15.7%</span>
                        </div>
                    </div>
                    <div class="stats-value">¥89,456</div>
                    <div class="stats-label">今日销售额</div>
                </div>
                <div class="stats-card">
                    <div class="stats-header">
                        <div class="stats-icon stats-icon-warning">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="stats-change positive">
                            <i class="fa fa-arrow-up"></i>
                            <span>23.1%</span>
                        </div>
                    </div>
                    <div class="stats-value">5,842</div>
                    <div class="stats-label">活跃用户</div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filter">
                <div class="search-input">
                    <i class="fa fa-search"></i>
                    <input type="text" placeholder="搜索商品名称、SKU或分类...">
                </div>
                <select class="border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary">
                    <option value="">所有分类</option>
                    <option value="1">电子产品</option>
                    <option value="2">服装鞋帽</option>
                    <option value="3">家居用品</option>
                    <option value="4">美妆个护</option>
                </select>
                <select class="border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary">
                    <option value="">所有状态</option>
                    <option value="1">在售</option>
                    <option value="2">下架</option>
                    <option value="3">缺货</option>
                </select>
                <button class="btn-primary">
                    <i class="fa fa-search mr-2"></i>搜索
                </button>
                <button class="btn-outline">
                    <i class="fa fa-filter mr-2"></i>筛选
                </button>
            </div>

            <!-- 表格 -->
            <div class="card">
                <div class="card-header">
                    <h3>商品列表</h3>
                    <button class="btn-secondary">
                        <i class="fa fa-plus mr-2"></i>添加商品
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>商品名称</th>
                                    <th>分类</th>
                                    <th>价格</th>
                                    <th>库存</th>
                                    <th>销量</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>001</td>
                                    <td>
                                        <div class="flex items-center">
                                            <img src="https://picsum.photos/id/96/40/40" alt="商品图片" class="w-10 h-10 rounded object-cover mr-3">
                                            <span>无线蓝牙耳机</span>
                                        </div>
                                    </td>
                                    <td>电子产品</td>
                                    <td>¥299.00</td>
                                    <td>156</td>
                                    <td>328</td>
                                    <td><span class="status-tag status-active">在售</span></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button class="btn-primary text-sm">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn-outline text-sm">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                            <button class="btn-outline text-sm text-red-500">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>002</td>
                                    <td>
                                        <div class="flex items-center">
                                            <img src="https://picsum.photos/id/119/40/40" alt="商品图片" class="w-10 h-10 rounded object-cover mr-3">
                                            <span>智能手表</span>
                                        </div>
                                    </td>
                                    <td>电子产品</td>
                                    <td>¥499.00</td>
                                    <td>89</td>
                                    <td>156</td>
                                    <td><span class="status-tag status-active">在售</span></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button class="btn-primary text-sm">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn-outline text-sm">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                            <button class="btn-outline text-sm text-red-500">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>003</td>
                                    <td>
                                        <div class="flex items-center">
                                            <img src="https://picsum.photos/id/160/40/40" alt="商品图片" class="w-10 h-10 rounded object-cover mr-3">
                                            <span>运动背包</span>
                                        </div>
                                    </td>
                                    <td>户外装备</td>
                                    <td>¥159.00</td>
                                    <td>234</td>
                                    <td>98</td>
                                    <td><span class="status-tag status-active">在售</span></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button class="btn-primary text-sm">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn-outline text-sm">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                            <button class="btn-outline text-sm text-red-500">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>004</td>
                                    <td>
                                        <div class="flex items-center">
                                            <img src="https://picsum.photos/id/201/40/40" alt="商品图片" class="w-10 h-10 rounded object-cover mr-3">
                                            <span>户外运动鞋</span>
                                        </div>
                                    </td>
                                    <td>户外装备</td>
                                    <td>¥249.00</td>
                                    <td>0</td>
                                    <td>124</td>
                                    <td><span class="status-tag status-pending">缺货</span></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button class="btn-primary text-sm">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn-outline text-sm">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                            <button class="btn-outline text-sm text-red-500">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>005</td>
                                    <td>
                                        <div class="flex items-center">
                                            <img src="https://picsum.photos/id/26/40/40" alt="商品图片" class="w-10 h-10 rounded object-cover mr-3">
                                            <span>智能手环</span>
                                        </div>
                                    </td>
                                    <td>电子产品</td>
                                    <td>¥89.00</td>
                                    <td>176</td>
                                    <td>456</td>
                                    <td><span class="status-tag status-active">在售</span></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <button class="btn-primary text-sm">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn-outline text-sm">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                            <button class="btn-outline text-sm text-red-500">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="page-info">
                            显示 1 到 5 条，共 245 条记录
                        </div>
                        <div class="page-controls">
                            <button class="page-btn">
                                <i class="fa fa-angle-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">4</button>
                            <button class="page-btn">5</button>
                            <button class="page-btn">
                                <i class="fa fa-angle-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 侧边栏切换
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('open');
        });
        
        // 为表格行添加悬停效果
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.classList.add('bg-gray-50');
            });
            
            row.addEventListener('mouseleave', () => {
                row.classList.remove('bg-gray-50');
            });
        });
        
        // 模拟按钮点击效果
        const actionButtons = document.querySelectorAll('.btn');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                this.classList.add('scale-95');
                setTimeout(() => {
                    this.classList.remove('scale-95');
                }, 150);
            });
        });
    </script>
</body>
</html>
    
`;