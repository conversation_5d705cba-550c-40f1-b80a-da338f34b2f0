import {useContext, useEffect} from "react";
import {FormInstance} from "@codingapi/ui-framework";
import { FlowViewReactContext } from "@/components/flow-pc";
import { FlowFromTriggerContext } from "@/components/flow-pc/Flow/components/FlowFromTriggerContext";


export const useRegisterFormTrigger = (form: FormInstance, trigger: (validateState:boolean) => Promise<boolean>) => {
    const flowViewReactContext = useContext(FlowViewReactContext);

    useEffect(() => {
        const nodeCode = flowViewReactContext?.flowRecordContext.getNodeCode();
        if (nodeCode) {
            FlowFromTriggerContext.getInstance().register(nodeCode, async () => {
                const validateState = await form.validate();
                if (validateState) {
                    return await trigger(validateState);
                }
                return false;
            })
        }
    }, []);
}
