/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-15 23:12:54
 * @Description: 
 * @LastEditTime: 2025-08-04 08:43:01
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import "./index.scss";

interface HRBodyProps {
    children?: React.ReactNode,
    style?: any
}

const HRBody: React.FC<HRBodyProps> = (props) => {

    return (
        <div className="hr-body" style={props.style}>
            {props.children}
        </div>
    )
}

export default HRBody;
