import React, {useState, useEffect, useRef} from 'react';
import './index.scss';
import {list} from "@/api/template/template";
import {valueTreeByLabelCode} from "@/api/dict/value";

interface ChoiceTemplateProps {
    selectedTemplate?: string;
}

// 根据接口返回值定义 TemplateCard 接口
interface TemplateCard {
    id: number;
    imageUrl: string;
    name: string;
    uploadUnit: string;
    uploadUser: string;
    pageSize: number;
    publishStatus: number;
    publishTime: number;
    citeNum: number | null;
}

interface TemplateType {
    id: number;
    code: string;
    name: string;
    parentId: number;
    children: TemplateType[];
}

const templateCards: TemplateCard[] = [];

// 简单的 Tooltip 组件
const Tooltip: React.FC<{ content: string; children: React.ReactNode }> = ({ content, children }) => {
    const [visible, setVisible] = useState(false);
    const childRef = useRef<HTMLDivElement>(null);

    const showTooltip = () => {
        setVisible(true);
    };

    const hideTooltip = () => {
        setVisible(false);
    };

    return (
        <div
            className="tooltip-wrapper"
            onMouseEnter={showTooltip}
            onMouseLeave={hideTooltip}
            ref={childRef}
            style={{ position: 'relative', display: 'inline-block' }}
        >
            {children}
            {visible && (
                <div
                    className="custom-tooltip"
                    style={{
                        position: 'absolute',
                        bottom: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        color: 'white',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        fontSize: '14px',
                        zIndex: 1000,
                        minWidth: '200px',
                        maxWidth: '300px',
                        wordWrap: 'break-word',
                        marginBottom: '10px',
                        textAlign: 'center',
                    }}
                >
                    {content}
                    <div
                        style={{
                            content: '""',
                            position: 'absolute',
                            top: '100%',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            border: '5px solid transparent',
                            borderTopColor: 'rgba(0, 0, 0, 0.8)',
                        }}
                    />
                </div>
            )}
        </div>
    );
};

const ChoiceTemplate: React.FC<ChoiceTemplateProps> = (props) => {
    const [templates, setTemplates] = useState<TemplateCard[]>(templateCards);
    const [templateTypes, setTemplateTypes] = useState<TemplateType[]>([]);
    const [selectedTemplate, setSelectedTemplate] = useState<TemplateCard | null>(null);
    const [selectedType, setSelectedType] = useState<string | null>(null);

    // 分页相关状态
    const [current, setCurrent] = useState(1);
    const pageSize = 6;

    useEffect(() => {
        console.log('开始获取模板分类数据');
        valueTreeByLabelCode("Le6OdVVP5KC").then(res => {
            console.log('模板分类数据响应:', res);
            if (res && res.data && Array.isArray(res.data.list)) {
                console.log('设置模板分类数据:', res.data.list);
                setTemplateTypes(res.data.list);
            } else {
                console.log('模板分类数据为空或格式不正确');
                setTemplateTypes([]);
            }
        }).catch(error => {
            console.error('获取模板分类失败:', error);
            setTemplateTypes([]);
        });
    }, []);

    // 当 selectedType 改变时获取对应类型的模板数据
    useEffect(() => {
        console.log('开始获取模板数据, selectedType:', selectedType, 'current:', current);

        if (selectedType) {
            const params: any = {
                current: current,
                pageSize: pageSize,
                category: selectedType,
                publishStatus: 1
            };

            list(params, null, null, []).then(res => {
                if (res && res.data && Array.isArray(res.data)) {
                    setTemplates(res.data);
                } else {
                    setTemplates([]);
                }
            }).catch(error => {
                setTemplates([]);
            });
        }

    }, [current, selectedType]);


    // 初始化时默认选中第一个类型
    useEffect(() => {

        if (props.selectedTemplate) {
            setSelectedType(props.selectedTemplate);
        }

        if (templateTypes.length > 0 && selectedType === null) {
            const firstChild = templateTypes[0]?.children?.[0];
            if (firstChild) {
                setSelectedType(firstChild.code);
            }
        }
    }, [templateTypes]);

    const handleTemplateSelect = (templateId: number) => {
        if (selectedTemplate && selectedTemplate.id === templateId) {
            setSelectedTemplate(null);
        } else {
            const selected = templates.find(card => card.id === templateId);
            setSelectedTemplate(selected || null);
        }
    };

    const handleUseTemplate = () => {
        if (!selectedTemplate) {
            alert('请选择一个模板');
            return;
        }
        alert(`使用模板 ${selectedTemplate.id} 编辑`);
    };

    const handleUseBlank = () => {
        alert('使用空白文档编辑');
    };

    // 处理类型切换
    const handleTypeSelect = (typeCode: string) => {
        setSelectedTemplate(null);
        setSelectedType(typeCode);
        setCurrent(1); // 切换类型时重置页码
    };

    const findTemplateTypeByCode = (types: TemplateType[], code: string): TemplateType | null => {
        for (const type of types) {
            if (type.code === code) {
                return type;
            }
            if (type.children) {
                const found = findTemplateTypeByCode(type.children, code);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    };

    // 根据当前页和每页显示数量，获取当前页的模板卡片
    const getCurrentPageTemplates = (templates: TemplateCard[], currentPage: number, itemsPerPage: number) => {
        if (!Array.isArray(templates)) {
            return [];
        }
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const result = templates.slice(startIndex, endIndex);
        return result;
    };

    // 不再进行额外过滤，直接使用接口返回的数据
    const currentTemplates = getCurrentPageTemplates(templates, current, pageSize);

    // 分页控件
    const paginate = (pageNumber: number) => setCurrent(pageNumber);

    // 渲染树状结构
    const renderTree = (nodes: TemplateType[], level = 0) => {
        if (!Array.isArray(nodes)) {
            return null;
        }

        return nodes.map(node => (
            <div key={node.id} className="tree-node" style={{paddingLeft: `${level * 20}px`}}>
                {node.children && node.children.length > 0 ? (
                    <div className="tree-item has-children">
                        <span className="tree-label">{node.name}</span>
                        <div className="tree-children">
                            {renderTree(node.children, level + 1)}
                        </div>
                    </div>
                ) : (
                    <div
                        className={`tree-item ${selectedType === node.code ? 'selected' : ''}`}
                        onClick={() => handleTypeSelect(node.code)}
                    >
                        <span className="tree-label">{node.name}</span>
                    </div>
                )}
            </div>
        ));
    };

    return (
        <div className="choice-template-container">
            {/* 左侧区域 - 模板分类树 */}
            <div className="left-panel">
                <div className="type-tree">
                    <h4>模板分类</h4>
                    <div className="tree-container">
                        {renderTree(templateTypes)}
                    </div>
                </div>
            </div>

            {/* 中间区域 - 模板卡片 */}
            <div className="middle-panel">
                <div className="template-cards">
                    {currentTemplates && currentTemplates.length > 0 ? (
                        currentTemplates.map((card) => (
                            <div
                                key={card.id}
                                className={`template-card ${selectedTemplate?.id === card.id ? 'selected' : ''}`}
                                onClick={() => handleTemplateSelect(card.id)}
                            >
                                <img
                                    //src={card.imageUrl}
                                    src={'https://img0.baidu.com/it/u=3151931393,4221021653&fm=253&app=138&f=JPEG?w=800&h=1345'}
                                    alt={card.name}
                                    className="preview-image"
                                />
                                <div className="card-content">
                                    <Tooltip content={card.name}>
                                        <h3 className="card-title">{card.name}</h3>
                                    </Tooltip>
                                    <p>上传单位：{card.uploadUnit}</p>
                                    <p>上传用户：{card.uploadUser}</p>
                                    <p>页面数量：{card.pageSize}</p>
                                    <p>发布状态：{card.publishStatus === 1 ? '已发布' : '未发布'}</p>
                                    <p>发布时间：{new Date(card.publishTime).toLocaleString()}</p>
                                    <p>引用次数：{card.citeNum ?? '0'}</p>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="no-templates">
                            <p>暂无模板数据</p>
                            <p>当前模板数量: {templates.length}</p>
                        </div>
                    )}
                </div>

                {/* 分页控件 */}
                <div className="pagination">
                    {Array.from({length: Math.ceil((Array.isArray(templates) ? templates.length : 0) / pageSize)}, (_, i) => (
                        <button
                            key={i + 1}
                            onClick={() => paginate(i + 1)}
                            className={current === i + 1 ? 'active' : ''}
                        >
                            {i + 1}
                        </button>
                    ))}
                </div>
            </div>

            {/* 右侧区域 - 保持现有样式 */}
            <div className="right-panel">
                <button
                    className="btn btn-new-blank"
                    onClick={selectedTemplate ? handleUseTemplate : handleUseBlank}
                >
                    {selectedTemplate ? '使用模板编辑' : '新建空白文档'}
                </button>

                <div className="divider"></div>

                {!selectedTemplate && (
                    <div className="tooltip">
                        <p>
                            <strong>选择模板用法：</strong>
                            <br/>
                            1. 在左侧选择一个模板卡片。
                            <br/>
                            2. 点击右侧“使用模板编辑”按钮，即可基于该模板创建文档。
                            <br/>
                            3. 如果不需要模板，可点击“新建空白文档”按钮创建空白文档。
                        </p>
                    </div>
                )}

                {selectedTemplate && (
                    <div className="template-first-page">
                        <div className="first-page-title">
                            {selectedTemplate.name}
                        </div>
                        <img
                            // src={selectedTemplate.imageUrl}
                            src={'https://img0.baidu.com/it/u=3151931393,4221021653&fm=253&app=138&f=JPEG?w=800&h=1345'}
                            alt={`1`}
                            className="first-page-image"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ChoiceTemplate;
