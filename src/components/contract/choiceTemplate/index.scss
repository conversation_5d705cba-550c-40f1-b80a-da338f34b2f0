.choice-template-container {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 6px;
  height: 80vh;
}

.left-panel {
  width: 8%; /* 保持10%宽度 */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 100px;

  .type-tree {
    h4 {
      margin: 0 0 10px;
      font-size: 16px;
      font-weight: bold;
    }

    .tree-container {
      .tree-node {
        .tree-item {
          padding: 5px 0;
          cursor: pointer;
          transition: background-color 0.2s;
          border-radius: 4px;
          display: block;
          width: 100%;

          /* 移除悬停时的背景颜色变化 */
          &:hover {
            /* background-color: #f0f0f0; */ /* 注释掉这行 */
          }

          &.selected {
            background-color: #4096ff;
            color: white;
          }

          &.has-children {
            .tree-label {
              font-weight: bold;
            }
          }

          .tree-label {
            display: block;
            padding: 5px 10px;
            width: 100%;
            box-sizing: border-box;
          }

          .tree-children {
            margin-top: 5px;
          }
        }

        /* 只对最终可选择的节点添加悬停效果 */
        .tree-children .tree-item:not(.has-children) {
          &:hover {
            /* background-color: #f0f0f0; */ /* 注释掉这行 */
          }
        }
      }
    }
  }
}

.middle-panel {
  width: 65%; /* 从50%增加到65% */
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 10px; /* 添加内边距避免扩大时被遮挡 */

  .template-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); /* 增加卡片最小宽度 */
    gap: 20px;
    overflow-y: auto;
    flex: 1;
    padding: 5px; /* 添加内边距避免扩大时被遮挡 */

    .no-templates {
      grid-column: 1 / -1;
      text-align: center;
      padding: 40px 0;
      color: #999;

      p {
        margin: 10px 0;
        font-size: 16px;
      }
    }

    .template-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 16px;
      cursor: pointer;
      transition: all 0.2s ease; /* 使用更平滑的过渡 */
      border: 2px solid transparent;
      width: 88%; /* 使用相对宽度而不是固定宽度 */
      height: 160px; /* 增加卡片高度 */
      display: flex;
      flex-direction: row;
      align-items: center;
      transform-origin: center; /* 设置变换原点为中心 */
      position: relative; /* 为 Tooltip 定位做准备 */

      &:hover {
        transform: scale(1.02);
        z-index: 10; /* 提高层级确保不被遮挡 */
        position: relative; /* 配合z-index使用 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
      }

      &.selected {
        border-color: #4096ff;
        box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
      }

      .preview-image {
        width: 110px; /* 增加图片宽度 */
        height: 130px; /* 增加图片高度 */
        border-radius: 8px;
        margin-right: 15px;
        object-fit: cover;
        flex-shrink: 0; /* 防止图片被压缩 */
      }

      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        position: relative; /* 为内部元素定位做准备 */

        .card-title {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          position: relative; /* 为 Tooltip 定位做准备 */
        }

        p {
          margin: 4px 0;
          color: #666;
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    button {
      background-color: #f0f0f0;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      margin: 0 5px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #e0e0e0;
      }

      &.active {
        background-color: #4096ff;
        color: white;
      }
    }
  }
}

.right-panel {
  width: 20%; /* 从25%减少到20% */
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .btn-new-blank {
    background-color: #4096ff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color 0.2s;
    width: 100%;
    box-sizing: border-box;

    &:hover {
      background-color: #0078ff;
    }
  }

  .divider {
    width: 100%;
    height: 1px;
    background-color: #ccc;
    margin: 20px 0;
  }

  .template-first-page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 20px;

    .first-page-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .tooltip {
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
    width: 100%;
    box-sizing: border-box;

    p {
      margin: 0;
      line-height: 1.6;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .choice-template-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel,
  .middle-panel,
  .right-panel {
    width: 100%;
  }

  .left-panel {
    margin-bottom: 20px;
    min-width: unset;
  }

  .middle-panel {
    order: 2;
    padding: 0;
  }

  .right-panel {
    order: 3;
  }

  .middle-panel .template-cards {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    padding: 5px;

    .template-card {
      width: 100%;
      height: auto;
      min-height: 160px;

      &:hover {
        transform: none; /* 在小屏幕上禁用扩大效果 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

@media (max-width: 768px) {
  .choice-template-container {
    padding: 10px;
  }

  .middle-panel .template-cards {
    grid-template-columns: 1fr;
  }

  .left-panel .type-tree .tree-container .tree-node .tree-item .tree-label {
    padding: 8px 10px;
  }
}
