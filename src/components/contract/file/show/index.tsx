
import React from 'react';
import { downloadOssFile } from "@/utils/download";
import { EyeOutlined } from "@ant-design/icons";

interface File {
    name: string;
    fileKey: string;
    fileUrl: string;
}

interface FileDisplayProps {
    files: File[];
}

const FileDisplay: React.FC<FileDisplayProps> = ({ files }) => {
    const handleDownload = (fileKey: string) => {
        downloadOssFile(fileKey).then(res => {
            // 处理下载逻辑
        });
    };

    const handlePreview = (fileUrl: string) => {
        // 调用后端文件预览接口并打开新窗口
        window.open(`/itm-legal-review-contract/api/query/file/preview?fileUrl=${encodeURIComponent(fileUrl)}`, '_blank');
    };

    return (
        <div>
            {files.map((file, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    {/* 左侧文件名，可点击下载 */}
                    <span
                        style={{ cursor: 'pointer', marginRight: '16px', color: 'blue', textDecoration: 'underline' }}
                        onClick={() => handleDownload(file.fileKey)}
                    >
                        {file.name}
                    </span>
                    {/* 右侧预览标志 */}
                    <button
                        style={{ marginLeft: '8px', cursor: 'pointer' }}
                        onClick={() => handlePreview(file.fileUrl)}
                    >
                        <EyeOutlined /> {/* 使用Ant Design图标库中的眼睛图标表示预览 */}
                    </button>
                </div>
            ))}
        </div>
    );
};

export default FileDisplay;
