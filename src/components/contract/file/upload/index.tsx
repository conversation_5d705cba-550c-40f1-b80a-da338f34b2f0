import OssFileUploader from "@/components/oss/OssFileUpload";
import React, {useState, useCallback, useEffect} from "react";
import {Tooltip} from "antd";
import {DeleteOutlined} from "@ant-design/icons";
import "./index.css";
import {downloadOssFile} from "@/utils/download";
import {message} from "antd";

interface ContractFileUploadProps {
    label?: string;
    fileType?: string;
    maxCount?: number;
    bucket?: string;
    value?: string;
    onChange?: (value: string) => void;
}

export const ContractFileUpload: React.FC<ContractFileUploadProps> =
    ({
         label = "",
         fileType = "application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
         maxCount = 5,
         bucket = "document",
         value,
         onChange
     }) => {
        const [files, setFiles] = useState<{ key: string; name: string }[]>([]);
        const [deletedKeys, setDeletedKeys] = useState<string[]>([]);

        const [filesKeys, setFilesKeys] = useState<string>('');

        useEffect(() => {
            onChange?.(filesKeys)
        }, [filesKeys]);

        // 初始化 value 回显
        useEffect(() => {
            if (value) {
                const keys = value.split(",");
                setFilesKeys(value);
                const newFiles = keys
                    .filter(key => !deletedKeys.includes(key))
                    .map(key => ({
                        key,
                        name: key.split("/").pop() || key
                    }));
                setFiles(newFiles);
            }
        }, [value, deletedKeys]);

        const handleUpload = useCallback(
            (newKey: string) => {
                if (!newKey) return;

                const keyList = newKey.split(",");
                const lastKey = keyList[keyList.length - 1];

                // 仅检查当前用户已上传的文件，排除从 value 初始化的文件
                const exists = files.some(file => file.key === lastKey);

                if (exists) {
                    message.warning("该文件已存在");
                    return;
                }

                setFiles((prev) => {
                    let newFiles = [...prev];

                    if (newFiles.length >= maxCount) {
                        message.warning(`最多只能上传 ${maxCount} 个文件`);
                        return prev;
                    }

                    const fileName = lastKey.split("/").pop() || lastKey;
                    newFiles.push({key: lastKey, name: fileName});

                    const fileKeys = newFiles.map(file => file.key).join(",");
                    setFilesKeys(fileKeys);

                    return newFiles;
                });
            },
            [maxCount, files]
        );




        const handleRemove = useCallback(
            (index: number) => {
                const removedKey = files[index].key;

                setDeletedKeys((prev) => [...prev, removedKey]);

                setFiles((prev) => {
                    const newFiles = prev.filter((_, i) => i !== index);

                    const fileKeys = newFiles.map(file => file.key).join(",");

                    setFilesKeys(fileKeys);

                    return newFiles;
                });
            },
            [files]
        );

        const handleDownload = (key: string) => {
            downloadOssFile(key).then(() => {
            });
        };

        return (
            <div className="contract-file-upload">
                <div className="file-list">
                    {files.map((file, index) => (
                        <div
                            key={file.key}
                            className="file-item"
                            style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                width: "100%",
                            }}
                        >
                            <Tooltip title={file.name}>
                                <div
                                    style={{
                                        flex: 1,
                                        whiteSpace: "nowrap",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        maxWidth: "300px",
                                        color: "#1890ff",
                                        cursor: "pointer",
                                    }}
                                    onClick={() => handleDownload(file.key)}
                                >
                                    附件{index + 1}：{file.name}
                                </div>
                            </Tooltip>

                            <DeleteOutlined
                                style={{marginLeft: 8, color: "#1890ff", cursor: "pointer"}}
                                onClick={(e: React.MouseEvent) => {
                                    e.preventDefault();
                                    handleRemove(index);
                                }}
                            />
                        </div>
                    ))}
                </div>
                <div style={{display: "flex", alignItems: "center"}}>
                    <OssFileUploader
                        label={label}
                        uploaderAccept={fileType}
                        uploaderMaxCount={9999}
                        bucket={bucket}
                        ossFileKey={filesKeys}
                        onKeyChange={handleUpload}
                        showUploadList={false}
                    />
                </div>
            </div>
        );
    };
