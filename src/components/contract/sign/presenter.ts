/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-09 08:36:43
 * @Description: 
 * @LastEditTime: 2025-07-19 10:31:23
 * @LastEditors: wangjiantao
 */
import { Api, Dispatch, State } from "@/components/contract/sign/types";
import dayjs from "dayjs";

export class Presenter {
  private readonly dispatch: Dispatch<State>;

  private readonly api: Api;

  private state: State;

  public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
    this.dispatch = dispatch;
    this.api = api;
    this.state = state;
  }

  public syncState = (newStatus: State) => {
    this.state = newStatus;
  };

  public setMainFiles(files: any[]) {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        mainFiles: [...files],
      };
    });
  }

  public setOtherFiles(files: any[]) {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        otherFiles: files,
      };
    });
  }

    public setPartBFiles(files: any[]) {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        partBFiles: files,
      };
    });
  }

  public changeDate(date: any) {
    const [startDate, endDate] = date;
    this.dispatch(prev => {
      return {
        ...prev,
        startDate,
        endDate,
      }
    })
  }

  
}
