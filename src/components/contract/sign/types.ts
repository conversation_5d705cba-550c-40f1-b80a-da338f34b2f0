export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface State {
  contract: any;

  startDate: any;

  endDate: any;

  mainFiles: {
    fileCategory: string;
    fileKey: string;
  }[];
  otherFiles: {
    fileCategory: string;
    fileKey: string;
  }[];
  partBFiles: {
    fileCategory: string;
    fileKey: string;
  }[];
}

export interface Api {
  getContractDetail: (params: any) => Promise<any>;
}
