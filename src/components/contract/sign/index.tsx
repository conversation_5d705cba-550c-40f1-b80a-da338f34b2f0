/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-07 11:53:09
 * @Description:
 * @LastEditTime: 2025-07-19 10:34:15
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import { forwardRef, useImperativeHandle } from "react";
import "../../../pages/contract/authorize/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import { usePresenter } from "@/components/contract/sign/hooks/usePresenter";
import OssFileUploader from "@/components/oss/OssFileUpload";
import moment from "moment";
import { DatePicker, Form } from "antd";
import {FormDate} from "@/components/form-pc";
import dayjs from "dayjs";
import { ValidateUtils } from "@codingapi/ui-framework";
interface SignProps {
  isEdit: boolean;
  contract: any;
  defaultActive?: boolean;
}

export const Sign = forwardRef((props: SignProps, ref: any) => {
  const { state, presenter } = usePresenter();
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    state: () => {
      return {
        state,
      };
    },
    form,
  }));

  // 合同签订表格数据
  const tableRowsSign = [
    {
      label: "合同起止时间",
      value: props.isEdit ? (
        <Form.Item
          name={"dates"}
          rules={[{ required: true, message: "请选择合同起止时间" }]}
        >
          <DatePicker.RangePicker
            onChange={(date) => {
              presenter.changeDate(date);
            }}
          />
        </Form.Item>
      ) : (
        moment(props.contract?.endDate).format("YYYY-MM-DD HH:mm:ss")
      ),
    },
    {
      label: "附件",
      children: [
        {
          label: "合同盖章扫描件",
          width: "100%",
          value: (
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                height: 150,
                overflow: "auto",
              }}
            >
              <OssFileUploader
                label=""
                uploaderAccept="application/pdf"
                uploaderMaxCount={5}
                bucket="document"
                ossFileKey={state.mainFiles
                  .map((item) => item.fileKey)
                  .join(",")}
                onKeyChange={(key: string) => {
                  const keys = key.split(",");
                  const files: any[] = [];
                  keys.forEach((_key) =>
                    files.push({ fileKey: _key, fileCategory: "Verdx8CC55M" })
                  );
                  presenter.setMainFiles(files);
                }}
              />
            </div>
          ),
        },
        {
          label: "甲方授权委托书盖章扫描件（如有）",
          width: "100%",
          value: (
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                height: 150,
                overflow: "auto",
              }}
            >
              <OssFileUploader
                label=""
                uploaderAccept="application/pdf"
                uploaderMaxCount={5}
                bucket="document"
                ossFileKey={state.otherFiles
                  .map((item) => item.fileKey)
                  .join(",")}
                onKeyChange={(key: string) => {
                  const keys = key.split(",");
                  const files: any[] = [];
                  keys.forEach((_key) =>
                    files.push({ fileKey: _key, fileCategory: "VerdyoDc3TE" })
                  );
                  presenter.setOtherFiles(files);
                }}
              />
            </div>
          ),
        },
        {
          label: "乙方授权委托书盖章扫描件（如有）",
          width: "100%",
          value: (
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                height: 150,
                overflow: "auto",
              }}
            >
              <OssFileUploader
                label=""
                uploaderAccept="application/pdf"
                uploaderMaxCount={5}
                bucket="document"
                ossFileKey={state.partBFiles
                  .map((item) => item.fileKey)
                  .join(",")}
                onKeyChange={(key: string) => {
                  const keys = key.split(",");
                  const files: any[] = [];
                  keys.forEach((_key) =>
                    files.push({ fileKey: _key, fileCategory: "Vf9B78y3o9Y" })
                  );
                  presenter.setPartBFiles(files);
                }}
              />
            </div>
          ),
        },
      ],
      isLabelRed: true,
    },
  ];
  return (
    <Form form={form}>
      <CollapseFormTable
        title="合同签订信息"
        isShow={props.defaultActive ? "1" : "0"}
      >
        <GenericFormTable isBorder={true} rows={tableRowsSign} />
      </CollapseFormTable>
    </Form>
  );
});
