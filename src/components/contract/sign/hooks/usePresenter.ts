/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-09 08:36:43
 * @Description: 
 * @LastEditTime: 2025-07-18 10:24:17
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import {State} from "@/components/contract/sign/types";
import React from "react";
import {Presenter} from "@/components/contract/sign/presenter";
import {ApiImpl} from "@/components/contract/sign/model";

const initialState: State = {

    contract: {},

    startDate: null,

    endDate: null,

    mainFiles: [],
    
    otherFiles: [],

    partBFiles: [],
}

export function usePresenter() {
    const [state, dispatch] = React.useState<State>(initialState);

    const presenterRef = React.useRef<Presenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new Presenter(dispatch, new ApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}