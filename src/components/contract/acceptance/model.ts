/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 19:57:21
 * @Description:
 * @LastEditTime: 2025-07-11 11:10:46
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { Api } from "./types";
import {
  loadPerformancePlanList,
  loadPerformanceRecordList,
  loadContractAcceptanceList
} from "@/api/contract/performance";

export class ApiImpl implements Api {
  loadPerformancePlanList(contractId: number): Promise<any> {
    return loadPerformancePlanList(contractId);
  }

  loadContractAcceptanceList(contractId: number): Promise<any> {
    return loadContractAcceptanceList(contractId);
  }
}
