/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-11 16:39:03
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import { State } from "../types";
import { Presenter } from "../presenter";
import { ApiImpl } from "../model";

const initialState: State = {
  //authorize列表
  performancePlanList: [],
  formData: [],
};

export function usePresenter() {
  const [state, dispatch] = React.useState<State>(initialState);

  const presenterRef = React.useRef<Presenter | null>(null);

  // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
  if (!presenterRef.current) {
    presenterRef.current = new Presenter(dispatch, new ApiImpl(), state);
  }

  // 当 state 发生变化时，更新 presenter 的状态
  React.useEffect(() => {
    presenterRef.current?.syncState(state);
  }, [state]);

  return {
    state,
    presenter: presenterRef.current,
  };
}
