export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface State {
  performancePlanList: any[];
  formData: {
    acceptanceDate: string;
    overallPerformance: '0' | '1';
    attachments: string | any[];
    other: string,
    main: string
  }[];
}

export interface Api {
  loadPerformancePlanList: (contractId: number) => Promise<any>;
  loadContractAcceptanceList: (contractId: number) => Promise<any>;
}
