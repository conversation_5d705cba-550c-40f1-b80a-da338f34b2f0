/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-11 11:08:00
 * @Description:
 * @LastEditTime: 2025-07-11 16:38:56
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 19:57:21
 * @Description:
 * @LastEditTime: 2025-07-11 10:07:04
 * @LastEditors: wangjiantao
 */
import { Api, Dispatch, State } from "./types";

export class Presenter {
  private readonly dispatch: Dispatch<State>;

  private readonly api: Api;

  private state: State;

  public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
    this.dispatch = dispatch;
    this.api = api;
    this.state = state;
  }

  public syncState = (newStatus: State) => {
    this.state = newStatus;
  };

  public loadPerformancePlanList = (contractId: number) => {
    this.api.loadPerformancePlanList(contractId).then((res) => {
      const formData: any[] = [];
      const list = res.data?.list || [];
      this.dispatch((prevState) => {
        list.forEach((item: any, index: number) => {
          item.serialNumber = index;
          formData.push({
            acceptanceDate: "",
            overallPerformance: "1",
            main: "",
            other: "",
          });
        });
        return {
          ...prevState,
          performancePlanList: res.data?.list || [],
          formData: prevState.formData?.length ? prevState.formData : [...formData],
        };
      });
    });
  };

  public onFormChange = (
    key: keyof {
      acceptanceDate: string;
      overallPerformance: string;
      main: string;
      other: string;
    },
    value: string,
    index: number
  ) => {
    this.dispatch((prevState) => {
      const formData = [...prevState.formData];
      (formData[index] as any)[key] = value;
      return {
        ...prevState,
        formData,
      };
    });
  };

  public loadContractAcceptanceList = (contractId: number) => {
    this.api.loadContractAcceptanceList(contractId).then((res) => {
      const list = res.data?.list || [];
      if (!list.length) return;
      list.forEach((i: any) => {
        let other = "";
        let main = "";
        i.attachments.forEach((file: any) => {
          if (file.fileCategory === "VexV1h5Nxjs") {
            main += file.fileKey;
          }
          if (file.fileCategory === "VexUysElR7w") {
            other += file.fileKey;
          }
        });
        i.other = other;
        i.main = main;
      });

      this.dispatch((prevState) => {
        return {
          ...prevState,
          formData: [...list],
        };
      });
    });
  };
}
