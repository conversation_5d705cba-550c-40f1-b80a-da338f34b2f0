import {
  forwardRef,
  useImperativeHandle,
  Fragment,
  useEffect,
} from "react";
import "../../../pages/contract/authorize/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import { DatePicker, RadioGroup } from "@itm/components-pc";
import { Form } from "antd";
import OssFileUploader from "@/components/oss/OssFileUpload";
import { usePresenter } from "./hooks/usePresenter";
import dayjs from "dayjs";

interface AcceptanceProps {
  defaultActive?: boolean;
  contractId: number;
}

export const Acceptance = forwardRef((props: AcceptanceProps, ref: any) => {
  const { state, presenter } = usePresenter();
  const [form] = Form.useForm();

  // 暴露表单方法给父组件
  useImperativeHandle(ref, () => ({
    state,
    form,
  }));

  useEffect(() => {
    presenter.loadContractAcceptanceList(props.contractId);
    presenter.loadPerformancePlanList(props.contractId);
  }, [props.contractId]);

  const options = [
    { label: "完全履行", value: 0 },
    { label: "部分履行", value: 1 },
    { label: "延期履行", value: 2 },
  ];

  const generateTableRows = () => {

    return state.performancePlanList.map((_, index) => {
      const fieldPrefix = `stage_${index}`;

      return {
        text: `阶段${index + 1}`,
        table: [
          {
            label: "验收时间",
            value: (
              <DatePicker
                value={
                  state.formData[index]?.acceptanceDate
                    ? dayjs(state.formData[index]?.acceptanceDate)
                    : undefined
                }
                onChange={(_, dateString) =>
                  presenter.onFormChange(
                    "acceptanceDate",
                    dateString as string,
                    index
                  )
                }
                placeholder="请选择日期"
              />
            ),
          },
          {
            label: "附件",
            children: [
              {
                label: "验收报告",
                value: (
                  <OssFileUploader
                    bucket="document"
                    uploaderAccept="application/pdf"
                    label=""
                    uploaderMaxCount={5}
                    ossFileKey={state.formData[index]?.main || ""}
                    onKeyChange={(keys) => {
                      form.setFieldValue("main", keys);
                      presenter.onFormChange("main", keys, index);
                    }}
                  />
                ),
              },
              {
                label: "其他附件",
                value: (
                  <OssFileUploader
                    bucket="document"
                    label=""
                    uploaderMaxCount={5}
                    uploaderAccept="application/pdf"
                    ossFileKey={state.formData[index]?.other || ""}
                    onKeyChange={(keys) => {
                      form.setFieldValue("other", keys);
                      presenter.onFormChange("other", keys, index);
                    }}
                  />
                ),
              },
            ],
          },
          {
            label: "合同整体履行情况",
            value: (
              <RadioGroup
                options={options}
                value={state.formData[index]?.overallPerformance}
                block
                onChange={(e) =>
                  presenter.onFormChange(
                    "overallPerformance",
                    e.target.value,
                    index
                  )
                }
              />
            ),
          },
        ],
      };
    });
  };

  return (
    <>
      <CollapseFormTable
        title="合同验收情况"
        isShow={props.defaultActive ? "1" : "0"}
      >
        {generateTableRows().map((item, index) => (
          <Fragment key={index}>
            <div
              style={{
                height: "40px",
                fontSize: "23px",
                lineHeight: "40px",
                color: "red",
                borderTop: "2px solid red",
                fontWeight: "bold",
              }}
            >
              {item.text}
            </div>
            <GenericFormTable isBorder={true} rows={item.table} />
          </Fragment>
        ))}
      </CollapseFormTable>
    </>
  );
});
