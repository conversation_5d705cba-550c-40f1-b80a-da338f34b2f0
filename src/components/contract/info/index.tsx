/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-18 09:58:39
 * @LastEditors: wangjiantao
 */
import React, { forwardRef, Fragment } from "react";
import "@/pages/contract/authorize/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import { Input } from "@itm/components-pc";
import dayjs from "dayjs";
import moment from "moment";
import { UserOutlined } from "@ant-design/icons";
import { Divider } from "antd";
import "../../../pages/contract/update/index.scss";
import { downloadOssFile } from "@/utils/download";
interface ContractInfoProps {
  contract: any;
  adviceShow?: boolean;
  isEdit?: boolean;
  defaultActive?: boolean;
}

export interface File {
  id: number;
  fileKey: string;
  fileName: string;
  fileType: string;
  bucket: string;
  fileCategory: string;
  fileCategoryName: string;
}

// 自定义组件
const DraftInfo = ({
  draftName,
  draftDepartment,
  draftTime,
}: {
  draftName: string;
  draftDepartment: string;
  draftTime: string;
}) => (
  <div
    style={{
      display: "flex",
      justifyContent: "space-between",
      marginBottom: "10px",
    }}
  >
    <div>
      <span style={{ color: "red" }}>起草人:</span> {draftDepartment}/
      {draftName}
    </div>
    <div>
      <span style={{ color: "red" }}>起草时间:</span>{" "}
      {dayjs(draftTime).format("YYYY.MM.DD HH:mm:ss")}
    </div>
  </div>
);

export const ContractInfo = forwardRef((props: ContractInfoProps, ref: any) => {
  const { contract } = props;

  const hash: Record<string, string> = {
    VeNp1ked1iC: "采购文件",
    VeNp4tRQdn6: "中标人投标文件",
    VeVRkxM4RmK: "中标通知书",
    VeVRsuuFf1M: "相关附件",
    VeVSK5c725w: "谈判纪要",
    VeVSLA74eP2: "请示批准文件",
    VeVSLjib4ym: "相关结果报告",
  };

  const categorizedFiles = (contract?.attachments ?? []).reduce(
    (acc: any, file: File): Record<string, File[]> => {
      const { fileCategory, ...rest } = file;
      // 只处理 hash 中存在的 fileCategory
      if (hash[fileCategory]) {
        const friendlyName = hash[fileCategory];

        if (!acc[friendlyName]) {
          acc[friendlyName] = [];
        }
        acc[friendlyName].push(rest);
      }
      return acc;
    },
    {}
  );

  // 表格数据
  const tableRows = [
    {
      label: "合同名称",
      value: contract?.name,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "合同相对方",
      value: contract?.partyBName,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "合同来源",
      value: contract?.sourceName,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "履约部门",
      value: contract?.performanceDeptName,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "标的类别",
      value: contract?.categoryName,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "合同类型",
      value: contract?.typeName,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "合同金额（万元）",
      value: contract?.amount,
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "是否需缴纳履约保证金",
      value: contract?.depositRequired ? "是" : "否",
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "附件",
      children: Object.entries(categorizedFiles).flatMap(
        ([category, files]) => [
          {
            label: category,
            value: (files as File[]).map((file: File) => (
              <div key={file.id}>
                <a onClick={() => downloadOssFile(file.fileKey)}>
                  {file.fileName}
                </a>
              </div>
            )),
          },
        ]
      ),
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "已办意见",
      value: (
        <div
          style={{
            maxHeight: 200,
            overflow: "auto",
            padding: "12px 0",
          }}
        >
          <div
            style={{
              padding: "8px 0",
              position: "relative",
              display: "flex",
              alignItems: "center",
            }}
          >
            <div
              style={{
                color: "#666",
              }}
            >
              <UserOutlined style={{ color: "var(--primary-color)" }} />
              [合同起草人]{contract?.drafterName} 《{contract?.name}
              》请审核。
            </div>
            <div
              style={{
                marginLeft: 10,
                color: "#999",
                fontSize: 12,
              }}
            >
              {moment(contract?.draftTime).format("YYYY-MM-DD HH:mm:ss")}
            </div>
          </div>
          <Divider size="small" />
          {(contract?.opinions || []).map((i: any, index: number) => (
            <Fragment key={index}>
              <div
                style={{
                  fontSize: " var(--content-font-size)",
                }}
              >
                <div>【{i.deptName}】</div>
                <div
                  style={{
                    padding: "8px 0",
                    position: "relative",
                    display: "flex",
                    alignItems: "center",
                  }}
                  key={index}
                >
                  <div
                    style={{
                      color: "#666",
                    }}
                  >
                    <UserOutlined style={{ color: "var(--primary-color)" }} />
                    [审批人]{i.username}: {i.advice}
                  </div>
                  <div
                    style={{
                      marginLeft: 10,
                      color: "#999",
                      fontSize: 12,
                    }}
                  >
                    {moment(i.createTime).format("YYYY-MM-DD HH:mm:ss")}
                  </div>
                </div>
              </div>
              <Divider size="small" />
            </Fragment>
          ))}
        </div>
      ),
      isLabelRed: true,
      isEditable: props.isEdit,
    },
    {
      label: "拟办意见",
      value: <Input name={"opinion"}></Input>,
      isShow: props.adviceShow || false,
    },
  ];

  return (
    <CollapseFormTable
      title="合同起草信息"
      isShow={props.defaultActive ? "1" : ""}
    >
      <DraftInfo
        draftName={contract?.drafterName}
        draftDepartment={contract?.draftDepartment}
        draftTime={contract?.draftTime}
      />
      <GenericFormTable rows={tableRows} isBorder={true} />
    </CollapseFormTable>
  );
});
