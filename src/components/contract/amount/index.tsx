import React, {useEffect} from "react";
import { Input } from "antd";

interface AmountInputProps {
    name?: string;
    defaultValue?: string | number | undefined;
    onChange?: (value: string) => void;
    max?: number;
    min?: number;
    amountStyle?: React.CSSProperties;
    [key: string]: any;
}

// 保留 6 位小数
const formatWithFixed6 = (value: string): string => {
    const num = parseFloat(value);
    return isNaN(num) ? "0.000000" : num.toFixed(6);
};

// 千分位格式化
const formatWithCommas = (value: string): string => {
    if (!value) return "";
    const [integer, decimal] = value.split(".");
    return `${parseFloat(integer).toLocaleString()}${decimal ? `.${decimal}` : ""}`;
};

// 去除千分位逗号
const parseWithoutCommas = (value: string): string => {
    const cleaned = value.replace(/,/g, "");
    const num = parseFloat(cleaned);
    return isNaN(num) ? "0.000000" : num.toFixed(6);
};

// 补 0 格式化（如 .23 → 0.23）
const formatInputValue = (value: string): string => {
    if (value === ".") return "0.";
    if (/^\.\d+$/.test(value)) return `0${value}`;
    return value;
};

const AmountInput: React.FC<AmountInputProps> = ({ defaultValue, onChange,amountStyle, ...rest }) => {
    const [inputValue, setInputValue] = React.useState<string>("0.000000");
    const [rawValue, setRawValue] = React.useState<string>("");

    // ✅ 使用 useEffect 监听 defaultValue 变化
    useEffect(() => {
        if (defaultValue != null) {
            const value = String(defaultValue);
            const fixedValue = formatWithFixed6(value);
            const displayValue = formatWithCommas(fixedValue);
            setInputValue(displayValue);
            setRawValue(displayValue);
        } else {
            setInputValue("");
            setRawValue("");
        }
    }, [defaultValue]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value;

        // ✅ 只在输入以 . 开头时补 0（如 .23 → 0.23）
        if (value === ".") {
            value = "0.";
        } else if (/^\.\d+$/.test(value)) {
            value = `0${value}`;
        }

        setInputValue(value);
        setRawValue(value);
    };

    const handleBlur = () => {
        const parsedValue = parseFloat(parseWithoutCommas(rawValue));
        const isValid = !isNaN(parsedValue);

        if (isValid) {
            let finalValue = parsedValue;

            // ✅ 判断 min 限制
            if (rest.min !== undefined && finalValue < rest.min) {
                finalValue = rest.min;
            }

            // ✅ 判断 max 限制
            if (rest.max !== undefined && finalValue > rest.max) {
                finalValue = rest.max;
            }

            const displayValue = formatWithCommas(finalValue.toFixed(6));
            setInputValue(displayValue);

            if (onChange) {
                onChange(finalValue.toFixed(6));
            }
        } else {
            if (onChange) {
                onChange(""); // 非法输入时返回空
            }
        }
    };

    return (
        <Input
            {...rest}
            style={amountStyle}
            value={inputValue}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="请输入金额"
        />
    );
};

export default AmountInput;
