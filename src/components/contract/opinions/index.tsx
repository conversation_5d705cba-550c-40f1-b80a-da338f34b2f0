import React, { useState } from 'react';

interface Opinion {
    id: number;
    contractId: number;
    processId: number;
    userId: number;
    username: string;
    deptId: number;
    deptName: string;
    advice: string;
    flowNode: string;
    success: boolean;
    createTime: number;
}

interface OpinionsProps {
    opinions: Opinion[];
}

const Opinions: React.FC<OpinionsProps> = (props) => {
    const [viewMode, setViewMode] = useState<'department' | 'time'>('department');

    // 按部门汇总
    const opinionsByDepartment = () => {
        const groupedOpinions = props.opinions.reduce((acc, opinion) => {
            if (!acc[opinion.deptName]) {
                acc[opinion.deptName] = [];
            }
            acc[opinion.deptName].push(opinion);
            return acc;
        }, {} as { [key: string]: Opinion[] });

        return Object.entries(groupedOpinions).map(([deptName, deptOpinions]) => (
            <div key={deptName}>
                <h4>{deptName}</h4>
                {deptOpinions.map(opinion => (
                    <div key={opinion.id}>
                        <p>
                            <strong>[{opinion.flowNode}]</strong> {opinion.username}：{opinion.advice}。
                            {new Date(opinion.createTime).toLocaleString()}
                        </p>
                    </div>
                ))}
            </div>
        ));
    };

    // 按时间汇总
    const opinionsByTime = () => {
        return props.opinions.map(opinion => (
            <div key={opinion.id}>
                <p>
                    <strong>[{opinion.flowNode}]</strong> {opinion.username}：{opinion.advice}。
                    {new Date(opinion.createTime).toLocaleString()}
                </p>
            </div>
        ));
    };

    return (
        <div>
            <div style={{ display: 'flex', marginBottom: '10px' }}>
                <button
                    style={{ marginRight: '10px', padding: '5px 10px', backgroundColor: viewMode === 'department' ? '#ccc' : '#fff', border: 'none', cursor: 'pointer' }}
                    onClick={() => setViewMode('department')}
                >
                    按部门汇总
                </button>
                <button
                    style={{ padding: '5px 10px', backgroundColor: viewMode === 'time' ? '#ccc' : '#fff', border: 'none', cursor: 'pointer' }}
                    onClick={() => setViewMode('time')}
                >
                    按时间汇总
                </button>
            </div>

            <div>
                {viewMode === 'department' ? opinionsByDepartment() : opinionsByTime()}
            </div>
        </div>
    );
};

export default Opinions;