export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

interface File {
  Verdx8CC55M: {
    fileCategory: string;
    fileKey: string;
  }[];
  VerdyoDc3TE: {
    fileCategory: string;
    fileKey: string;
  }[];
}
export interface State {
  authorizeList: any[];

  authorizedSelectData: any;

  selectedAuthorize: any;

  serialNumber: any;

  files: File | {};

  name: string,

  visible: boolean,
  file: any,
}

export interface Api {
  // 授权书列表
  authorizeList: () => Promise<any>;

  // 授权书详情
  authorizeDetail: (params: any) => Promise<any>;

  getSerialNumber: (contractId: any) => Promise<any>;
}
