import {Api} from "@/components/contract/authorization/types";
import {authorizeList,authorizeDetail} from "@/api/contract/authorize";
import {getSerialNumber} from "@/api/contract/contract";

export class ApiImpl implements Api {
    authorizeList(): Promise<any>{
        return authorizeList();
    }

    authorizeDetail(params: any) : Promise<any>{
        return authorizeDetail(params);
    }

    getSerialNumber(contractId: any) : Promise<any>{
        return getSerialNumber(contractId);
    }

}