/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-18 10:48:36
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-15 15:14:01
 * @LastEditors: wangji<PERSON>ao
 */
/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-10 15:37:08
 * @LastEditors: wangjiantao
 */
import {forwardRef, useEffect, useImperativeHandle, useMemo} from "react";
import "../../../pages/contract/authorize/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import {usePresenter} from "@/components/contract/authorization/hooks/usePresenter";
import {Select, Input, Button, Modal} from "antd";
import {Row} from "@itm/components-pc";
import PowerOfAttorney from "@/pages/contract/attorney";
import {downloadOssFile} from "@/utils/download";
import "./index.scss"

interface AuthorizationProps {
    contract: any;
    isEdit: boolean;
    defaultActive?: boolean;
}

export const Authorize = forwardRef((props: AuthorizationProps, ref: any) => {
    const {state, presenter} = usePresenter();

    useImperativeHandle(ref, () => ({
        state: () => {
            return state;
        },
    }));

    useEffect(() => {
        if (!props.contract?.id) return;
        presenter.loadAuthorizeList();
        if (props.isEdit) {
            presenter.getSerialNumber(props.contract.id);
        } else {
            presenter.setSerialNumber(props.contract.serialNumber);
            presenter.setSelectedAuthorize(props.contract.powerOfAttorneyId);
        }
    }, [props.contract]);

    // 授权委托书信息表格数据
    const tableRows = useMemo(() => {
        return [
            {
                label: "合同编号",
                value: (
                    <>
                        {
                            <Input
                                name={"serialNumber"}
                                value={state.serialNumber}
                                onChange={(e) => presenter.setSerialNumber(e.target.value)}
                                disabled={!props.isEdit}
                            />
                        }
                    </>
                ),
                isLabelRed: true,
            },
            {
                label: "授权委托书",
                value: props.isEdit ? (
                    <Row>
                        <Row.Col span={16}>
                            <a onClick={() => downloadOssFile(state.file?.fileUrl)}>
                                {state.file?.fileName}
                            </a>
                        </Row.Col>
                        <Row.Col span={8} style={{textAlign: "right"}}>
                            <Button
                                type="primary"
                                onClick={() => presenter.changeVisible(true)}
                            >
                                选择授权委托书
                            </Button>
                        </Row.Col>
                    </Row>
                ) : (
                    <a onClick={() => downloadOssFile(state.file?.fileUrl)}>
                        {
                            state.authorizeList.find(
                                (i: any) => i.id === props.contract.powerOfAttorneyId
                            )?.fileName
                        }
                    </a>
                ),
                isLabelRed: true,
            },
        ];
    }, [state.serialNumber, state.authorizedSelectData, state.name, state.file]);

    return (
        <div
            style={{
                marginTop: "10px",
                width: "100%",
            }}
        >
            <Modal
                className="flow-modal"
                open={state.visible}
                onCancel={() => presenter.changeVisible(false)}
                onOk={() => presenter.changeVisible(false)}
            >
                <PowerOfAttorney
                    isComponent
                    onSelectedChange={(id, record) => {
                        presenter.setSelectedAuthorize(id, record);
                    }}
                />
            </Modal>
            <CollapseFormTable
                title="授权委托书信息"
                isShow={props.defaultActive ? "1" : "0"}
            >
                <div className={"contract-authorize-table"}>
                    <GenericFormTable isBorder={false} rows={tableRows}/>
                </div>
            </CollapseFormTable>
        </div>
    );
});
