/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 11:55:50
 * @Description:
 * @LastEditTime: 2025-07-18 09:48:48
 * @LastEditors: wangjiantao
 */
import {
  Api,
  Dispatch,
  State,
} from "@/components/contract/authorization/types";

interface selectOption {
  label: string;
  value: string;
  children?: selectOption[];
}

export class Presenter {
  private readonly dispatch: Dispatch<State>;

  private readonly api: Api;

  private state: State;

  public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
    this.dispatch = dispatch;
    this.api = api;
    this.state = state;
  }

  public syncState = (newStatus: State) => {
    this.state = newStatus;
  };

  public loadAuthorizeList = () => {
    this.api.authorizeList().then((res) => {
      this.dispatch((prevState) => {
        const authorizeList = res.data?.list;
        const authorizedSelectData = this.getSelectOptions(authorizeList);
        return {
          ...prevState,
          authorizeList: [...authorizeList],
          authorizedSelectData: [...authorizedSelectData],
        };
      });
    });
  };
  public getSelectOptions = (data: any[]): selectOption[] => {
    return data.map((item) => ({
      label: item.fileName,
      value: item.id.toString(),
    }));
  };

  public getSerialNumber = (contractId: any) => {
    this.api.getSerialNumber(contractId).then((res) => {
      this.dispatch((prevState) => {
        return {
          ...prevState,
          serialNumber: res.data,
        };
      });
    });
  };

  public setSelectedAuthorize = (value: number, record?: any) => {
    
    this.dispatch((prevState) => {
      return {
        ...prevState,
        selectedAuthorize: value,
        file: record,
      };
    });
  };

  public changeVisible(value: boolean) {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        visible: value,
      };
    });
  }

  public setSerialNumber(value: string) {
    this.dispatch((prevState) => {
      return {
        ...prevState,
        serialNumber: value,
      };
    });
  }
}
