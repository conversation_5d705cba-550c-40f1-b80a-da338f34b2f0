import React, { useState } from 'react';
import { Input } from '@itm/components-pc';
interface IEditInfo{
    value: any,
  row: any,
  column: any,
  updateMyData:any,
}
const EditableCell:React.FC<IEditInfo> = (props) => {
    const {
  value: initialValue,
  row: { index },
  column: { id },
  updateMyData, // 这是一个自定义函数，用于更新数据
} = props;
  const [value, setValue] = useState(initialValue);
  const [editable, setEditable] = useState(false);

  const onChange = (e:any) => {
    setValue(e.target.value);
  };

  const onBlur = () => {
    setEditable(false);
    if (initialValue !== value) {
      updateMyData(index, id, value);
    }
  };

  const onClick = () => {
    setEditable(true);
  };

  return editable ? (
    <Input.TextArea
        value={value}
        autoFocus
        onBlur={onBlur}
        onChange={onChange}
        autoSize={{ minRows: 3, maxRows: 5 }}
      />
  ) : (
    <div onClick={onClick}>
      {value || ''}
    </div>
  );
};
export default EditableCell;