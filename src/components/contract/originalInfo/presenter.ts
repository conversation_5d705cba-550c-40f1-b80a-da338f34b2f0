import {Api, Dispatch, State} from "@/components/contract/originalInfo/types";

export class Presenter {
    private readonly dispatch: Dispatch<State>;

    private readonly api: Api;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    };

    public loadContract(contractId: number) {
        this.api.loadContract(contractId).then((res) => {
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    contract: res.data,
                };
            });
        });
    }
}