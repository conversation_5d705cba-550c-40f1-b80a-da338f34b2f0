import {useEffect, useMemo} from "react";
import {usePresenter} from "@/components/contract/originalInfo/hooks/usePresenter";
import CollapseFormTable from "@/components/CollapseFormTable";
import GenericFormTable from "@/components/GenericFormTable";
import './index.scss'
import {formatAmount} from "@/utils/formatAmount";

interface OriginalContractInfoProps {
    originalContractId: number;
    defaultActive?: boolean;
    onChange?: (value: any) => void;
}

const OriginalContractInfo: React.FC<OriginalContractInfoProps> = (props) => {
    // 示例：使用 contractId
    const {state, presenter} = usePresenter();

    useEffect(() => {
        presenter.loadContract(props.originalContractId);
    }, [props.originalContractId]);

    useEffect(() => {
        if (state.contract) {
            props.onChange?.(state.contract)
        }
    }, [state.contract]);

    const rows = useMemo(() => {
        const tableRows: any[] = [];
        tableRows.push(
            {
                label: "原合同名称",
                value: state.contract?.name
            }, {
                label: "合同相对方",
                value: state.contract?.partyBName
            }, {
                label: "合同金额（万元）",
                value: formatAmount(state.contract?.amount)
            }
        )
        return tableRows;
    }, [state.contract]);


    return (
        <CollapseFormTable
            title="原合同信息"
            isShow={props.defaultActive ? "1" : "0"}
            bordered={false}
        >
            <div className={"contract-original-table"}>
                <GenericFormTable isBorder={true} rows={rows}/>
            </div>
        </CollapseFormTable>
    );
};

export default OriginalContractInfo;