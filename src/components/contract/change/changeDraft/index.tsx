import {useEffect, useMemo, useState} from "react";
import CollapseFormTable from "@/components/CollapseFormTable";
import GenericFormTable from "@/components/GenericFormTable";
import AmountInput from "@/components/contract/amount";
import {Input} from "@/components-pc";
import {ContractFileUpload} from "@/components/contract/file/upload";
import './index.scss';

interface RenewalContractInfoProps {
    contractName: string;
    onChange: (value: any) => void;
}

const RenewalContractInfoDraft: React.FC<RenewalContractInfoProps> = (props) => {

    const [currentTime, setCurrentTime] = useState(new Date()); // 新增：存储当前时间的状态
    const [renewalName, setRenewalName] = useState<string>("");
    const [amount, setAmount] = useState<number>(0);
    const [reason, setReason] = useState<string>("");
    const [attachments, setAttachments] = useState<string>("");

    const [contract, setContract] = useState<any>(null);

    const currentUserName = localStorage.getItem("usernickname");
    const currentUserDeptName = localStorage.getItem("userdeptname");

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        // 组件卸载时清除定时器
        return () => {
            clearInterval(timer);
        };
    }, []);

    useEffect(() => {
        if (props.contractName) {
            setRenewalName(props.contractName + "（续签）")
        }
    }, [props.contractName]);

    // 使用 useMemo 根据 contract 构建数据
    const rows = useMemo(() => {
        const tableRows: any[] = [];
        tableRows.push(
            {
                label: "变更合同名称",
                value: <Input
                    style={{border: 'none'}}
                    value={renewalName}
                    onChange={(e) => {
                        if (e.target.value && e.target.value.trim() !== "") {
                            setRenewalName(e.target.value)
                        }
                    }}
                />
            }, {
                label: "合同金额（万元）",
                value: <AmountInput
                    name="amount"
                    amountStyle={{border: 'none'}}
                    onChange={(value) => {
                        setAmount(Number(value))
                    }}/>
            }, {
                label: "变更事由",
                value: <Input style={{
                    height: "120px",
                    marginTop: "5px",
                    marginBottom: "5px",
                    border: 'none'
                }}
                              onChange={(e) => {
                                  setReason(e.target.value)
                              }}
                              placeholder="请输入变更事由" name="reason"/>
            }, {
                label: "变更附件",
                value: (
                    <div className="file-upload-container">
                        <ContractFileUpload
                            maxCount={5}
                            fileType="image/jpeg,image/jpg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            bucket="document"
                            onChange={(value) => {
                                if (value) {
                                    setAttachments(value);
                                }
                            }}
                        />
                    </div>
                )
            }
        )
        return tableRows;
    }, [renewalName]);

    const formatAttachments = (attachments: string) => {
        if (!attachments) {
            return [];
        }
        return attachments.split(",").map((item) => {
            return {
                fileKey: item,
                fileCategory: 'Vfbf1pbfWTI',
            };
        });
    };

    const contractData =
        useMemo(() => {
            return {
                name: renewalName,
                amount: amount,
                reason: reason,
                attachments: formatAttachments(attachments),
            }
        }, [renewalName, amount, reason, attachments]);

    useEffect(() => {
        if (contractData && contractData.name) {
            setContract(contractData)
        }
    }, [contractData]);

    useEffect(() => {
        if (contract) {
            props.onChange?.(contract)
        }
    }, [contract]);

    return (
        <CollapseFormTable title="合同续签信息" isShow={"1"} bordered={false}>
            <div className="contract-f">
                <div className="contract-info-row">
                    <div className="contract-info-item">
                        <div className="contract-info-label">发起人：</div>
                        <div className="contract-info-separator"></div>
                        {/* 竖线分隔符 */}
                        <div className="contract-info-value">{`${currentUserDeptName}/${currentUserName}`}</div>
                    </div>
                    <div className="contract-info-separator"></div>
                    {/* 竖线分隔符 */}
                    <div className="contract-info-item">
                        <div className="contract-info-label">发起时间：</div>
                        <div className="contract-info-separator"></div>
                        {/* 竖线分隔符 */}
                        <div className="contract-info-value">{currentTime.toLocaleString()}</div>
                    </div>
                </div>
            </div>
            <div className="contract-renewal-table">
                <GenericFormTable isBorder={true} rows={rows}/>
            </div>
        </CollapseFormTable>
    );
};

export default RenewalContractInfoDraft;