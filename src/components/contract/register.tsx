/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-07 10:39:22
 * @Description:
 * @LastEditTime: 2025-07-11 10:39:49
 * @LastEditors: wangjiantao
 */
import { ComponentBus } from "@codingapi/ui-framework";
import ContractDraft from "@/pages/contract/draft";
import ContractReview from "@/pages/contract/review";
import ContractAuthorize from "@/pages/contract/authorize";
import PerformancePlanPage from "@/pages/contract/performance/plan";
import PerformanceRecordPage from "@/pages/contract/performance/record";
import AcceptancePage from "@/pages/contract/acceptance";
ComponentBus.getInstance().registerComponent("ContractDraft", ContractDraft);
ComponentBus.getInstance().registerComponent("ContractReview", ContractReview);
ComponentBus.getInstance().registerComponent("ContractPlan", PerformancePlanPage);
ComponentBus.getInstance().registerComponent("PerformanceRecord", PerformanceRecordPage);
ComponentBus.getInstance().registerComponent("AcceptancePage", AcceptancePage);
ComponentBus.getInstance().registerComponent(
  "ContractAuthorize",
  ContractAuthorize
);
