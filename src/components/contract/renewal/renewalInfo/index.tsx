import {useEffect, useMemo, useState} from "react";
import {queryContractById} from "@/api/contract/contract";
import {formatAmount} from "@/utils/formatAmount";
import CollapseFormTable from "@/components/CollapseFormTable";
import GenericFormTable from "@/components/GenericFormTable";
import {Input} from "antd";
import FileDisplay from "@/components/contract/file/show";
import Opinions from "@/components/contract/opinions";

interface RenewalContractInfoReviewProps {
    contract: any;
    defaultActive?: boolean;
}

const RenewalContractInfo: React.FC<RenewalContractInfoReviewProps> = (props) => {

    const rows = useMemo(() => {
        const tableRows: any[] = [];
        tableRows.push(
            {
                label: "续签合同名称",
                value: props.contract?.name
            }, {
                label: "合同金额（万元）",
                value: formatAmount(props.contract?.amount)
            }, {
                label: "续签事由",
                value: props.contract?.reason
            }, {
                label: "续签附件",
                value: props.contract?.attachments?.length > 0 ? (
                    <div>
                        {props.contract?.attachments.map((attachment: any, index: number) => (
                            <div key={index} style={{marginBottom: '10px'}}>
                                <FileDisplay files={[{
                                    name: attachment.fileName,
                                    fileKey: attachment.fileKey,
                                    fileUrl: attachment.fileUrl
                                }]}/>
                            </div>
                        ))}
                    </div>
                ) : "暂无附件"
            }, {
                label: "已办意见",
                value: props.contract?.opinions?.length > 0 ? (
                    <Opinions opinions={props.contract?.opinions}/>
                ) : ""
            }
        )
        return tableRows;
    }, [props.contract]);

    return (
        <CollapseFormTable title="合同续签信息" isShow={props.defaultActive ? "1" : "0"} bordered={false}>
            <div className="contract-f">
                <div className="contract-info-row">
                    <div className="contract-info-item">
                        <div className="contract-info-label">发起人：</div>
                        <div className="contract-info-separator"></div>
                        {/* 竖线分隔符 */}
                        <div
                            className="contract-info-value">{`${props.contract?.drafterDeptName}/${props.contract?.drafterName}`}</div>
                    </div>
                    <div className="contract-info-separator"></div>
                    {/* 竖线分隔符 */}
                    <div className="contract-info-item">
                        <div className="contract-info-label">发起时间：</div>
                        <div className="contract-info-separator"></div>
                        {/* 竖线分隔符 */}
                        <div className="contract-info-value">{props?.contract?.draftTime}</div>
                    </div>
                </div>
            </div>
            <div className="contract-renewal-table">
                <GenericFormTable isBorder={true} rows={rows}/>
            </div>
        </CollapseFormTable>
    );
};
export default RenewalContractInfo;