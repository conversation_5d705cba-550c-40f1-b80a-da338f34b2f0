/* ./index.scss */

.contract-f {
    display: flex;
    align-items: center;
    border-top: 1px solid red;
}

.contract-info-row {
    display: flex;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    flex-wrap: wrap; /* 允许换行 */
}

.contract-info-item {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 16px; /* 项目之间的间距 */
}

.contract-info-label {
    width: 100px; /* 标签固定宽度 */
    text-align: center;
    font-weight: bold;
    box-sizing: border-box;
    color: red;
}

.contract-info-value {
    flex: 1; /* 值占剩余空间 */
    box-sizing: border-box;
    text-align: center;
}

.contract-info-separator {
    width: 1px; /* 竖线宽度 */
    height: 24px; /* 竖线高度 */
    background-color: red;
    margin: 0 16px; /* 竖线左右间距 */
}

.file-upload-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%; /* 确保容器高度为父元素的100% */
}
.contract-renewal-table{
    .form-label {
        color: red !important;
        font-weight: bold;
        border-right: 1px solid red !important;
        border-top: 1px solid red !important;
    }

    .form-value{
        border-top: 1px solid red !important;
    }

    .form-table td {
        border: 0;
    }

    .form-table_top_border {
        border-top: 0 !important;
        border-bottom: 0 !important;
        border-left: 0 !important;
        border-right: 0 !important;
    }
}