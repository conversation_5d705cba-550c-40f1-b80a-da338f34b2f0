/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 19:57:21
 * @Description:
 * @LastEditTime: 2025-07-11 09:46:35
 * @LastEditors: wangji<PERSON><PERSON>
 */
import { Api } from "./types";
import {
  loadPerformancePlanList,
  savePerformancePlan,
  saveOnePlan,
  delPlan,
  loadPerformanceRecordList,
} from "@/api/contract/performance";

export class ApiImpl implements Api {
  loadPerformancePlanList(contractId: number): Promise<any> {
    return loadPerformancePlanList(contractId);
  }

  loadPerformanceRecordList(contractId: number): Promise<any> {
    return loadPerformanceRecordList(contractId);
  }
}
