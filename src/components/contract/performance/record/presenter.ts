/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 19:57:21
 * @Description:
 * @LastEditTime: 2025-07-18 10:45:56
 * @LastEditors: wangjiantao
 */
import { Api, Dispatch, State } from "./types";
import moment, { Moment } from "moment";

export class Presenter {
  private readonly dispatch: Dispatch<State>;

  private readonly api: Api;

  private state: State;

  public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
    this.dispatch = dispatch;
    this.api = api;
    this.state = state;
  }

  public syncState = (newStatus: State) => {
    this.state = newStatus;
  };

  public loadPerformancePlanList = (contractId: number) => {
    this.api.loadPerformancePlanList(contractId).then((res) => {
      const formData: any[] = [];
      this.dispatch((prevState) => {
        const list = res.data?.list || [];
        list.forEach((item: any, index: number) => {
          item.serialNumber = index;
          formData.push({
            content: "",
            finishDate: "",
            paymentTerms: false,
            attachments: "",
          });
        });
        return {
          ...prevState,
          performancePlanList: res.data?.list || [],
          formData: prevState.formData?.length
            ? prevState.formData
            : [...formData],
        };
      });
    });
  };

  public onFormChange = (
    key: keyof {
      content: string;
      finishDate: string;
      paymentTerms: boolean;
      attachments: string;
    },
    value: string | number,
    index: number
  ) => {
    this.dispatch((prevState) => {
      const formData = prevState.formData;
      (formData[index] as any)[key] = value;
      return {
        ...prevState,
        formData,
      };
    });
  };

  public loadPerformanceRecordList = (contractId: number) => {
    this.api.loadPerformanceRecordList(contractId).then((res) => {
      const list = res.data?.list || [];
      if (!list.length) return;
      this.dispatch((prevState) => {
        return {
          ...prevState,
          formData: [...list],
        };
      });
    });
  };

  public onCheckChange = (value: any, index: number) => {
    this.dispatch((prevState) => {
      const formData = prevState.formData;
      formData[index].paymentTerms = value;
      return {
        ...prevState,
        formData: [...formData],
      };
    });
  };

    public onInputChange = (value: any, index: number) => {
    this.dispatch((prevState) => {
      const formData = prevState.formData;
      formData[index].content = value;
      return {
        ...prevState,
        formData: [...formData],
      };
    });
  };
}
