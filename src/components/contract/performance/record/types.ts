export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface State {
  contractId: number;
  performancePlanList: any[];
  formData: {
    content: string;
    finishDate: string;
    paymentTerms: '0' | '1';
    attachments: string |any[];
  }[];
}

export interface Api {
  loadPerformancePlanList: (contractId: number) => Promise<any>;
  loadPerformanceRecordList: (contractId: number) => Promise<any>;
}
