import React, {
  forwardRef,
  useImperativeHandle,
  useEffect,
  Fragment,
  useMemo,
} from "react";
import "../../../../pages/contract/authorize/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import {
  Button,
  Checkbox,
  RadioGroup,
  CheckboxGroup,
  DatePicker,
  Input,
  Space,
} from "@itm/components-pc";
import type { CheckboxGroupProps } from "antd/es/checkbox";
import { usePresenter } from "./hooks/usePresenter";
import OssFileUploader from "@/components/oss/OssFileUpload";
import { Form } from "antd";
import dayjs from "dayjs";

interface PerformanceRecordProps {
  defaultActive?: boolean;
  contractId?: number;
  amount?: number;
  isEdit?: boolean;
}

export const PerformanceRecord = forwardRef(
  (props: PerformanceRecordProps, ref: any) => {
    const { state, presenter } = usePresenter();

    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      form,
      state,
    }));

    useEffect(() => {
      presenter.loadPerformancePlanList(props.contractId!);
      presenter.loadPerformanceRecordList(props.contractId!);
    }, []);

    const options: CheckboxGroupProps<number>["options"] = [
      { label: "是", value: 1 },
      { label: "否", value: 2 },
    ];

    const { isEdit } = props;

    const rows = useMemo(() => {
      const tableRowSituation: any[] = [];

      state.performancePlanList.forEach((item, index) =>
        tableRowSituation.push({
          text: `阶段${index + 1}`,
          table: [
            {
              label: "履行计划",
              children: [
                {
                  label: "合同约定履行内容",
                  width: "200px",
                  value: item.content,
                },
                {
                  label: "预计完成时间",
                  width: "200px",
                  value: item.planCompletionDate,
                },
                {
                  label: "预计支付金额(万元)",
                  width: "200px",
                  value: Number(props.amount || 0) * (Number(item.ratio) / 100),
                },
              ],
              isLabelRed: true,
            },
            {
              label: "实际履行情况",
              children: [
                {
                  label: "合同实际履行内容",

                  width: "200px",
                  value: isEdit ? (
                    <>
                      <Input.TextArea
                        onChange={(e) =>
                          presenter.onInputChange(e.target.value, index)
                        }
                        value={state.formData[index]?.content}
                        style={{ width: "100%" }}
                        placeholder="请输入合同实际履行内容"
                        rows={2}
                      />
                    </>
                  ) : (
                    <span>{state.formData[index]?.content}</span>
                  ),
                },
                {
                  label: "实际完成时间",
                  width: "200px",
                  value: isEdit ? (
                    <>
                      <DatePicker
                        value={
                          state.formData[index]?.finishDate
                            ? dayjs(state.formData[index]?.finishDate)
                            : undefined
                        }
                        onChange={(_, dateString) =>
                          presenter.onFormChange(
                            "finishDate",
                            dateString as string,
                            index
                          )
                        }
                        placeholder="请选择日期"
                        style={{ width: "100%" }}
                      />
                    </>
                  ) : (
                    <span>{state.formData[index]?.finishDate}</span>
                  ),
                  isLabelRed: true,
                },
                {
                  label: "是否满足支付条件",
                  width: "200px",
                  value: isEdit ? (
                    <RadioGroup
                      value={state.formData[index]?.paymentTerms}
                      onChange={(e) =>
                        presenter.onCheckChange(e.target.value, index)
                      }
                      options={options}
                    />
                  ) : (
                    <span>
                      {state.formData[index]?.paymentTerms ? "是" : "否"}
                    </span>
                  ),
                  isLabelRed: true,
                },
                {
                  label: "附件资料",
                  width: "200px",
                  value: isEdit ? (
                    <OssFileUploader
                      label=""
                      uploaderMaxCount={5}
                      bucket="document"
                      uploaderAccept="application/pdf"
                      ossFileKey={
                        form.getFieldValue("attachments") ||
                        state.formData[index]?.attachments ||
                        ""
                      }
                      onKeyChange={(keys) => {
                        form.setFieldValue("attachments", keys);
                        presenter.onFormChange("attachments", keys, index);
                      }}
                    />
                  ) : (
                    <Space direction="vertical">
                      {Array.isArray(state.formData[index]?.attachments)
                        ? state.formData[index]?.attachments.map((i: any) => (
                            <a key={i.fileName}>{i.fileName}</a>
                          ))
                        : null}
                    </Space>
                  ),
                  isLabelRed: true,
                },
                {
                  label: "流程启动",
                  width: "200px",
                  value: (
                    <Space>
                      <Button>函件起草</Button>
                      <Button>资金支付</Button>
                      <Button>纠纷处理</Button>
                    </Space>
                  ),
                  isLabelRed: true,
                },
              ],
              isLabelRed: true,
            },
          ],
        })
      );
      return tableRowSituation;
    }, [state.formData, props.amount]);

    return (
      <CollapseFormTable
        title="合同履行情况"
        isShow={props.defaultActive ? "1" : "0"}
      >
        {/*  */}
        {rows.map((item, index) => (
          <Fragment key={index}>
            <div
              style={{
                height: "40px",
                fontSize: "23px",
                lineHeight: "40px",
                color: "red",
                borderTop: "2px solid red",
                fontWeight: "bold",
              }}
            >
              {item.text}
            </div>
            <GenericFormTable isBorder={true} rows={item.table} />
          </Fragment>
        ))}
      </CollapseFormTable>
    );
  }
);
