import {
    Api,
    Dispatch,
    State,
} from "@/components/contract/performance/plan/types";
import moment, {Moment} from "moment";

export class Presenter {
    private readonly dispatch: Dispatch<State>;

    private readonly api: Api;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: Api, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    };

    public loadPerformancePlanList = (contractId: number) => {
        this.api.loadPerformancePlanList(contractId).then((res) => {
            this.dispatch((prevState) => {
                const list = res.data?.list || [];
                list.forEach((item: any, index: number) => {
                    item.serialNumber = index;
                });
                return {
                    ...prevState,
                    performancePlanList: res.data?.list,
                };
            });
        });
    };

    public addNewPerformancePlan(newData: any) {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                performancePlanList: [...prevState.performancePlanList, newData],
            };
        });
    }

    public setContractId(contractId: number) {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                contractId: contractId,
            };
        });
    }

    public deletePerformancePlan(id: number) {
        this.api.deletePerformancePlan(id).then((res) => {
            this.loadPerformancePlanList(this.state.contractId);
        });
    }

    public saveAllPlan(saveData: any) {
        console.log("saveData:", saveData);
    }
}
