import {Api} from "@/components/contract/performance/plan/types";
import {loadPerformancePlanList, savePerformancePlan, saveOnePlan,delPlan} from "@/api/contract/performance";

export class ApiImpl implements Api {
    deletePerformancePlan(id: number): Promise<any> {
        return delPlan(id);
    };

    loadPerformancePlanList(contractId: number): Promise<any> {
        return loadPerformancePlanList(contractId);
    }

    savePerformancePlan(params: any): Promise<any> {
        return savePerformancePlan(params)
    }

    savePlan(params: any): Promise<any> {
        return saveOnePlan(params)
    }
}