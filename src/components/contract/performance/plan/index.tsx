import React, {
  useState,
  forwardRef,
  useEffect,
  memo,
  useImperativeHandle,
  useRef,
} from "react";
import CollapseFormTable from "@/components/CollapseFormTable";
import { Space } from "@itm/components-pc";
import {
  ActionType,
  EditableProTable,
  ProColumns,
} from "@ant-design/pro-components";
import { Button } from "@itm/components-pc";
import "../../../../pages/contract/performance/plan/index.scss";
import { Form, Input, InputNumber } from "antd";
import { message } from "antd";
import { usePresenter } from "@/components/contract/performance/plan/hooks/usePresenter";
import { Moment } from "moment";

interface PerformanceProps {
  contractId: number;
  defaultActive?: boolean;
  noticeId?: number;
  isEdit?: boolean;
  onChange?: (data: any) => void;
}

type PerformancePlanTableData = {
  id?: number;

  name: string;

  content: string;

  planCompletionDate: Moment | null;

  needToAccept: boolean; // 是否启动验收

  needToPay: boolean; // 是否存在资金支付

  perform?: boolean;

  serialNumber: number;
};

// 定义组件
export const PerformancePlan = memo(
  forwardRef((props: PerformanceProps, ref) => {
    const { state, presenter } = usePresenter();
    const [dataSource, setDataSource] = useState<PerformancePlanTableData[]>(
      []
    );
    const actionRef = useRef<ActionType>();
    const [form] = Form.useForm();
    const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

    // 暴露表单方法给父组件
    useImperativeHandle(ref, () => ({
      form,
    }));

    // 初始化数据
    useEffect(() => {
      presenter.setContractId(props.contractId);
      if (!props.contractId) return;
      presenter.loadPerformancePlanList(props.contractId);
    }, [props.contractId]);

    // 同步数据到 dataSource
    useEffect(() => {
      if (state.performancePlanList.length > 0) {
        setDataSource([...state.performancePlanList]);
        setEditableRowKeys(
          state.performancePlanList
            .filter((item) => !item.perform)
            .map((item) => item.serialNumber)
        );
      }
    }, [state.performancePlanList]);

    // 新增数据
    const handleAddData = () => {
      const newRecord = {
        id: Date.now(),
        name: `阶段 ${dataSource.length + 1}`,
        content: "",
        planCompletionDate: null,
        needToAccept: false,
        needToPay: false,
        perform: false,
        serialNumber: dataSource.length,
      };

      // 更新数据源
      const newData = [...dataSource, newRecord];
      setDataSource(newData);

      // 设置新行为可编辑状态
      setEditableRowKeys([...editableKeys, newRecord.serialNumber]);

      // 如果需要立即编辑新行
      actionRef.current?.startEditable?.(newRecord.id);
    };

    // 表格列定义 - 更新复选框列
    const columns: ProColumns<PerformancePlanTableData>[] = [
      {
        title: "阶段",
        dataIndex: "name",
        fixed: "left",

        render: (_: any, record: any, index: number) => `阶段${index + 1}`,
        editable: false,
        width: "15%",
        align: "center",
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
      },
      {
        title: "合同约定履行内容",
        dataIndex: "content",
        align: "center",
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
        renderFormItem: () => {
          return (
            <Input.TextArea placeholder="请输入补充说明" maxLength={200} />
          );
        },
      },
      {
        title: "预计完成时间",
        dataIndex: "planCompletionDate",
        align: "center",
        valueType: "date",
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
      },
      {
        title: "是否启动验收",
        key: "needToAccept",
        dataIndex: "needToAccept",
        valueType: "radio",
        initialValue: (record: any) => record.needToAccept ?? false,
        fieldProps: {
          // 添加 onChange 处理
          onChange: (e: any) => {
            const { rowKey, form } = e;
            form?.setFieldsValue({ [rowKey]: e.target.value });
          },
        },
        align: "center",
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
        valueEnum: {
          true: "是",
          false: "否",
        },
      },
      {
        title: "是否存在资金支付",
        key: "needToPay",
        dataIndex: "needToPay",
        valueType: "radio",
        initialValue: (record: any) => record.needToPay ?? false,
        fieldProps: {
          // 添加 onChange 处理
          onChange: (e: any) => {
            const { rowKey, form } = e;
            form?.setFieldsValue({ [rowKey]: e.target.value });
          },
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
        valueEnum: {
          true: "是",
          false: "否",
        },
        align: "center",
      },
      {
        title: "支付比例",
        dataIndex: "ratio",
        formItemProps: {
          rules: [
            {
              required: true,
              message: "此项为必填项",
            },
          ],
        },
        fieldProps: (form: any, props: any) => {
          const { rowKey, rowIndex } = props;
          const value = form.getFieldValue([rowKey || "", "title"]);
        },
        render: (value: any) => `${value}%`,
        align: "center",
        renderFormItem: () => {
          return <InputNumber max={100} min={0} step={1} />;
        },
      },
      {
        title: "操作",
        valueType: "option",
        width: 150,
        hidden: true,
        fixed: "right",
        render: (text: any, record: any, _: any, action: any) => (
          <Space>
            <a
              key="editable"
              onClick={() => {
                action?.startEditable?.(record.id);
              }}
            >
              编辑
            </a>
            <a
              key="delete"
              onClick={() => {
                presenter.deletePerformancePlan(record.id);
              }}
            >
              删除
            </a>
            ,
          </Space>
        ),
        align: "center",
      },
    ];

    return (
      <div style={{ marginTop: "10px", width: "100%" }}>
        <CollapseFormTable
          title="合同履行计划"
          isShow={props.defaultActive ? "1" : "0"}
        >
          {props.isEdit && (
            <div style={{ padding: "10px", borderTop: "2px solid red" }}>
              <Button type="primary" onClick={handleAddData}>
                新增
              </Button>
            </div>
          )}

          <EditableProTable<PerformancePlanTableData>
            rowKey="id"
            actionRef={actionRef}
            scroll={{ x: 960 }}
            recordCreatorProps={false}
            loading={false}
            columns={columns}
            value={dataSource}
            editable={{
              form,
              type: "multiple",
              editableKeys,
              onChange: setEditableRowKeys,
              onValuesChange: (_, recordList) => {
                setDataSource(recordList);
              },
              actionRender: () => [], // 隐藏默认操作按钮
            }}
          />
        </CollapseFormTable>
      </div>
    );
  })
);
