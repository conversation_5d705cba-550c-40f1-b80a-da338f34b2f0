export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface State {
    contractId: number;
    performancePlanList: any[];
}

export interface Api {
    loadPerformancePlanList: (contractId: number) => Promise<any>;

    savePerformancePlan: (params: any) => Promise<any>;

    savePlan: (params: any) => Promise<any>;

    deletePerformancePlan:(id: number)=> Promise<any>;
}