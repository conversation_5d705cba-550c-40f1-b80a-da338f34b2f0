import React from "react";
import {OssFileState} from "@/components/oss/OssFileUpload/types";
import {ValuePresenter} from "@/components/oss/OssFileUpload/presenter";
import {OssFileApiImpl} from "@/components/oss/OssFileUpload/model";

const initialState: OssFileState = {
    uploadedFile: {filename: "", base64: ""},
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<OssFileState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new OssFileApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}