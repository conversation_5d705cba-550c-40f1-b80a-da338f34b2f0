import {OssFileApi, OssFileState, Dispatch} from "@/components/oss/OssFileUpload/types";
import {base64ToBlob2} from "@/utils/base64";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<OssFileState>;

    private readonly api: OssFileApi;

    private state: OssFileState;

    public constructor(dispatch: Dispatch<OssFileState>, api: OssFileApi, state: OssFileState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: OssFileState) => {
        this.state = newStatus;
    }
    // 在 presenter.ts 中明确返回类型
    public addUploadFile = async (filename: string, base64: string, bucket: string): Promise<string> => {
        try {
            const formData = new FormData();
            const blob = base64ToBlob2(base64);
            const file = new File([blob], filename, {type: blob.type});
            formData.append("file", file);
            formData.append("bucket", bucket);

            const res = await this.api.updateOssFile(formData);

            if (res?.data) {
                return res.data.key;
            }
            throw new Error('上传失败：未获取到文件key');
        } catch (error) {
            console.error('文件上传失败:', error);
            throw error;
        }
    }

    public generateFileAuth = async (fileKey: string): Promise<string> => {
        try {
            const params = {
                fileKey: fileKey
            };

            const res = await this.api.generateFileAuth(params);

            if (res?.data?.code) {
                return res.data;
            }
            throw new Error('生成文件授权码失败：未获取到授权码');
        } catch (error) {
            console.error('生成文件授权码失败:', error);
            throw error;
        }
    }

}