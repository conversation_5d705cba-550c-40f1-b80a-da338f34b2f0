/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description:
 * @LastEditTime: 2025-07-11 15:02:03
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React, {FC} from "react";

import {Form, FormUploader} from "@/components/form-pc";
import {useValuePresenter} from "@/components/oss/OssFileUpload/hooks/usePresenter";

interface OssFileUploaderProps {
    disabled?: boolean;//是否禁用
    label: string;//展示的表单字段名称
    uploaderMaxCount: number;//最大上传个数
    uploaderAccept: string;//上传文件后缀
    bucket: string;//上传桶
    ossFileKey: string;//文件key,多个逗号分隔
    onKeyChange?: (key: string) => void;//回调，上传完成时返回文件key，多个逗号分隔返回
    showUploadList?: boolean;
}

const OssFileUploader: FC<OssFileUploaderProps> = (props) => {
    const {state, presenter} = useValuePresenter();
    const form = Form.useForm();
    React.useEffect(() => {
        form.reset()
        form.setFieldsValue({
            OssFile: {
                key: props.ossFileKey
            }
        });
    }, [props.ossFileKey]);

    return (
        <Form
            form={form}
            layout={"vertical"}
        >
            <FormUploader
                itemProps={{showUploadList: props.showUploadList, multiple:true}}
                name={["OssFile", "key"]}
                required={false}
                label={props.label}
                disabled={props.disabled || false}
                uploaderMaxCount={props.uploaderMaxCount}
                uploaderAccept={props.uploaderAccept}
                onUploaderUpload={async (filename: string, base64: string) => {
                    const key = await presenter?.addUploadFile(filename, base64, props.bucket);
                    return {
                        id: key as string,
                        name: filename,
                        url: base64
                    };
                }}
                onUploaderLoad={async (ids) => {
                    if (ids && props.onKeyChange && ids != props.ossFileKey) {
                        props.onKeyChange(ids);
                    }
                    try {
                        const idList = props.ossFileKey.split(',')
                        const result = await Promise.all(
                            idList.map(async (id) => {
                                if (id == null || id == ""){
                                    return null;
                                }
                                const fileAuth = await presenter?.generateFileAuth(id);
                                return {
                                    id: id,
                                    name: (fileAuth as any)?.name,
                                    url: `/itm-oss/open/file/download?code=${(fileAuth as any)?.code}`
                                };
                            })
                        );
                        return result.filter((item): item is NonNullable<typeof item> => item !== null);
                    } catch (error) {
                        console.error('加载文件列表失败:', error);
                        return [];
                    }
                }}
            />
        </Form>)
}

export default OssFileUploader;