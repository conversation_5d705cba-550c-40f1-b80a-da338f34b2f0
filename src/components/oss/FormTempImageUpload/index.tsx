import React, {FC, useEffect, useState} from "react";
import {useSysFiletPresenter} from "@/pages/oss/sys/hooks/useSysFilePresenter";
import {RcFile} from "antd/es/upload/interface";
import {Message,Upload} from "@itm/components-pc";
import {base64ToBlob2} from "@/utils/base64";

interface FormTempImageUploaderProps {
    value?: string;
    onChange?: (id: string) => void;
}

const Index: FC<FormTempImageUploaderProps> = ({value, onChange}) => {
    const {presenter} = useSysFiletPresenter();
    const [fileList, setFileList] = useState<any[]>([]);

    // 加载已有图片
    useEffect(() => {
        const loadFile = async () => {
            if (value) {
                try {
                    const res = await presenter?.loadSysFiles(value);
                    const fileUrl = (res as any)?.data?.fileUrl;
                    setFileList([
                        {
                            uid: value,
                            name: value,
                            url: fileUrl,
                            status: "done",
                        },
                    ]);
                } catch (e) {
                    Message.error("加载图片失败");
                }
            }
        };
        loadFile();
    }, [value]);

    // 处理上传
    const onUpload = async (file: File) => {
        const base64 = await new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
        });

        const blob = base64ToBlob2(base64);
        const newFile = new File([blob], file.name, {type: blob.type});
        const formData = new FormData();
        formData.append("file", newFile);
        formData.append("bucket", "temp");

        try {
            const res = await presenter?.uploadSysFile(formData);
            const key = (res as any)?.data?.key;
            if (key) {
                onChange?.(key);
                setFileList([
                    {
                        uid: key,
                        name: key,
                        url: base64,
                        status: "done",
                    },
                ]);
            }
        } catch (error) {
            Message.error("上传失败");
        }

        return false;
    };

    const beforeUpload = (file: RcFile, fileList: RcFile[]) => {
        onUpload(file as File);
        return false; // 阻止默认上传行为
    };

    return (
        <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            customRequest={({onSuccess}) => {
                setTimeout(() => {
                    onSuccess?.("ok");
                }, 0);
            }}
            onRemove={() => {
                setFileList([]);
                onChange?.("");
            }}
        >
            {fileList.length < 1 && "点击上传"}
        </Upload>
    );


    const customRequest = ({onSuccess}: any) => {
        setTimeout(() => {
            onSuccess?.("ok");
        }, 0);
    };

    return (
        <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            customRequest={customRequest}
            onRemove={() => {
                setFileList([]);
                onChange?.("");
            }}
        >
            {fileList.length < 1 && "点击上传"}
        </Upload>
    );
};


export default Index;
