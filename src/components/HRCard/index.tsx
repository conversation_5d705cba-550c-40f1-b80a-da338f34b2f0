import React from "react";
import { ProCard } from "@ant-design/pro-components";
import type { CardProps } from "@ant-design/pro-card/es/typing";
import "./index.scss";

interface HRCardProps {
    children?: React.ReactNode,
    cardProps?: CardProps,
    className?: any
}

const HRCard: React.FC<HRCardProps> = (props) => {

    return (
        <ProCard
            className={`${props.className} hr-page-card`}
            {...props.cardProps}
        >
            {props.children}
        </ProCard>
    )
}

export default HRCard;
