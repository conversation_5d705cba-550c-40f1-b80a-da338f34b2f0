.hr-page-card {
  height: calc(100vh - 82px);
  margin-top: 10px;
  overflow: auto;

  .ant-tabs-nav {
    padding: 0 20px
  }

  .ant-pro-card-body {
    padding: 0;
  }
}
.ant-modal-body {
  .hr-page-card {
    margin-top: 10px;
    height: 100%;
    overflow: hidden;
    .ant-tree-list {
      height: 60vh;
      overflow-y: auto;
    }
  }
}
.HRCard-tree-browse {
  .ant-tree-list {
    height: 78vh;
    overflow-y: auto;
  }
}
.HRCard-tree {
  .ant-pro-card-body {
    padding: 20px;
  }
  .ant-tree-list {
    height: 80vh;
    overflow-y: auto;
  }
  .ant-tree .ant-tree-treenode {
    width: 100% !important;
    display: flex;
    .ant-tree-title {
      width: 100% !important;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      display: -webkit-box; /* 使用 Flexbox */
      -webkit-box-orient: vertical; /* 垂直方向布局 */
      overflow: hidden;
      text-overflow: ellipsis; /* 显示省略号 */
    }
  }
}

.HRCard-org-table {
  position: relative;
  .ant-pro-table-alert {
    top: 10px;
    right: auto;
    bottom: auto;
    left: auto;
    border: none;
  }
  .ant-spin-container {
    .ant-table {
      height: 75vh;
      overflow: hidden;
      .ant-table-body {
        height: 68vh;
      }
    }
    .ant-table-placeholder {
      height: 611px;
    }
    .ant-pagination {
      margin: 0;
    }
  }
}
.changeWarning-table {
  position: relative;
  .ant-pro-table-alert {
    position: absolute;
    top: 10px;
    right: auto;
    bottom: auto;
    left: auto;
    border: none;
  }
  .ant-spin-container {
    .ant-table {
      height: 81vh;
      overflow: hidden;
      .ant-table-body {
        height: 75vh;
      }
    }
    .ant-table-placeholder {
      height: 670px;
    }
    .ant-pagination {
      margin: 0;
    }
  }
}

.search-tree-select {
  position: relative;
  z-index: 10;
}

.employee-table {
  .ant-spin-container {
    .ant-table {
      height: 75vh;
      overflow: hidden;
      .ant-table-body {
        height: 68vh;
      }
    }
    .ant-table-placeholder {
      height: 611px;
    }
    .ant-pagination {
      margin: 0;
    }
  }
}

.employee-table-delay {
  .ant-spin-container {
    .ant-table {
      height: 75vh;
      overflow: hidden;
      .ant-table-body {
        height: 68vh;
      }
    }
    .ant-table-content {
      overflow: auto;
    }
    .ant-table-placeholder {
      height: 611px;
    }
    .ant-pagination {
      margin: 0;
    }
  }
}
.employee-cont-page {
  height: 88vh;
  overflow: hidden;
  .hr-header {
    padding-bottom: 0;
  }
  .employee-table {
    .ant-spin-container {
      height: 78vh !important;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: normal;
      flex-wrap: nowrap;
      .ant-table-body {
        height: 69vh;
      }
    }
    .ant-table-placeholder {
      height: 626px;
    }
  }
}
// 去掉列表上方选择内容
.table-alert-none {
  .ant-pro-table-alert {
    display: none;
  }
}

// 预警中心table
.warn-table {
  .ant-spin-container {
    height: 73vh !important;
    .ant-table {
      height: 72vh !important;
      overflow: hidden;
      .ant-table-body {
        height: 64vh;
      }
    }
    .ant-table-placeholder {
      height: 611px;
    }
    .ant-pagination {
      margin: 0;
    }
  }
}
//兼职信息管理
.HRCard-include-title {
  height: 83vh;
  .ant-spin-container {
    .ant-table {
      height: 72vh !important;
      overflow: hidden;
      .ant-table-body {
        height: 66vh;
      }
    }
    .ant-table-placeholder {
      height: 64vh;
    }
  }
}
