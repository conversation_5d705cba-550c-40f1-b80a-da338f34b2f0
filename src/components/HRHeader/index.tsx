/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-15 23:12:54
 * @Description: 
 * @LastEditTime: 2025-08-04 08:59:06
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import { Row, Space } from "antd";
import "./index.scss";
interface HRHeaderProps {
    tools?: React.ReactNode;
    title?: string;
    icon?: React.ReactNode;
}

const HRHeader: React.FC<HRHeaderProps> = (props) => {

    return (
        <Row
            className="hr-header"
            justify="space-between"
            >
            <Space >
                <span className="header-icon" >{props.icon}</span>
                <span style={{ fontSize: '18px', fontWeight: 600 }}>{props.title}</span>
            </Space>
            <Space>
                {props.tools}
            </Space>
        </Row>
    )
}

export default HRHeader;
