/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-07-04 15:29:08
 * @Description:
 * @LastEditTime: 2025-07-19 17:05:54
 * @LastEditors: wangjiantao
 */
import React, {useEffect, useState} from "react";
import "@logicflow/core/es/index.css";
import "@logicflow/extension/lib/style/index.css";
import {LogicFlow, Options} from "@logicflow/core";
import {DndPanel, Menu, MiniMap, Snapshot} from "@logicflow/extension";
import "@/components/flow-pc/Flow/index.scss";
import {
    CirculateNodeConfig,
    ControlLayoutPanel,
    FlowContext,
    FlowPanelContext,
    NodeNodeConfig,
    OverNodeConfig,
    StartNodeConfig,
} from "../flow-pc";
import {processImgProperties} from "@/api/user/flow";
import {FlowOperator} from "@/components/flow-pc/Flow/nodes/type";
import EdgeType = Options.EdgeType;

interface FlowChartProps {
    edgeType?: EdgeType;
    recordId?: number;
    workCode?: string;
}

export const FlowImage: React.FC<FlowChartProps> = (props) => {

    const edgeType = props.edgeType || "polyline";
    const container = React.useRef<HTMLDivElement>(null);
    const lfRef = React.useRef<LogicFlow>(null);
    const [flowData, setFlowData] = useState<any>(null);

    const flowPanelContext = new FlowPanelContext(lfRef);
    FlowContext.getInstance().setFlowPanelContext(flowPanelContext);

    const getFlowSchema = () => {
        if (flowData!.schema) {
            const schema = JSON.parse(flowData!.schema);

            schema.nodes.map((node: any, index: number) => {
                const nodeCode = node.properties.code;

                node.properties.settingVisible = false;
                node.properties.editable = false;

                if (!flowData.nodeOperators) {
                    return
                }

                const operators = flowData.nodeOperators?.[nodeCode] as FlowOperator[];

                if (nodeCode == 'start') {
                    node.properties.startUser = operators?.[0];
                    node.properties.state = getNodeState(operators);
                } else if (nodeCode == 'over') {
                    node.properties.state = flowData.finished ? 'done' : 'wait';
                } else if (node.type === 'circulate-node') {
                    node.properties.operators = operators;
                    node.properties.state = operators?.length > 0 ? 'done' : 'wait';
                } else {
                    node.properties.operators = operators;
                    node.properties.state = getNodeState(operators);
                }

            });
            return schema;
        }
        return null;
    };

    const getNodeState = (operators: FlowOperator[]) => {
        // operators 为空，说明未执行，返回 wait ; 不为空且全部为 completed 返回done; 否则返回 current;
        if (!operators || operators.length === 0) {
            return "wait";
        }
        if (operators.every((item: FlowOperator) => item.completed)) {
            return "done";
        }
        return "current";
    };

    useEffect(() => {
        // 获取流程详情
        const getFlowDetail = async () => {
            const {success, data} = await processImgProperties(props.workCode, props.recordId);
            if (!success) return;
            setFlowData(data);
        };
        getFlowDetail();
    }, []);

    useEffect(() => {
        if (!flowData) return;
        const SilentConfig = {
            isSilentMode: true,
            stopScrollGraph: true,
            stopMoveGraph: false,
            stopZoomGraph: true,
            edgeTextEdit: false,
        };

        if (!container.current) return; // 确保容器存在
        //@ts-ignore
        lfRef.current = new LogicFlow({
            container: container.current!,
            ...SilentConfig,
            background: {
                backgroundColor: "#f3f5f8",
            },
            plugins: [Menu, DndPanel, MiniMap, Snapshot],
            grid: false,
            edgeType: edgeType,
        });

        lfRef.current.setTheme({
            bezier: {
                stroke: "#8f94e3",
                strokeWidth: 1,
            },
            polyline: {
                stroke: "#8f94e3",
                strokeWidth: 1,
            },
            line: {
                stroke: "#8f94e3",
                strokeWidth: 1,
            },
        });
        lfRef.current.register(StartNodeConfig);
        lfRef.current.register(NodeNodeConfig);
        lfRef.current.register(OverNodeConfig);
        lfRef.current.register(CirculateNodeConfig);
        lfRef.current.render(getFlowSchema());
        lfRef.current.fitView()

        lfRef.current.on('node:add', (data) => {
        });
    }, [flowData]);

    return (
        <div className="flow-chart">
            <ControlLayoutPanel/>
            <div className={"flow-chart-content"} ref={container}/>
        </div>
    );
};
