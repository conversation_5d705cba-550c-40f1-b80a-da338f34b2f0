  .i-table-container {
    padding: 20px 20px 0;
    .ant-form {
      padding: 24px 0!important;
    }

    .ant-pro-table-list-toolbar {
      display: none;
    }
  }
.page-table {
  border-radius: 1px !important;
  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .ant-table-thead {
    height: 40px;
  }
  /* 覆盖默认的 - 显示为空白 */
}
.ant-table-wrapper .ant-table-tbody .ant-table-row > .ant-table-cell-row-hover {
  background-color: #eee !important;
}

.ant-table-wrapper .ant-table-container::after {
  display: none !important;
}

//修复滚动条样式错位问题
＃body-table.ant-table-body {
  overflow-y: auto !important;
}
＃body-table .ant-table-header {
  overflow: hidden !important;
  margin-bottom: 0px !important;
}

.custom-row {
  height: 40px !important;
}


.row-selected {
  background-color: aquamarine !important;
}

.page-table {
  .ant-table-container {
    border: 1px solid #eee;
  }
  .react-resizable-handle {
    position: absolute;
    right: 0; // 靠右对齐
    top: 0; // 和表头顶部对齐
    bottom: 0; // 和表头底部对齐
    width: 1px; // 竖线宽度
    height: 100%;
    background-color: transparent; // 默认竖线颜色
    cursor: col-resize; // 鼠标样式
  }

  .react-resizable-handle:hover {
    background: rgba(0, 0, 0, 0.1); // 鼠标悬停时高亮
  }
}
