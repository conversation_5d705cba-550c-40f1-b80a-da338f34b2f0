import {ProTable} from "@ant-design/pro-components";
import "./index.scss";
import "react-resizable/css/styles.css";
import {type ProTableProps} from "@ant-design/pro-components";


export interface IProTableProps extends ProTableProps<any, any> {

}

const IProTable: React.FC<IProTableProps> = (props) => {
    return <div className="i-table-container"><ProTable className={"page-table"} {...props} /></div>;
};

export default IProTable;
