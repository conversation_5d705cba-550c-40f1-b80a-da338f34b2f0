import {getMenuTree, TreeData} from "@/api/user/menu";
import {FormItemProps, FormOption} from "@codingapi/ui-framework";
import {FormInstance} from "@codingapi/ui-framework/dist/Form/instance";
import {FormSelect} from "@/components/form-pc";

interface MenuTreeSelectProps extends FormItemProps {
    defaultExpandAll?: boolean,
}

type MenuTreeFormOption = FormOption & {
    obj: TreeData
}

const MenuTreeFormSelect: React.FC<MenuTreeSelectProps> = (props) => {

    const convertTreeData = (treeData: TreeData[]) => {
        const treeDataNode: MenuTreeFormOption[] = [];
        treeData.forEach((item) => {
            const treeNode = {
                value: item.nodeId,
                label: item.title,
                children: item.children ? convertTreeData(item.children) : undefined,
                obj: item,
                disabled: false
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    const fetchData = async (form?: FormInstance | undefined): Promise<MenuTreeFormOption[]> => {
        try {
            const res = await getMenuTree();

            return convertTreeData(res.data);

        } catch (error) {
            console.error('Error fetching data:', error);
        }
        return []
    };
    //
    // useEffect(() => {
    //
    //     fetchData().then(r => {
    //     });
    // }, [props.parentNodeId]);

    return (
        <>
            <FormSelect
                placeholder="Please select"
                {...props}
                itemProps={{'treeDefaultExpandAll': props.defaultExpandAll}}
                loadOptions={fetchData}
            />
        </>

    )
};

export default MenuTreeFormSelect;