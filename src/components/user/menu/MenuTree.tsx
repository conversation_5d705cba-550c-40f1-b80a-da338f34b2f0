import {TreeDataNode} from "antd";
import {DownOutlined} from "@ant-design/icons";
import {Key, useEffect, useState} from "react";
import {getMenuTree, TreeData} from "@/api/user/menu";
import {Spin, Tree} from "@itm/components-pc";

type MenuTreeDataNode = TreeDataNode & {
    obj: TreeData
};

export interface MenuTreeSelectCallBack {
    (selectedKeys: Key[], nodes: TreeData[]): void;
}

interface MenuTreeProps {
    multiSelect?: boolean;
    onSelect: MenuTreeSelectCallBack,
    selectedKeys?: Key[] | string[],
    defaultExpandAll?: boolean
}

const MenuTree: React.FC<MenuTreeProps> = (props) => {

    const [loading, setIsLoading] = useState(true);

    const [treeData, setTreeData] = useState<TreeData[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await getMenuTree();
                setTreeData(res.data);
                setIsLoading(false);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };
        fetchData().then(r => {
        });
    }, []);

    // props.treeData 转成 TreeDataNode
    const convertTreeData = (treeData: TreeData[]) => {
        const treeDataNode: TreeDataNode[] = [];
        treeData.forEach((item) => {
            const treeNode: MenuTreeDataNode = {
                key: item.nodeId,
                title: item.title,
                children: item.children ? convertTreeData(item.children) : undefined,
                obj: item
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    const onSelect = (selectedKeys: React.Key[], info: any) => {
        if (props.multiSelect) {
            return
        }
        const nodes: TreeData[] = [];
        info.selectedNodes.forEach((node: MenuTreeDataNode) => {
            nodes.push(node.obj);
        });
        props.onSelect(selectedKeys, nodes);
    }

    const onCheck = (selectedKeys: {
        checked: Key[];
        halfChecked: Key[];
    } | React.Key[], info: any) => {
        if (!props.multiSelect) {
            return
        }
        const nodes: TreeData[] = [];
        info.checkedNodes.forEach((node: MenuTreeDataNode) => {
            nodes.push(node.obj);
        });
        // 如果是对象类型，取checked ，否则直接取key[]
        props.onSelect(Array.isArray(selectedKeys) ? selectedKeys : selectedKeys.checked, nodes);
    }

    const selectKey: React.Key[] | string[] | undefined = props.multiSelect ? [] : props.selectedKeys;
    const checkedKey: React.Key[] | string[] | undefined = props.multiSelect ? props.selectedKeys : [];

    console.log("selectKey", selectKey)
    console.log("checkedKey", checkedKey)

    return (
        <Spin spinning={loading}>
            {treeData && treeData.length > 0 && (
                <Tree
                    checkable={props.multiSelect}
                    showLine
                    switcherIcon={<DownOutlined/>}
                    defaultExpandAll={props.defaultExpandAll}
                    defaultSelectedKeys={selectKey}
                    selectedKeys={selectKey}
                    defaultCheckedKeys={checkedKey}
                    checkedKeys={checkedKey}
                    onSelect={onSelect}
                    onCheck={onCheck}
                    treeData={convertTreeData(treeData)}
                />
            )}
        </Spin>
    );
};


export default MenuTree;