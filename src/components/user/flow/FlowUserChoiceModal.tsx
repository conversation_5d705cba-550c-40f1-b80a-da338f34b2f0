import React, {useEffect} from "react";
import {UserSelectFormProps} from "@codingapi/ui-framework";
import {ProForm} from "@ant-design/pro-components";
import {getUserDetailsByIds} from "@/api/user/user";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {SelectableUser, UserChoiceType} from "@/components/user/choice/type";

/**
 * 流程人员选择包装
 */
const FlowUserChoiceModal: React.FC<UserSelectFormProps> = (props) => {

    const [form] = ProForm.useForm();

    const [selectedUsers, setSelectedUsers] = React.useState<SelectableUser[]>([]);


    useEffect(() => {
        if (props.specifyUserIds) {
            getUserDetailsByIds(props.specifyUserIds).then((res) => {
                setSelectedUsers(res.data);
            })
        }
    }, [props.specifyUserIds]);

    return (
        <UserChoiceModal
            visible={props.visible}
            hide={() => {
                props.setVisible(false);
            }}
            multiple={props.multiple}
            type={UserChoiceType.COMMON}
            customAttribute={{
                specifyUserIds: props.specifyUserIds,
            }}
            selectedUsers={selectedUsers}
            onFinish={(values: SelectableUser[]) => {
                // values转成 FlowUser[]
                props.onFinish(values.map((item: SelectableUser) => {
                    return {
                        ...item,
                        id: item.id.toString(),
                        name: item.name,
                    }
                }))
            }}
        />
    )
}

export default FlowUserChoiceModal;
