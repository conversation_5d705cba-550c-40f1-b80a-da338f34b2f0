import React from "react";
import {FormItemProps} from "@/ui-framework";
import {Form} from "antd";
import {RoleSelect} from "@/components/user/role/RoleSelect";

export const FormRoleSelect: React.FC<FormItemProps> = (props) => {


    return (
        <Form.Item {...props}>
            <RoleSelect
                multiple={props.selectMultiple}
                value={props.value}
                onChange={props.onChange}
            />
        </Form.Item>
    )

}