import React, {useEffect, useState} from "react";
import {Select} from "@/components-pc";
import {Role} from "@/components/user/role/RoleList";
import {queryRoleList} from "@/api/user/role";

interface RoleSelectProps {
    style?: React.CSSProperties;
    multiple?: boolean;
    value?: (string | number)[];
    onChange?: (value: (string | number)[]) => void;
}

export const RoleSelect: React.FC<RoleSelectProps> = (props) => {
    const [roles, setRoles] = useState<Role[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await queryRoleList();
                setRoles(res.data || []);
            } catch (error) {
                console.error('Error fetching role list:', error);
            }
        };

        fetchData();
    }, []);

    return (
        <Select
            style={props.style}
            placeholder={"选择角色"}
            mode={props.multiple ? 'multiple' : undefined}
            value={props.value}
            onChange={(value) => {
                // 判断是不是数组
                if (Array.isArray(value)) {
                    props.onChange?.(value);
                } else {
                    props.onChange?.(value? [value]: []);
                }
            }}
            fieldNames={{label: 'name', value: 'id'}}
            options={roles}
        />
    )
}