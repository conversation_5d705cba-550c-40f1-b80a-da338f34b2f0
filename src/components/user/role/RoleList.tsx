import React, {useEffect, useState} from 'react';
import {Checkbox, CheckboxGroup, Spin} from '@itm/components-pc';
import {queryRoleList} from '@/api/user/role';

export interface Role {
    id: string | number;
    name: string;
    code: string;
}

export interface RoleListSelectCallBack {
    (selectedRoleIds: (string | number)[], roles: Role[]): void;
}

interface RoleListProps {
    onSelect?: RoleListSelectCallBack;
    selectedRoleIds?: (string | number)[]; // 受控模式支持
}

const RoleList: React.FC<RoleListProps> = ({onSelect, selectedRoleIds}) => {
    const [loading, setLoading] = useState(true);
    const [roles, setRoles] = useState<Role[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await queryRoleList(); // 替换为你自己的 API
                setRoles(res.data || []);
                setLoading(false);
            } catch (error) {
                console.error('Error fetching role list:', error);
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const handleCheck = (checkedValues: any[]) => {
        const selectedRoles = roles.filter(role => checkedValues.includes(role.id));
        if (onSelect) {
            onSelect(checkedValues, selectedRoles);
        }
    };

    return (
        <Spin spinning={loading}>
            {roles.length > 0 ? (
                <CheckboxGroup
                    value={selectedRoleIds}
                    onChange={handleCheck}
                    style={{display: 'flex', flexDirection: 'column'}}
                >
                    {roles.map(role => (
                        <Checkbox key={role.id} value={role.id}>
                            {role.name}
                        </Checkbox>
                    ))}
                </CheckboxGroup>

            ) : (
                <div>暂无角色数据</div>
            )}
        </Spin>
    );
};

export default RoleList;