/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-27 16:11:57
 * @Description:
 * @LastEditTime: 2025-07-15 11:59:49
 * @LastEditors: wangjiantao
 */
import { getOrgTree, OrgTreeQuery, TreeData } from "@/api/user/org";
import {
  FormItemProps,
  FormOption,
  FormValidateContent,
} from "@codingapi/ui-framework";
import {FormSelect} from "@/components/form-pc";
import { FormInstance } from "@codingapi/ui-framework/dist/Form/instance";
import { NamePath } from "rc-field-form/es/interface";
import { CSSProperties } from "react";

export enum DisplayType {
  name = "name",
  fullName = "fullName",
  shortName = "shortName",
}

interface FormOrgTreeSelectProps extends FormItemProps {
  query: OrgTreeQuery;
  parentNodeFieldName?: NamePath;
  selectable: string[];
  multiple?: boolean;
  defaultExpandAll?: boolean;
  style?: CSSProperties;
  value?: string | null;
  // 展示的名称，可选：name,fullName,shortName
  display?: DisplayType;
  validateFunction?: (
    content: FormValidateContent,
    message?: string
  ) => Promise<string[]>;
}

type OrgTreeFormOption = FormOption & {
  obj: TreeData;
};

const FormOrgTreeSelect: React.FC<FormOrgTreeSelectProps> = (props) => {
  const convertTreeData = (treeData: TreeData[]) => {
    const treeDataNode: OrgTreeFormOption[] = [];
    treeData.forEach((item) => {
      let displayName: any;
      if (props.display) {
        displayName = item[props.display] ?? item.name;
      } else {
        displayName = item.name;
      }
      const treeNode = {
        value: item.nodeId,
        label: displayName,
        children: item.children ? convertTreeData(item.children) : undefined,
        obj: item,
        disabled: !props.selectable.includes(item.type),
      };
      treeDataNode.push(treeNode);
    });
    return treeDataNode;
  };

  const fetchData = async (
    form?: FormInstance | undefined
  ): Promise<OrgTreeFormOption[]> => {
    try {
      const query = {
        ...props.query,
        related: false,
      };

      if (props.parentNodeFieldName) {
        query.parentNodeId = form?.getFieldValue(props.parentNodeFieldName);
        query.related = true;
      }

      const res = await getOrgTree(query);

      return convertTreeData(res.data);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
    return [];
  };
  //
  // useEffect(() => {
  //
  //     fetchData().then(r => {
  //     });
  // }, [props.parentNodeId]);

  return (
    <div style={props.style}>
      <FormSelect
        placeholder="Please select"
        {...props}
        itemProps={{ treeDefaultExpandAll: props.defaultExpandAll }}
        loadOptions={fetchData}
      />
    </div>
  );
};

export default FormOrgTreeSelect;
