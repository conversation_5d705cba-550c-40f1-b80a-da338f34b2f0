import {TreeDataNode} from "antd";
import {DownOutlined} from "@ant-design/icons";
import {Key, useEffect, useState} from "react";
import {getOrgTree, OrgTreeQuery, TreeData} from "@/api/user/org";
import {Spin, Tree} from "@itm/components-pc";


type OrgTreeDataNode = TreeDataNode & {
    obj: TreeData;
}

export interface OrgTreeSelectCallBack {
    (selectedKeys: Key[], nodes: TreeData[]): void;
}

interface OrgTreeProps {
    query: OrgTreeQuery,
    onSelect: OrgTreeSelectCallBack,
    defaultSelectedKeys?: Key[] | string[],
    defaultExpandAll?: boolean,
    // 是否可勾选
    checkable?: boolean,
    onCheck?: (checked: {
        checked: Key[];
        halfChecked: Key[];
    } | Key[]) => void,
    checkedKeys?: any[],
}

const OrgTree: React.FC<OrgTreeProps> = (props) => {

    const [loading, setIsLoading] = useState(true);

    const [treeData, setTreeData] = useState<TreeData[]>([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await getOrgTree(props.query);
                setTreeData(res.data);
                setIsLoading(false);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };
        fetchData().then(r => {
        });
    }, []);

    // props.treeData 转成 TreeDataNode
    const convertTreeData = (treeData: TreeData[]) => {
        const treeDataNode: TreeDataNode[] = [];
        treeData.forEach((item) => {
            const treeNode: OrgTreeDataNode = {
                key: item.nodeId,
                title: item.name,
                children: item.children ? convertTreeData(item.children) : undefined,
                obj: item
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    const onSelect = (selectedKeys: React.Key[], info: any) => {
        const nodes: TreeData[] = [];
        info.selectedNodes.forEach((node: OrgTreeDataNode) => {
            nodes.push(node.obj);
        });
        props.onSelect(selectedKeys, nodes);
    }

    return (
        <Spin spinning={loading}>
            {treeData && treeData.length > 0 && (
                <Tree
                    checkable={props.checkable}
                    onCheck={props.onCheck}
                    checkedKeys={props.checkedKeys}
                    showLine
                    switcherIcon={<DownOutlined/>}
                    defaultExpandAll={props.defaultExpandAll}
                    // 默认展开顶级节点
                    defaultExpandedKeys={treeData.length > 0 ? [treeData[0].nodeId] : []}
                    defaultSelectedKeys={props.defaultSelectedKeys}
                    onSelect={onSelect}
                    treeData={convertTreeData(treeData)}
                />
            )}
        </Spin>
    );
};


export default OrgTree;