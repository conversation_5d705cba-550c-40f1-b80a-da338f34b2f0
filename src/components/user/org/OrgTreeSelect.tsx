import {getOrgTree, OrgTreeQuery, TreeData} from "@/api/user/org";
import {FormOption} from "@codingapi/ui-framework";
import {FormInstance} from "@codingapi/ui-framework/dist/Form/instance";
import {NamePath} from "rc-field-form/es/interface";
import React, {useEffect, useState} from "react";
import {TreeSelect} from "antd";

export enum SelectableOrgType {
    UNIT = 'UNIT',
    DEPT = 'DEPT',
    POST = 'POST'
}

export interface OrgTreeSelectProps {

    value?: string | null,
    onChange?: (value: string | null, data: any[], from?: FormInstance) => void,
    disabled?: boolean,
    multiple?: boolean,

    query: OrgTreeQuery,
    // 当需要联动父节点时必传，当前字段的名称
    fieldName?: NamePath,
    // 当需要联动父节点时必传，当前字段的父节点名称
    parentNodeFieldName?: NamePath,
    // 当需要联动父节点时必传，表单实例
    form?: FormInstance,
    // 可选的组织类型范围
    selectable: SelectableOrgType[]

    defaultExpandAll?: boolean,

}

type OrgTreeFormOption = FormOption & {
    obj: TreeData
}

const OrgTreeSelect: React.FC<OrgTreeSelectProps> = (props) => {

    const [treeData, setTreeData] = useState<any[]>()

    const convertTreeData = (treeData: TreeData[]) => {
        const treeDataNode: OrgTreeFormOption[] = [];
        treeData.forEach((item) => {
            const treeNode = {
                value: item.nodeId,
                label: item.name,
                children: item.children ? convertTreeData(item.children) : undefined,
                obj: item,
                //props.selectable 是否包含当前记录的类型，如果不包含则禁用
                disabled: !props.selectable.includes(item.type as SelectableOrgType)
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    const fetchData = async () => {
        const query = {
            ...props.query,
            related: false
        }
        if (props.parentNodeFieldName) {
            query.parentNodeId = props.form?.getFieldValue(props.parentNodeFieldName)
            query.related = true
        }

        getOrgTree(query).then(res => setTreeData(convertTreeData(res.data)))
    };

    useEffect(() => {

        fetchData();

        if (props.parentNodeFieldName) {
            props.form?.getFormFieldReloadListenerContext().addListener(props.fieldName, () => {
                fetchData()
            })
        }

    }, [props.query, props.selectable, props.parentNodeFieldName])

    return (
        <TreeSelect
            allowClear
            value={props.multiple ? props.value?.split(",") : props.value}
            placeholder={"请选择"}
            disabled={props.disabled}
            multiple={props.multiple}
            treeDefaultExpandAll={props.defaultExpandAll}
            onClear={() => {
                props.onChange?.(null, [], props.form);
            }}
            onChange={(value, data) => {
                if (props.multiple) {
                    // 如果是多选，确保 value 是数组并使用 join
                    props.onChange?.((value as string[])?.join(","), data, props.form);
                } else {
                    // 单选直接传递 value（可能是 string 或 null）
                    props.onChange?.(value as string | null, data, props.form);
                }
            }}
            treeData={treeData}
        />

    )
};

export default OrgTreeSelect;