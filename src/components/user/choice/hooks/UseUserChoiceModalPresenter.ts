import {useEffect, useRef, useState} from "react";
import {OrgTreeMode, SelectableUser, UserChoiceModalState, UserChoiceType} from "@/components/user/choice/type";
import {UserChoiceModalApiImpl} from "@/components/user/choice/model/UserChoiceModalModel";
import UserChoiceModalPresenter from "@/components/user/choice/presenter/UserChoiceModalPresenter";


const initialState = {
    modalVisible: false,
    pageSize: 20,
    current: 1,
    total: 0,

    selectedUsers: [],
    orgTreeData: [],
    tableData: [],

    tableRefreshVersion: 0,
    treeRefreshVersion: 0,
}

export function useUserChoiceModalPresenter(hide: () => void,
                                            onFinish?: (values: SelectableUser[], customAttribute?: any) => void,
                                            multiple?: boolean,
                                            type?: UserChoiceType,
                                            orgTreeMode?: OrgTreeMode,
                                            customAttribute?: any) {

    const [state, dispatch] = useState<UserChoiceModalState>({
        ...initialState,
        multiple: multiple,
        // 不是部门选择时才展示组织树
        showOrgTree: type !== UserChoiceType.DEPT_USERS,
        userChoiceProps: {
            type: type ? type : UserChoiceType.COMMON,
            orgTreeMode: orgTreeMode ? orgTreeMode : OrgTreeMode.DEPT,
            customAttribute
        },
        hide,
        onFinish
    });
    const presenterRef = useRef<UserChoiceModalPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new UserChoiceModalPresenter(state, new UserChoiceModalApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}