import {useEffect, useRef, useState} from "react";
import {OrgTreeMode, SelectableUser, UserChoiceSelectState, UserChoiceType} from "@/components/user/choice/type";
import {UserChoiceSelectApiImpl} from "@/components/user/choice/model/UserChoiceSelectModel";
import UserChoiceSelectPresenter from "@/components/user/choice/presenter/UserChoiceSelectPresenter";


const initialState = {
    modalVisible: false,
    selectedUsers: [],
    selectedUserIds: [],
    options: []
}

export function useUserChoiceSelectPresenter(onChange?: (value: string | null, data?: SelectableUser | SelectableUser[] | null) => void,
                                             multiple?: boolean, type?: UserChoiceType, orgTreeMode?: OrgTreeMode, customAttribute?: any) {

    const [state, dispatch] = useState<UserChoiceSelectState>({
        ...initialState,
        userChoiceProps: {
            type: type ? type : UserChoiceType.COMMON,
            orgTreeMode: orgTreeMode ? orgTreeMode : OrgTreeMode.DEPT,
            customAttribute
        },
        onChange,
        multiple,
    });
    const presenterRef = useRef<UserChoiceSelectPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new UserChoiceSelectPresenter(state, new UserChoiceSelectApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}