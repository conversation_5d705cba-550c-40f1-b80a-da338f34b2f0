import {Avatar, Button, Checkbox, Input, Modal, Pagination, Row, Tree} from "@itm/components-pc";
import {CloseOutlined, DownOutlined, MinusCircleFilled, SearchOutlined} from "@ant-design/icons";
import React, {useEffect, useMemo} from "react";
import {useUserChoiceModalPresenter} from "@/components/user/choice/hooks/UseUserChoiceModalPresenter";
import {Empty, List} from "antd";
import {SelectableUser, UserChoiceProps} from "@/components/user/choice/type";
import "./index.scss";


interface UserChoiceModalProps extends UserChoiceProps {
    visible: boolean;
    hide: () => void;
    onFinish?: (values: SelectableUser[], customAttribute?: any) => void;

    selectedUsers?: SelectableUser[],

    multiple?: boolean,
}

const UserChoiceModal: React.FC<UserChoiceModalProps> = (props) => {

    const {Search} = Input;

    const {
        state,
        presenter
    } = useUserChoiceModalPresenter(props.hide, props.onFinish, props.multiple, props.type, props.orgTreeMode, props.customAttribute);

    useEffect(() => {
        if (props.visible) {
            presenter?.showModal(props.selectedUsers)
        } else {
            presenter?.hideModal()
        }

    }, [props.visible])

    const orgTreeComponent = state.showOrgTree ? useMemo(() => {
        return (<Tree
            key={state.treeRefreshVersion}
            showLine
            switcherIcon={<DownOutlined/>}
            defaultExpandAll={false}
            onSelect={(selectedKeys: any, nodes: any) => {
                presenter?.changeOrgNode(selectedKeys[0])
            }}
            treeData={state.orgTreeData}
        />)
    }, [state.treeRefreshVersion]) : (<></>);

    return (
        <Modal
            title={'选择人员'}
            width={state.showOrgTree ? '80%' : '60%'}
            open={state.modalVisible}
            onCancel={props.hide}
            centered
            maskClosable={false}
            footer={[
                <Button key="submit" onClick={() => {
                    presenter?.onFinish(props.customAttribute)
                }} type="primary">
                    确定({state.selectedUsers.length})
                </Button>,
                <Button key="clear" onClick={() => {
                    presenter?.clearChecked()
                }}>
                    清空已选
                </Button>
            ]}>
            <div className="userModal" style={{maxHeight: '80vh', overflowY: 'hidden', overflowX: 'hidden'}}>
                <Row>
                    <Row.Col span={24}>
                        <Search
                            placeholder="请输入内容"
                            prefix={<SearchOutlined/>}
                            enterButton="搜索"
                            allowClear
                            onSearch={(value: any) => {
                                presenter?.keywordSearch(value)
                            }}
                        />
                    </Row.Col>
                </Row>
                <Row className={"content"} gutter={4}>
                    {state.showOrgTree ? (<Row.Col span={6} style={{height: '70vh', overflowY: 'auto'}}>
                        {orgTreeComponent}
                    </Row.Col>) : (<></>)}

                    <Row.Col span={state.showOrgTree ? 10 : 14} style={{height: '70vh', overflowY: 'auto'}}>
                        {state.tableData && state.tableData.length ? (
                            <List
                                itemLayout="horizontal"
                                dataSource={state.tableData}
                                renderItem={(item, index) => (
                                    <List.Item key={index}>
                                        <div style={{width: '90%'}}>
                                            <List.Item.Meta
                                                style={{alignItems: "center"}}
                                                avatar={<Avatar
                                                    className={"user-avatar"}>{item.name?.slice(-2)}</Avatar>}
                                                title={<span>{item.name}</span>}
                                                description={item.orgShortNamePath}
                                            />
                                        </div>
                                        <div style={{flex: 1, textAlign: 'end', width: "10%"}}>
                                            <Checkbox checked={item.checked} onChange={() => {
                                                presenter?.check(item)
                                            }}> </Checkbox>
                                        </div>
                                    </List.Item>
                                )}
                            />
                        ) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>}
                        <Pagination
                            showSizeChanger
                            total={state.total}
                            showTotal={(total) => `共 ${total} 条`}
                            defaultPageSize={20}
                            current={state.current}
                            size={"small"}
                            onChange={(current, pageSize) => {
                                presenter?.changePageSetting(current, pageSize)
                            }}
                            onShowSizeChange={(current, pageSize) => {
                                presenter?.changePageSetting(1, pageSize)
                            }}
                        />
                    </Row.Col>
                    <Row.Col span={state.showOrgTree ? 8 : 10} style={{height: '70vh', overflowY: 'auto'}}>
                        已选择列表
                        {state.selectedUsers && state.selectedUsers.length ? (
                            <List
                                itemLayout="horizontal"
                                dataSource={state.selectedUsers}
                                renderItem={(item, index) => (
                                    <List.Item key={index}
                                               actions={[
                                                   <Button
                                                       key="remove"
                                                       type="text"
                                                       icon={<MinusCircleFilled/>}
                                                       onClick={() => presenter?.check(item)}
                                                       style={{color: 'red'}}
                                                   />
                                               ]}>
                                        <List.Item.Meta
                                            style={{alignItems: "center"}}
                                            avatar={<Avatar className={"user-avatar"}>{item.name?.slice(-2)}</Avatar>}
                                            title={<span>{item.name}</span>}
                                            description={item.orgShortNamePath}
                                        />
                                    </List.Item>
                                )}
                            />
                        ) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>}
                    </Row.Col>
                </Row>
            </div>
        </Modal>
    )
}

export default UserChoiceModal