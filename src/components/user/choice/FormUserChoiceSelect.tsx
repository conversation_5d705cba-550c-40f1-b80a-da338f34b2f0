import React, {CSSProperties, useEffect} from "react";
import {Form} from "antd";
import UserChoiceSelect from "@/components/user/choice/UserChoiceSelect";
import {FormItemProps} from "@codingapi/ui-framework/dist/Form/types";
import {UserChoiceProps} from "@/components/user/choice/type";
import formFieldInit from "@/components/form-pc/Form/common";

interface FormUserChoiceSelectProps extends FormItemProps, UserChoiceProps {
    onChange?: (value: any, data?: any) => void
    disabled?: boolean;
    value?: any;// 多选时，用英文逗号分隔id
    style?: CSSProperties,
}

const FormUserChoiceSelect: React.FC<FormUserChoiceSelectProps> = (props) => {

    // 添加校验逻辑
    const {formContext} = formFieldInit(props);

    useEffect(() => {
        formContext?.addFormField(
            {
                type: 'FormUserChoiceSelect',
                props: props
            }
        );
    }, []);

    return (<Form.Item name={props.name} label={props.label} style={props.style}>
        <UserChoiceSelect
            disabled={props.disabled}
            value={props.value}
            multiple={props.selectMultiple}
            type={props.type}
            orgTreeMode={props.orgTreeMode}
            customAttribute={props.customAttribute}
            onChange={props.onChange}
        />
    </Form.Item>)
}

export default FormUserChoiceSelect;