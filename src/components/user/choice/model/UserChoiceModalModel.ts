import {OrgTreeMode, UserChoiceModalApi, UserChoiceType} from "@/components/user/choice/type";
import {userChoiceGetOrgTree, UserChoicePageQuery, userChoiceQueryPage} from "@/api/user/userChoice";


export class UserChoiceModalApiImpl implements UserChoiceModalApi {

    page(param: UserChoicePageQuery, type?: UserChoiceType): Promise<any> {
        return userChoiceQueryPage(param, type);
    }

    tree(type?: string, orgTreeMode?: OrgTreeMode): Promise<any> {
        return userChoiceGetOrgTree(type, orgTreeMode);
    }


}