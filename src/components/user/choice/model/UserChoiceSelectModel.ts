import {UserChoiceSelectApi, UserChoiceType} from "@/components/user/choice/type";
import {userChoiceKeywordSearch} from "@/api/user/userChoice";
import {getUserDetailsByIds} from "@/api/user/user";


export class UserChoiceSelectApiImpl implements UserChoiceSelectApi {
    keywordSearch(keyword?: string, type?: UserChoiceType): Promise<any> {
        return userChoiceKeywordSearch(keyword, type);
    }

    detailByIds(ids: any[]): Promise<any> {
        return getUserDetailsByIds(ids);
    }

}