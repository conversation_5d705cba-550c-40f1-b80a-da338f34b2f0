/*
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2025-06-27 16:11:57
 * @Description: 
 * @LastEditTime: 2025-06-30 09:01:55
 * @LastEditors: wangjiantao
 */
import {SelectableUser, UserChoiceSelectApi, UserChoiceSelectState} from "@/components/user/choice/type";
import {Dispatch} from "@/utils/mvvm";


export default class UserChoiceSelectPresenter {
    private readonly api: UserChoiceSelectApi;
    private readonly dispatch: Dispatch<UserChoiceSelectState>;
    private state: UserChoiceSelectState;

    public constructor(state: UserChoiceSelectState, api: UserChoiceSelectApi, dispatch: Dispatch<UserChoiceSelectState>) {
        this.state = state;
        this.api = api;
        this.dispatch = dispatch;
    }

    public syncState = (newState: UserChoiceSelectState) => {
        this.state = newState;
    }

    public async setSelectUser(ids: any[]) {

        let users;
        let userIds;
        if (!ids || ids.length == 0) {
            users = [];
            userIds = [];
        } else {
            if (!ids.length) return;
            const res = await this.api.detailByIds(ids);
            users = res.data || [];
            userIds = users.map((item: any) => item.id)
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedUsers: users,
                selectedUserIds: userIds
            }
        })
    }

    public handleSelectChange(value: string[] | string, data?: SelectableUser | SelectableUser[] | null) {
        let valStr;
        if (Array.isArray(value)) {
            // 转成逗号分隔的
            valStr = value?.join(",");
        } else {
            valStr = value
        }
        this.state.onChange?.(valStr, data);
    }

    public showModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                modalVisible: true
            }
        });
    }

    public hideModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                modalVisible: false
            }
        });
    }

    public handleModalOk = (values: SelectableUser[]) => {

        let newUserId;
        let newUser;

        if (this.state.multiple) {
            newUser = values || [];
            // 转成逗号分隔的
            newUserId = values.map((item: SelectableUser) => item.id).join(",");
        } else {
            newUser = values && values.length > 0 ? values[0] : null;
            newUserId = newUser ? `${newUser.id}` : null;
        }

        this.state.onChange?.(newUserId, newUser);
    }

    public keywordSearch = (keyword: any) => {

    }

}