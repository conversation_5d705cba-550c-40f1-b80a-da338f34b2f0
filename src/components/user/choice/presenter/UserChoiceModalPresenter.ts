import {SelectableUser, UserChoiceModalApi, UserChoiceModalState} from "@/components/user/choice/type";
import {Dispatch} from "@/utils/mvvm";


export default class UserChoiceModalPresenter {
    private readonly api: UserChoiceModalApi;
    private readonly dispatch: Dispatch<UserChoiceModalState>;
    private state: UserChoiceModalState;

    public constructor(state: UserChoiceModalState, api: UserChoiceModalApi, dispatch: Dispatch<UserChoiceModalState>) {
        this.state = state;
        this.api = api;
        this.dispatch = dispatch;
    }

    public syncState = (newState: UserChoiceModalState) => {
        this.state = newState;
    }

    public refreshTable = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableRefreshVersion: prevState.tableRefreshVersion + 1
            }
        })
    }

    public refreshTree = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                treeRefreshVersion: prevState.treeRefreshVersion + 1
            }
        })
    }

    public onFinish(customAttribute?: any) {
        this.state.onFinish?.(this.state.selectedUsers, customAttribute)
        this.state.hide()
    }

    public check = (record: SelectableUser) => {
        // 找到id相同的并更新checked状态
        const selected = this.state.selectedUsers;

        const records = this.state.tableData
        records.forEach((item) => {
            if (item.id === record.id) {
                item.checked = !item.checked;
                if (item.checked) {
                    selected.push(item)
                } else {
                    // 使用 filter 过滤掉 id 相同的元素
                    const index = selected.findIndex((selectedItem) => selectedItem.id === item.id);
                    if (index !== -1) {
                        selected.splice(index, 1);
                    }
                }
            } else if (!this.state.multiple) {
                // 不是多选，就取消勾选，使用 filter 过滤掉 id 相同的元素
                item.checked = false;
                const index = selected.findIndex((selectedItem) => selectedItem.id === item.id);
                if (index !== -1) {
                    selected.splice(index, 1);
                }
            }
        })

        // 更新状态
        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableData: [...records],
                selectedUsers: selected
            }
        })
    }

    public clearChecked = () => {

        const records = this.state.tableData

        records.forEach((item) => {
            item.checked = false
        })

        this.dispatch((prevState) => {
            return {
                ...prevState,
                tableData: records,
                selectedUsers: []
            }
        })
    }

    public showModal = async (selectedUsers?: SelectableUser[]) => {

        const orgTreeData = this.state.showOrgTree
            ? this.convertTreeData((await this.api.tree(this.state.userChoiceProps.type, this.state.userChoiceProps.orgTreeMode)).data)
            : [];

        const reset = {
            keyword: null,
            current: 1,
            pageSize: this.state.pageSize,
            orgNodeId: null,
            customAttribute: this.state.userChoiceProps.customAttribute,
            orgTreeData: orgTreeData
        }

        const res = await this.api.page(reset, this.state.userChoiceProps.type)

        this.dispatch((prevState) => {
            return {
                ...prevState,
                ...reset,
                total: res.total,
                tableData: this.handleChecked(res.data, selectedUsers),
                modalVisible: true,
                selectedUsers: selectedUsers ? selectedUsers : [],
                treeRefreshVersion: prevState.treeRefreshVersion + 1
            }
        });

    }
    public hideModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                modalVisible: false,
                selectedUsers: []
            }
        });
    }

    public keywordSearch = (keyword: any) => {
        this.api.page({
            current: 1,
            pageSize: this.state.pageSize,
            keyword: keyword,
            orgNodeId: this.state.orgNodeId,
            customAttribute: this.state.userChoiceProps.customAttribute
        }, this.state.userChoiceProps.type)
            .then(res => {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        current: 1,
                        keyword: keyword,
                        tableData: this.handleChecked(res.data),
                        total: res.total
                    }
                });
            })
    }

    public changeOrgNode = (orgNodeId: any) => {
        this.api.page({
            current: 1,
            pageSize: this.state.pageSize,
            keyword: this.state.keyword,
            orgNodeId: orgNodeId,
            customAttribute: this.state.userChoiceProps.customAttribute
        }, this.state.userChoiceProps.type)
            .then(res => {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        current: 1,
                        selectedOrgNodeId: orgNodeId,
                        tableData: this.handleChecked(res.data),
                        total: res.total
                    }
                });
            })
    }

    public changePageSetting = (current: number, pageSize: number) => {
        this.api.page({
            current: current,
            pageSize: pageSize,
            keyword: this.state.keyword,
            orgNodeId: this.state.orgNodeId,
            customAttribute: this.state.userChoiceProps.customAttribute
        }, this.state.userChoiceProps.type)
            .then(res => {
                this.dispatch((prevState) => {
                    return {
                        ...prevState,
                        current: current,
                        pageSize: pageSize,
                        tableData: this.handleChecked(res.data),
                        total: res.total
                    }
                })
            })
    }

    convertTreeData = (treeData: any[]) => {
        const treeDataNode: any[] = [];
        treeData?.forEach((item) => {
            const treeNode: any = {
                key: item.nodeId,
                title: item.name,
                children: item.children ? this.convertTreeData(item.children) : undefined,
                obj: item,
            };
            treeDataNode.push(treeNode);
        });
        return treeDataNode;
    }

    handleChecked = (record: any[], selected?: any[]) => {

        if (!selected) {
            selected = this.state.selectedUsers
        }

        // 和selectedUsers对比，存在则checked
        if (record.length > 0) {
            record.forEach((item) => {
                item.checked = selected.findIndex((user) => user.id === item.id) >= 0
            })
        }

        return record
    }

}