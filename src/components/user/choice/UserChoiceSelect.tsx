/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 08:54:24
 * @Description: 
 * @LastEditTime: 2025-06-30 09:02:37
 * @LastEditors: wangjiantao
 */
import React, {useEffect} from "react";
import {Button, Select, Space} from "@itm/components-pc";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {useUserChoiceSelectPresenter} from "@/components/user/choice/hooks/UseUserChoiceSelectPresenter";
import {SelectableUser, UserChoiceProps} from "@/components/user/choice/type";


interface UserChoiceSelectProps extends UserChoiceProps {
    onChange?: (value: string | null, data?: SelectableUser | SelectableUser[] | null) => void
    disabled?: boolean;
    value?: string;// 多选时，用英文逗号分隔id
    multiple?: boolean;

}

const UserChoiceSelect: React.FC<UserChoiceSelectProps> = (props) => {

    const {
        state,
        presenter
    } = useUserChoiceSelectPresenter(props.onChange, props.multiple, props.type, props.orgTreeMode, props.customAttribute);

    useEffect(() => {
        if (!props.value) {
            presenter.setSelectUser([])
            return
        }
        // 处理已选
        if (props.multiple) {
            presenter?.setSelectUser(props.value ? props.value.split(",") : [])
        } else {
            presenter?.setSelectUser([props.value])
        }
    }, [props.value])

    return (
        <div>
            <Space.Compact block>
                <Select
                    style={{width: '100%'}}
                    value={state.selectedUserIds}
                    placeholder={"请选择人员"}
                    disabled={props.disabled}
                    onClear={() => {
                        props.onChange?.(null, null);
                    }}
                    optionLabelProp="name"
                    mode={props.multiple ? 'multiple' : undefined}
                    fieldNames={{label: 'name', value: 'id'}}
                    onChange={(value, data) => {
                        presenter?.handleSelectChange(value, data as SelectableUser[])
                    }}
                    options={state.selectedUsers}
                    optionRender={(option) => (
                        <span>{option.data.orgShortNamePath + "/" + option.data.name}</span>
                    )}
                />
                <Button type="primary" disabled={props.disabled} onClick={() => {
                    presenter?.showModal()
                }}> 选 择</Button>
            </Space.Compact>
            <UserChoiceModal
                visible={state.modalVisible}
                hide={presenter?.hideModal}
                multiple={props.multiple}
                onFinish={presenter?.handleModalOk}
                type={state.userChoiceProps.type}
                orgTreeMode={state.userChoiceProps.orgTreeMode}
                customAttribute={props.customAttribute}
                selectedUsers={state.selectedUsers}
            />
        </div>
    )
}

export default UserChoiceSelect;