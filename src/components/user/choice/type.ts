import {UserChoicePageQuery} from "@/api/user/userChoice";
import {BaseOptionType} from "rc-select/lib/Select";


export enum UserChoiceType {
    // 通用
    COMMON = 'common',
    // 选择指定部门下的人员，左侧组织树就不需要了
    DEPT_USERS = 'deptUsers',

    FLOW_AUDIT = 'flowAudit'
}

export enum OrgTreeMode {
    UNIT = 'unit',
    DEPT = 'dept',
    POST = 'post'
}

export interface UserChoiceProps {
    type?: UserChoiceType;
    orgTreeMode?: OrgTreeMode;
    customAttribute?: any;
}

export interface SelectableUser extends BaseOptionType {
    // 核心字段
    id: number,
    name: string,
    orgNamePath?: string,
    orgShortNamePath?: string,

    // 允许任意扩展字段
    [key: string]: any;
}

export interface UserChoiceModalState {

    modalVisible: boolean;
    multiple?: boolean;
    userChoiceProps: UserChoiceProps;

    // 是否展示组织树，如果是指定部门的选择，则不用展示组织树
    showOrgTree: boolean;
    orgTreeData: any[],
    orgNodeId?: any;

    tableData: any[],
    keyword?: any;
    pageSize: number;
    current: number;
    total: number;

    selectedUsers: SelectableUser[];

    tableRefreshVersion: number;
    treeRefreshVersion: number;

    hide: () => void;
    onFinish?: (values: SelectableUser[], customAttribute?: any) => void;

}

export interface UserChoiceSelectState {

    modalVisible: boolean;
    userChoiceProps: UserChoiceProps;
    selectedUsers: SelectableUser[];
    selectedUserIds: any[];
    options: any[];
    onChange?: (value: string | null, data?: SelectableUser | SelectableUser[] | null) => void;
    multiple?: boolean;

}

export interface UserChoiceModalApi {
    page: (param: UserChoicePageQuery, type?: UserChoiceType) => Promise<any>;
    tree: (type?: string, orgTreeMode?: OrgTreeMode) => Promise<any>;
}

export interface UserChoiceSelectApi {
    keywordSearch: (keyword?: string, type?: UserChoiceType) => Promise<any>;
    detailByIds: (ids: any[]) => Promise<any>;
}