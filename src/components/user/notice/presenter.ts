import {Dispatch} from "@/utils/mvvm";
import {NoticeModalViewApi, NoticeModalViewState} from "@/components/user/notice/types";


export default class NoticeModalViewPresenter {
    private readonly api: NoticeModalViewApi;
    private readonly dispatch: Dispatch<NoticeModalViewState>;
    private state: NoticeModalViewState;

    public constructor(state: NoticeModalViewState, api: NoticeModalViewApi, dispatch: Dispatch<NoticeModalViewState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: NoticeModalViewState) => {
        this.state = newState;
    }

    public showModal() {


        this.dispatch((prevState) => {
            return {
                ...prevState,
                visible: true
            }
        });
    }

    public hideModal() {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                visible: false,
                noticeRecord: undefined,
                componentKey: undefined,
                isFlow: undefined
            }
        });
    }

    public async setCurrentRecord(id: any) {

        let currentRecord: any;
        let componentKey: any;
        let isFlow: any;

        if (!id) {
            currentRecord = null;
            componentKey = undefined;
            isFlow = undefined;
        } else {
            const res = await this.api.getNoticeRecordDetail(id)
            if (!res.data) {
                throw new Error("待办记录不存在")
            }
            currentRecord = res.data
            isFlow = currentRecord.type === 'FLOW_TODO' || currentRecord.type === 'FLOW_NOTICE'
            if (isFlow && !currentRecord.componentKey) {
                const flowRes = await this.api.getFlowRecordDetail(currentRecord.businessId)
                if (!flowRes.data) {
                    throw new Error("流程记录不存在")
                }
                componentKey = flowRes.data.flowNode.view
            } else {
                componentKey = currentRecord.componentKey
            }
        }

        this.dispatch((prevState) => {
            return {
                ...prevState,
                noticeRecord: currentRecord,
                componentKey: componentKey,
                isFlow: isFlow
            }
        });

    }
}