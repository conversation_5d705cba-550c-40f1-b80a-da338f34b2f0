import React, {useEffect} from "react";
import {Modal} from "@itm/components-pc";
import {ComponentBus, FlowFormViewProps} from "@codingapi/ui-framework";
import {FlowModelView} from "@/components/flow-pc";
import {message} from "antd";
import DefaultNoticeForm from "@/components/user/notice/DefaultNoticeForm";
import {useNoticeModalViewPresenter} from "@/components/user/notice/hooks/UseNoticeModalViewPresenter";
import {NoticeRecordData} from "@/components/user/notice/types";

export interface NoticeModalViewProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    id: string;
    componentKey?: string;
}



export interface NoticeFormProps {
    setVisible: (visible: boolean) => void;
    data?: NoticeRecordData; // 待办信息
    componentKey?: string;
}

const NoticeModalView: React.FC<NoticeModalViewProps> = (props) => {

    // 根据id 获取待办详情，传递给form

    const [modal, setModal] = React.useState<any>();

    const {state, presenter} = useNoticeModalViewPresenter(props.setVisible)

    
    // 封装错误处理和关闭逻辑
    const handleErrorAndClose = () => {
        message.error("待办页面未找到");
        props.setVisible(false);
    };

    useEffect(() => {
        presenter?.setCurrentRecord(props.id)
    }, [props.id]);

    useEffect(() => {
        if (props.visible) {
            presenter?.showModal()
        } else {
            presenter?.hideModal()
        }
    }, [props.visible]);

    useEffect(() => {

        if (state.isFlow === undefined || !state.componentKey) {
            setModal(null)
            return
        }

        if (state.isFlow && state.componentKey) {
            const FlowComponent = ComponentBus.getInstance().getComponent<FlowFormViewProps>(state.componentKey, undefined);
            if (!FlowComponent) {
                handleErrorAndClose();
                return;
            }

            setModal(
                <FlowModelView
                    id={String(state.noticeRecord?.businessId)}
                    visible={state.visible}
                    setVisible={props.setVisible}
                    view={FlowComponent}
                />
            )
        } else {

            const NoticeComponent = state.componentKey
                ? ComponentBus.getInstance().getComponent<NoticeFormProps>(state.componentKey, DefaultNoticeForm)
                : DefaultNoticeForm;
            
            setModal(
                <Modal
                    className={"flow-modal"}
                    open={state.visible}
                    onCancel={() => {
                        console.log("cancel")
                        props.setVisible(false)
                    }}
                    onOk={() => props.setVisible(false)}
                    destroyOnHidden={true}
                    footer={false}
                >
                    {NoticeComponent && <NoticeComponent
                        setVisible={props.setVisible}
                        data={state.noticeRecord}
                        componentKey={props.componentKey}
                    />}
                </Modal>
            );
        }

    }, [state.isFlow, state.componentKey, state.visible])

    return modal ? modal : null;
}

export default NoticeModalView;