export interface NoticeRecordData {
    id: number,
    // FLOW_TODO, FLOW_NOTICE, CUSTOM_TODO, CUSTOM_NOTICE
    type: string,
    // TODO NOTICE
    commonType: string,
    title: string,
    content: string,
    componentKey: string,
    businessCode: string,
    businessId: number,
    senderId: number,
    senderName: string,
    todoUserId: number,
    customParams: string,
    // 状态， UNREAD, READ, DONE
    status: string,

    // 允许任意扩展字段
    [key: string]: any;

}

export interface NoticeModalViewState {
    visible: boolean;
    setVisible: (visible: boolean) => void;

    noticeRecord?: NoticeRecordData;
    componentKey?: string;
    isFlow: boolean | undefined;

}

export interface NoticeModalViewApi {

    getNoticeRecordDetail: (id: any) => Promise<any>;

    getFlowRecordDetail: (id: any) => Promise<any>;
}