import {useEffect, useRef, useState} from "react";
import {NoticeModalViewState} from "@/components/user/notice/types";
import {NoticeModalViewApiImpl} from "@/components/user/notice/model";
import NoticeModalViewPresenter from "@/components/user/notice/presenter";

const initialState = {
    visible: false,
    noticeRecord: undefined,
    isFlow: undefined
}

export function useNoticeModalViewPresenter(setVisible: (visible: boolean) => void) {
    const [state, dispatch] = useState<NoticeModalViewState>({
        ...initialState,
        setVisible,
    });
    const presenterRef = useRef<NoticeModalViewPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new NoticeModalViewPresenter(state, new NoticeModalViewApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}