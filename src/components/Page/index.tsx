/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-15 23:12:54
 * @Description: 
 * @LastEditTime: 2025-08-04 09:20:29
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import RoleKeyProvider from "@/framework/Permission/RoleKeyProvider";
import { PageContainer } from "@ant-design/pro-components";
import { PageContainerProps } from "@ant-design/pro-layout/es/components/PageContainer";
import AccessProvider from "@/framework/Permission/AccessProvider";
import './index.scss'
import { Watermark } from "antd";

interface PageProps {
    children: React.ReactNode;
    enablePageContainer?: boolean;
    pageContainerProps?: PageContainerProps;
    className?: any
}


const Page: React.FC<PageProps> = (props) => {
    return (
        <Watermark content={ localStorage.getItem("usernickname")! }>
        <RoleKeyProvider>
            <AccessProvider>
                {props.enablePageContainer && (
                    <PageContainer {...props.pageContainerProps} className={props.className}>
                        {props.children}
                    </PageContainer>
                )}
                {!props.enablePageContainer && (
                    <div className={props.className}>
                        {props.children}
                    </div>
                )}
            </AccessProvider>
            </RoleKeyProvider>
            </Watermark>
    )
}

export default Page;