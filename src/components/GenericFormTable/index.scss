.form-table {
  border-collapse: collapse;
  width: 100%;
  height: 100%;
  font-size: 20px;
  table-layout: fixed; // 改为 auto，让列宽自适应内容

  th,
  td {
    border: 1px solid red;
    padding: 8px;
    text-align: left;
    overflow: visible; // 允许内容溢出（或换行）
    text-overflow: clip; // 不截断内容
    white-space: normal; // 允许换行
    font-size: var(--content-font-size);
  }

  .form-label,
  .form-value,
  .info_lable {
    overflow: visible; // 允许内容溢出（或换行）
    text-overflow: clip; // 不截断内容
    white-space: normal; // 允许换行
  }

  .header_red {
    border-top: 1px solid red;
  }

  .label-column {
    width: 20%;
  }

  .time-column {
    width: 200px;
  }

  .button-column {
    width: 100px;
  }

  .form-label {
    font-size: var(--content-font-size) !important;
    font-weight: bold;

    color: red;

    &.red-label {
      color: red;
    }
  }

  .info_lable {
    text-align: center;
    font-size: var(--content-font-size);
  }

  .form-value {
    padding: 0 8px;
  }

  .time-cell {
    font-size: var(--content-font-size) !important;
    font-weight: normal !important;
  }

  .flex_between {
    display: flex;
    justify-content: space-between;
  }

}
