import React from "react";
import "./index.scss";
import { Button } from "@itm/components-pc";
import { Tooltip } from "antd";

interface TableListItem {
  label?: string;
  value?: React.ReactNode;
  width?: string;
}

interface TableRow {
  label: string;
  value?: React.ReactNode;
  isLabelRed?: boolean;
  isButtonCell?: boolean;
  buttonText?: string;
  colSpan?: number;
  valueType?: React.ReactNode;
  children?: TableListItem[];
  onButtonClick?: () => void;
  isShow?: boolean;
}

interface TableHeader {
  label?: string;
  value?: string;
  isformLabel?: boolean;
  isTimeCell?: boolean;
}

interface GenericFormTableProps {
  header?: TableHeader[];
  rows?: TableRow[];
  isBorder?: boolean;
}

const GenericFormTable: React.FC<GenericFormTableProps> = ({
  header = [],
  rows = [],
  isBorder = false,
}) => {
  return (
    <table
      className={`form-table ${
        isBorder ? "form-table_top_border" : "form-table_all_border"
      }`}
    >
      <colgroup>
        <col style={{ width: "20%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "20%" }} />
        <col style={{ width: "15%" }} />
      </colgroup>
      <tbody>
        {header.length > 0 && (
          <tr>
            {header.map((item, index) => (
              <React.Fragment key={index}>
                <td className={item.isformLabel ? "form-label" : ""}>
                  {item.label}
                </td>
                <td colSpan={2} className={item.isTimeCell ? "" : "time-cell"}>
                  {item.value}
                </td>
              </React.Fragment>
            ))}
          </tr>
        )}
        {rows.map((row, index) => {
          if (row.isShow === false) return null;

          if (row.colSpan === 3) {
            return (
              <tr key={index}>
                <td
                  className={`form-label ${row.isLabelRed ? "red-label" : ""}`}
                >
                  {row.label}
                </td>
                <td className="form-value" colSpan={2}>
                  {row.value}
                </td>
                <td className="form-value" colSpan={3}>
                  {row.valueType}
                </td>
              </tr>
            );
          }

          if (row.children && row.children.length > 0) {
            return row.children.map((info, i) => (
              <tr key={i}>
                {i === 0 && (
                  <td
                    rowSpan={row.children?.length || 0}
                    className={`form-label ${
                      row.isLabelRed ? "red-label" : ""
                    }`}
                  >
                    {row.label}
                  </td>
                )}
                {info.label && (
                  <Tooltip placement="topLeft" title={info.label} arrow>
                    <td
                      className="info_lable"
                      style={info.width ? { width: info.width } : {}}
                    >
                      {info.label}
                    </td>
                  </Tooltip>
                )}
                <td colSpan={info.label ? 4 : 5}>{info.value}</td>
              </tr>
            ));
          }

          return (
            <tr key={index} className={index === 0 ? "header_red" : ""}>
              <td className={`form-label ${row.isLabelRed ? "red-label" : ""}`}>
                {row.label}
              </td>
              <td colSpan={row.isButtonCell ? 4 : 5} className="form-value">
                {row.value}
              </td>
              {row.isButtonCell && (
                <td className="button-cell">
                  <Button className="form-button" onClick={row.onButtonClick}>
                    {row.buttonText || "按钮"}
                  </Button>
                </td>
              )}
            </tr>
          );
        })}
      </tbody>
    </table>
  );
};

export default GenericFormTable;
