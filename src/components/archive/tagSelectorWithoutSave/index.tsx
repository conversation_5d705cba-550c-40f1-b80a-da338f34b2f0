import React, {useState, useEffect} from 'react';
import './index.css';
import {findTagsByDataType, relationList, saveRelationData} from "@/api/archive/tag";
import {message} from "antd";

interface TreeNode {
    id: number;
    code: string;
    name: string;
    parentId: number | null;
    sysIndex: number;
    sysEnabled: number;
    nodeId: number;
    parentNodeId: number | null;
    children?: TreeNode[];
}

interface TransformedTag extends Omit<TreeNode, 'children'> {
    level: number;
    fullPath?: string;
    children?: TransformedTag[];
}

interface SelectedTag {
    id: number;
    tagCode: string;
    dataType: string;
    dataId: number;
    secondLevelId?: number;
}

// 按层级组织的标签数据结构
interface HierarchicalTags {
    [firstLevelId: number]: {
        id: number;
        name: string;
        code: string;
        secondLevels: {
            [secondLevelId: number]: {
                id: number;
                name: string;
                code: string;
                thirdLevels: TransformedTag[];
            };
        };
    };
}

// ✅ 定义二级标签类型
type SecondLevelTag = {
    id: number;
    name: string;
    code: string;
    thirdLevels: TransformedTag[];
};

interface TagSelectorProps {
    dataType: string;
    dataId: number;
    onChange?: (value: any) => void; // 选中值变化时的回调
}

const TagSelectorWithoutSave: React.FC<TagSelectorProps> = (props) => {
    const [allTags, setAllTags] = useState<HierarchicalTags>({});
    const [selectedTags, setSelectedTags] = useState<{ [tagId: number]: SelectedTag }>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [activeFirstLevelId, setActiveFirstLevelId] = useState<number | null>(null);
    // 新增：控制哪些三级标签被展开
    const [expandedTags, setExpandedTags] = useState<{ [tagId: number]: boolean }>({});


    // 递归转换树形结构为 TransformedTag 结构
    const transformNode = (node: TreeNode, level: number, pathNames: string[]): TransformedTag => {
        const fullPath = [...pathNames, node.name].join(' > ');

        return {
            ...node,
            level,
            fullPath,
            children: node.children?.map(child =>
                transformNode(child, level + 1, [...pathNames, node.name])
            )
        };
    };

    // 将树形结构转换为层级对象结构
    const transformToHierarchical = (nodes: TreeNode[]): HierarchicalTags => {
        const result: HierarchicalTags = {};

        if (!nodes || nodes.length === 0) return result;

        nodes.filter(node => node.sysEnabled === 1).forEach(firstLevel => {
            if (!firstLevel.children || firstLevel.children.length === 0) return;

            result[firstLevel.id] = {
                id: firstLevel.id,
                name: firstLevel.name,
                code: firstLevel.code,
                secondLevels: {}
            };

            firstLevel.children
                .filter(child => child.sysEnabled === 1)
                .sort((a, b) => a.sysIndex - b.sysIndex)
                .forEach(secondLevel => {
                    if (!secondLevel.children || secondLevel.children.length === 0) return;

                    const thirdLevels = secondLevel.children
                        .filter(child => child.sysEnabled === 1)
                        .sort((a, b) => a.sysIndex - b.sysIndex)
                        .map(thirdLevel => transformNode(thirdLevel, 2, [firstLevel.name, secondLevel.name]));

                    result[firstLevel.id].secondLevels[secondLevel.id] = {
                        id: secondLevel.id,
                        name: secondLevel.name,
                        code: secondLevel.code,
                        thirdLevels
                    };
                });
        });

        return result;
    };

    const loadData = async () => {
        setLoading(true);
        setError(null);

        const dataType = props.dataType;
        const dataId = props.dataId;

        try {
            const [tagsRes, selectedRes] = await Promise.all([
                findTagsByDataType(dataType),
                relationList(dataType, dataId)
            ]);

            const tagResponse = tagsRes.data?.list || [];
            const selectedResponse = selectedRes.data?.list || [];

            const hierarchicalTags = transformToHierarchical(tagResponse);
            setAllTags(hierarchicalTags);

            const firstLevelIds = Object.keys(hierarchicalTags).map(Number);
            if (firstLevelIds.length > 0) {
                setActiveFirstLevelId(firstLevelIds[0]);
            }

            const initialSelectedTags: { [tagId: number]: SelectedTag } = {};

            selectedResponse.forEach((selectedItem: SelectedTag) => {
                let found = false;

                for (const firstLevelId in hierarchicalTags) {
                    const firstLevel = hierarchicalTags[firstLevelId];

                    for (const secondLevelId in firstLevel.secondLevels) {
                        const thirdLevels = firstLevel.secondLevels[secondLevelId].thirdLevels;

                        // 先在第三层级查找
                        const matchedThirdLevel = thirdLevels.find(tag => tag.code === selectedItem.tagCode);
                        if (matchedThirdLevel) {
                            initialSelectedTags[matchedThirdLevel.id] = {
                                id: matchedThirdLevel.id,
                                tagCode: matchedThirdLevel.code,
                                dataType,
                                dataId,
                                secondLevelId: Number(secondLevelId)
                            };
                            found = true;
                            break;
                        }

                        // 再在第四层级查找
                        for (const thirdLevel of thirdLevels) {
                            if (thirdLevel.children && thirdLevel.children.length > 0) {
                                const matchedFourthLevel = thirdLevel.children.find(child => child.code === selectedItem.tagCode);
                                if (matchedFourthLevel) {
                                    initialSelectedTags[matchedFourthLevel.id] = {
                                        id: matchedFourthLevel.id,
                                        tagCode: matchedFourthLevel.code,
                                        dataType,
                                        dataId,
                                        secondLevelId: Number(secondLevelId)
                                    };
                                    found = true;
                                    break;
                                }
                            }
                        }

                        // 再在第五层级查找
                        for (const thirdLevel of thirdLevels) {
                            if (thirdLevel.children && thirdLevel.children.length > 0) {
                                for (const fourthLevel of thirdLevel.children) {
                                    if (fourthLevel.children && fourthLevel.children.length > 0) {
                                        const matchedFifthLevel = fourthLevel.children.find(grandchild => grandchild.code === selectedItem.tagCode);
                                        if (matchedFifthLevel) {
                                            initialSelectedTags[matchedFifthLevel.id] = {
                                                id: matchedFifthLevel.id,
                                                tagCode: matchedFifthLevel.code,
                                                dataType,
                                                dataId,
                                                secondLevelId: Number(secondLevelId)
                                            };
                                            found = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        if (found) break;
                    }

                    if (found) break;
                }
            });
            setSelectedTags(initialSelectedTags);
        } catch (err) {
            setError(err instanceof Error ? err.message : '标签加载失败');
        } finally {
            setLoading(false);
        }
    };


    useEffect(() => {
        loadData().then(r => {
        });
    }, [props.dataType, props.dataId]);

    const handleFirstLevelSelect = (firstLevelId: number) => {
        setActiveFirstLevelId(firstLevelId);
    };

    const findParentNode = (childId: number): TransformedTag | undefined => {
        for (const firstLevel of Object.values(allTags)) {
            for (const secondLevel of Object.values(firstLevel.secondLevels)) {
                // ✅ 显式类型断言
                const level = secondLevel as {
                    id: number;
                    name: string;
                    code: string;
                    thirdLevels: TransformedTag[];
                };

                for (const thirdLevel of level.thirdLevels) {
                    if (thirdLevel.children?.some(child => child.id === childId)) {
                        return thirdLevel; // 返回父节点
                    }
                    if (thirdLevel.children) {
                        for (const fourthLevel of thirdLevel.children) {
                            if (fourthLevel.children?.some(grandchild => grandchild.id === childId)) {
                                return fourthLevel; // 返回父节点
                            }
                        }
                    }
                }
            }
        }
        return undefined;
    };


    const handleTagSelect = (tag: TransformedTag, secondLevelId: number, e?: React.MouseEvent) => {
        if (e) {
            e.stopPropagation();
        }

        if (!tag) return;

        // 如果是父级标签（有子节点）
        if (tag.children && tag.children.length > 0) {
            setExpandedTags(prev => {
                const newExpandedTags = {...prev};

                // ✅ 找到当前点击标签的父节点（用于判断是第几级）
                const parentTag = findParentNode(tag.id);

                if (parentTag) {
                    // 是四级标签：关闭同级的其他四级标签
                    parentTag.children?.forEach(child => {
                        if (child.id !== tag.id) {
                            delete newExpandedTags[child.id];
                        }
                    });
                } else {
                    // 是三级标签：关闭其他三级标签
                    const currentSecondLevel = activeFirstLevel?.secondLevels[secondLevelId];
                    currentSecondLevel?.thirdLevels.forEach(thirdLevel => {
                        if (thirdLevel.id !== tag.id) {
                            delete newExpandedTags[thirdLevel.id];
                        }
                    });
                }

                // ✅ 切换当前标签的展开状态
                newExpandedTags[tag.id] = !newExpandedTags[tag.id];

                return newExpandedTags;
            });
            return;
        }

        // 以下为原有选中逻辑
        const parentThirdLevels = Object.values(activeFirstLevel?.secondLevels[secondLevelId]?.thirdLevels || []);
        const siblings = parentThirdLevels.find(t => t.id === tag.id)?.children || [];

        if (siblings.length > 0) return;

        setSelectedTags(prev => {
            const newSelectedTags = {...prev};

            // 检查是否已经选中
            const isSelected = prev[tag.id] && prev[tag.id].tagCode === tag.code;

            // 如果已经选中，则取消选中
            if (isSelected) {
                const {[tag.id]: removed, ...rest} = prev;
                return rest;
            }

            // 否则添加选中项（不再清除同级）
            return {
                ...newSelectedTags,
                [tag.id]: {
                    id: tag.id,
                    tagCode: tag.code,
                    dataType: props.dataType,
                    dataId: props.dataId,
                    secondLevelId
                }
            };
        });
    };


    const saveTags = async () => {
        if (Object.keys(selectedTags).length === 0) {
            message.warning('请至少选择一个标签');
            return;
        }

        const selectedTagList = Object.values(selectedTags).map(tag => ({
            code: tag.tagCode,
            name: findTagName(tag.tagCode) // ✅ 根据 tagCode 查找对应的 name
        }));

        props.onChange?.({ tags: selectedTagList });
    };

    const findTagName = (tagCode: string): string => {
        for (const firstLevel of Object.values(allTags)) {
            for (const secondLevel of Object.values(firstLevel.secondLevels)) {
                // ✅ 使用类型别名
                const level = secondLevel as SecondLevelTag;

                for (const thirdLevel of level.thirdLevels) {
                    if (thirdLevel.code === tagCode) {
                        return thirdLevel.name;
                    }
                    if (thirdLevel.children) {
                        for (const fourthLevel of thirdLevel.children) {
                            if (fourthLevel.code === tagCode) {
                                return fourthLevel.name;
                            }
                            if (fourthLevel.children) {
                                for (const fifthLevel of fourthLevel.children) {
                                    if (fifthLevel.code === tagCode) {
                                        return fifthLevel.name;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return tagCode; // 如果找不到，返回 code 本身
    };

    const handleRetry = () => {
        loadData().then(r => {
        });
    };

    if (loading) {
        return <div className="tag-selector-loading">Loading tags...</div>;
    }

    if (error) {
        return (
            <div className="tag-selector-error">
                <p>{error}</p>
                <button className="retry-button" onClick={handleRetry}>Retry</button>
            </div>
        );
    }

    const activeFirstLevel = activeFirstLevelId ? allTags[activeFirstLevelId] : null;

    const hasSelectedChild = (tag: TransformedTag): boolean => {
        if (!tag.children || tag.children.length === 0) {
            return false;
        }

        return tag.children.some(child => {
            const isSelected = !!selectedTags[child.id];
            const hasSelectedDescendant = hasSelectedChild(child); // 递归判断
            return isSelected || hasSelectedDescendant;
        });
    };

    // ✅ 显式定义 secondLevel 类型
    type SecondLevelType = {
        id: number;
        name: string;
        code: string;
        thirdLevels: TransformedTag[];
    };

    return (
        <>
            <div className="tag-selector-container">
                {/* 第一级标签 */}
                <div className="first-level-container">
                    {Object.values(allTags).map(firstLevel => (
                        <div
                            key={firstLevel.id}
                            className={`first-level-tag ${activeFirstLevelId === firstLevel.id ? 'active' : ''}`}
                            onClick={() => handleFirstLevelSelect(firstLevel.id)}
                        >
                            {firstLevel.name}
                        </div>
                    ))}
                </div>

                {/* 标签内容区域 - 可滚动 */}
                <div className="content-wrapper">
                    {activeFirstLevel ? (
                        Object.values(activeFirstLevel.secondLevels).map((secondLevel) => {
                            // ✅ 显式类型断言
                            const level = secondLevel as {
                                id: number;
                                name: string;
                                code: string;
                                thirdLevels: TransformedTag[];
                            };

                            return (
                                <div key={level.id} className="level-row">
                                    <div className="second-level-title-container">
                                        <div className="second-level-title">{level.name}</div>
                                    </div>
                                    <div className="third-level-container">
                                        {level.thirdLevels.map((tag: TransformedTag) => (
                                            <div key={tag.id}>
                                                <div className="third-level-tag-wrapper" style={{position: 'relative'}}>
                                                    <div
                                                        className={`third-level-tag ${selectedTags[tag.id]?.tagCode === tag.code ? 'selected' : ''} ${hasSelectedChild(tag) ? 'has-selected-child' : ''}`}
                                                        onClick={(e) => handleTagSelect(tag, level.id, e)}
                                                    >
                                                        {tag.name}
                                                        {tag.children && tag.children.length > 0 && (
                                                            <span className="expand-icon">
                                            {expandedTags[tag.id] ? '▼' : '▶'}
                                        </span>
                                                        )}
                                                    </div>

                                                    {/* 展开第四级标签 */}
                                                    {expandedTags[tag.id] && tag.children && tag.children.length > 0 && (
                                                        <div className="fourth-level-container">
                                                            {tag.children.map(child => (
                                                                <div key={child.id}>
                                                                    <div className="third-level-tag-wrapper"
                                                                         style={{position: 'relative'}}>
                                                                        <div
                                                                            className={`third-level-tag fourth-level-tag 
                                                        ${selectedTags[child.id]?.tagCode === child.code ? 'selected' : ''}                                                        ${hasSelectedChild(child) ? 'has-selected-child' : ''}`}
                                                                            onClick={(e) => handleTagSelect(child, level.id, e)}
                                                                        >
                                                                            {child.name}
                                                                            {child.children && child.children.length > 0 && (
                                                                                <span className="expand-icon">
                                                                {expandedTags[child.id] ? '▼' : '▶'}
                                                            </span>
                                                                            )}
                                                                        </div>

                                                                        {/* 展开第五级标签 */}
                                                                        {expandedTags[child.id] && child.children && child.children.length > 0 && (
                                                                            <div className="fifth-level-container">
                                                                                {child.children.map(grandchild => (
                                                                                    <div
                                                                                        key={grandchild.id}
                                                                                        className={`third-level-tag fifth-level-tag ${selectedTags[grandchild.id]?.tagCode === grandchild.code ? 'selected' : ''}`}
                                                                                        onClick={(e) => handleTagSelect(grandchild, level.id, e)}
                                                                                    >
                                                                                        {grandchild.name}
                                                                                    </div>
                                                                                ))}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            );
                        })
                    ) : (
                        <div className="no-tags-message">暂无数据</div>
                    )}
                </div>
            </div>
            {/* 底部按钮 */}
            <div className="footer-buttons">
                <button className="btn-cancel" onClick={() => {
                    props.onChange?.(false)
                }}>取消
                </button>
                <button className="btn-confirm" onClick={saveTags}>确认</button>
            </div>
        </>
    );
};

export default TagSelectorWithoutSave;
