.tag-selector-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', sans-serif;
    color: #333;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    height: 540px;
    position: relative;
    box-sizing: border-box; /* 确保 padding 不影响宽度 */
}

.first-level-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #d9d9d9;
    flex-wrap: wrap;
}

.first-level-tag {
    padding: 8px 16px;
    background-color: #f5f5f5;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    color: #595959;
    border: 1px solid transparent;
}

.first-level-tag.active {
    background-color: #4096ff;
    color: white;
    border-color: #4096ff;
}

.first-level-tag:hover:not(.active) {
    background-color: #e6e6e6;
}

.content-wrapper {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
}

.level-row {
    display: flex;
    /*align-items: center;*/
    gap: 10px;
    margin-bottom: 15px;
    margin-top: 10px;
    flex-wrap: wrap;
}
/* 第二层级容器 */
.second-level-title-container {
    display: flex;
    align-items: flex-start; /* 垂直顶部对齐 */
    margin-top: 6px;
}

.second-level-title {
    font-weight: 600;
    min-width: 100px;
    text-align: center;
    margin-right: 10px;
    margin-top: 0;
    color: #262626;
}

.third-level-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: 1;
}

/*.third-level-tag {*/
/*    padding: 6px 12px;*/
/*    border: 1px solid #d9d9d9;*/
/*    border-radius: 6px;*/
/*    cursor: pointer;*/
/*    transition: all 0.2s ease-in-out;*/
/*    background-color: #f5fbff; !* ✅ 稍微变深一点的淡蓝 *!*/
/*    min-width: 100px;*/
/*    text-align: center;*/
/*    color: #595959;*/
/*    font-size: 13px;*/
/*}*/

.third-level-tag {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: #f5fbff; /* ✅ 稍微变深一点的淡蓝 */
    min-width: 100px;
    text-align: center;
    color: #595959;
    font-size: 13px;
    position: relative; /* 确保相对定位 */
    z-index: 0; /* 默认 z-index */
}

.third-level-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.third-level-tag.selected {
    border-color: #4096ff;
    /* 选中后颜色变深 */
    background-color: #bae7ff;
    color: #1890ff;
    font-weight: 600;
}

/* 第三层级标签 - 有已选中的子标签 */
.third-level-tag.has-selected-child:not(.selected) {
    background-color: #d9f7be; /* 浅绿色背景 */
    color: #237804; /* 深绿色文字 */
    border-color: #b7eb8f;
}

/* 箭头图标 */
.third-level-tag .expand-icon {
    margin-left: 8px;
    font-size: 10px;
}

/* 第四级容器 */
/*.fourth-level-container {*/
/*    display: flex;*/
/*    flex-wrap: wrap;*/
/*    gap: 10px;*/
/*    margin-top: 6px;*/
/*    padding-left: 20px;*/
/*    border-left: 2px solid #e8e8e8;*/
/*    position: absolute;*/
/*    margin-left: 10px;*/
/*}*/

.fourth-level-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 6px;
    padding-left: 20px;
    border-left: 2px solid #e8e8e8;
    position: absolute; /* 修改为绝对定位 */
    max-height: 200px; /* 设置最大高度 */
    overflow-y: auto; /* 添加垂直滚动条 */
    background-color: #fff; /* 可选：设置背景颜色以增强视觉效果 */
    z-index: 1; /* 提高 z-index 以覆盖其他元素 */
}


/* 第四级标签样式 - 美化后 */
.fourth-level-tag {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    margin-top: 5px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: #f9f9f9; /* ✅ 更浅的淡灰背景 */
    color: #666;
    font-size: 13px;
    min-width: 90px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}


/* 鼠标悬停效果 */
.fourth-level-tag:hover {
    transform: translateY(-1px);
    background-color: #f5f5f5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 选中状态（复用 third-level-tag 的 selected 类） */
.fourth-level-tag.selected {
    border-color: #4096ff;
    background-color: #bae7ff;
    color: #1890ff;
    font-weight: 600;
}

.no-tags-message {
    color: #bbb;
    text-align: center;
    padding: 20px;
    width: 100%;
    font-size: 13px;
}
.footer-buttons-outer {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px; /* 调整左右内边距 */
    display: flex;
    justify-content: center; /* 水平居中 */
    box-sizing: border-box; /* 确保 padding 不影响宽度 */
}

.footer-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e8e8e8;
    background-color: #fafafa;
    padding: 10px 0; /* 调整内边距 */
    width: 100%; /* 确保宽度为 100% */
    box-sizing: border-box; /* 确保 padding 不影响宽度 */
}

.footer-buttons button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    transition: all 0.2s;
}

.footer-buttons .btn-cancel {
    background-color: #ffffff;
    color: #595959;
}

.footer-buttons .btn-confirm {
    background-color: #4096ff;
    color: white;
    border-color: #4096ff;
}

.footer-buttons .btn-cancel:hover {
    background-color: #f5f5f5;
}

.footer-buttons .btn-confirm:hover {
    background-color: #1890ff;
}


.third-level-tag.disabled {
    color: #aaa;
    pointer-events: none;
    cursor: not-allowed;
}

.fifth-level-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 4px;
    padding-left: 20px;
    border-left: 2px solid #e8e8e8;
    background-color: transparent;
    position: relative;
    max-height: none; /* 移除高度限制 */
    overflow: visible; /* 移除滚动 */
    z-index: auto;
}

.fifth-level-tag {
    padding: 5px 10px;
    border: 1px solid #d9d9d9;
    margin-top: 3px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: #f9f9f9;
    color: #666;
    font-size: 12px;
    min-width: 80px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.fifth-level-tag:hover {
    transform: translateY(-1px);
    background-color: #f5f5f5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.fifth-level-tag.selected {
    border-color: #4096ff;
    background-color: #bae7ff;
    color: #1890ff;
    font-weight: 600;
}