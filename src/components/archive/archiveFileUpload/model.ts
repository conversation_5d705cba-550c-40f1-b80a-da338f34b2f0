import {ArchiveFileApi} from "./types";
import {
    updateArchiveFile,
    generateFileAuthFromArchive
} from "@/api/archive/archive";


export class ArchiveFileApiImpl implements ArchiveFileApi {
    
    updateArchiveFile(formData: FormData): Promise<any> {
        return updateArchiveFile(formData);
    }

    async generateFileAuth(params: any): Promise<{ success: boolean }> {
        return generateFileAuthFromArchive(params);
    }

    

}