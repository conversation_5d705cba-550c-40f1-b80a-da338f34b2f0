import React, {FC} from "react";

import {Form, FormUploader} from "@/components/form-pc";
import {useValuePresenter} from "@/components/archive/archiveFileUpload/hooks/usePresenter";

interface ArchiveFileUploaderProps {
    label: string;
    uploaderMaxCount: number;
    uploaderAccept: string;
    archiveFileKey: string;
    onKeyChange?: (key: string) => void;
}

const ArchiveFileUploader: FC<ArchiveFileUploaderProps> = (props) => {
    const {state, presenter} = useValuePresenter();
    const form = Form.useForm();
    React.useEffect(() => {
        console.log("props.archiveFileKey", props.archiveFileKey)
        form.reset()
        form.setFieldsValue({
            archiveFile: {
                key: props.archiveFileKey
            }
        });
    }, [props.archiveFileKey]);

    return (
        <Form
            form={form}
            layout={"vertical"}
        >
            <FormUploader
                name={["archiveFile", "key"]}
                required={false}
                label={props.label}
                uploaderMaxCount={props.uploaderMaxCount}
                uploaderAccept={props.uploaderAccept}
                onUploaderUpload={async (filename: string, base64: string) => {
                    console.log("filename", filename)
                    const key = await presenter?.addUploadFile(filename, base64);
                    return {
                        id: key as string,
                        name: filename,
                        url: base64
                    };
                }}
                onUploaderLoad={async (ids) => {
                    console.log("ids", ids)
                    if (ids && props.onKeyChange) {
                        props.onKeyChange(ids);
                    }
                    try {
                        const idList = ids.split(',')
                        return await Promise.all(
                            idList.map(async (id) => {
                                const fileAuth = await presenter?.generateFileAuth(id);
                                return {
                                    id: id,
                                    name: (fileAuth as any)?.name,
                                    url: `/itm-archive-oss/open/cmd/file/download?code=${(fileAuth as any)?.code}`
                                };
                            })
                        );
                    } catch (error) {
                        console.error('加载文件列表失败:', error);
                        return [];
                    }
                }}
            />
        </Form>)
}

export default ArchiveFileUploader;