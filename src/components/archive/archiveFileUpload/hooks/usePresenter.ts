import React from "react";
import {ArchiveFileState} from "@/components/archive/archiveFileUpload/types";
import {ValuePresenter} from "@/components/archive/archiveFileUpload/presenter";
import {ArchiveFileApiImpl} from "@/components/archive/archiveFileUpload/model";

const initialState: ArchiveFileState = {
    uploadedFile: {filename: "", base64: ""},
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<ArchiveFileState>(initialState);

    const presenterRef = React.useRef<ValuePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 Presenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new ValuePresenter(dispatch, new ArchiveFileApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}