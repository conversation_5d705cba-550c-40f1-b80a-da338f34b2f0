import {ArchiveFileApi, ArchiveFileState, Dispatch} from "@/components/archive/archiveFileUpload/types";


interface TreeOption {
    title: string;
    key: string;
    children?: TreeOption[];
}

interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class ValuePresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<ArchiveFileState>;

    private readonly api: ArchiveFileApi;

    private state: ArchiveFileState;

    public constructor(dispatch: Dispatch<ArchiveFileState>, api: ArchiveFileApi, state: ArchiveFileState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: ArchiveFileState) => {
        this.state = newStatus;
    }
    // 在 presenter.ts 中明确返回类型
    public addUploadFile = async (filename: string, base64: string): Promise<string> => {
        try {
            const formData = new FormData();
            const blob = this.dataURLtoBlob(base64);
            const file = new File([blob], filename, {type: blob.type});
            formData.append("file", file);
            formData.append("source", "VeBrSrY4Vm4");

            const res = await this.api.updateArchiveFile(formData);

            if (res?.data) {
                return res.data;
            }
            throw new Error('上传失败：未获取到文件key');
        } catch (error) {
            console.error('文件上传失败:', error);
            throw error;
        }
    }

    public generateFileAuth = async (fileKey: string): Promise<string> => {
        try {
            const params = {
                fileKey: fileKey
            };

            const res = await this.api.generateFileAuth(params);

            if (res?.data?.code) {
                return res.data;
            }
            throw new Error('生成文件授权码失败：未获取到授权码');
        } catch (error) {
            console.error('生成文件授权码失败:', error);
            throw error;
        }
    }

    public dataURLtoBlob(dataUrl: string): Blob {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "";
        const bStr = atob(arr[1]);
        let n = bStr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bStr.charCodeAt(n);
        }
        return new Blob([u8arr], {type: mime});
    }
}