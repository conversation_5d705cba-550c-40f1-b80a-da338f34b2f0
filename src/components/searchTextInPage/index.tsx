import React, { useState, useRef, useEffect } from "react";
import { Input, Button, Space, Tooltip, Checkbox } from "antd";
import {
  SearchOutlined,
  CloseOutlined,
  RightOutlined,
  LeftOutlined,
} from "@ant-design/icons";
import "./index.scss";

interface TextSearchProps {
  containerRef: React.RefObject<HTMLElement>;
  className?: string;
}

/**
 * 页面文字搜索组件
 */
const TextSearch: React.FC<TextSearchProps> = ({ containerRef, className }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [matches, setMatches] = useState<Range[]>([]);
  const [currentMatchIndex, setCurrentMatchIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const [jumpInputValue, setJumpInputValue] = useState(""); // 新增：跳转输入框的值
  const observer = useRef<MutationObserver | null>(null);
  const [showSearch, setShowSearch] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 清除所有高亮
  const clearHighlights = () => {
    const highlights = document.querySelectorAll(".search-highlight");
    highlights.forEach((highlight) => {
      const parent = highlight.parentNode;
      if (parent) {
        // 替换高亮元素为原始文本节点
        while (highlight.firstChild) {
          parent.insertBefore(highlight.firstChild, highlight);
        }
        parent.removeChild(highlight);
      }
    });
    setMatches([]);
    setCurrentMatchIndex(-1);
    setIsSearching(false);
  };

  // 执行搜索并高亮匹配项
  const searchText = () => {
    if (!searchTerm.trim() || !containerRef.current) return;

    clearHighlights();
    setIsSearching(true);

    const container = containerRef.current;
    const textToSearch = searchTerm.toLowerCase();
    const matches: Range[] = [];

    // 创建文本遍历器
    const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, {
      acceptNode: (node) => {
        // 跳过脚本、样式和已高亮的元素
        if (
          (node.parentElement && node.parentElement.tagName === "SCRIPT") ||
          (node.parentElement && node.parentElement.tagName === "STYLE") ||
          (node.parentElement &&
            node.parentElement.classList.contains("search-highlight"))
        ) {
          return NodeFilter.FILTER_SKIP;
        }
        return NodeFilter.FILTER_ACCEPT;
      },
    });

    let node: Text | null;
    while ((node = walker.nextNode() as Text | null)) {
      const text = node.textContent?.toLowerCase();
      if (!text || !text.includes(textToSearch)) continue;

      let startIndex = 0;
      while (startIndex < text.length) {
        const index = text.indexOf(textToSearch, startIndex);
        if (index === -1) break;

        // 创建匹配范围
        const range = document.createRange();
        range.setStart(node, index);
        range.setEnd(node, index + textToSearch.length);
        matches.push(range);

        startIndex = index + textToSearch.length;
      }
    }

    // 高亮所有匹配项
    matches.forEach((range, index) => {
      const span = document.createElement("span");
      span.className = `search-highlight ${
        index === currentMatchIndex ? "search-highlight-current" : ""
      }`;
      span.style.backgroundColor =
        index === currentMatchIndex ? "#ffeb3b" : "#ffff00";
      span.style.fontWeight = index === currentMatchIndex ? "bold" : "normal";
      range.surroundContents(span);
    });

    setMatches(matches);
    if (matches.length > 0) {
      setCurrentMatchIndex(0);
      scrollToMatch(0);
    }
  };

  // 滚动到指定匹配项
  const scrollToMatch = (index: number) => {
    if (index < 0 || index >= matches.length || !containerRef.current) return;

    const highlights: any = document.querySelectorAll(".search-highlight");
    if (highlights[index]) {
      highlights[index].scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
      highlights[index].focus();
    }
  };

  // 导航到下一个匹配项
  const goToNextMatch = () => {
    if (matches.length === 0) return;
    const nextIndex = (currentMatchIndex + 1) % matches.length;
    setCurrentMatchIndex(nextIndex);
    scrollToMatch(nextIndex);
  };

  // 导航到上一个匹配项
  const goToPrevMatch = () => {
    if (matches.length === 0) return;
    const prevIndex = (currentMatchIndex - 1 + matches.length) % matches.length;
    setCurrentMatchIndex(prevIndex);
    scrollToMatch(prevIndex);
  };

  // 处理跳转输入框变化
  const handleJumpInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setJumpInputValue(value);

    // 如果输入的是数字，自动跳转
    if (/^\d+$/.test(value)) {
      const n = parseInt(value, 10);
      if (n >= 1 && n <= matches.length) {
        setCurrentMatchIndex(n - 1);
        scrollToMatch(n - 1);
      }
    }
  };

  // 监听输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // 监听回车键搜索
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      if (searchTerm.trim() && matches.length > 0) {
        goToNextMatch();
      } else {
        searchText();
      }
    }
  };

  // 监听容器DOM变化，清除高亮
  useEffect(() => {
    if (!containerRef.current) return;

    // 创建MutationObserver监听DOM变化
    observer.current = new MutationObserver(() => {
      if (isSearching) clearHighlights();
    });

    // 观察容器内的子节点和属性变化
    observer.current.observe(containerRef.current, {
      childList: true,
      attributes: true,
      subtree: true,
    });

    return () => {
      observer.current?.disconnect();
    };
  }, [containerRef, isSearching]);

  // 清理组件时清除高亮
  useEffect(() => {
    return () => {
      clearHighlights();
    };
  }, []);


  return (
    <div className={`text-search-container ${className || ""}`}>
      <Space>
        <div className="search-container">
          <div
            className={`search-input-wrapper ${showSearch && "expanded"}`}
            id="searchBox"
            onClick={() => {
              setShowSearch(true);
              inputRef.current?.focus();
            }}
          >
            <div className="search-icon">
              <SearchOutlined style={{ color: "var(--primary-color)" }} />
            </div>
            <input
              className="search-input"
              ref={inputRef}
              placeholder="页内搜索"
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              value={searchTerm}
              onBlur={() => setShowSearch(false)}
            />
            {isSearching && (
              <span className="match-count">
                {currentMatchIndex + 1}/{matches.length}
              </span>
            )}
          </div>
        </div>

        {/* <Input
          placeholder="页内搜索"
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          value={searchTerm}
          prefix={<SearchOutlined />}
          size="small"
          style={{ width: 200 }}
          suffix={
            searchTerm && (
              <Tooltip title="清除搜索">
                <CloseOutlined
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setSearchTerm("");
                    clearHighlights();
                  }}
                />
              </Tooltip>
            )
          }
        /> */}
        <Button
          icon={<LeftOutlined />}
          size="small"
          onClick={goToPrevMatch}
          disabled={!isSearching || matches.length === 0}
        />
        <Button
          icon={<RightOutlined />}
          size="small"
          onClick={goToNextMatch}
          disabled={!isSearching || matches.length === 0}
        />
      </Space>
     
      <Space
        size={1}
        style={{
          fontSize: 12,
          fontWeight: 500,
          marginLeft: 5,
        }}
      >
        <span>跳至第</span>
        <Input
          style={{
            width: 35,
            padding: "0 6px",
          }}
          size="small"
          value={jumpInputValue}
          onChange={handleJumpInputChange}
          onKeyDown={(e) => {
            // 按回车键跳转
            if (e.key === "Enter") {
              const n = parseInt(jumpInputValue, 10);
              if (n >= 1 && n <= matches.length) {
                setCurrentMatchIndex(n - 1);
                scrollToMatch(n - 1);
              } else {
                alert(`请输入 1 到 ${matches.length} 之间的数字`);
              }
            }
          }}
        />
        <span>条</span>
      </Space>
    </div>
  );
};

export default TextSearch;
