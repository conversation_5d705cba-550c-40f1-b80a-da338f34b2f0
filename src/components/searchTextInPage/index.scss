.text-search-container {
  padding: 10px;
  // background-color: #f5f5f5;
  border-radius: 4px;
}



 .search-container {
      display: flex;
      align-items: center;
      position: relative;
    }

    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      transition: width 0.3s ease;
      width: 40px;
      overflow: hidden;
      border-radius: 20px;
      border: 1px solid #ccc;
      background: white;
      cursor: pointer;
    }

    .search-input-wrapper.expanded {
      width: 200px;
      cursor: text;
    }

    .search-icon {
      width: 20px;
      height: 20px;
      margin: 10px 0 10px 10px;
      pointer-events: none;
      font-size: 16px;
    }

    .search-input {
      border: none;
      outline: none;
      font-size: 14px;
      background: transparent;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .search-input-wrapper.expanded .search-input {
      opacity: 1;
    }

    svg {
      fill: #888;
    }



.match-count {
  color: #888;
  font-size: 12px;
}

.no-results {
  margin-left: 8px;
  color: #f5222d;
  font-size: 12px;
}

.content-area {
  padding: 20px;
  line-height: 1.8;
}

.search-highlight {
  transition: background-color 0.3s;
}

.search-highlight-current {
  outline: 2px solid #1890ff;
  z-index: 1;
}
