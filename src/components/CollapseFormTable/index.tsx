import React from 'react';
import './index.scss'
import {Button, Collapse, Space} from '@itm/components-pc';
import {DoubleRightOutlined} from '@ant-design/icons';
import {ExpandIconPosition} from 'antd/es/collapse/Collapse';

interface TableListItem {
    label?: string;
    value: React.ReactNode;

}

// 定义表格行数据类型
interface TableRow {
    label: string;
    value?: React.ReactNode;
    isLabelRed?: boolean;
    isButtonCell?: boolean;
    buttonText?: string;
    children?: TableListItem[];
    onButtonClick?: () => void;
}

// 定义表格头数据类型
interface tableHeader {
    label?: string,
    value?: string,
    isformLabel?: boolean,
    isTimeCell?: boolean
}

// 定义组件props类型
interface GenericFormTableProps {
    title?: string;
    isShow?: string;
    children?: React.ReactNode
    expandIconPosition?: ExpandIconPosition,
    bordered?: boolean
}

const CollapseFormTable: React.FC<GenericFormTableProps> = (props) => {
    const {title = '', isShow = '', children, expandIconPosition = 'start'} = props;
    return (
        <>
            <Collapse
                collapsible="header"
                bordered={props.bordered}
                className={`table_collapse ${expandIconPosition=='end'?'':'red_Border'}`}
                expandIconPosition={expandIconPosition}
                defaultActiveKey={[isShow]}
                expandIcon={({isActive}) => <DoubleRightOutlined style={{fontSize: '20px', color: 'green'}}
                                                                 rotate={isActive ? (-90) : 90}/>}
                items={[
                    {
                        key: '1',
                        label: (<span style={{fontSize: '20px', color: 'black', fontWeight: 'bold'}}>{title}</span>),
                        children: (
                            children
                        ),
                    },
                ]}
            />
        </>


    );
};

export default CollapseFormTable;    