/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description:
 * @LastEditTime: 2025-07-14 18:03:59
 * @LastEditors: wangjiantao
 */
import * as flowApi from "@/api/user/flow";
import {ComponentBus, PostponedFormViewKey, UserSelectFormViewKey,} from "@codingapi/ui-framework";
import {FlowApi, FlowApiContent} from "@/components/flow-pc";
import PostponedFormView from "@/components/flow/PostponedFormView";
import FlowUserChoiceModal from "@/components/user/flow/FlowUserChoiceModal";
import LeaveForm from "@/pages/user/flow/leave/LeaveForm";

ComponentBus.getInstance().registerComponent(
    "default",
    LeaveForm
);

ComponentBus.getInstance().registerComponent(
    UserSelectFormViewKey,
    FlowUserChoiceModal
);
ComponentBus.getInstance().registerComponent(
    PostponedFormViewKey,
    PostponedFormView
);

export class DefaultFlowApiImpl implements FlowApi {
    startFlow(body: any): Promise<any> {
        return flowApi.startFlow(body);
    }

    submitFlow(body: any): Promise<any> {
        return flowApi.submitFlow(body);
    }

    removeFlow(body: any): Promise<any> {
        return flowApi.removeFlow(body);
    }

    saveFlow(body: any): Promise<any> {
        return flowApi.saveFlow(body);
    }

    postponedFlow(body: any): Promise<any> {
        return flowApi.postponed(body);
    }

    triggerCustomFlow(body: any): Promise<any> {
        return flowApi.custom(body);
    }

    transferFlow(body: any): Promise<any> {
        return flowApi.transfer(body);
    }

    urgeFlow(body: any): Promise<any> {
        return flowApi.urge(body);
    }

    recallFlow(body: any): Promise<any> {
        return flowApi.recall(body);
    }

    trySubmitFlow(body: any): Promise<any> {
        return flowApi.trySubmitFlow(body);
    }

    getDetailById(id: any): Promise<any> {
        return flowApi.detail(id);
    }

    getDetailByWorkCode(workCode: any): Promise<any> {
        return flowApi.detail(null, workCode);
    }

    getDetailByWorkCodeWithUnitCustom(workCode: any): Promise<any> {
        return flowApi.flowWorkDetailWithUnitCustom(workCode);
    }

}

FlowApiContent.getInstance().registerFlowApi(new DefaultFlowApiImpl());
