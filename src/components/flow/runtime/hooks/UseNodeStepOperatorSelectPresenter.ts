import {useEffect, useRef, useState} from "react";
import {NodeStepOperatorSelectState} from "@/components/flow/runtime/types";
import {NodeStepOperatorSelectPresenter} from "@/components/flow/runtime/presenter";
import {NodeStepOperatorSelectApiImpl} from "@/components/flow/runtime/model";

const initialState: NodeStepOperatorSelectState = {
    value: {},
};

export function useNodeStepOperatorSelectPresenter() {

    const [state, dispatch] = useState<NodeStepOperatorSelectState>(initialState);
    const presenterRef = useRef<NodeStepOperatorSelectPresenter>();

    if (!presenterRef.current) {
        presenterRef.current = new NodeStepOperatorSelectPresenter(state, new NodeStepOperatorSelectApiImpl(), dispatch)
    }

    useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    };
}