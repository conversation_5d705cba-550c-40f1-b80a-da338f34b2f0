import React, {CSSProperties, useEffect} from "react";
import {Steps, Tabs} from "@itm/components-pc";
import "./index.scss";
import {NodeOperatorList} from "@/components/flow/runtime/NodeOperatorList";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, NodeOperatorsMap} from "@/components/flow/runtime/types";
import {useNodeStepOperatorSelectPresenter} from "@/components/flow/runtime/hooks/UseNodeStepOperatorSelectPresenter";
import {StepProps} from "antd";
import {InfoCircleOutlined, StopOutlined} from "@ant-design/icons";
import {UserChoiceProps} from "@/components/user/choice/type";

export interface FlowStepOperatorView {
    style?: CSSProperties;
    workCode: string;
    recordId?: number;
    processId?: string;
    // 部分审批节点下可以不允许编辑 只查看
    readonly?: boolean;
    // 不可调整的节点，这些节点不展示x和新增
    notEditableNodes?: string[];
    // 高亮节点，主要是做数据校验时校验必填，然后把这些没填的高亮展示
    highlightNodes?: string[];
    value?: NodeOperatorsMap,
    onChange?: (value: NodeOperatorsMap) => void;
    // 节点选人时，选人组件的配置
    nodeUserChoiceProps?: { [key: string]: UserChoiceProps };
    // 提示信息, 可以用组件，比如图标+span
    tips?: React.ReactNode;
}

const FlowStepOperatorsView: React.FC<FlowStepOperatorView> = ({
                                                                   style,
                                                                   workCode,
                                                                   recordId,
                                                                   processId,
                                                                   readonly,
                                                                   notEditableNodes,
                                                                   highlightNodes,
                                                                   value,
                                                                   onChange,
                                                                   nodeUserChoiceProps,
                                                                   tips
                                                               }) => {

        const {state, presenter} = useNodeStepOperatorSelectPresenter();

        useEffect(() => {
            presenter.init(workCode, recordId);
        }, [workCode, recordId]);

        // 补充节点上的相关信息
        const parseStepNodesToStepItem = (nodes: NodeChain[]): StepProps => {

            // nodes里是否有高亮节点
            const hasHighlightNode = nodes.some((node) => highlightNodes?.includes(node.code));

            if (nodes?.length > 1) {
                return {
                    title: <Tabs
                        onChange={(key) => {
                            presenter.onTabChange(key)
                        }}
                        type="card"
                        items={nodes.map((node) => {

                            const nodeOperators = value?.[node.code] || [];

                            const editable = !notEditableNodes?.includes(node.code) && !node.fixed && !readonly;

                            return {
                                label: <span
                                    style={{color: highlightNodes?.includes(node.code) ? "red" : undefined}}>{node.name}</span>,
                                key: node.mark ?? node.code,
                                children: <NodeOperatorList {...nodeUserChoiceProps?.[node.code]}
                                                            nodeCode={node.code}
                                                            nodeOperators={nodeOperators}
                                                            editable={editable}
                                                            onChange={(nodeCode, type, users) => {
                                                                presenter.onChooseUser(nodeCode, type, users, onChange)
                                                            }}/>
                            }
                        })}
                    />,
                    icon: hasHighlightNode
                        ? <InfoCircleOutlined style={{color: "red"}}/>
                        : undefined
                }
            } else {
                const node = nodes[0]

                const nodeOperators = value?.[node.code] || [];

                const editable = !notEditableNodes?.includes(node.code) && !node.fixed && !readonly;

                let title;
                let icon;
                if (hasHighlightNode) {
                    title = <span style={{color: "red"}}>{node.name}</span>;
                    icon = <InfoCircleOutlined style={{color: "red"}}/>;
                } else {
                    title = node.name;
                    icon = node.code === "over" ? <StopOutlined style={{color: "red"}}/> : undefined;
                }

                return {
                    title: title,
                    icon: icon,
                    description: <NodeOperatorList nodeCode={node.code}
                                                   nodeOperators={nodeOperators}
                                                   editable={editable}
                                                   {...nodeUserChoiceProps?.[node.code]}
                                                   onChange={(nodeCode, type, users) => {
                                                       presenter.onChooseUser(nodeCode, type, users, onChange)
                                                   }}/>,
                }
            }
        }

        useEffect(() => {
            // 把用户传入的选人结果混合到state中
            presenter.setValue(value);
        }, [value]);

        return (

            state.stepChain ? (
                    <div>
                        {tips}
                        < div style={style}>
                            < Steps
                                direction="vertical"
                                current={1}
                                items={
                                    state.stepChain.map((nodes) =>
                                        parseStepNodesToStepItem(nodes)
                                    )
                                }
                            />
                        </div>
                    </div>) :
                <></>

        );

    }
;

export default FlowStepOperatorsView;