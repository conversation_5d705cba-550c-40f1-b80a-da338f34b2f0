import React from "react";
import {Avatar} from "@itm/components-pc";
import {CloseOutlined} from "@ant-design/icons";
import "./index.scss"
import {NodeOperatorProps} from "@/components/flow/runtime/types";


export const NodeOperator: React.FC<NodeOperatorProps> = ({
                                                              userId,
                                                              name,
                                                              fixed,
                                                              onRemove
                                                          }) => {

    return (<div key={userId} className="user-card">
        <Avatar className={"user-avatar"}>{name?.slice(-2)}</Avatar>
        <span className="user-name">{name}</span>
        {!fixed && <CloseOutlined className="remove-icon" onClick={() => {
            onRemove?.({userId, name})
        }}/>}
    </div>)
}