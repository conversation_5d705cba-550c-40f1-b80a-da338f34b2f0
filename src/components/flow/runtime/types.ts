
import {FlowOperator} from "@/components/flow-pc/Flow/nodes/type";


export interface NodeOperatorProps extends FlowOperator {
    // 是否为固定人员，不可移除
    fixed?: boolean;
    onRemove?: (user: FlowOperator) => void;

}

export interface FlowNodeProps {

    title: string;
    code: string;
    operators: NodeOperatorProps[];

}

export interface NodeOperatorsMap {
    [key: string] : NodeOperatorProps[];
}

export interface NodeChain {

    code:string;
    name: string;
    mark?:string;
    fixed?: boolean;
    next?: NodeChain[];

}

export interface NodeStepOperatorSelectState {

    value: NodeOperatorsMap;
    initNodeChain?: NodeChain;
    stepChain?: NodeChain[][],


}

export interface NodeStepOperatorSelectApi {

    getNodeStepView: (workCode: string, recordId?: number) => Promise<any>;
    
}