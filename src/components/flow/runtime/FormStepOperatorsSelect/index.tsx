import React, {CSSProperties, useEffect, useState} from "react";
import {Form, Space} from "antd";
import FlowStepOperatorsView from "@/components/flow/runtime/FlowStepOperatorsView";
import {UserChoiceProps} from "@/components/user/choice/type";
import {InfoCircleFilled} from "@ant-design/icons";
import {FormItemProps} from "@codingapi/ui-framework";
import formFieldInit from "@/components/form-pc/Form/common";

// @ts-ignore

export interface FormStepOperatorsSelectProps extends FormItemProps {
    style?: CSSProperties;
    workCode: string;
    recordId?: number;
    readonly?: boolean;
    // 不可调整的节点，这些节点不展示x和新增
    notEditableNodes?: string[];
    // 必须选人的节点
    requiredNodes?: string[];
    // 节点选人时，选人组件的配置
    nodeUserChoiceProps?: { [key: string]: UserChoiceProps };
    defaultTips?: React.ReactNode;
}

export const FormStepOperatorsSelect: React.FC<FormStepOperatorsSelectProps> = (props) => {

    // 提示语
    const [tips, setTips] = useState<React.ReactNode>(null);
    const [highlightNodes, setHighlightNodes] = useState<string[]>([]);

    const formProps = {
        ...props,
        validateFunction: props.required ? (content: FormItemProps) => {

            let highlightNodes: string[] = [];
            if (!content.value && props.requiredNodes) {
                highlightNodes = [...props.requiredNodes]
            } else if (!props.requiredNodes || props.requiredNodes.length === 0) {
                highlightNodes = []
            } else {
                const remains = props.requiredNodes.filter(node => !content.value?.[node]?.length);
                highlightNodes = [...remains]
            }
            setHighlightNodes(highlightNodes);

            if (!highlightNodes.length) {
                setTips(null);
                return Promise.resolve([]);
            } else {
                setTips(<Space style={{paddingBottom: "4px", "color": "red"}}>
                    <InfoCircleFilled/>请选择后续节点审批人员</Space>);
                return Promise.resolve(["请选择审批人"]);
            }
        } : undefined
    }

    // 添加校验逻辑
    const {formContext} = formFieldInit(formProps);

    useEffect(() => {
        formContext?.addFormField(
            {
                type: 'FormStepOperatorsSelect',
                props: formProps
            }
        );
    }, []);


    return <Form.Item {...formProps} validateStatus={"error"}>
        <FlowStepOperatorsView
            workCode={props.workCode}
            recordId={props.recordId}
            readonly={props.readonly || props.disabled || props.hidden}
            notEditableNodes={props.notEditableNodes}
            highlightNodes={highlightNodes}
            tips={tips || props.defaultTips}
            onChange={(value) => {
                props.name && formContext?.setFieldValue(props.name, value);
                props.onChange && props.onChange(value, formContext);
            }}
        />
    </Form.Item>;
}