import {Dispatch} from "@/utils/mvvm";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    NodeOperatorsMap,
    NodeStepOperatorSelectApi,
    NodeStepOperatorSelectState
} from "@/components/flow/runtime/types";
import {FlowOperator} from "@/components/flow-pc/Flow/nodes/type";


export class NodeStepOperatorSelectPresenter {

    private readonly dispatch: Dispatch<NodeStepOperatorSelectState>;

    private readonly api: NodeStepOperatorSelectApi;

    private state: NodeStepOperatorSelectState;

    public constructor(state: NodeStepOperatorSelectState, api: NodeStepOperatorSelectApi, dispatch: Dispatch<NodeStepOperatorSelectState>) {
        this.state = state
        this.api = api
        this.dispatch = dispatch
    }

    public syncState = (newState: NodeStepOperatorSelectState) => {
        this.state = newState;
    }

    public setValue = (value?: NodeOperatorsMap) => {

        this.dispatch(prevState => {
            return {
                ...prevState,
                value: {...value},
            }
        });

    }

    public onChooseUser = (nodeCode: any, type: "add" | "remove", users: FlowOperator[], onChange?: (value: NodeOperatorsMap) => void) => {
        const value = this.state.value;

        const prevOperators = value[nodeCode] || [];

        let afterUsers;
        if (type === "add") {
            const newUsers = users.filter(user => !prevOperators.find(prevUser => prevUser.userId === user.userId));
            afterUsers = [...prevOperators, ...newUsers];
        } else {
            afterUsers = [...prevOperators.filter(user => !users.find(removeUser => user.userId === removeUser.userId))];
        }

        value[nodeCode] = afterUsers;
        onChange?.({...value});
    }

    public recursiveSetNodeMark = (nodeChain: NodeChain) => {
        nodeChain.next?.map((item: any, index: number) => {
            item.mark = `${nodeChain.mark}-${index}`;
            this.recursiveSetNodeMark(item);
        })
    }

    public recursiveFindNodeByMark = (node: NodeChain, nodeMark: string): any => {
        if (!node) return null;

        if (node.mark === nodeMark) {
            return node;
        }

        if (node.next && Array.isArray(node.next)) {
            for (const child of node.next) {
                const found = this.recursiveFindNodeByMark(child, nodeMark);
                if (found) {
                    return found;
                }
            }
        }

        return null;
    };

    public recursiveBuildNextStepChain = (nodeChain: NodeChain, chain: NodeChain[][]) => {
        // 如果nodeChain 为空 或它的next 为空，则返回空
        if (!nodeChain || !nodeChain.next || nodeChain.next.length === 0) {
            return;
        }

        chain.push(nodeChain.next);

        this.recursiveBuildNextStepChain(nodeChain.next[0], chain);
    }

    public buildNextStepChain = (nodeChain: NodeChain, nodeMark: string) => {
        const targetNode = this.recursiveFindNodeByMark(nodeChain, nodeMark);

        const chain: NodeChain[][] = [];
        this.recursiveBuildNextStepChain(targetNode, chain);
        return chain;
    }

    public onTabChange = (key: string) => {

        this.dispatch(prevState => {

            if (!prevState.initNodeChain || !prevState.stepChain) return prevState;
            const targetNode = this.recursiveFindNodeByMark(prevState.initNodeChain, key)
            // 找到指定的位置
            const index = prevState.stepChain.findIndex((nodes) =>
                nodes.findIndex(node => node.mark === key) >= 0);

            // 删掉index后面的节点
            const keepNodes = prevState.stepChain.slice(0, index + 1);
            const afterNodes = this.buildNextStepChain(targetNode, key);
            return {
                ...prevState,
                stepChain: [...keepNodes, ...afterNodes],
            }
        })
    };

    public init(workCode: string, recordId?: number) {
        this.api.getNodeStepView(workCode, recordId).then((res) => {

            const initNodeChain = res.data;

            const nextStepChain = this.buildNextStepChain(initNodeChain, "0");
            const newStepChain = [[initNodeChain], ...nextStepChain];

            this.dispatch(prevState => {
                return {
                    ...prevState,
                    initNodeChain: initNodeChain,
                    stepChain: newStepChain
                }
            })
        })
    }
}