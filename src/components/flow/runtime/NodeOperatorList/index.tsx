import {NodeOperator,} from "@/components/flow/runtime/NodeOperator";
import React, {useMemo} from "react";
import {PlusOutlined} from "@ant-design/icons";
import UserChoiceModal from "@/components/user/choice/UserChoiceModal";
import {UserChoiceProps} from "@/components/user/choice/type";
import "./index.scss"
import {NodeOperatorProps} from "@/components/flow/runtime/types";


export interface NodeOperatorListProps extends UserChoiceProps {
    nodeCode: string,
    nodeOperators?: NodeOperatorProps[];
    // 是否可以加人
    editable?: boolean;
    // 选人时触发
    onChange?: (nodeCode: string, type: "add" | "remove", nodeOperators: NodeOperatorProps[]) => void;
    // 是否多选
    multiple?: boolean;
}

export const NodeOperatorList: React.FC<NodeOperatorListProps> = ({
                                                                      nodeCode,
                                                                      nodeOperators,
                                                                      editable = false,
                                                                      onChange,
                                                                      ...props
                                                                  }) => {
    const [showUserChoiceModal, setShowUserChoiceModal] = React.useState(false);

    const NodeOperatorChoiceModal = useMemo(() => {
        return (<UserChoiceModal
            {...props}
            visible={showUserChoiceModal}
            hide={() => setShowUserChoiceModal(false)}
            customAttribute={{nodeCode}}
            multiple={props.multiple}
            onFinish={(users, customAttribute) => {

                const newSelected = users.map((user) => ({
                    userId: user.id,
                    name: user.name,
                    fixed: false,
                }));
                onChange?.(customAttribute.nodeCode, "add", [...newSelected]);
            }}
        />)
    }, [props.multiple, nodeCode, showUserChoiceModal]);

    return (<div className="user-list">
        {nodeOperators && nodeOperators.map((user, index) => {
            // 如果节点不可编辑，节点下的人员不可移除
            const fixed = !editable || user.fixed;
            return (
                <NodeOperator
                    key={user.userId}
                    {...user}
                    fixed={fixed}
                    onRemove={(user) => {
                        onChange?.(nodeCode, "remove", [user])
                    }}/>
            )
        })}
        {editable && (
            <div className="add-user-btn" onClick={() => {
                setShowUserChoiceModal(true)
            }}>
                <PlusOutlined/>
            </div>
        )}
        {NodeOperatorChoiceModal}
    </div>)
}