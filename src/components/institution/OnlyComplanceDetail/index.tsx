import React, { useEffect } from 'react';
import { Modal, Table } from 'antd';
import './App.css';
import { usePresenter } from "@/components/institution/OnlyComplanceDetail/hooks/usePresenter";

interface ComplianceDetailModalProps {
    visible: boolean;
    onClose: () => void;
    institutionId: number;
}

const ComplianceDetailModal: React.FC<ComplianceDetailModalProps> = ({ visible, onClose, institutionId}) => {
    const { state, presenter } = usePresenter();

    useEffect(() => {
        if (visible && institutionId) {
            presenter?.getInstitutionDetail(Number(institutionId));
            presenter?.loadComplianceDetail(Number(institutionId), String("inner"));
        }
        // eslint-disable-next-line
    }, [visible, institutionId]);

    const columns: any[] = [
        {
            title: '序号',
            dataIndex: 'key',
            key: 'key',
            render: (text: string, record: any, index: number) => index + 1
        },
        {
            title: '合规义务',
            dataIndex: 'institutionContent',
            key: 'institutionContent',
            width: "30%",
            render: (text: string, record: any) => (
                <div
                    title={record.institutionContent}
                    style={{
                        height: '7.5em',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        lineHeight: 1.5,
                    }}
                >
                    {record.institutionContent}
                </div>
            )
        },
        {
            title: '合规风险',
            dataIndex: 'complianceRisk',
            key: 'complianceRisk',
            width: '20%',
            render: (text: string, record: any) => (
                <div
                    style={{
                        height: '7.5em',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        lineHeight: 1.5,
                    }}
                >
                    {record.complianceRisk}
                </div>
            )
        },
        {
            title: '管控措施',
            dataIndex: 'controlMeasures',
            key: 'controlMeasures',
            width: '20%',
            render: (text: string, record: any) => (
                <div
                    style={{
                        height: '7.5em',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        lineHeight: 1.5,
                    }}
                >
                    {record.controlMeasures}
                </div>
            )
        },
        {
            title: '落实措施途径',
            dataIndex: 'implMeasures',
            key: 'implMeasures',
            width: '20%',
            render: (text: string, record: any) => (
                <div
                    style={{
                        height: '7.5em',
                        overflowY: 'auto',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        lineHeight: 1.5,
                    }}
                >
                    {record.implMeasures}
                </div>
            )
        }
    ];

    return (
        <Modal
            title={state.selectedInstitutionDetail?.title || '合规风险详情'}
            open={visible}
            onCancel={onClose}
            footer={null}
            width="70vw"
            style={{ maxHeight: '80vh', overflow: 'auto' }}
        >
            <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '14px' }}>
                    {state.selectedInstitutionDetail?.drawUnit || state.selectedInstitutionDetail?.drafter ? (
                        <p>{state.selectedInstitutionDetail?.drawUnit}/{state.selectedInstitutionDetail?.drafter}</p>
                    ) : null}
                    {state.selectedInstitutionDetail?.drawTime && (
                        <p>起草时间：{state.selectedInstitutionDetail?.drawTime}</p>
                    )}
                </div>
            </div>
            <Table
                dataSource={state.rows}
                columns={columns}
                pagination={false}
                bordered
                rowKey="key"
            />
        </Modal>
    );
};

export default ComplianceDetailModal;
