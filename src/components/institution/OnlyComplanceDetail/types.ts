export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

/**
 * 数据状态类型的定义
 */


export interface InstitutionState {
    selectedInstitutionDetail: {
        title?: string;
        drawUnit?: string;
        drafter?: string;
        drawTime?: string;
        htmlContent?: string;
        contentList?: {id: number, content: string}[];
    },
    rows: {
        key?: string;
        institutionContentId?: number;
        institutionContent?: string;
        complianceRisk?: string;
        controlMeasures?: string;
        implMeasures?: string;
    }[]
}


export interface InstitutionApi {

    getInstitutionDetail: (params: any) => Promise<any>,

    complianceDetail: (params: any) => Promise<any>;

}