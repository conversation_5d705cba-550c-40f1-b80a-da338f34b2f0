import React from "react";
import {InstitutionState} from "@/components/institution/OnlyComplanceDetail/types";
import {InstitutionPresenter} from "@/components/institution/OnlyComplanceDetail/presenter";
import {InstitutionApiImpl} from "@/components/institution/OnlyComplanceDetail/model";

const initialState: InstitutionState = {
    selectedInstitutionDetail: {},
    rows: [],
}

export function usePresenter() {
    const [state, dispatch] = React.useState<InstitutionState>(initialState);

    const presenterRef = React.useRef<InstitutionPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new InstitutionPresenter(dispatch, new InstitutionApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }

}