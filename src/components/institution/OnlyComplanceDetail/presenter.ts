import {InstitutionApi, InstitutionState, Dispatch} from "@/components/institution/OnlyComplanceDetail/types";


export class InstitutionPresenter {

    //更新数据状态
    private readonly dispatch: Dispatch<InstitutionState>;

    private readonly api: InstitutionApi;

    private state: InstitutionState;

    public constructor(dispatch: Dispatch<InstitutionState>, api: InstitutionApi, state: InstitutionState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: InstitutionState) => {
        this.state = newStatus;
    }


    public getInstitutionDetail = async (id: number) => {
        const params = {
            id: id,
        }
        const res = await this.api.getInstitutionDetail(params);
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectedInstitutionDetail: {
                    ...res.data
                }
            }
        })
    }

    public loadComplianceDetail = async (id: number, type: string) => {

        const param = {
            institutionId: id,
            institutionType: type,
        };
        const res = await this.api.complianceDetail(param)
        this.dispatch((prevState) => {
            return {
                ...prevState,
                rows: (res.data.list as any[]).map((row, index) => ({
                    ...row,
                    key: (index + 1).toString()
                })),
                recordId: res.data.recordId
            };
        });
    }
    public setRecallReviewVisible = (v: boolean) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                recallReviewVisible: v
            }
        })
    }

    public hideSelectUserModal = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                selectUserVisible: false,
                currentRoleId: null,
                currentSelectedUser: []
            }
        });
    }

    public setViewType = (type: string) => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                viewType: type
            }
        })
    }


}