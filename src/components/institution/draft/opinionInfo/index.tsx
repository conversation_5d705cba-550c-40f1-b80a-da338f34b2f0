import React, {forwardRef, Fragment} from "react";
import "@/pages/institution/draft/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import dayjs from "dayjs";
import "@/pages/institution/update/index.scss";
import OssFileUploader from "@/components/oss/OssFileUpload";
import {usePresenter} from "@/components/institution/draft/opinionInfo/hooks/usePresenter";

interface DraftInfoProps {
    draft: any;
    adviceShow?: boolean;
    isEdit?: boolean;
    defaultActive?: boolean;
    itemList: { selectedContent: string, content: string }[]
}


export interface File {
    id: number;
    fileKey: string;
    fileName: string;
    fileType: string;
    bucket: string;
    fileCategory: string;
    fileCategoryName: string;
}


export const DraftOpinionInfo = forwardRef((props: DraftInfoProps, ref: any) => {
    const {draft} = props;

    const {state, presenter} = usePresenter();

    // 表格数据
    const tableRows = [
        {
            label: "制度名称",
            value: draft?.title,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "征求意见截止时间",
            value: dayjs(draft?.opinionEndTime).format("YYYY年MM月DD日"),
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "征求意见说明",
            value: draft?.opinionExplain,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "征求意见范围",
            value: Array.isArray(draft?.opinionTargetList)
                ? draft.opinionTargetList.map((target: any) => {
                    if (typeof target === 'object' && target !== null) {
                        // 根据实际数据结构调整
                        return target.targetName || target.targetId || '未知目标';
                    }
                    return String(target);
                }).join('、')
                : '',
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: '反馈意见附件',
            value: <>
                <OssFileUploader
                    label=""
                    uploaderAccept="image/jpeg,image/jpg,application/pdf"
                    uploaderMaxCount={5}
                    bucket="document"
                    ossFileKey={state.ossFileKeys || ""}
                    onKeyChange={(keys) => {
                        presenter?.setOssFileKeys(keys);
                    }}
                />
            </>
        },
        {
            label: "反馈意见",
            value: (
                <div>
                    {Array.isArray(props.itemList) && props.itemList.map((item, index) => (
                        <div key={index} style={{ marginBottom: '8px' }}>
                            <div>{item.selectedContent}</div>
                            <div>反馈意见：{item.content}</div>
                        </div>
                    ))}
                </div>
            ),
            isLabelRed: true,
            isEditable: false,
        }
    ];
    return (
        <CollapseFormTable
            title="制度起草信息"
            isShow="1"
        >
            <GenericFormTable rows={tableRows} isBorder={true}/>
        </CollapseFormTable>
    );
});
