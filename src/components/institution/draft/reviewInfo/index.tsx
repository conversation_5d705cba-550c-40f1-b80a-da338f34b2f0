import React, {forwardRef, useEffect} from "react";
import "@/pages/institution/draft/index.scss";
import GenericFormTable from "@/components/GenericFormTable";
import CollapseFormTable from "@/components/CollapseFormTable";
import "@/pages/institution/update/index.scss";
import {Col, Form as AForm, Row} from "antd";
import { Radio } from "antd";
import {downloadArchiveFile} from "@/utils/download";


interface DraftInfoProps {
    nodeCode: string;
    draft: any;
    draftId: number;
    targetId: number;
    tagList?: any[];
    institutionAbolishEnable?: boolean;
    abolishInstitutionList?: any[];
    adviceShow?: boolean;
    isEdit?: boolean;
    defaultActive?: boolean;
    itemList: { selectedContent: string, content: string }[]
}


export interface File {
    id: number;
    fileKey: string;
    fileName: string;
    fileType: string;
    bucket: string;
    fileCategory: string;
    fileCategoryName: string;
}


export const DraftReviewInfo = forwardRef((props: DraftInfoProps, ref: any) => {
    const {draft} = props;
    useEffect(() => {
    }, []);

    console.log(draft)
    // 表格数据
    const tableRows = [
        {
            label: "起草类型",
            value: draft?.type,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "制度名称",
            value: draft?.title,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "制度标签",
            value: (
                <AForm.Item
                    name="tags"
                    rules={[{required: true, message: "请选择标签"}]}
                >
                    <Row justify="space-between" align="middle" style={{padding: "0 12px"}}>
                        <Col span={21} style={{display: "flex", alignItems: "center"}}>
                            {Array.isArray(props.tagList) ? props.tagList.map((tag: any) => tag.name).join(", ") : ""}
                        </Col>
                    </Row>
                </AForm.Item>
            ),
            isEditable: false,
        },
        {
            label: "废止原制度或相关制度",
            value: (
                <div style={{ paddingLeft: 12 }}>
                    <Row gutter={8} align="middle">
                        <Col span={6}>
                            <Radio.Group
                                value={draft?.institutionAbolishEnable ? 'true' : 'false'}
                            >
                                <Radio value="true">是</Radio>
                                <Radio value="false">否</Radio>
                            </Radio.Group>
                        </Col>
                    </Row>
                    <div style={{ padding: "12px 0" }}>
                            {draft.abolishInstitutionList?.map((institution: any, index: number) => (
                                <Row key={institution.id} gutter={8} style={{ marginBottom: 8, alignItems: "center" }}>
                                    <Col span={20}>
                                        <span
                                            style={{
                                                color: 'blue',
                                                cursor: 'pointer',
                                                textDecoration: 'underline'
                                            }}
                                            onClick={() => {
                                                downloadArchiveFile(institution.originFileKey);
                                            }}
                                        >
                                          {institution.name}
                                        </span>
                                    </Col>
                                </Row>
                            ))}
                        </div>
                </div>
            ),
            isLabelRed: true,
        },
        {
            label: "是否开展公平竞争审查",
            value: draft?.opinionExplain,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "办理意见",
            value: null,
            isLabelRed: true,
            isEditable: props.isEdit,
        },
        {
            label: "拟办意见",
            value: (
                <div>
                    {Array.isArray(props.itemList) && props.itemList.map((item, index) => (
                        <div key={index} style={{ marginBottom: '8px' }}>
                            <div>{item.selectedContent}</div>
                            <div>反馈意见：{item.content}</div>
                        </div>
                    ))}
                </div>
            ),
            isLabelRed: true,
            isEditable: false,
        }
    ];
    return (
        <CollapseFormTable
            title="制度起草信息"
            isShow="1"
        >
            <GenericFormTable rows={tableRows} isBorder={true}/>
        </CollapseFormTable>
    );
});
