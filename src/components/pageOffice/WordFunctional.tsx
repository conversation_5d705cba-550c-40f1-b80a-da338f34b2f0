import { useState, useEffect, useRef } from "react";
import "./WordFunctional.scss";
import { wordFunctionalApi } from "@/api/pageoffice";
import { contractToHtml } from "@/api/contract/flow";

// 声明全局变量
/* global pageofficectrl */
declare global {
  interface Window {
    [key: string]: any;
  }
}

const WordFunction = ({ fileId }: { fileId: number }) => {
  const [poHtmlCode, setPoHtmlCode] = useState<any>("");
  const containerRef = useRef<any>(null); // 父容器 ref
  // PageOffice 控件方法
  const Save = async () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.WebSave();
    }
  };

  const SaveAs = () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.ShowDialog(3);
    }
  };

  const PrintSet = () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.ShowDialog(5);
    }
  };

  const PrintFile = () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.ShowDialog(4);
    }
  };

  const Close = () => {
    window.pageofficectrl.CloseWindow();
  };

  const IsFullScreen = () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.FullScreen = !window.pageofficectrl.FullScreen;
    }
  };

  // PageOffice 事件回调函数
  const OnPageOfficeCtrlInit = () => {
    if (window.pageofficectrl) {
      window.pageofficectrl.AddCustomToolButton("保存", "Save", 1);
      window.pageofficectrl.AddCustomToolButton("另存为", "SaveAs", 12);
      window.pageofficectrl.AddCustomToolButton("页面设置", "PrintSet", 0);
      window.pageofficectrl.AddCustomToolButton("打印", "PrintFile", 6);
      window.pageofficectrl.AddCustomToolButton("全屏/还原", "IsFullScreen", 4);
      window.pageofficectrl.AddCustomToolButton("-", "", 0);
      window.pageofficectrl.AddCustomToolButton("关闭", "Close", 21);
    }
  };

  const AfterDocumentOpened = () => {
    // 文档打开后事件回调
  };

  const BeforeDocumentSaved = () => {
    // 文档保存前事件回调
  };

  const AfterDocumentSaved = () => {
    // 文档保存后事件回调
  };

  const init = () => {
    // 将方法挂载到window对象
    window.OnPageOfficeCtrlInit = OnPageOfficeCtrlInit;
    window.AfterDocumentOpened = AfterDocumentOpened;
    window.BeforeDocumentSaved = BeforeDocumentSaved;
    window.AfterDocumentSaved = AfterDocumentSaved;

    window.Save = Save;
    window.SaveAs = SaveAs;
    window.PrintSet = PrintSet;
    window.PrintFile = PrintFile;
    window.Close = Close;
    window.IsFullScreen = IsFullScreen;
  };

  const fetchFile = async () => {
    if (!fileId) return;
    try {
      const params = {
        id: fileId,
      };
      const response = await wordFunctionalApi(params);

      setPoHtmlCode(response);
    } catch (error) {
      console.error("获取文件失败:", error);
    }
  };

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      if (window.pageofficectrl && containerRef.current) {
        // 调用 PageOffice 调整宽度的 API（以官方为准，这里是伪代码）
        window.pageofficectrl.Width = containerRef.current?.offsetWidth;
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    init();
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    fetchFile();
  }, [fileId]);

  return (
    <div className={"container"} ref={containerRef}>
      <div
        style={{ height: "100%", width: "100%" }}
        dangerouslySetInnerHTML={{ __html: poHtmlCode }}
      />
    </div>
  );
};

export default WordFunction;
