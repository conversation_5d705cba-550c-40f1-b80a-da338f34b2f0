/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-14 14:34:07
 * @Description:
 * @LastEditTime: 2025-07-26 15:15:45
 * @LastEditors: wangjiantao
 */
import { Splitter, SplitterProps } from "antd";
import { FC, ReactNode, useRef } from "react";
import './index.scss'

interface Iprops extends SplitterProps {
  left?: ReactNode;
  right: ReactNode;
  htmlContent?: string;
  leftDefaultSize?: string | number;
  rightDefaultSize?: string | number;
  onResizeEnd?: (sizes: number[]) => void;
}

const CustomSplitter: FC<Readonly<Iprops>> = ({
  style,
  left,
  right,
  htmlContent,
  leftDefaultSize,
  rightDefaultSize,
  onResizeEnd,
  ...restProps
}) => {
  const splitterRef = useRef<HTMLDivElement | null>(null);

  return (
    <div ref={splitterRef}>
      <Splitter
        className="Splitter"
        onResizeEnd={onResizeEnd && onResizeEnd}
        style={{
          boxShadow: "0 0 10px rgba(3, 2, 2, 0.1)",
          height: "calc(100vh - 111px)",
          width: "100%",
          ...style,
        }}
        {...restProps}
      >
        <Splitter.Panel collapsible defaultSize={leftDefaultSize}>
          {htmlContent ? (
            <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
          ) : (
            left
          )}
        </Splitter.Panel>
        <Splitter.Panel collapsible defaultSize={rightDefaultSize}>{right}</Splitter.Panel>
      </Splitter>
    </div>
  );
};

export default CustomSplitter;
