import {DictApi, State, Dispatch} from "@/components/dict/types";


interface selectOption {
    label: string;
    value: string;
    children?: selectOption[];
}

export class DictPresenter {
    //更新数据状态
    private readonly dispatch: Dispatch<State>;

    private readonly api: DictApi;

    private state: State;

    public constructor(dispatch: Dispatch<State>, api: DictApi, state: State) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: State) => {
        this.state = newStatus;
    }

    // 数据
    public loadDictSelect = () => {
        this.api.loadDictSelect().then(data => {
            const rawData = data.data?.list || [];
            const transformedData = this.getSelectOptions(rawData);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    dictData: rawData,
                    filteredData: transformedData,
                    loading: false
                }
            })
        });
    }

    public getSelectOptions = (data: any[]): selectOption[] => {
        return data.map(item => ({
            label: item.name,
            value: item.code,
            children: item.children ? this.getSelectOptions(item.children) : undefined,
        }));
    };

    public filteredData = (labelCode: string) => {
        this.api.loadDictSelectByLabelCode(labelCode).then(data => {
            const rawData = data.data?.list || [];
            const transformedData = this.getSelectOptions(rawData);
            this.dispatch((prevState) => {
                return {
                    ...prevState,
                    filteredData: transformedData,
                    loading: false
                }
            })
        });
    }

    public loading = () => {
        this.dispatch((prevState) => {
            return {
                ...prevState,
                loading: true
            }
        })
    }
}