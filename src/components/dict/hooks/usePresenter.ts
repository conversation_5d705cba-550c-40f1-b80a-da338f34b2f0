import React from "react";
import {State} from "@/components/dict/types";
import {DictPresenter} from "@/components/dict/presenter";
import {DictApiImpl} from "@/components/dict/model";

const initialState: State = {
    filteredData: [],
    dictData: [],
    loading: true
}

export function useValuePresenter() {
    const [state, dispatch] = React.useState<State>(initialState);

    const presenterRef = React.useRef<DictPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new DictPresenter(dispatch, new DictApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}