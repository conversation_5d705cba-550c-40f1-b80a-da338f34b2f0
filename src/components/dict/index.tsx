/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description:
 * @LastEditTime: 2025-07-15 14:11:07
 * @LastEditors: wangjiantao
 */
import React, { CSSProperties, memo, useEffect, useMemo } from "react";
import { useValuePresenter } from "@/components/dict/hooks/usePresenter";
import {FormSelect} from "@/components/form-pc";
import { FormValidateContent } from "@codingapi/ui-framework";

interface DictSelectProps {
  name: string;
  key?: string;
  hidden?: boolean;
  disabled?: boolean;
  title?: string;
  selectMultiple?: boolean;
  required?: boolean;
  labelCode?: string; // 可选的字典类型code
  onChange?: (value: string) => void; // 选中值变化时的回调
  value?: string | null;
  style?: CSSProperties;
  validateFunction?: (
    content: FormValidateContent,
    message?: string
  ) => Promise<string[]>;
}

export const DictSelect: React.FC<DictSelectProps> = memo(
  (props) => {
    const { state, presenter } = useValuePresenter();

    // 根据labelCode过滤数据
    useEffect(() => {
      if (props.labelCode) {
        presenter.loading();
        presenter.filteredData(props.labelCode);
      } else {
        presenter.loadDictSelect();
      }
    }, [props.labelCode]);

    return (
      <div className="dict-select" style={props.style}>
        {state.loading ? (
          <div>加载中...</div>
        ) : state.filteredData.length > 0 ? (
          <FormSelect
            validateFunction={props.validateFunction}
            name={props.name}
            key={props.key}
            label={props.title}
            placeholder={props.title ? `请选择${props.title}` : "请选择"}
            disabled={props.disabled}
            hidden={props.hidden}
            selectMultiple={props.selectMultiple}
            options={state.filteredData}
            required={props.required}
            value={props.value}
            onChange={(value: string) => {
              if (props.onChange) {
                props.onChange(value);
              }
            }}
          />
        ) : (
          <div>暂无数据</div>
        )}
      </div>
    );
  },
  (prev: DictSelectProps, next: DictSelectProps) => {
    return prev.labelCode === next.labelCode && prev.value === next.value;
  }
);
