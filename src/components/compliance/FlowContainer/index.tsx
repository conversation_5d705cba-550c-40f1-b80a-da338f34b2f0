/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 13:59:34
 * @Description: 
 * @LastEditTime: 2025-07-15 10:32:04
 * @LastEditors: wangjiantao
 */
import React, {useRef, useEffect, CSSProperties} from 'react';
import {FlowImage} from "@/components/flowImage";

interface FlowContainerProps {
    id: number;
}

export const FlowContainer: React.FC<FlowContainerProps> = (props) => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (containerRef.current) {
            const container = containerRef.current;
            const scrollWidth = container.scrollWidth;
            const clientWidth = container.clientWidth;

            // 居中滚动条
            container.scrollLeft = (scrollWidth - clientWidth) / 2;
        }
    }, []);

    return (
        <div
            ref={containerRef}
            style={{
                width: "100%",
                overflow: "auto",
                height: 'calc(100vh - 120px)',
                whiteSpace: "nowrap",
            }}
        >
            <div
                style={{
                    display: "inline-block",
                    transform: "scale(1.15)",
                    transformOrigin: "top left",
                    width: "1300px",
                }}
            >
                <FlowImage recordId={props.id}/>
            </div>
        </div>
    );
};
